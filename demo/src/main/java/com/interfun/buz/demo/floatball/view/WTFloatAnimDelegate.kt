package com.interfun.buz.demo.floatball.view

// import com.interfun.buz.basefloat.EasyFloat
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.graphics.Color
import android.transition.AutoTransition
import android.transition.Fade
import android.transition.TransitionManager
import android.transition.TransitionSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import androidx.core.transition.addListener
import androidx.core.view.postDelayed
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.anim.VisibilityRotationTransition
import com.interfun.buz.common.anim.VisibilityScaleTransition
import com.interfun.buz.demo.R
import com.interfun.buz.demo.databinding.LayoutFloatTestBinding
import com.interfun.buz.demo.floatball.entity.WTFloatType
import kotlin.math.roundToInt

/**
 * <AUTHOR>
 * @date 2023/2/17
 * @desc
 */
class WTFloatAnimDelegate(private val binding: LayoutFloatTestBinding) {

    companion object {
        const val TAG = "WTFloatAnim"
        const val FLOAT_ANIM_DURATION = 200L
    }

    private val floatTag = R.string.common_tag_wt_float_view.asString()
    private val root get() = binding.root as ConstraintLayout

    private val floatTransAnim: AutoTransition
    private var onTransAnimEndCallback: DefaultCallback? = null
    private var isTransAnimation = false
    private val expandedW = 246.dp
    private val expandedH = 212.dp
    private val halfExpandedW = 216.dp
    private val halfExpandedH = 110.dp
    private val minimizedW = 78.dp
    private val minimizedH = 110.dp

    private var windowWidth = 0f
    private var windowHeight = 0f
    private var windowX = 0f
    private var windowY = 0f
    private var leftDistance = 0f
    private var rightDistance = 0f

    private val expandAnimator: ValueAnimator
    private var screenStartX = 0
    private var screenEndX = root.context.screenWidth
    private var screenBottomY = root.context.screenHeight
    private var isAutoToggle = false
    private var isLastCollapse = false
    private val sideWidth = 50.dp
    private val maxBgRadius = 42.dp
    private val minBgRadius = 20.dp
    private val scaleHeight = 0.85f
    private val scaleWidth = 0.77f
    private val maxBgRadiusSmall = 37.dp
    private val minBgRadiusSmall = 15.dp
    private val scaleHeightSmall = 0.5636f
    private val scaleWidthSmall = 1.41f
    private val transXSmall = (-17).dpFloat

    private val collapsedPlayingAnimator: ValueAnimator
    private val argbEvaluator = ArgbEvaluator()
    private val normalBgColor = Color.parseColor("#292929")
    private val highlightBgColor = R.color.basic_primary.asColor()
    private val normalArrowRes = R.drawable.chat_wt_float_arrow_light.asDrawable()
    private val highlightArrowRes = R.drawable.chat_wt_float_arrow_dark.asDrawable()

    private var _isLeft = false
    val isLeft get() = _isLeft
    var isCollapsed = false
        private set
    var currentType = WTFloatType.Minimized
        private set
    var floatTransStatusCallback: TwoParamCallback<WTFloatType, Boolean>? = null
    val isMinimized get() = currentType == WTFloatType.Minimized
    val isHalfExpanded get() = currentType == WTFloatType.HalfExpanded
    val isExpanded get() = currentType == WTFloatType.Expanded
    var isInitialized = false
    var isPlaying = false
        set(value) {
            if (field != value) {
                field = value
                updatePlayingStatus()
            }
        }

    init {
        updateLayoutDirection(false)
        floatTransAnim = AutoTransition().apply {
            duration = FLOAT_ANIM_DURATION
            addListener(onEnd = {
                when (currentType) {
                    // Avoid update the coordinates after layoutChange, which will cause flickering
                    // so handle the positioning yourself after the end of the put away animation
                    // 避免在layoutChange后，将坐标重新定位，会导致闪烁，所以在收起动画结束后自己处理定位
                    WTFloatType.Minimized -> {
                        if (isLeft.not()) {
                            setLayoutChangedGravity(Gravity.START)
                            root.layoutSize(minimizedW, minimizedH)
                            updateFloat(x = screenEndX - minimizedW)
                        } else {
                            root.layoutSize(minimizedW, minimizedH)
                        }
                    }
                    WTFloatType.HalfExpanded -> {
                        if (isLeft.not()) {
                            setLayoutChangedGravity(Gravity.START)
                            root.layoutSize(halfExpandedW, halfExpandedH)
                            updateFloat(x = screenEndX - halfExpandedW)
                        } else {
                            root.layoutSize(halfExpandedW, halfExpandedH)
                        }
                    }
                    WTFloatType.Expanded -> root.layoutSize(expandedW, expandedH)
                }
                isTransAnimation = false
                val sidePercent = if (isMinimized) 0.3f else 0.4f
                // EasyFloat.setSidePercent(sidePercent, floatTag)
                onTransAnimEndCallback?.invoke()
                floatTransStatusCallback?.invoke(currentType, isAutoToggle)
                isAutoToggle = false
            })
        }
        expandAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            addUpdateListener {
                val p = it.animatedFraction
                val w = when (currentType) {
                    WTFloatType.Expanded -> expandedW
                    WTFloatType.HalfExpanded -> halfExpandedW
                    else -> minimizedW
                }
                val start = if (isLeft) {
                    screenStartX + sideWidth - w
                } else {
                    screenEndX - sideWidth
                }
                val end = if (isLeft) {
                    screenStartX
                } else {
                    screenEndX - w
                }
                val value = start + (end - start) * p
                log(TAG, "expandAnimator p:$p value:$value")
                updateFloat(x = value.roundToInt())
            }
            addListener(onEnd = {
                onCollapsedStateChange()
            })
        }
        collapsedPlayingAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = FLOAT_ANIM_DURATION
            addUpdateListener {
                val f = it.animatedFraction
                val colorBg = if (isPlaying) {
                    argbEvaluator.evaluate(f, normalBgColor, highlightBgColor) as Int
                } else {
                    argbEvaluator.evaluate(f, highlightBgColor, normalBgColor) as Int
                }
                binding.viewBgCollapsed.backgroundColor(colorBg)
                binding.viewBgCollapsedSmall.backgroundColor(colorBg)
                val res = if (f >= 0.5) {
                    if (isPlaying) highlightArrowRes else normalArrowRes
                } else {
                    if (isPlaying) normalArrowRes else highlightArrowRes
                }
                binding.ivArrowRight.setImageDrawable(res)
                binding.ivArrowLeft.setImageDrawable(res)
                binding.ivArrowRightSmall.setImageDrawable(res)
                binding.ivArrowLeftSmall.setImageDrawable(res)
                log(TAG, "updateCollapsedPlayingStatus f:$f")
            }
        }
        initClickListener()
    }

    private fun initClickListener() {
        binding.flPortrait.click {
            toggle()
        }
        binding.viewLeftClick.click {
            expandAnimator.start()
        }
        binding.viewRightClick.click {
            expandAnimator.start()
        }
    }

    fun toggle() {
        if (isCollapsed) {
            expandAnimator.start()
        } else if (isExpanded) {
            minimize()
        } else {
            expand()
        }
    }

    fun minimize() {
        if (!isCollapsed && isExpanded) {
            if (isPlaying) {
                transFloat(WTFloatType.HalfExpanded)
            } else {
                transFloat(WTFloatType.Minimized)
            }
        }
    }

    fun expand() {
        transFloat(WTFloatType.Expanded)
    }

    private fun transFloat(type: WTFloatType) {
        log(TAG, "transFloat type:$type currentType:$currentType")
        if (type.level == currentType.level) {
            onTransAnimEndCallback = null
            return
        }
        if (isTransAnimation) {
            onTransAnimEndCallback = {
                transFloat(type)
            }
            return
        }
        onTransAnimEndCallback = null
        isTransAnimation = true
        if (type.level > currentType.level) {
            when (type) {
                WTFloatType.Minimized -> root.layoutSize(minimizedW, minimizedH)
                WTFloatType.HalfExpanded -> root.layoutSize(halfExpandedW, halfExpandedH)
                WTFloatType.Expanded -> root.layoutSize(expandedW, expandedH)
            }
        }
        currentType = type
        binding.clRoot.postDelayed(20) {
            TransitionManager.beginDelayedTransition(root, floatTransAnim)
            if (isHalfExpanded || isMinimized) {
                updateLayoutDirection(isLeft)
            } else if (isExpanded) {
                updateLayoutDirection(true)
            }
            when (currentType) {
                WTFloatType.Minimized -> {
                    binding.groupExpand.gone()
                    binding.groupHalfExpand.gone()
                    showOrDismissMorePop(false,false)
                }
                WTFloatType.HalfExpanded -> {
                    binding.groupExpand.gone()
                    binding.groupHalfExpand.visible()
                    showOrDismissMorePop(false,false)
                }
                WTFloatType.Expanded -> {
                    binding.groupExpand.visible()
                    binding.groupHalfExpand.visible()
                    showOrDismissMorePop(false,false)
                }
            }
            if (windowY + windowHeight > screenBottomY) {
                ValueAnimator.ofFloat(windowY, screenBottomY - windowHeight + 16.dp).apply {
                    duration = FLOAT_ANIM_DURATION
                    addUpdateListener {
                        updateFloat(y = (it.animatedValue as Float).roundToInt())
                    }
                    start()
                }
            }
        }
    }

    fun updateWindowView(windowView: View, params: WindowManager.LayoutParams) {
        windowWidth = windowView.width.toFloat()
        windowHeight = windowView.height.toFloat()
        windowX = params.x.toFloat()
        windowY = params.y.toFloat()
        leftDistance = windowX - screenStartX
        rightDistance = screenEndX - (windowX + windowWidth)
        _isLeft = leftDistance < rightDistance
        log(TAG, "updateWindowView leftDistance: $leftDistance rightDistance: $rightDistance")
        // after update window, reset layout change gravity,
        // because when trans to expand finished, the gravity is flipped by a moment
        // 在float window的LayoutListener中回调后，将这个gravity重设回正常值
        // 因为在屏幕右侧执行收起动画后，会将window的gravity改为反方向的End，避免坐标更新后的闪烁
        if (isLeft) {
            setLayoutChangedGravity(Gravity.START)
        } else {
            setLayoutChangedGravity(Gravity.END)
        }
        if (isTransAnimation || !isInitialized) {
            log(TAG, "updateWindowView isTransAnimation return")
            return
        }
        val animProgress = if (windowX < screenStartX) {
            (windowX - screenStartX + windowWidth - sideWidth) / (windowWidth - sideWidth)
        } else if (windowX > screenEndX - windowWidth) {
            1f - (windowX - screenEndX + windowWidth) / (windowWidth - sideWidth)
        } else {
            1f
        }
        log(TAG, "updateWindowView x: $windowX progress: $animProgress width: $windowWidth")
        isCollapsed = if (isExpanded) {
            animProgress < 0.38f
        } else if (isHalfExpanded) {
            animProgress < 0.48f
        } else {
            animProgress < 0.18f
        }
        updateProgress(animProgress)
    }

    private fun updateProgress(progress: Float) {
        val p = when (progress) {
            in 0.25f..1f -> 0f
            in 0f..0.25f -> 1f - 4f * progress
            else -> 1f
        }
        binding.viewLeftClick.visibleIf(p == 1f)
        binding.viewRightClick.visibleIf(p == 1f)
        binding.clRoot.enableIf(p == 0f)

        val alpha = when (progress) {
            in 0.5f..1f -> 0f
            in 0.25f..0.5f -> 2f - progress * 4f
            else -> 1f
        }

        if (alpha == 1f) {
            binding.clRoot.alpha = 0f
        } else {
            binding.clRoot.alpha = 1f
        }

        if (isExpanded) {
            updateExpandCollapseAnim(p, alpha)
        } else {
            updateCollapseAnim(p, alpha)
        }
    }

    private fun updateExpandCollapseAnim(p:Float, alpha:Float){
        val scaleX = 1f - (1f - scaleWidth) * p
        val scaleY = 1f - (1f - scaleHeight) * p
        binding.flCollapsed.scaleX = scaleX
        binding.flCollapsed.scaleY = scaleY

        binding.ivArrowLeft.scaleX = 1f / scaleX
        binding.ivArrowLeft.scaleY = 1f / scaleY

        binding.ivArrowRight.scaleX = 1f / scaleX
        binding.ivArrowRight.scaleY = 1f / scaleY

        if (isLeft) {
            binding.ivArrowRight.alpha = p
            binding.ivArrowLeft.alpha = 0f
        } else {
            binding.ivArrowLeft.alpha = p
            binding.ivArrowRight.alpha = 0f
        }

        val radius = maxBgRadius - (maxBgRadius - minBgRadius) * p
        val radii = if (isLeft) {
            floatArrayOf(0f, 0f, radius, radius, radius, radius, 0f, 0f)
        } else {
            floatArrayOf(radius, radius, 0f, 0f, 0f, 0f, radius, radius)
        }
        binding.viewBgCollapsed.backgroundRadii(radii)
        binding.flCollapsed.alpha = alpha
        binding.flCollapsed.visibleIf(alpha > 0f)
    }

    private fun updateCollapseAnim(p:Float, alpha:Float){
        val transX = transXSmall * (1f - p)
        binding.flCollapsedSmall.translationX = if (isLeft) -transX else transX

        val scaleX = scaleWidthSmall + (1f - scaleWidthSmall) * p
        val scaleY = scaleHeightSmall + (1f - scaleHeightSmall) * p
        binding.flCollapsedSmall.scaleX = scaleX
        binding.flCollapsedSmall.scaleY = scaleY

        binding.ivArrowLeftSmall.scaleX = 1f / scaleX
        binding.ivArrowLeftSmall.scaleY = 1f / scaleY

        binding.ivArrowRightSmall.scaleX = 1f / scaleX
        binding.ivArrowRightSmall.scaleY = 1f / scaleY

        if (isLeft) {
            binding.ivArrowRightSmall.alpha = p
            binding.ivArrowLeftSmall.alpha = 0f
        } else {
            binding.ivArrowLeftSmall.alpha = p
            binding.ivArrowRightSmall.alpha = 0f
        }

        val radius = maxBgRadiusSmall - (maxBgRadiusSmall - minBgRadiusSmall) * p
        val radii = if (isLeft) {
            floatArrayOf(0f, 0f, radius, radius, radius, radius, 0f, 0f)
        } else {
            floatArrayOf(radius, radius, 0f, 0f, 0f, 0f, radius, radius)
        }
        binding.viewBgCollapsedSmall.backgroundRadii(radii)
        binding.flCollapsedSmall.alpha = alpha
        binding.flCollapsedSmall.visibleIf(alpha > 0f)
        if (alpha > 0f) {
            if (isLeft.not()) {
                binding.spaceCollapseEnd.gone()
                binding.spaceCollapseStart.visible()
            } else {
                binding.spaceCollapseEnd.visible()
                binding.spaceCollapseStart.gone()
            }
        } else {
            binding.spaceCollapseEnd.gone()
            binding.spaceCollapseStart.gone()
        }
    }

    fun onCollapsedStateChange() {
        if (isLastCollapse != isCollapsed) {
            log(WTFloatViewGroup.TAG, "dragEnd isCollapsed:$isCollapsed")
            isLastCollapse = isCollapsed
            autoTrans()
        }
    }

    fun updateOrientation(screenStartX: Int, screenEndX: Int) {
        this.screenStartX = screenStartX
        this.screenEndX = screenEndX
        this.screenBottomY = root.context.screenHeight
        this._isLeft = false
    }

    fun updateInLeft(isLeft: Boolean) {
        _isLeft = isLeft
        if (isHalfExpanded || isMinimized) {
            updateLayoutDirection(isLeft)
        }
    }

    fun onDragAnimStart(){
        if (isHalfExpanded || isMinimized) {
            updateLayoutDirection(isLeft)
        }
        setLayoutChangedGravity(if (isLeft) Gravity.START else Gravity.END)
    }

    private fun autoTrans() {
        if (!isCollapsed) {
            if (isPlaying && isMinimized) {
                isAutoToggle = true
                transFloat(WTFloatType.HalfExpanded)
            } else if (!isPlaying && isHalfExpanded) {
                isAutoToggle = true
                transFloat(WTFloatType.Minimized)
            }
        }
    }

    private fun updatePlayingStatus(){
        autoTrans()
        if (!collapsedPlayingAnimator.isRunning) {
            collapsedPlayingAnimator.start()
        }
    }

    private fun setLayoutChangedGravity(gravity: Int) {
        // EasyFloat.setLayoutChangedGravity(floatTag, gravity)
    }

    private fun updateFloat(
        x: Int = -1,
        y: Int = -1,
        width: Int = -1,
        height: Int = -1,
        delay: Long = 0L
    ) {
        // EasyFloat.updateFloat(floatTag, x, y, width, height, delay)
    }

    private fun updateLayoutDirection(isLTR: Boolean) {
        binding.clRoot.layoutDirection =
            if (isLTR) View.LAYOUT_DIRECTION_LTR else View.LAYOUT_DIRECTION_RTL
        val bias = if (isLTR) 0f else 1f
        binding.tvName.constraintHorizontalBias(bias)
        binding.clDescribe.constraintHorizontalBias(bias)
    }

    fun showOrDismissMorePop(show: Boolean, needAnim: Boolean = true) {
        if (needAnim) {
            TransitionManager.beginDelayedTransition(
                binding.root as ViewGroup,
                TransitionSet().apply {
                    duration = 200
                    addTarget(binding.viewSpeakBg)
                    addTarget(binding.flSpeak)
                    addTarget(binding.tvHide)
                    addTarget(binding.tvGoToBuz)
                    addTarget(binding.viewBgMorePopup)
                    addTarget(binding.iftvMore)
                    addTarget(binding.iftvDismiss)
                    addTransition(Fade(Fade.IN))
                    addTransition(VisibilityScaleTransition(0.3f,0.3f).apply {
                        excludeTarget(binding.viewBgMorePopup, true)
                        excludeTarget(binding.iftvMore, true)
                        excludeTarget(binding.iftvDismiss, true)
                    })
                    addTransition(Fade(Fade.OUT))
                    addTransition(VisibilityRotationTransition(0f,90f).apply {
                        excludeTarget(binding.viewSpeakBg, true)
                        excludeTarget(binding.flSpeak, true)
                        excludeTarget(binding.tvHide, true)
                        excludeTarget(binding.tvGoToBuz, true)
                        excludeTarget(binding.viewBgMorePopup, true)
                        excludeTarget(binding.iftvDismiss, true)
                    })
                    addTransition(VisibilityRotationTransition(0f,-90f).apply {
                        excludeTarget(binding.viewSpeakBg, true)
                        excludeTarget(binding.flSpeak, true)
                        excludeTarget(binding.tvHide, true)
                        excludeTarget(binding.tvGoToBuz, true)
                        excludeTarget(binding.viewBgMorePopup, true)
                        excludeTarget(binding.iftvMore, true)
                    })
                }
            )
        }
        val isExpandType = currentType == WTFloatType.Expanded
        binding.iftvMore.visibleIf(isExpandType && show.not())
        binding.iftvDismiss.visibleIf(isExpandType && show)
        binding.flSpeak.visibleIf(isExpandType && show.not())
        binding.viewSpeakBg.visibleIf(isExpandType && show.not())
        binding.viewBgMorePopup.visibleIf(isExpandType && show)
        binding.tvHide.visibleIf(isExpandType && show)
        binding.tvGoToBuz.visibleIf(isExpandType && show)
    }

    fun toggleMorePopup(){
        val isMoreShown = binding.viewBgMorePopup.isVisible()
        showOrDismissMorePop(isMoreShown.not())
    }

    fun getStateLogString() = if (isCollapsed) "collapse" else if (isExpanded) "expand" else "fold"
}