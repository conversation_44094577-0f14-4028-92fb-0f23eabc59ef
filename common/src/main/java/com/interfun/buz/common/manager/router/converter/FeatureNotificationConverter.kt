package com.interfun.buz.common.manager.router.converter

import android.os.Bundle
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.parseString
import com.interfun.buz.common.constants.*
import org.json.JSONObject

class FeatureNotificationRouterArgs(
    val url:String
)

class FeatureNotificationConverter: RouterConverter<FeatureNotificationRouterArgs> {

    override val scheme = RouterSchemes.Common.FEATURE_NOTIFICATION
    override val path = PATH_COMMON_DIALOG_NEW_FEATURE

    override fun convertToBundle(extraData: JSONObject?, result: Bundle): Bo<PERSON>an {
        if (extraData == null) {
            return false
        }
        try {
            val url = extraData.parseString(RouterParamKey.Common.KEY_URL)
            result.putString(RouterParamKey.Common.KEY_URL, url)
        } catch (e: Exception) {
            logError(e)
            return false
        }
        return true
    }

    override fun convertToExtraData(args: FeatureNotificationRouterArgs): JSONObject {
        return JSONObject().apply {
            put(RouterParamKey.Common.KEY_URL, args.url)
        }
    }
}