package com.interfun.buz.chat.common.view.widget

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.LocalIndication
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.containsIgnoreCase
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.viewmodel.GroupMentionItem
import com.interfun.buz.chat.common.viewmodel.GroupMentionItem.AiCharacterItem
import com.interfun.buz.chat.common.viewmodel.GroupMentionItem.UserMemberItem
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.ktx.getUsername
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.compose.components.HighlightSelectedText
import com.interfun.buz.compose.components.PortraitImage
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.Flow
import kotlin.collections.set

// Immutable data classes for persistent state
private data class FilteredMemberData(
    val members: PersistentList<MentionItemData>,
    val aiCharacters: PersistentList<MentionItemData>
)

private data class MentionItemData(
    val id: Long,
    val remark: String?,
    val username: String,
    val portraitUrl: String?,
    val userRelationInfo: UserRelationInfo
)

@Composable
fun MentionPanelContent(
    isVisible: Boolean,
    queryString: String,
    itemsListFlow: Flow<List<GroupMentionItem>>,
    onMentionItemClicked: OneParamCallback<UserRelationInfo>,
    onTapOutsideArea: () -> Unit
) {
    val itemsList by itemsListFlow.collectAsStateWithLifecycle(initialValue = emptyList())

    // Create persistent lists for filtered items
    val filteredMemberData = remember(itemsList, queryString) {
        val members = itemsList.mapNotNull { item ->
            (item as? UserMemberItem)?.takeIf {
                !it.groupMember.userId.isMe() && (it.groupMember.remark?.containsIgnoreCase(queryString) == true || it.groupMember.getUsername().containsIgnoreCase(queryString))
            }
        }.map {
            MentionItemData(
                id = it.groupMember.userId,
                remark = it.groupMember.remark,
                username = it.groupMember.getUsername(),
                portraitUrl = it.groupMember.portrait,
                userRelationInfo = it.groupMember
            )
        }.toPersistentList()

        val aiCharacters = itemsList.mapNotNull { item ->
            (item as? AiCharacterItem)?.takeIf {
                it.aiCharacter.remark?.containsIgnoreCase(queryString) == true || it.aiCharacter.getUsername().containsIgnoreCase(queryString)
            }
        }.map {
            MentionItemData(
                id = it.aiCharacter.userId,
                remark = it.aiCharacter.remark,
                username = it.aiCharacter.getUsername(),
                portraitUrl = it.aiCharacter.portrait,
                userRelationInfo = it.aiCharacter
            )
        }.toPersistentList()

        FilteredMemberData(members, aiCharacters)
    }

    val shouldShow by remember(isVisible, filteredMemberData) {
        derivedStateOf {
            isVisible && (filteredMemberData.members.isNotEmpty() || filteredMemberData.aiCharacters.isNotEmpty())
        }
    }
    val innerPanelBounds = remember { mutableStateOf(Rect.Zero) }

    AnimatedVisibility(
        visible = shouldShow,
        enter = slideInVertically(initialOffsetY = { it }),
        exit = slideOutVertically(targetOffsetY = { it })
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = { offset ->
                            // Check if the tap is outside the inner panel's bounds
                            if (!innerPanelBounds.value.contains(offset)) {
                                // If yes, dismiss the panel
                                onTapOutsideArea.invoke()
                            }
                        }
                    )
                }
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .align(Alignment.BottomCenter)
                    .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
                    .background(color = R.color.color_background_5_default.asColor())
                    .onGloballyPositioned { layoutCoordinates ->
                        // Update innerPanelBounds with the actual bounds of this Box
                        innerPanelBounds.value = layoutCoordinates.boundsInRoot()
                    }
                    .animateContentSize()
            ) {
                PersistentMentionList(
                    queryString = queryString,
                    filteredMemberData = filteredMemberData,
                    onMentionItemClicked = onMentionItemClicked
                )
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun PersistentMentionList(
    queryString: String,
    filteredMemberData: FilteredMemberData,
    onMentionItemClicked: OneParamCallback<UserRelationInfo>
) {
    // To avoid recomposition, create a stable reference to the click handler
    val clickHandlerMap = remember { mutableMapOf<Long, () -> Unit>() }

    // Pre-create all click handlers
    for (item in filteredMemberData.members) {
        if (!clickHandlerMap.containsKey(item.id)) {
            clickHandlerMap[item.id] = { onMentionItemClicked.invoke(item.userRelationInfo) }
        }
    }

    for (item in filteredMemberData.aiCharacters) {
        if (!clickHandlerMap.containsKey(item.id)) {
            clickHandlerMap[item.id] = { onMentionItemClicked.invoke(item.userRelationInfo) }
        }
    }

    LazyColumn(
        state = rememberLazyListState(),
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        // Display user member list
        if (filteredMemberData.members.isNotEmpty()) {
            stickyHeader(key = "user-member-header") {
                MentionItemTitle(modifier = Modifier.animateItem(), R.string.group_members_header.asString())
            }
            items(
                items = filteredMemberData.members,
                key = { it.id },
                contentType = { "member" }
            ) { item ->
                val clickHandler = clickHandlerMap[item.id] ?: { onMentionItemClicked.invoke(item.userRelationInfo) }

                PersistentMentionItem(
                    modifier = Modifier.animateItem(),
                    queryString = queryString,
                    itemData = item,
                    onClick = clickHandler
                )
            }
        }

        // Display bot member list
        if (filteredMemberData.aiCharacters.isNotEmpty()) {
            stickyHeader(key = "ai-member-header") {
                MentionItemTitle(modifier = Modifier.animateItem(), R.string.ai_characters_header.asString())
            }
            items(
                items = filteredMemberData.aiCharacters,
                key = { it.id },
                contentType = { "ai" }
            ) { item ->
                val clickHandler = clickHandlerMap[item.id] ?: { onMentionItemClicked.invoke(item.userRelationInfo) }

                PersistentMentionItem(
                    modifier = Modifier.animateItem(),
                    queryString = queryString,
                    itemData = item,
                    onClick = clickHandler
                )
            }
        }
    }
}

@Composable
private fun MentionItemTitle(modifier: Modifier = Modifier, title: String) {
    Text(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(color = R.color.color_background_5_default.asColor())
            .clickable(
                indication = null,
                interactionSource = null
            ) { }
            .padding(start = 20.dp, top = 13.dp, bottom = 13.dp),
        text = title,
        style = TextStyles.bodyMedium(),
        color = R.color.color_text_white_tertiary.asColor()
    )
}

@Composable
private fun PersistentMentionItem(
    modifier: Modifier = Modifier,
    queryString: String,
    itemData: MentionItemData,
    onClick: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val itemBgColor = if (isPressed) R.color.color_background_5_pressed.asColor() else R.color.transparent.asColor()
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = interactionSource,
                indication = null
            ) { onClick() }
            .background(color = itemBgColor)
            .padding(horizontal = 20.dp, vertical = 10.dp)
    ) {
        Box {
            PortraitImage(
                modifier = Modifier.size(50.dp),
                url = itemData.portraitUrl
            )
        }
        Spacer(modifier = Modifier.width(10.dp))
        PersistentMentionItemNames(
            queryString = queryString,
            remark = itemData.remark,
            username = itemData.username
        )
    }
}

@Composable
private fun PersistentMentionItemNames(
    queryString: String,
    remark: String? = null,
    username: String
) {
    Column(verticalArrangement = Arrangement.SpaceBetween, modifier = Modifier.fillMaxWidth()) {
        // Shows remark if exists
        if (!remark.isNullOrEmpty()) {
            HighlightSelectedText(
                modifier = Modifier.fillMaxWidth(),
                originalString = remark,
                highlightText = queryString,
                highlightColor = R.color.basic_primary.asColor(),
                textColor = R.color.color_text_white_primary.asColor(),
                textStyle = TextStyles.labelLarge()
            )
        }
        // Shows username. Style changes based on the existence of remark
        HighlightSelectedText(
            modifier = Modifier.fillMaxWidth(),
            originalString = username,
            highlightText = queryString,
            highlightColor = R.color.basic_primary.asColor(),
            textColor = if (remark.isNullOrEmpty()) R.color.color_text_white_primary.asColor() else R.color.color_text_white_tertiary.asColor(),
            textStyle = if (remark.isNullOrEmpty()) TextStyles.labelLarge() else TextStyles.bodyMedium()
        )
    }
}

@Preview
@Composable
private fun PreviewMentionItem() {
    val previewItem = MentionItemData(
        id = 5736427615927465,
        remark = "louis",
        username = "浪漫的我们",
        portraitUrl = "",
        userRelationInfo = UserRelationInfo(123)
    )
    PersistentMentionItem(queryString = "浪漫", itemData = previewItem) {}
}

@Preview
@Composable
private fun PreviewMentionItemTitle() {
    MentionItemTitle(title = "Group Members")
}