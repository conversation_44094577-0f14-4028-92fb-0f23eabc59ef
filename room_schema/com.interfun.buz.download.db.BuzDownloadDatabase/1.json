{"formatVersion": 1, "database": {"version": 1, "identityHash": "0a95e5bd6f0abff72358595aa848ea62", "entities": [{"tableName": "buz_download_cache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`taskId` TEXT NOT NULL DEFAULT '', `storagePath` TEXT NOT NULL DEFAULT '', `remoteUrl` TEXT NOT NULL DEFAULT '', `totalLength` INTEGER NOT NULL DEFAULT 0, `fileName` TEXT NOT NULL DEFAULT '', `status` INTEGER NOT NULL DEFAULT 0, `startTime` INTEGER NOT NULL DEFAULT 0, `endTime` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`taskId`, `storagePath`))", "fields": [{"fieldPath": "taskId", "columnName": "taskId", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "storagePath", "columnName": "storagePath", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "remoteUrl", "columnName": "remoteUrl", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "totalLength", "columnName": "totalLength", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "fileName", "columnName": "fileName", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["taskId", "storagePath"]}, "indices": [{"name": "index_buz_download_cache_taskId", "unique": false, "columnNames": ["taskId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_buz_download_cache_taskId` ON `${TABLE_NAME}` (`taskId`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0a95e5bd6f0abff72358595aa848ea62')"]}}