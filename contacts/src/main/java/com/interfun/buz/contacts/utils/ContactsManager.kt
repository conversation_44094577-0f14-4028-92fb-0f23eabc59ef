package com.interfun.buz.contacts.utils

import android.Manifest
import android.os.SystemClock
import com.buz.idl.user.bean.Contact
import com.buz.idl.user.request.RequestGetBlackList
import com.buz.idl.user.request.RequestGetRegisteredContacts
import com.buz.idl.user.request.RequestGetUploadContactType
import com.buz.idl.user.request.RequestUploadContacts
import com.interfun.buz.common.net.newInstanceBuzNetUserServiceClient
import com.google.gson.Gson
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.utils.PinyinUtils
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.RouterParamValues.ChatHome
import com.interfun.buz.common.database.UserDatabase
import com.interfun.buz.common.database.entity.ContactsBean
import com.interfun.buz.common.eventbus.ContactsSyncCompleteEvent
import com.interfun.buz.common.ktx.getContactFullName
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.ktx.phone
import com.interfun.buz.common.ktx.regionCode
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.service.LoginService
import com.interfun.buz.common.utils.ClientTracker
import com.interfun.buz.common.utils.TraceIdGenerator
import com.interfun.buz.contacts.constants.ContactsMMKV
import com.interfun.buz.contacts.manager.FriendsManager
import kotlinx.coroutines.*
import java.io.ByteArrayOutputStream
import java.util.zip.GZIPOutputStream

/**
 * Contacts manger
 */
object ContactsManager {

    const val TAG = "ContactsManager"

    private val userDb get() = UserDatabase.currInstance
    private val contactsDao get() = userDb?.getContactsDao()
    private var syncContactsJob: Job? = null
    var isContactsSyncComplete = false
        private set

    private val userService by lazy { newInstanceBuzNetUserServiceClient() }

    fun logout() {
        if (syncContactsJob?.isActive == true) {
            syncContactsJob?.cancel()
        }
        isContactsSyncComplete = false
        routerServices<LoginService>().value?.clearNewEditedInfoRegisterRecord()
    }

    /**
     * Get UnRegister contact list
     * order by firstLetter, a-z > #
     */
    suspend fun getUnRegisterContactSortList(): List<ContactsBean>? {
        val startTime = System.currentTimeMillis()
        val contactList = getUnRegisterContactList()
        logInfo(TAG, "getUnRegisterContactList costTime: ${System.currentTimeMillis() - startTime}")
        val sortStartTime = System.currentTimeMillis()
        val sortList = contactList
            ?.takeIf { it.isNotEmpty() }
            ?.asSequence()
            ?.map { contact -> PinyinUtils.toPinyin(contact.getContactFullName()) to contact }
            ?.sortedWith(compareBy(String.CASE_INSENSITIVE_ORDER) { it.first })?.map { it.second }
            ?.toList()
        logInfo(TAG, "getUnRegisterContactSortList costTime: ${System.currentTimeMillis() - sortStartTime}")
        return sortList
    }

    /**
     * Get UnRegister contact list
     * 未注册通讯录联系人列表 = 本地所有通讯录联系人 - 全部已注册联系人
     */
    private suspend fun getUnRegisterContactList(): List<ContactsBean>? {
        val localAllContacts = contactsDao?.queryAllContacts()
        if (localAllContacts.isNullOrEmpty()) {
            return null
        }
        val registeredContactsResp =
            userService
                .getRegisteredContacts(RequestGetRegisteredContacts(2))
        if (registeredContactsResp.isSuccess) {
            val registeredContactList = registeredContactsResp.data?.contactList
            if (registeredContactList.isNullOrEmpty()) {
                return localAllContacts
            }
            val phonesThatNeedFiltering = mutableSetOf<String?>().apply {
                addAll(registeredContactList.map { it.userInfo.phone })
                add(UserSessionManager.phone)
            }
            val unRegisterContactList = localAllContacts.filterNot { contact ->
                phonesThatNeedFiltering.any { phone ->
                    ContactsUtil.isPhoneMatch(contact, phone)
                }
            }
            return unRegisterContactList
        } else {
            return localAllContacts
        }
    }

    /**
     * Synchronize local contacts and upload
     */
    fun syncLocalContactsAndUpload(forceStopPrevious: Boolean, source: String? = null) {
        if (isPermissionGranted(Manifest.permission.READ_CONTACTS)) {
            if (syncContactsJob?.isActive == true) {
                if (forceStopPrevious) {
                    syncContactsJob?.cancel()
                } else {
                    return
                }
            }
            syncContactsJob = GlobalScope.launch(Dispatchers.IO) {
                val syncContactsResult = syncAllContactsData(source)
                isContactsSyncComplete = true
                //发送liveeventbus通知数据同步完成
                ContactsSyncCompleteEvent.post(syncContactsResult)
                ContactsMMKV.contactsSyncCompleteTime = SystemClock.uptimeMillis()
            }
        } else {
            logInfo("syncLocalContactsAndUpload without contact permission")
        }
    }

    /**
     * Notice: Please IO thread execution
     */
    private suspend fun syncAllContactsData(source: String? = null): Boolean {
        try {
            val contactMap = ContactsUtil.queryContactsFromPhone(appContext)
            logInfo(TAG, "queryContactListFromPhone size = ${contactMap.size}, source: $source")
            var result = false
            var queryAllContacts: List<ContactsBean>? = null
            val uploadType = if (ContactsMMKV.isFirstTimeUploadContacts) {
                if (contactMap.isEmpty()) {
                    // 通讯录为空时，不上传
                    logInfo(TAG, "isFirstTimeUploadContacts but contactList is empty")
                    return false
                }
                // 在第一次全量上传完成前，都是直接全量上传
                logInfo(TAG, "isFirstTimeUploadContacts")
                ContactUploadType.FULL.value
            } else {
                queryAllContacts = contactsDao?.queryAllContacts()
                if (queryAllContacts.isNullOrEmpty()) {
                    if (contactMap.isEmpty()) {
                        // 通讯录为空时，不上传
                        logInfo(TAG, "isFirstTimeUploadContacts but contactList is empty")
                        return false
                    }
                    // 本地数据库为空，也走全量上传
                    logInfo(TAG, "local db contacts table has no data, all data new upload to service")
                    ContactUploadType.FULL.value
                } else {
                    if (source == ChatHome.OPEN_FROM_LOGIN || source == ChatHome.OPEN_FROM_REGISTER) {
                        // 从LoginActivity进入首页的话，获取服务端返回的上传方式（也就是注册或登录场景）
                        getUploadContactType(contactMap.size)
                    } else {
                        // 非注册或登录场景，直接增量上传
                        ContactUploadType.DIFF.value
                    }
                }
            }

            if (uploadType == ContactUploadType.FULL.value) {
                val contactList = contactMap.map { it.value }
                val contactsNewAdd = contactsBeanTransform(contactList, ContactModifyType.ADD)
                val uploadNewAddResult = uploadContacts(contactsNewAdd, ContactUploadType.FULL)
                if (uploadNewAddResult) {
                    ContactsMMKV.isFirstTimeUploadContacts = false
                    contactsDao?.clearAllContacts()
                    contactsDao?.insertContactsList(contactList)
                    result = true
                }
            } else if (queryAllContacts != null) {
                //集合求差集
                val (addList, deleteList, modifyList) = ContactsUtil.diffList(contactMap, queryAllContacts)

                //得到需要添加和删除的差集集合 然后上传给服务端
                if (addList.isEmpty() && deleteList.isEmpty() && modifyList.isEmpty()) {
                    //无差集时
                    result = true
                } else {
                    //有差集时 通过先上传给服务器，再依赖服务端推送的(op = 2001)来获取好友列表信息进行更新
                    val uploadDiffList = mutableListOf<Contact>()
                    if (addList.isNotEmpty()) {
                        val contacts2Add = contactsBeanTransform(addList, ContactModifyType.ADD)
                        uploadDiffList.addAll(contacts2Add)
                    }
                    if (deleteList.isNotEmpty()) {
                        val contacts2Del = contactsBeanTransform(deleteList, ContactModifyType.DELETE)
                        uploadDiffList.addAll(contacts2Del)
                    }
                    if (modifyList.isNotEmpty()) {
                        val contacts2Mod = contactsBeanTransform(modifyList, ContactModifyType.MODIFY)
                        uploadDiffList.addAll(contacts2Mod)
                    }
                    if (uploadDiffList.isNotEmpty()) {
                        val uploadResult = uploadContacts(uploadDiffList, ContactUploadType.DIFF)
                        if (uploadResult) {
                            if (addList.isNotEmpty()) {
                                contactsDao?.insertContactsList(addList)
                            }
                            if (deleteList.isNotEmpty()) {
                                contactsDao?.delContactsList(deleteList)
                            }
                            if (modifyList.isNotEmpty()) {
                                contactsDao?.updateContactsList(modifyList)
                            }
                            result = true
                        }
                    }
                }
            }
            return result
        } catch (e: Exception) {
            logError(TAG, e)
            return false
        }
    }

    /**
     * ContactsBean list transform to Contact list
     * @param modifyType 1-add 2-del 3-update
     */
    private fun contactsBeanTransform(
        contactsBeanList: List<ContactsBean>,
        modifyType: ContactModifyType
    ) = contactsBeanList.map {
        Contact(
            firstName = it.firstName,
            lastName = it.lastName,
            phone = null,
            type = modifyType.value,
            originalPhone = it.phone
        )
    }

    /**
     * Upload Contacts data to service
     * @param uploadType 1: Overlay upload | 2:Incremental
     */
    private suspend fun uploadContacts(
        contactList: List<Contact>,
        uploadType: ContactUploadType
    ): Boolean {
        val request = RequestUploadContacts(
            contactList = null,
            type = uploadType.value,
            uploaderRegionCode = UserSessionManager.regionCode,
            compressContacts = compressListToGzipByteArray(contactList),
            traceId = TraceIdGenerator.getTraceIdUUID()
        )
        val resp = uploadContacts(request)
        val uploadTypeName = if (uploadType == ContactUploadType.DIFF) "increment" else "full"
        ContactsTracker.onRB2024060701(
            size = contactList.size,
            uploadType = uploadTypeName,
            rCode = resp.code
        )
        return if (resp.isSuccess) {
            logInfo(TAG, "uploadContacts success type:$uploadTypeName, size: ${contactList.size}")
            true
        } else {
            val respRetry = uploadContacts(request)
            logInfo(TAG, "retry uploadContacts code: ${respRetry.code}, type:$uploadTypeName, size: ${contactList.size}")
            respRetry.isSuccess
        }
    }

    private suspend fun uploadContacts(request: RequestUploadContacts) =
        // 这是由于这个接口可能会上传大量的通讯录，以免影响性能，先不使用蘑菇的加密
        com.buz.idl.user.service.BuzNetUserServiceClient()
            .withConfig().uploadContacts(request)

    /**
     * 1. 将 List 转换为 JSON 字符串
     * 2. 返回gzip压缩后的字节数组
     */
    private fun compressListToGzipByteArray(contactList: List<Contact>): ByteArray {
        val gson = Gson()
        val jsonString = gson.toJson(contactList)
        val byteArrayOutputStream = ByteArrayOutputStream()
        GZIPOutputStream(byteArrayOutputStream).use { gzipOutputStream ->
            gzipOutputStream.write(jsonString.toByteArray(Charsets.UTF_8))
        }
        return byteArrayOutputStream.toByteArray()
    }


    private suspend fun getUploadContactType(contactSize: Int): Int {
        val resp = userService.getUploadContactType(
            RequestGetUploadContactType(contactSize)
        )
        return if (resp.isSuccess) {
            logInfo(TAG, "getUploadContactType isSuccess, uploadType: ${resp.data?.uploadType}")
            resp.data?.uploadType ?: ContactUploadType.FULL.value
        } else {
            logInfo(TAG, "getUploadContactType failed")
            ContactUploadType.FULL.value
        }
    }

    enum class ContactUploadType(val value: Int) {
        FULL(1), //全量上传
        DIFF(2), //增量上传
    }

    enum class ContactModifyType(val value: Int) {
        ADD(1),    //新增 (newMap中的contactId在oldMap中不存在)
        DELETE(2), //删除（oldMap中的contactId在newMap中不存在）
        MODIFY(3)  //修改 (newMap中的contactId在oldMap中存在, firstName、lastName、filteredPhone至少有一个发生了改变)
    }
}
