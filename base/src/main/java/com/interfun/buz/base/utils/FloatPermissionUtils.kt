package com.interfun.buz.base.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.provider.Settings
import androidx.annotation.RequiresApi
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.packageName

/**
 * <AUTHOR>
 * @date 2023/2/13
 * @desc
 */
object FloatPermissionUtils {
    private val TAG = "FloatPermissionUtils"

    /**
     * 检测是否有悬浮窗权限
     * 6.0 版本之后由于 google 增加了对悬浮窗权限的管理，所以方式就统一了
     */
    @JvmStatic
    fun checkPermission(context: Context = appContext): Boolean {
        //Android 6.0 以下无需获取权限，可直接展示悬浮窗
        return if (VERSION.SDK_INT >= VERSION_CODES.M) {
            try {
                Settings.canDrawOverlays(context)
            } catch (e: Exception) {
                false
            }
        } else {
            true
        }
    }

    fun requestPermission(activity: Activity){
        //判断是否拥有悬浮窗权限，无则跳转悬浮窗权限授权页面
        if (VERSION.SDK_INT >= VERSION_CODES.M) {
            val canDrawOverlays = Settings.canDrawOverlays(activity)
            log(TAG,"requestPermission:$canDrawOverlays")
            if (!canDrawOverlays) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$packageName")
                )
                activity.startActivityForResult(intent, 100)
            }
        }
    }

    @RequiresApi(VERSION_CODES.M)
    fun getPermissionRequestIntent(): Intent {
        return Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            Uri.parse("package:$packageName")
        )
    }

}