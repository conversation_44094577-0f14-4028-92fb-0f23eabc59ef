@file:Suppress("unused")

package com.interfun.buz.base.ktx

import android.Manifest.permission.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkCapabilities.*
import android.net.NetworkRequest
import android.net.wifi.ScanResult
import android.net.wifi.WifiManager
import android.os.Build
import androidx.annotation.RequiresPermission
import androidx.core.content.getSystemService
import androidx.lifecycle.LiveData
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job

@get:RequiresPermission(ACCESS_NETWORK_STATE)
val isNetworkAvailable: Boolean
    get() = application.getSystemService<ConnectivityManager>()?.run {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            getNetworkCapabilities(activeNetwork)?.run {
                hasCapability(NET_CAPABILITY_INTERNET) && hasCapability(NET_CAPABILITY_VALIDATED)
            }
        } else {
            @Suppress("DEPRECATION")
            activeNetworkInfo?.isConnectedOrConnecting
        }
    } ?: false

@get:RequiresPermission(ACCESS_NETWORK_STATE)
val isWifiConnected: Boolean
    get() = application.getSystemService<ConnectivityManager>()?.run {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            getNetworkCapabilities(activeNetwork)?.hasTransport(TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            activeNetworkInfo?.run { isConnected && type == ConnectivityManager.TYPE_WIFI }
        }
    } ?: false

@get:RequiresPermission(ACCESS_NETWORK_STATE)
val isMobileData: Boolean
    get() = application.getSystemService<ConnectivityManager>()?.run {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            getNetworkCapabilities(activeNetwork)?.hasTransport(TRANSPORT_CELLULAR)
        } else {
            @Suppress("DEPRECATION")
            activeNetworkInfo?.run { isAvailable && type == ConnectivityManager.TYPE_MOBILE }
        }
    } ?: false

@get:RequiresPermission(ACCESS_WIFI_STATE)
inline val isWifiEnabled: Boolean
    get() = application.getSystemService<WifiManager>()?.isWifiEnabled == true

inline val ScanResult.is24GHz: Boolean
    get() = frequency in 2400..2550

inline val ScanResult.is5GHz: Boolean
    get() = frequency in 5500..5800

class NetworkAvailableLiveData @RequiresPermission(ACCESS_NETWORK_STATE) constructor() :
    LiveData<Boolean>() {

    private val connectivityManager by lazy { application.getSystemService<ConnectivityManager>() }

    @RequiresPermission(ACCESS_NETWORK_STATE)
    override fun onActive() {
        super.onActive()
        logError("luoying", "NetworkAvailableLiveData.onActive")
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                connectivityManager?.registerDefaultNetworkCallback(networkCallback)
            } else {
                connectivityManager?.registerNetworkCallback(
                    networkRequest,
                    networkCallback
                )
            }
            postValue(isNetworkAvailable)
        } catch (e: Exception) {
            e.log("NetworkAvailableLiveData")
        }
    }

    override fun onInactive() {
        super.onInactive()
        try {
            connectivityManager?.unregisterNetworkCallback(networkCallback)
        } catch (e: Exception) {
            e.log("NetworkAvailableLiveData")
        }
    }

    override fun setValue(value: Boolean) {
        if (this.value != value) {
            super.setValue(value)
        }
    }

    private val networkRequest by lazy {
        NetworkRequest.Builder()
            .addTransportType(TRANSPORT_CELLULAR)
            .addTransportType(TRANSPORT_ETHERNET)
            .addTransportType(TRANSPORT_WIFI)
            .build()
    }

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                postValue(true)
            }
        }

        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                networkCapabilities.run {
                    postValue(hasCapability(NET_CAPABILITY_INTERNET) && hasCapability(NET_CAPABILITY_VALIDATED))
                }
            }
        }

        override fun onLost(network: Network) {
            postValue(false)
        }
    }
}

class WifiListLiveData @RequiresPermission(allOf = [ACCESS_WIFI_STATE, CHANGE_WIFI_STATE]) constructor() : LiveData<List<ScanResult>?>() {

    private val wifiManager: WifiManager by lazy(LazyThreadSafetyMode.NONE) {
        application.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }

    @Suppress("DEPRECATION")
    // @Deprecated("The ability for apps to trigger scan requests will be removed in a future release.")
    fun startScan() {
        if (!wifiManager.startScan()) {
            value = null
        }
    }

    override fun onActive() {
        application.registerReceiver(wifiScanReceiver, IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION))
    }

    override fun onInactive() {
        application.unregisterReceiver(wifiScanReceiver)
    }

    private val wifiScanReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M ||
                intent.getBooleanExtra(WifiManager.EXTRA_RESULTS_UPDATED, false)
            ) {
                value = wifiManager.scanResults
            }
        }
    }
}

sealed interface NetworkSwitchEvent {
    object WifiToCellular : NetworkSwitchEvent // wifi 切换到 流量
    object CellularToWifi : NetworkSwitchEvent // 流量 切换到 wifi
    object WifiOffToWifiOn : NetworkSwitchEvent // wifi 关闭 到 wifi 打开
    object CellularOffToCellularOn : NetworkSwitchEvent // 流量 关闭 到 流量 打开
    object Disconnected : NetworkSwitchEvent // 网络已断开
    data class Connected(val type: NetworkType) : NetworkSwitchEvent // 网络已连接
}

sealed interface NetworkType {
    object WifiOnly : NetworkType
    object CellularOnly : NetworkType
    object WifiAndCellular : NetworkType
    object Unknown : NetworkType
}

class NetworkSwitchLiveData @RequiresPermission(ACCESS_NETWORK_STATE) constructor() :
    LiveData<NetworkSwitchEvent>() {

    private val connectivityManager by lazy { application.getSystemService<ConnectivityManager>() }
    private var lastTransportType: Int? = if (isWifiConnected) TRANSPORT_WIFI else if (isMobileData) TRANSPORT_CELLULAR else -1
    private var lastConnected: Boolean = isNetworkAvailable
    private var delayCheckJob: Job? = null

    @RequiresPermission(ACCESS_NETWORK_STATE)
    override fun onActive() {
        super.onActive()
        GlobalScope.launchIO {
            try {
                when {
                    Build.VERSION.SDK_INT >= Build.VERSION_CODES.N ->
                        connectivityManager?.registerDefaultNetworkCallback(networkCallback)

                    else ->
                        connectivityManager?.registerNetworkCallback(
                            networkRequest,
                            networkCallback
                        )
                }
            } catch (e: Exception) {
                e.log("NetworkSwitchLiveData")
            }
        }
    }

    override fun onInactive() {
        super.onInactive()
        GlobalScope.launchIO {
            try {
                connectivityManager?.unregisterNetworkCallback(networkCallback)
            } catch (e: Exception) {
                e.log("NetworkSwitchLiveData")
            }
        }
        delayCheckJob?.cancel()
    }

    private val networkRequest by lazy {
        NetworkRequest.Builder()
            .addTransportType(TRANSPORT_CELLULAR)
            .addTransportType(TRANSPORT_WIFI)
            .build()
    }

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onCapabilitiesChanged(
            network: Network,
            networkCapabilities: NetworkCapabilities
        ) {
            val isWifi = networkCapabilities.hasTransport(TRANSPORT_WIFI)
            val isCellular = networkCapabilities.hasTransport(TRANSPORT_CELLULAR)
            val connected =
                networkCapabilities.hasCapability(NET_CAPABILITY_INTERNET) && networkCapabilities.hasCapability(
                    NET_CAPABILITY_VALIDATED
                )
            val currentTransportType = when {
                isWifi -> TRANSPORT_WIFI
                isCellular -> TRANSPORT_CELLULAR
                else -> -1
            }
            val type = when {
                isWifi && isCellular -> NetworkType.WifiAndCellular
                isWifi -> NetworkType.WifiOnly
                isCellular -> NetworkType.CellularOnly
                else -> NetworkType.Unknown
            }

            logDebug("NetworkSwitchLiveData", "onCapabilitiesChanged==>currentTransportType: $currentTransportType," +
                    "lastTransportType: $lastTransportType, lastConnected:$lastConnected ,connected: $connected")

            // 处理各种切换场景
            if (lastTransportType == TRANSPORT_WIFI && currentTransportType == TRANSPORT_CELLULAR) {
                postValue(NetworkSwitchEvent.WifiToCellular)
            } else if (lastTransportType == TRANSPORT_CELLULAR && currentTransportType == TRANSPORT_WIFI) {
                postValue(NetworkSwitchEvent.CellularToWifi)
            } else if (!lastConnected && connected && currentTransportType == TRANSPORT_WIFI) {
                postValue(NetworkSwitchEvent.WifiOffToWifiOn)
            } else if (!lastConnected && connected && currentTransportType == TRANSPORT_CELLULAR) {
                postValue(NetworkSwitchEvent.CellularOffToCellularOn)
            } else if (lastConnected && !connected) {
                postValue(NetworkSwitchEvent.Disconnected)
            } else if (!lastConnected && connected) {
                postValue(NetworkSwitchEvent.Connected(type))
            }
            delayCheckJob?.cancel()
            lastTransportType = currentTransportType
            lastConnected = connected
        }

        @RequiresPermission(ACCESS_NETWORK_STATE)
        override fun onLost(network: Network) {
            logDebug("NetworkSwitchLiveData", "onLost==>lastTransportType: $lastTransportType, lastConnected:$lastConnected, " +
                    "isMobileData: $isMobileData,isWifiConnected: $isWifiConnected")
            if (delayCheckJob?.isActive == true) {
                delayCheckJob?.cancel()
            }
            delayCheckJob = GlobalScope.launchDelay(delayTimes = 300) {
                // 避免Wi-Fi切流量过程中，过渡期间回调了一次Disconnected
                if (!isNetworkAvailable) {
                    postValue(NetworkSwitchEvent.Disconnected)
                    lastConnected = false
                    lastTransportType = -1
                }
            }
        }
    }

    companion object {
        fun getDefaultNetworkSwitchEvent(): NetworkSwitchEvent {
            return when {
                isWifiConnected && isMobileData -> NetworkSwitchEvent.Connected(NetworkType.WifiAndCellular)
                isWifiConnected -> NetworkSwitchEvent.Connected(NetworkType.WifiOnly)
                isMobileData -> NetworkSwitchEvent.Connected(NetworkType.CellularOnly)
                else -> NetworkSwitchEvent.Disconnected
            }
        }
    }
}


