package com.interfun.buz.voicecall.util

import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import coil.imageLoader
import coil.request.ImageRequest
import coil.request.SuccessResult
import coil.transform.CircleCropTransformation
import com.interfun.buz.base.ktx.appContext

object BitmapUtil{
    suspend fun getBitmap(url: String?): Bitmap? {
        url?: return null

        if (url.isEmpty()) {
            return null
        }
        val loader = appContext.imageLoader
        val request = ImageRequest.Builder(appContext)
            .data(url)
            .transformations(CircleCropTransformation())
            // .allowHardware(false) // Disable hardware bitmaps.
            .build()
        val result = (loader.execute(request) as? SuccessResult)?.drawable
        return (result as? BitmapDrawable)?.bitmap
    }
}