package com.interfun.buz.chat.ai.guide

import androidx.annotation.Keep
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.common.bean.ReportUserInfo
import com.buz.idl.common.bean.SwitchStatus
import com.buz.idl.common.request.RequestReportUserInfo
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.net.withConfig
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Keep
data class RobotGuideConfig(val type: Int, val status: Boolean)

class RobotGuideViewModel : ViewModel() {

    private val client by lazy { BuzNetCommonServiceClient().withConfig() }

     fun indicateOpenedRobotGuide() {
         viewModelScope.launch {
             //      - type 类型 int 开关类型 1、AI弹窗
             //      - status  类型 bool 开关状态 ture 单表需要AI 弹窗 false 代表不需要
             val switchStatus = SwitchStatus(1, false)
             val requestReportUserInfo =
                 RequestReportUserInfo(
                     ReportUserInfo(
                         language = null,
                         quietMode = null,
                         sendTimestamp = System.currentTimeMillis(),
                         switchStatusList = listOf(switchStatus),
                         smartAsrSwitch = null,
                         settingMap = null
                     )
                 )
             val ret = withContext(Dispatchers.IO) {
                 client.reportUserInfo(requestReportUserInfo)
             }
             Logz.i("indicateOpenedRobotGuide.code = ${ret.code}  msg: ${ret.msg} abTestResult.data ${ret?.data}")
             if (ret.isSuccess) {
                 AppConfigRequestManager.requestAppConfigWithLogin()
             }
         }

    }
}
