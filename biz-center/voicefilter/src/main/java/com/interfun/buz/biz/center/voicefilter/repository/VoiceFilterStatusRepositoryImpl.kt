package com.interfun.buz.biz.center.voicefilter.repository

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * 语音滤镜状态仓库实现类
 * 使用单例模式确保在不同 Activity 之间共享状态
 */
class VoiceFilterStatusRepositoryImpl @Inject constructor() : VoiceFilterStatusRepository {


    private val _hasClickedSwitchVoiceFilterModeFlow = MutableStateFlow<Pair<Boolean, String?>>(false to null)
    override val hasClickedSwitchVoiceFilterModeFlow: StateFlow<Pair<Boolean, String?>> = _hasClickedSwitchVoiceFilterModeFlow.asStateFlow()

    private val _isVoiceFilterModeStateFlow = MutableStateFlow(false)
    override val isVoiceFilterModeStateFlow: StateFlow<Boolean> = _isVoiceFilterModeStateFlow.asStateFlow()

    private val _currentSelectTabTypeFlow = MutableStateFlow<Int?>(null)
    override val currentSelectTabTypeFlow: StateFlow<Int?> = _currentSelectTabTypeFlow.asStateFlow()

    private val _isVoiceFilterUserScrolledStateFlow = MutableStateFlow(false)
    override val isVoiceFilterUserScrolledStateFlow: StateFlow<Boolean> = _isVoiceFilterUserScrolledStateFlow.asStateFlow()

    // 按tab类型记录选中的VF ID
    private val _tabSelectedVoiceFilterIdMapFlow = MutableStateFlow<Map<Int, Long>>(emptyMap())
    override val tabSelectedVoiceFilterIdMapFlow: StateFlow<Map<Int, Long>> = _tabSelectedVoiceFilterIdMapFlow.asStateFlow()

    // 滤镜滚动状态
    private val _isVoiceFilterScrollingStateFlow = MutableStateFlow(false)
    override val isVoiceFilterScrollingStateFlow: StateFlow<Boolean> = _isVoiceFilterScrollingStateFlow.asStateFlow()

    override suspend fun updateHasClickedSwitchVoiceFilterMode(open: Boolean, from: String?) {
        _hasClickedSwitchVoiceFilterModeFlow.emit(open to from)
    }
    
    override suspend fun updateVoiceFilterMode(isOpen: Boolean) {
        _isVoiceFilterModeStateFlow.emit(isOpen)
    }

    override suspend fun updateSelectTabType(type: Int) {
        _currentSelectTabTypeFlow.emit(type)
    }
    
    override suspend fun updateUserIsScrolled(isScrolled: Boolean) {
        _isVoiceFilterUserScrolledStateFlow.emit(isScrolled)
    }

    override suspend fun updateVoiceFilterScrolling(isScrolling: Boolean) {
        _isVoiceFilterScrollingStateFlow.emit(isScrolling)
    }

    override suspend fun updateTabSelectedVoiceFilterId(tabType: Int, voiceFilterId: Long) {
        val currentMap = _tabSelectedVoiceFilterIdMapFlow.value.toMutableMap()
        currentMap[tabType] = voiceFilterId
        _tabSelectedVoiceFilterIdMapFlow.emit(currentMap)
    }

    override fun getTabSelectedVoiceFilterId(tabType: Int): Long? {
        return _tabSelectedVoiceFilterIdMapFlow.value[tabType]
    }

    override suspend fun clearAllTabSelectedVoiceFilterIds() {
        _tabSelectedVoiceFilterIdMapFlow.emit(emptyMap())
    }
}
