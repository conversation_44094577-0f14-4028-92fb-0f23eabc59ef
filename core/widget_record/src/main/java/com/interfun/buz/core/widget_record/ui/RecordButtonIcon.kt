package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.scale
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.state.VoiceFilterRecordedBehavior
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.state.recordedBehavior

/**
 * <AUTHOR>
 * @date 2025/6/12
 * @desc
 */
@Composable
internal fun RecordButtonIcon(
    isRecordingLambda: () -> Boolean,
    isInActionBtnAreaLambda: () -> Boolean,
    isInVoiceFilterModeLambda: () -> Boolean,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    currentVoiceFilterLambda: () -> VoiceFilterUiData?,
    modifier: Modifier = Modifier,
) {
    SideEffect {
        RecomposeCountHelper.increment("RecordButtonIcon")
    }
    val operateStatus = recordOperateStatusLambda()
    val voiceFilter = currentVoiceFilterLambda()
    val iconColor by animateColorAsState(
        targetValue = if (!operateStatus.isEnable || isInActionBtnAreaLambda()) {
            R.color.color_text_white_tertiary.asColor()
        } else {
            val iconColor = when {
                operateStatus.isLocking -> R.color.color_foreground_highlight_default
                isRecordingLambda() -> R.color.color_text_black_primary
                else -> R.color.color_foreground_neutral_important_default
            }.asColor()
            if (operateStatus.isLocking) {
                voiceFilter?.backgroundColor ?: iconColor
            } else {
                voiceFilter?.microphoneColor ?: iconColor
            }
        }
    )
    val sizeConfig = LocalRecordUIConfig.current
    val iconScale by animateFloatAsState(
        targetValue = if (isInVoiceFilterModeLambda() && !operateStatus.isPressingOrLocking) {
            sizeConfig.voiceFilterMicIconScale
        } else {
            1f
        }
    )
    IconFontText(
        modifier = modifier.graphicsLayer {
            this.scale(iconScale)
        },
        iconRes = if (operateStatus.isLocking) {
            if (voiceFilter?.recordedBehavior == VoiceFilterRecordedBehavior.Preview.value) {
                R.string.ic_preview
            } else {
                R.string.ic_message
            }
        } else R.string.ic_mic_open,
        iconSize = sizeConfig.recordBtnMicIconSize,
        iconColor = iconColor
    )
}