package com.interfun.buz.common.arouter

import android.content.Context
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.annotation.Interceptor
import com.alibaba.android.arouter.facade.callback.InterceptorCallback
import com.alibaba.android.arouter.facade.template.IInterceptor
import com.alibaba.android.arouter.launcher.ARouter
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.PATH_ACTIVITY_REAL_TIME_CALL
import com.interfun.buz.common.constants.PATH_CHAT_ACTIVITY_HOME
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.voicecall.VoiceCallAcceptNewDialogEvent
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.onair.standard.LivePlaceType

/**
 * @Desc Interceptor jump into PrivateVoiceActivity or GroupVoiceCallActivity when is on real time call
 * @Author:<EMAIL>
 * @Date: 2024/4/1
 */

@Interceptor(priority = Int.MAX_VALUE - 2)
class VoiceCallInterceptor : IInterceptor {

    companion object {

        const val TAG = "VoiceCallInterceptor"

        private val interceptPaths = PATH_ACTIVITY_REAL_TIME_CALL

        internal fun isInterceptPath(path: String): Boolean {
            return interceptPaths.contains(path)
        }
    }

    override fun init(context: Context?) {
    }

    override fun process(postcard: Postcard, callback: InterceptorCallback) {
        if (isInterceptPath(postcard.path)) {
            val realTimeCallService = routerServices<RealTimeCallService>().value

            val isOnRealTimeCall = realTimeCallService?.isOnRealTimeCallEnhance() == true

            val onAirService = routerServices<IGlobalOnAirController>().value
            val isInOnAir = onAirService?.isInOnAir() == true

            val existVCallChannelId = realTimeCallService?.getVoiceCallRoomIfExist()?.roomChannelId
            val isTakingVideo = routerServices<ChatService>().value?.isTakingVideo() ?: false
            val onlineChatJumpInfo =
                postcard.extras.getParcelable<OnlineChatJumpInfo>(RouterParamKey.ChannelInvite.JUMP_INFO)
            val jumpFrom =
                postcard.extras.getInt(RouterParamKey.ChannelInvite.KEY_JUMP_INVITE_PAGE_FROM)

            logInfo(
                TAG,
                "isIntercept isOnRealTimeCall = $isOnRealTimeCall,isInOnAir = $isInOnAir onlineChatJumpInfo = $onlineChatJumpInfo, jumpFrom = $jumpFrom, existRoomChannelId = $existVCallChannelId, isTakingVideo = $isTakingVideo"
            )
            val livePlaceType = when(onlineChatJumpInfo?.channelType){
                ChannelType.TYPE_LIVE_PLACE_PRIVATE -> LivePlaceType.PRIVATE
                ChannelType.TYPE_LIVE_PLACE_GROUP -> LivePlaceType.GROUP
                else -> null
            }
            val isOnAirJump = livePlaceType.isNotNull()
            val isInSameOnAir =  if (null == livePlaceType){
                false
            }else{
                onAirService?.hitCurOnAir(livePlaceType,onlineChatJumpInfo?.targetId?:0) == true
            }

            val isNewVoiceCall =
                isOnRealTimeCall && (onlineChatJumpInfo?.channelId != existVCallChannelId || isOnAirJump)
            val isNewOnAir = isInOnAir && !isInSameOnAir
            val isNewChannel = isNewOnAir || isNewVoiceCall

            if (isNewChannel) {
                callback.onInterrupt(null)
                onlineChatJumpInfo?.let {
                    val vcNotificationID =
                        com.interfun.buz.common.manager.CallNotificationCache.getVoiceCallNotifiIdByChannelId(
                            onlineChatJumpInfo.channelId ?: 0
                        )
                    if (vcNotificationID != null) {
                        NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(
                            vcNotificationID,
                            channelType = onlineChatJumpInfo.channelType,
                            stopRingtone = false
                        )
                    }
                    VoiceCallAcceptNewDialogEvent.post(onlineChatJumpInfo, jumpFrom)
                    if (routerServices<RealTimeCallService>().value?.isPipModeExist() == true){
                        //如果当前在画中画模式路由中转到首页
                        logWarn(TAG, "RealTimeCallActivity is exist and is in PipMode, so jump to home page")
                        ARouter.getInstance().build(PATH_CHAT_ACTIVITY_HOME).navigation()
                    }
                }
            } else {
                callback.onContinue(postcard)
            }
        } else {
            callback.onContinue(postcard)
        }
    }
}