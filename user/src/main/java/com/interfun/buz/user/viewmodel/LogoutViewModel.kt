package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import com.buz.idl.login.request.RequestLogout
import com.buz.idl.login.response.ResponseLogout
import com.buz.idl.login.service.BuzNetLoginServiceClient
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.utils.PromptUtil
import com.lizhi.itnet.lthrift.service.ITResponse
import com.yibasan.lizhifm.lzlogan.Logz

class LogoutViewModel : ViewModel() {

    companion object{
        const val TAG = "UserSettingViewModel"
    }

    private val loginService by lazy { BuzNetLoginServiceClient().withConfig() }
    private val chatRouterService by lazy {
        routerServices<ChatService>().value
    }

    /**
     * @param type 1 主动退出 2 互踢
     */
    suspend fun logout(type: Int): ITResponse<ResponseLogout> {
        // turn off wt model before user logout
        if (chatRouterService?.isWTOnlineStatus() == true) {
            chatRouterService?.requestChangeSwitchStatus(false)
        }
        val resp = loginService.logout(RequestLogout(type))
        PromptUtil.parse(resp.data?.prompt)
        Logz.tag(TAG).d("logout code:${resp.code},msg:${resp.msg}")
        return resp
    }

}