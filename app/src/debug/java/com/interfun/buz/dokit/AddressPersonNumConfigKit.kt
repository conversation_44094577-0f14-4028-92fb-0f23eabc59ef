package com.interfun.buz.dokit

import android.app.Activity
import com.interfun.buz.R
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.widget.dialog.CommonAlertDialog

class AddressPersonNumConfigKit : BaseBusinessKit(R.string.kit_max_address_num_config) {

    override fun handleOnClick(activity: Activity) {
        CommonAlertDialog(
            activity,
            title = "Modify max addressing number",
            tips = "current:${CommonMMKV.maxAddressPersonNum}",
            positiveText = "modify to 1",
            positiveCallback = {
                CommonMMKV.maxAddressPersonNum = 1
                it.dismiss()
            },
            negativeText = "modify to 9999",
            negativeCallback = {
                CommonMMKV.maxAddressPersonNum = 9999
                it.dismiss()
            }
        ).show()
    }
}