package com.interfun.buz.feedback.view.activity

import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.collectLatestIn
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.PATH_FEEDBACK_SUGGESTION
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.feedback.R
import com.interfun.buz.feedback.view.fragment.FeedbackSuggestionFragment
import com.interfun.buz.feedback.view.fragment.FeedbackThanksFragment
import com.interfun.buz.feedback.viewmodel.FeedbackViewModel
import dagger.hilt.android.AndroidEntryPoint

@Route(path = PATH_FEEDBACK_SUGGESTION)
@AndroidEntryPoint
class FeedbackSuggestionActivity: SimpleContainerActivity() {
    private val feedbackSuggestionFragmentTag = "FeedbackSuggestionFragment"
    private val vm by viewModels<FeedbackViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val source = intent?.getIntExtra(
            RouterParamKey.Feedback.KEY_SOURCE,
            RouterParamKey.Feedback.FEEDBACK_SOURCE_SETTING
        ) ?: RouterParamKey.Feedback.FEEDBACK_SOURCE_SETTING
        val prefix = intent?.getStringExtra(RouterParamKey.Feedback.KEY_PREFIX)
        val savedFragment = supportFragmentManager.findFragmentByTag(feedbackSuggestionFragmentTag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, FeedbackSuggestionFragment.create(source,prefix), feedbackSuggestionFragmentTag)
                .commit()
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
                overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
            }
        })

        observeListener()
    }

    private fun observeListener(){
        vm.feedbackCodeFlow.collectLatestIn(this){
            if (it.isSuccess) {
                supportFragmentManager.beginTransaction()
                    .setCustomAnimations(R.anim.anim_nav_enter,R.anim.anim_nav_exit)
                    .replace(containerId, FeedbackThanksFragment())
                    .commitNow()
            }
        }
    }

}