package com.interfun.buz.onair.repository

import com.interfun.buz.common.bean.voicecall.DeviceInfoItemOption
import com.interfun.buz.onair.ui.state.OnAirBottomSheetUiState
import dagger.hilt.android.scopes.FragmentScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import org.webrtc.IceCandidate
import javax.inject.Inject
import javax.inject.Singleton

/**
 * <AUTHOR>
 * @date 2024/8/20
 * @desc
 */
class OnAirBottomSheetViewmodel  @Inject constructor(){

    private val _dialogState = MutableStateFlow<OnAirBottomSheetUiState>(OnAirBottomSheetUiState.Hide)
    val dialogState: StateFlow<OnAirBottomSheetUiState> = _dialogState

    fun showOrHideSpeakerSelector(isShow: Boolean,candidateList:
                                  List<DeviceInfoItemOption>) {
        _dialogState.update {
            if (isShow) {
                OnAirBottomSheetUiState.ShowSpeakerSelector(candidateList)
            } else {
                OnAirBottomSheetUiState.Hide
            }
        }
    }

    fun showOrHideSoundBoard(isShow: Boolean) {
        _dialogState.update {
            if (isShow) {
                OnAirBottomSheetUiState.ShowSoundBoard
            } else {
                OnAirBottomSheetUiState.Hide
            }
        }
    }

    fun showOrHideGift(isShow: Boolean) {
        _dialogState.update {
            if (isShow) {
                OnAirBottomSheetUiState.ShowGift
            } else {
                OnAirBottomSheetUiState.Hide
            }
        }
    }

}