package com.interfun.buz.common.bean.push

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.eventbus.voicecall.CancelVoiceCallInviteEvent
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.utils.NotificationUtil
import org.json.JSONObject

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2024/3/6
 */
class PushCancelVoiceCallInvite: PushDataBase() {


    /** {"op":3003,"sendTimestamp":1709726984067,"data":{"channelId":5369226094492780671}} */
    override fun handlePushEvent(op: Int, sendTimestamp: Long, data: JSONObject?) {
        data?.let {
            val channelId = data.getLong("channelId")
            val channelType = data.optInt("channelType")
            val callType = data.optInt("callType")
            if (0 == channelType) return@let
            val voiceCallNotifiId = CallNotificationCache.getVoiceCallNotifiIdByChannelId(channelId)
            logInfo("PushCancelVoiceCallInvite", "sendBroadcast channelId = $channelId, voiceCallNotifiId = $voiceCallNotifiId")
            if (voiceCallNotifiId != null){
                NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(voiceCallNotifiId,channelType)
            }
            if (ChannelInviteManager.containsInvite(channelId.toString())) {
                logInfo("PushCancelVoiceCallInvite", "voiceCall invite [channelId = $channelId] is existing, and can cancel normally")
                CancelVoiceCallInviteEvent.post(channelId, callType)
                ChannelInviteManager.cancelInviteCall(data, sendTimestamp)
            }
        }
    }
}