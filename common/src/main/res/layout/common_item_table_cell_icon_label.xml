<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clItemContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_list_bg_selector"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ic_info"
            android:textColor="@color/color_background_highlight_1_default"
            android:textSize="20sp"
            android:layout_marginStart="14dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible"/>

        <TextView
            android:id="@+id/tvContent"
            style="@style/text_title_small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:layout_marginBottom="13dp"
            android:layout_marginStart="16dp"
            app:layout_goneMarginStart="20dp"
            android:text="@string/user_about_buz"
            android:textColor="@color/color_text_white_primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iftvIcon"/>

        <View
            android:id="@+id/redDot"
            style="@style/redDot"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginEnd="6dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvLabel"
            tools:visibility="visible"/>

        <TextView
            android:id="@+id/tvLabel"
            style="@style/text_body_medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/color_text_white_secondary"
            android:layout_marginEnd="6dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iftvEndIcon"
            tools:visibility="visible"
            tools:text="Label"/>

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvEndIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ic_arrow_right"
            android:textColor="@color/color_text_white_primary"
            android:textSize="16sp"
            android:layout_marginEnd="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/separator"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="20dp"
        android:background="@drawable/common_separator_line_default"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clItemContent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>