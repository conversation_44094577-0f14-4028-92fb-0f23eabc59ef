package com.interfun.buz.chat.common.manager

import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toSafeLong
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.common.ktx.isOfficial
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.user.FriendStatusManager
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.IMSendStateEvent
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.BuzFileMessage
import com.interfun.buz.im.message.BuzVoiceGifMsg
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.interfun.buz.im.track.IMTracker
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * Author: ChenYouSheng
 * Date: 2023/6/15
 * Email: <EMAIL>
 * Desc:
 */
object IMEventTrackManager {

    private const val TAG: String = "IMEventTrackManager"

    private fun isLogin(): Boolean {
        return UserSessionManager.uid > 0L
    }
    private var flowJob: Job? = null

    fun login() {
        log(TAG, "init")
        if (!isLogin()) {
            log(TAG, "init,has not login")
            return
        }
        flowJob = GlobalScope.launchIO {
            IMAgent.msgSendStateFlow.collect { sendStateEvent ->
                when (sendStateEvent) {
                    is IMSendStateEvent.OnAttach -> {
                        sendStateEvent.msg.let { msg ->
                            msg.updateLocalSendNtpTime(NtpTime.nowForce())
                            logInfo(TAG, "消息发送onAttached:${msg.status},,msgId=${msg.msgId},serMsgId=${msg.serMsgId},contentExtra=${msg.content?.extra}")
                        }
                    }
                    is IMSendStateEvent.OnSuccess -> {
                        sendStateEvent.msg.let { msg ->
                            onIMResultTrack(msg, null, null)
                            if (msg.isPrivate) {
                                val userRelationInfo = UserRelationCacheManager.getUserRelationInfoByUid(
                                    msg.targetId?.toLongOrNull() ?: 0L
                                )
                                if (userRelationInfo?.isOfficial == true) {
                                    ChatTracker.onUserSendMsgResult(true)
                                }
                            }
                            logInfo(
                                TAG,
                                "消息发送onSuccess:${msg.status},,msgId=${msg.msgId},serMsgId=${msg.serMsgId},contentExtra=${msg.content?.extra}"
                            )
                        }

                    }
                    is IMSendStateEvent.OnError -> {
                         onIMResultTrack(sendStateEvent.msg, sendStateEvent.errorType, sendStateEvent.errorCode)
                        if (sendStateEvent.msg.isPrivate) {
                            val userRelationInfo = UserRelationCacheManager.getUserRelationInfoByUid(
                                sendStateEvent.msg.targetId?.toLongOrNull() ?: 0L
                            )
                            if (userRelationInfo?.isOfficial == true) {
                                ChatTracker.onUserSendMsgResult(false, sendStateEvent.errorCode.toString())
                            }
                        }
                        logInfo(
                            TAG,
                            "消息发送onError:${sendStateEvent.msg.status},,msgId=${sendStateEvent.msg.msgId},serMsgId=${sendStateEvent.msg.serMsgId},contentExtra=${sendStateEvent.msg.content?.extra}"
                        )
                    }
                    is IMSendStateEvent.OnCanceled -> {
                        onIMResultTrack(sendStateEvent.msg, null, null, isCancel = true)
                    }
                    else -> {}
                }
            }
        }

    }

    fun logout() {
        flowJob?.cancel()
        flowJob = null
    }

    private fun onIMResultTrack(
        message: IMessage,
        errorType: Int?,
        errorCode: Int?,
        isCancel: Boolean = false
    ) {
        val localExtra = message.localExtraModel()
        val beginSendTime = if (localExtra.msgSendNtpTime == 0L) NtpTime.nowForce()
            .toString() else localExtra.msgSendNtpTime.toString()
        IMTracker.onResultRB2024062501(
            message,
            errorCode == null && !isCancel,
            if (isCancel) "cancel" else errorCode?.toString() ?: ""
        )
        IMTracker.onResultRB2024112901(message = message)
        when (message.msgType) {
            IMType.TYPE_VOICE_EMOJI, IMType.TYPE_VOICE_EMOJI_IMG -> {
                GlobalScope.launch {
                    val wtVoiceEmojiMsg = message.content as WTVoiceEmojiMsg
                    val robotMsg = ""
                    IMTracker.onChatSendVoiceEmojiResult(
                        message = message,
                        wtVoiceEmojiMsg = wtVoiceEmojiMsg,
                        isSuccess = errorCode == null,
                        errorType = errorType,
                        errorCode = errorCode,
                        traceId = message.msgTraceId,
                        nptTime = beginSendTime,
                        atRobot = robotMsg,
                        isCancel = isCancel,
                    )
                }
            }
            IMType.TYPE_LOCATION_MSG -> {
                IMTracker.onChatSendLocationResult(
                    message = message,
                    errorCode = errorCode,
                )
            }
            IMType.TYPE_IMAGE_MSG -> {
                IMTracker.onChatSendImageMsgResult(
                    message = message,
                    errorType = errorType,
                    errorCode = errorCode,
                    quietMode = if (WTQuietModeManager.isQuietModeEnable) 1 else 0,
                    beginSendTime = beginSendTime,
                    isCancel = isCancel,
                )
            }

            IMType.TYPE_VIDEO_MSG -> {
                IMTracker.onChatSendVideoMsgResult(
                    message = message,
                    isQuietMode = WTQuietModeManager.isQuietModeEnable,
                    beginSendTime = NtpTime.nowForce().toString(),
                    errorType = errorType,
                    errorCode = errorCode,
                    isCancel = isCancel,
                )
            }

            IMType.TYPE_TEXT_MSG -> {
                IMTracker.onChatSendTextMsgResult(
                    message = message,
                    errorType = errorType,
                    errorCode = errorCode,
                    quietMode = if (WTQuietModeManager.isQuietModeEnable) 1 else 0,
                    beginSendTime = beginSendTime,
                    message.mentionedUsers,
                    isCancel = isCancel,
                )
            }

            IMType.TYPE_VOICE_MSG, IMType.TYPE_VOICE_TEXT, IMType.TYPE_VOICE_TEXT_NEW -> {
                IMTracker.onChatSendVoiceResult(
                    message = message,
                    errorType = errorType,
                    errorCode = errorCode,
                    quietMode = if (WTQuietModeManager.isQuietModeEnable) 1 else 0,
                    beginSendTime = beginSendTime,
                    message.mentionedUsers,
                    isCancel = isCancel,
                )
                onChatSendVoiceResultForPM(message,errorCode,isCancel)
            }

            IMType.TYPE_VOICE_GIF -> {
                val voiceGifMsg = message.content as? BuzVoiceGifMsg
                voiceGifMsg?.let {
                    IMTracker.onSendVoiceGifResult(
                        message = message,
                        voiceGifId = it.id,
                        errorCode = errorCode,
                        ntpTime = beginSendTime,
                        isCancel = isCancel
                    )
                }
            }

            IMType.TYPE_FILE_MSG -> {
                val fileMsg = message.content as? BuzFileMessage
                fileMsg?.let {
                    val isOnline =
                        if (message.isPrivate) FriendStatusManager.isOnline(message.targetId.toSafeLong()) else true
                    IMTracker.onSendFileMsgResult(
                        isPrivate = message.isPrivate,
                        conversationId = message.getConversationId(),
                        isOnline = isOnline,
                        traceId = message.msgTraceId ?: "",
                        extension = fileMsg.extName ?: "",
                        startTime = beginSendTime,
                        isCancel = isCancel,
                        errorType = errorType,
                        errorCode = errorCode
                    )
                }
            }
        }
    }

    private fun onChatSendVoiceResultForPM(message: IMessage, errorCode: Int?, isCancel: Boolean) {
        userLifecycleScope?.launch {
            IMTracker.onWTSendMsgResult(
                message,
                errorCode ?: 0,
                FriendStatusManager.getTotalOnlineFriendNum(),
                isCancel
            )
        }
    }
}