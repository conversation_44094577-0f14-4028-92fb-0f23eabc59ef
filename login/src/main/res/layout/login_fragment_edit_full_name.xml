<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/common_black_bg"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideStart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="40dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="40dp" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_marginTop="44dp" />

    <TextView
        android:id="@+id/tvPage"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_text_white_disable"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        tools:text="1/4" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvFeedbackIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="40dp"
        android:text="@string/ic_question_solid"
        android:textColor="@color/color_text_white_secondary"
        android:textSize="24dp"
        app:layout_constraintBottom_toBottomOf="@id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/spaceTitleBar" />
    <TextView
        android:id="@+id/tvTitle"
        style="@style/text_title_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/name_title"
        android:textColor="@color/color_text_white_primary"
        app:layout_constrainedWidth="true"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvTitleTips"
        style="@style/text_body_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/name_subtitle"
        android:textColor="@color/color_text_white_tertiary"
        app:layout_constrainedWidth="true"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        android:layout_marginTop="10dp"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/bgFirstName"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/common_rect_overlay_white_10_radius_12"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleTips"
        app:layout_constraintVertical_chainStyle="packed"
        app:round_radius="4dp"/>

    <EditText
        android:id="@+id/etFirstName"
        style="@style/text_body_large"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:hint="@string/first_name"
        android:background="@color/transparent"
        android:imeOptions="actionNext"
        android:maxLength="25"
        android:paddingStart="20dp"
        android:paddingEnd="0dp"
        android:singleLine="true"
        android:textColor="@color/text_white_main"
        android:textColorHint="@color/text_white_disable"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/iftvFirstClear"
        app:layout_constraintStart_toStartOf="@+id/bgFirstName"
        app:layout_constraintTop_toTopOf="@id/bgFirstName"
        app:layout_constraintVertical_chainStyle="packed"
        app:round_radius="4dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvFirstClear"
        android:layout_width="40dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/ic_clear_input_solid"
        android:textColor="@color/text_white_secondary"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/bgFirstName"
        app:layout_constraintEnd_toEndOf="@+id/bgFirstName"
        app:layout_constraintTop_toTopOf="@+id/bgFirstName"
        tools:visibility="visible" />


    <View
        android:id="@+id/bgLastName"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="40dp"
        android:background="@drawable/common_rect_overlay_white_10_radius_12"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/etFirstName"
        app:layout_constraintVertical_chainStyle="packed"
        app:round_radius="4dp" />

    <EditText
        android:id="@+id/etLastName"
        style="@style/text_body_large"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:background="@color/transparent"
        android:hint="@string/last_name"
        android:imeOptions="actionNext"
        android:maxLength="25"
        android:paddingStart="20dp"
        android:paddingEnd="0dp"
        android:singleLine="true"
        android:textColor="@color/text_white_main"
        android:textColorHint="@color/text_white_disable"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/iftvLastClear"
        app:layout_constraintStart_toStartOf="@+id/bgLastName"
        app:layout_constraintTop_toTopOf="@+id/bgLastName"
        app:layout_constraintVertical_chainStyle="packed"
        app:round_radius="4dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvLastClear"
        android:layout_width="40dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/ic_clear_input_solid"
        android:textColor="@color/text_white_secondary"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/bgLastName"
        app:layout_constraintEnd_toEndOf="@+id/bgLastName"
        app:layout_constraintTop_toTopOf="@+id/bgLastName"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnNext"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        android:alpha="0.3"
        android:enabled="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:text="@string/next"
        app:type="secondary_larger" />

</androidx.constraintlayout.widget.ConstraintLayout>