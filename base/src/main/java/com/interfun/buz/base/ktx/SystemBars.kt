@file:Suppress("unused")

package com.interfun.buz.base.ktx

import android.app.Activity
import android.graphics.Color
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager.LayoutParams
import androidx.annotation.ColorInt
import androidx.core.view.*
import androidx.core.view.WindowInsetsCompat.Type
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity

fun Fragment.immerseStatusBar(lightMode: Boolean = true) {
    activity?.immerseStatusBar(lightMode)
}

fun FragmentActivity.immerseStatusBar(lightMode: Boolean = true) {
    WindowCompat.setDecorFitsSystemWindows(window, false)
    window.decorView.windowInsetsControllerCompat?.systemBarsBehavior =
        WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
    if (VERSION.SDK_INT >= VERSION_CODES.P) {
        window.attributes.layoutInDisplayCutoutMode =
            LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
    }
    transparentStatusBar()
    isLightStatusBar = lightMode
//    contentView?.addNavigationBarHeightToMarginBottom()
    (findViewById<View>(android.R.id.content) as? ViewGroup)?.let { contentView ->
        contentView.setPadding(
            contentView.paddingLeft,
            contentView.paddingTop,
            contentView.paddingRight,
            navigationBarHeight
        )
    }
}

inline var Fragment.isLightStatusBar: Boolean
    get() = activity?.isLightStatusBar == true
    set(value) {
        view?.post { activity?.isLightStatusBar = value }
    }

inline var Activity.isLightStatusBar: Boolean
    get() = window.isLightStatusBar
    set(value) {
        window.isLightStatusBar = value
    }

inline var Window.isLightStatusBar: Boolean
    get() = this.decorView.windowInsetsControllerCompat?.isAppearanceLightStatusBars == true
    set(value) {
        this.decorView.windowInsetsControllerCompat?.isAppearanceLightStatusBars = value
    }

inline var Fragment.statusBarColor: Int
    get() = activity?.statusBarColor ?: -1
    set(value) {
        activity?.statusBarColor = value
    }

@setparam:ColorInt
inline var Activity.statusBarColor: Int
    get() = window.statusBarColor
    set(value) {
        window.statusBarColor = value
    }

fun Fragment.transparentStatusBar() {
    activity?.transparentStatusBar()
}

fun Activity.transparentStatusBar() {
    statusBarColor = Color.TRANSPARENT
}

inline var Fragment.isStatusBarVisible: Boolean
    get() = activity?.isStatusBarVisible == true
    set(value) {
        activity?.isStatusBarVisible = value
    }

inline var Activity.isStatusBarVisible: Boolean
    get() = window.decorView.isStatusBarVisible
    set(value) {
        window.decorView.isStatusBarVisible = value
    }

inline var View.isStatusBarVisible: Boolean
    get() = rootWindowInsetsCompat?.isVisible(Type.statusBars()) == true
    set(value) {
        windowInsetsControllerCompat?.run {
            if (value) show(Type.statusBars()) else hide(Type.statusBars())
        }
    }

/** 初始化状态栏高度 需要用在ConstraintLayout中 并且以此Space作为top起点 */
fun android.widget.Space.initStatusBarHeight() = run {
    constraintHeight(context.statusBarHeight)
}

fun androidx.legacy.widget.Space.initStatusBarHeight() = run {
    constraintHeight(context.statusBarHeight)
}

fun View.addStatusBarHeightToMarginTop() = post {
    if (isStatusBarVisible && isAddedMarginTop != true) {
        updateLayoutParams<ViewGroup.MarginLayoutParams> {
            updateMargins(top = topMargin + context.statusBarHeight)
            isAddedMarginTop = true
        }
    }
}

fun View.subtractStatusBarHeightToMarginTop() = post {
    if (isStatusBarVisible && isAddedMarginTop == true) {
        updateLayoutParams<ViewGroup.MarginLayoutParams> {
            updateMargins(top = topMargin - context.statusBarHeight)
            isAddedMarginTop = false
        }
    }
}

fun View.addStatusBarHeightToPaddingTop() = post {
    if (isAddedPaddingTop != true) {
        updatePadding(top = paddingTop + context.statusBarHeight)
        isAddedPaddingTop = true
    }
}

fun View.subtractStatusBarHeightToPaddingTop() = post {
    if (isAddedPaddingTop == true) {
        updatePadding(top = paddingTop - context.statusBarHeight)
        updateLayoutParams {
            height = measuredHeight - context.statusBarHeight
        }
        isAddedPaddingTop = false
    }
}

inline var Fragment.isLightNavigationBar: Boolean
    get() = activity?.isLightNavigationBar == true
    set(value) {
        activity?.isLightNavigationBar = value
    }

inline var Activity.isLightNavigationBar: Boolean
    get() = window.isLightNavigationBar
    set(value) {
        window.isLightNavigationBar = value
    }

inline var Window.isLightNavigationBar : Boolean
    get() = this.decorView.windowInsetsControllerCompat?.isAppearanceLightNavigationBars == true
    set(value) {
        this.decorView.windowInsetsControllerCompat?.isAppearanceLightNavigationBars = value
    }

fun Fragment.transparentNavigationBar(lightMode: Boolean = true) {
    activity?.transparentNavigationBar()
    isLightNavigationBar = lightMode
}

fun Activity.transparentNavigationBar(lightMode: Boolean = true) {
    navigationBarColor = Color.TRANSPARENT
    isLightNavigationBar = lightMode
}

inline var Fragment.navigationBarColor: Int
    get() = activity?.navigationBarColor ?: -1
    set(value) {
        activity?.navigationBarColor = value
    }

inline var Activity.navigationBarColor: Int
    get() = window.navigationBarColor
    set(value) {
        window.navigationBarColor = value
    }

inline var Fragment.isNavigationBarVisible: Boolean
    get() = activity?.isNavigationBarVisible == true
    set(value) {
        activity?.isNavigationBarVisible = value
    }

inline var Activity.isNavigationBarVisible: Boolean
    get() = window.decorView.isNavigationBarVisible
    set(value) {
        window.decorView.isNavigationBarVisible = value
    }

inline var View.isNavigationBarVisible: Boolean
    get() = rootWindowInsetsCompat?.isVisible(Type.navigationBars()) == true
    set(value) {
        windowInsetsControllerCompat?.run {
            if (value) show(Type.navigationBars()) else hide(Type.navigationBars())
        }
    }

fun View.addNavigationBarHeightToMarginBottom() = post {
    if (isNavigationBarVisible && isAddedMarginBottom != true) {
        updateLayoutParams<ViewGroup.MarginLayoutParams> {
            updateMargins(bottom = bottomMargin + context.navigationBarHeight)
            isAddedMarginBottom = true
        }
    }
}

fun View.subtractNavigationBarHeightToMarginBottom() = post {
    if (isNavigationBarVisible && isAddedMarginBottom == true) {
        updateLayoutParams<ViewGroup.MarginLayoutParams> {
            updateMargins(bottom = bottomMargin - context.navigationBarHeight)
            isAddedMarginBottom = false
        }
    }
}
