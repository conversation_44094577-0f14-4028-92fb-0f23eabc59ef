package com.interfun.buz.common.base.binding

import android.content.Context
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.core.view.doOnDetach
import androidx.viewbinding.ViewBinding
import com.drakeet.multitype.ItemViewDelegate
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.getHolder
import com.interfun.buz.base.ktx.getItem
import com.interfun.buz.base.ktx.simpleName
import com.interfun.buz.base.utils.ViewBindingUtil
import kotlinx.coroutines.CoroutineScope

/**
 * <AUTHOR>
 *
 * @date 2022/6/15
 *
 * @desc
 */
abstract class BaseBindingDelegate<T, VB : ViewBinding> :
    ItemViewDelegate<T, BindingViewHolder<VB>>() {

    fun interface OnItemClickListener<T, VB : ViewBinding> {
        fun onClick(holder: BindingViewHolder<VB>, item: T, position: Int)
    }

    private val onItemClickListenerList = mutableListOf<OnItemClickListener<T, VB>>()

    /**
     * it only be valid when [useItemClickListener] return true
     */
    fun addOnItemClickListener(l: OnItemClickListener<T, VB>) {
        onItemClickListenerList.add(l)
    }

    fun removeOnItemClickListener(l: OnItemClickListener<T, VB>) {
        onItemClickListenerList.remove(l)
    }

    //    @CallSuper
    override fun onCreateViewHolder(context: Context, parent: ViewGroup): BindingViewHolder<VB> =
        BindingViewHolder(ViewBindingUtil.inflateWithGeneric<VB>(this, parent)).apply {
            //初始化 ViewHolder，绑定生命周期
            initHolder(<EMAIL>)
            parent.doOnDetach {
                //监听RV销毁时，将 Holder 生命周期解绑
                this.unbindHolder("${<EMAIL>} doOnDetach ${parent.simpleName} ")
            }
            onViewHolderCreated(this)
        }

    open fun onViewHolderCreated(holder: BindingViewHolder<VB>) {

    }

    /**
     * 该方法内部会调用onBindViewHolder(holder: BindingViewHolder<VB>, item: T)
     */
    override fun onBindViewHolder(holder: BindingViewHolder<VB>, item: T, payloads: List<Any>) {
        prepareScopeForPayloads(holder)
        super.onBindViewHolder(holder, item, payloads)
        val pos = holder.bindingAdapterPosition
        onBindViewHolder(holder.getHolderScope(), holder.binding, item, pos,payloads)

    }

    @CallSuper
    override fun onBindViewHolder(holder: BindingViewHolder<VB>, item: T) {
        if (useItemClickListener(item)) {
            holder.binding.root.click(itemClickInterval()) {
                val pos = holder.bindingAdapterPosition
                onItemClickListenerList.forEach {
                    it.onClick(holder, item, pos)
                }
            }
        }
        val pos = holder.bindingAdapterPosition
        prepareScopeForPayloads(holder)
        onBindViewHolder(holder.binding, item, pos)
        onBindViewHolder(holder.getHolderScope(), holder.binding, item, pos)
    }

    protected fun prepareScopeForPayloads(holder: BindingViewHolder<VB>) {
        holder.bindHolder(simpleName)
    }

    /**
     * 重写该方法，可以在该方法中使用scope协程
     * @param position 简单描述：在某些case(eg:notifyItemInserted(int))更新数据集的情况下，旧数据的position不会及时更新，所以获取到的position可能不对，
     * 推荐使用getBindingAdapterPosition 去获取adapter更新的position
     * RecyclerView will not call the method[onBindViewHolder] again if the position of the item changes in the data set
     * unless the item itself is invalidated or the new position cannot be determined.
     * For this reason, you should only use the position parameter
     * while acquiring the related data item inside this method and should not keep a copy of it.
     * If you need the position of an item later on (e.g. in a click listener),
     * use RecyclerView.ViewHolder.getBindingAdapterPosition() which will have the updated adapter position.
     */
    open fun onBindViewHolder(scope: CoroutineScope? = null, binding: VB, item: T, position: Int) {
    }
    open fun onBindViewHolder(scope: CoroutineScope? = null, binding: VB, item: T, position: Int, payloads: List<Any>) {
    }

    open fun onBindViewHolder(binding: VB, item: T, position: Int) {
    }

    @CallSuper
    override fun onViewRecycled(holder: BindingViewHolder<VB>) {
        holder.unbindHolder(simpleName)
        super.onViewRecycled(holder)
    }

    open fun itemClickInterval(): Long {
        return 500L
    }

    open fun useItemClickListener(item: T): Boolean {
        return false
    }

    fun VB.getCurrentItem(default: T): T {
        return getHolder()?.let { holder ->
            return if (holder.absoluteAdapterPosition in adapterItems.indices) {
                getItem(holder.absoluteAdapterPosition)
            } else default
        } ?: default
    }
}
