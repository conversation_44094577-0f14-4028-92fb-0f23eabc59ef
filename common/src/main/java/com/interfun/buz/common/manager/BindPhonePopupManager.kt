package com.interfun.buz.common.manager

import com.interfun.buz.common.ktx.phone
import com.interfun.buz.common.ktx.uid

/**
 * <AUTHOR>
 * @date 2023/10/18
 * @desc
 */
object BindPhonePopupManager {
    private var hasCloseBindPhonePopupMap = hashMapOf<Long, Boolean>()

    fun reset() {
        hasCloseBindPhonePopupMap.clear()
    }

    fun checkNeedShowPopup(): Boolean {
        if (UserSessionManager.phone.isNullOrEmpty()) {
            val hasShow = hasCloseBindPhonePopupMap[UserSessionManager.uid] ?: false
            if (hasShow.not()) {
                return true
            }
        }
        return false
    }

    fun handleClosePopup() {
        hasCloseBindPhonePopupMap[UserSessionManager.uid] = true
    }
}