package com.interfun.buz.core.widget_liveplace.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.interfun.buz.compose.R
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.autoRTL
import com.interfun.buz.compose.components.haze.hazeEffect
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.LocalHazeState
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles

@Composable
fun CommonPlayStatusPopup(
    modifier: Modifier = Modifier,
    showLoading: Boolean = false,
    showSilent: Boolean = false,
    title: String = "",
    onclick: (() -> Unit)? = null,
) {
    Row(
        modifier
            .height(42.dp)
            .clip(shape = RoundedCornerShape(30.dp))
            .hazeEffect(LocalHazeState.current)
            .padding(horizontal = 16.dp)
            .debouncedClickable {
                onclick?.invoke()
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (showSilent) {
            AsyncImage(
                model = R.drawable.cp_icon_bottom_three_dot,
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
        } else if (showLoading) {
            CircularProgressIndicator(
                color = R.color.text_white_secondary.asColor(),
                strokeWidth = 2.5.dp,
                trackColor = Color.Transparent,
                modifier = Modifier.size(20.dp)
            )
        } else {
            val preloaderLottieComposition by rememberLottieComposition(
                LottieCompositionSpec.Asset("onair/onair_sound_playing.json")
            )
            LottieAnimation(
                isPlaying = true,
                iterations = LottieConstants.IterateForever,
                composition = preloaderLottieComposition,
                modifier = Modifier.size(20.dp)
            )
        }

        HorizontalSpace(4.dp)
        Text(
            text = title,
            maxLines = 1,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center,
            overflow = TextOverflow.Ellipsis,
            style = TextStyles.labelMedium(),
            color = R.color.color_text_white_important.asColor()
        )
//        AutoRTLBox {
            IconFontText(
                modifier = Modifier.autoRTL(),
                iconSize = 14.dp,
                iconText = stringResource(R.string.ic_arrow_right)
            )
//        }
    }
}

@Preview
@Composable
private fun CommonPlayStatusPopupPreview(
) {
    CommonPlayStatusPopup(
        modifier = Modifier.wrapContentSize(),
        title = "hello",
        onclick = {},
        showLoading = false
    )
}