package com.interfun.buz.im.entity

import androidx.annotation.Keep
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.utils.gsonInstance
import com.yibasan.lizhifm.sdk.platformtools.Const
import java.util.TimeZone

/**
 * 打点专用扩展类
 * @param sendFrom see [IMSendFrom]
 */
data class EventTrackExtra(
    @Keep val source: Int? = null,
    @Keep val contentName: String? = null,
    @Keep val contentId: Long? = null,
    @Keep val sendFrom: Int? = null,
    @Keep val isForwardMsg: Boolean = false //Android 可传null
)

/**
 * 用于控制消息样式的扩展类
 */
data class ContentStyleExtra(
    @Keep val isTopic: Boolean? = null,
    @Keep var isSendToBot: Boolean? = null,
    @Keep var hyperlinkMetadataExtra: HyperlinkMetadataExtra? = null
){
    companion object {
        val EMPTY = ContentStyleExtra()
    }
}


class HyperlinkMetadataExtra(
    @Keep val linkUrl: String? = null,
    @Keep val linkLogoPath: String? = null,
    @Keep val linkSiteName: String? = null,
    @Keep val linkImagePath: String? = null,
    @Keep val linkTitle: String? = null,
    @Keep val linkDesc: String? = null
)

// Determine if all metadata except icon is null. If true, do not need to display hyperlink card message
fun HyperlinkMetadataExtra?.isAllMetadataNullExceptIcon(): Boolean {
    return (this?.linkSiteName.isNullOrEmpty() &&
            this?.linkImagePath.isNullOrEmpty() &&
            this?.linkTitle.isNullOrEmpty() &&
            this?.linkDesc.isNullOrEmpty()).getBooleanDefault()
}

/**
 * 服务端专用扩展类
 * localReplyMsgId对应被回复消息的msgId,客户端本地记录
 * replyMsgId对应被回复消息的serMsgId,服务端会记录
 * isReply对应被回复消息的回复状态,true表示已回复,服务端记录(服务端通过编辑消息修改)
 * isTopic表示是否是话题
 * guidanceMsg 是否是首页引导消息
 * @param sourceLanguage 翻译机器人源语言 https://vocalbeats.sg.larksuite.com/wiki/B3shwN0zpiQwxAktlHDlllR1gmh
 * @param targetLanguage 翻译机器人目标语言 https://vocalbeats.sg.larksuite.com/wiki/B3shwN0zpiQwxAktlHDlllR1gmh
 */
@Keep
data class ServerExtra constructor(
    @Keep val isReply: Boolean? = null,
    @Keep var replyMsgId: String? = null,
    @Keep val localReplyMsgId: String? = null,
    @Keep val isTopic: Boolean? = null,
    @Keep val sourceLanguage : String? = null,
    @Keep val targetLanguage: String? = null,
    @Keep val timeZoneId : String? = null,// 给AI发消息时服务端使用，根据用户时区获取当前时间，传给 AI 作为预设, ai场景下需要传递
    @Keep var deviceType : String? = null, // 给AI发消息时服务端使用, 用于判断当时客户端版本是否支持某个 AI 回复类型, 预埋字段, ai场景下需要传递
    @Keep var clientVersion : String? = null, // 给AI发消息时服务端使用, 用于判断当时客户端版本是否支持某个 AI 回复类型, 预埋字段, ai场景下需要传递
    @Keep var guidanceMsg : Boolean? = null, //官方号回复，判断是否为引导消息，可空
    @Keep var guidanceStepType : Int? = null,
    @Keep var preDeliveryType:Int? = null
) {
    companion object {
        val EMPTY = ServerExtra()
    }
}

/**
 * @Desc {"streamId": 234454 }
 * @Author:<EMAIL>
 * @Date: 2023/4/17
 * @param isTopic 一开始协议没定好，导致这个东西有三个地方，ios,android，服务端一端一个
 */
data class IMMessageContentExtra(
    @Keep val eventTrackExtra: EventTrackExtra? = null,
    @Keep var serverExtra: ServerExtra? = null,
    @Keep var contentStyleExtra: ContentStyleExtra? = null,
    @Keep val isTopic: Boolean? = null
) {

    companion object {
        fun parseFromJson(json: String?): IMMessageContentExtra {
            if (json.isNullOrEmpty()) {
                //logDebug("IMMessageContentExtra parseFromJson json is empty")
                return IMMessageContentExtra()
            }
            logDebug("IMMessageContentExtra","IMMessageContentExtra parseFromJson json is--> $json")
            return try {
                gsonInstance.fromJson(json, IMMessageContentExtra::class.java)
            } catch (e: Exception) {
                e.log("IMMessageContentExtra")
                IMMessageContentExtra()
            }
        }

        fun toJson(contentExtra: IMMessageContentExtra): String {
            return contentExtra.toJson()
        }
    }

    fun toJson(): String {
        return gsonInstance.toJson(this)
    }

    override fun toString(): String {
        return toJson()
    }
}