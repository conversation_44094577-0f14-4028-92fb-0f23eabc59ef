package com.interfun.buz.common.view.fragment

import android.Manifest
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import androidx.core.text.buildSpannedString
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.share.ShareLink
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.databinding.CommonFragmentShareQrcodeBinding
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.ktx.toastRegularCorrect
import com.interfun.buz.common.ktx.toastSolidCorrect
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.utils.*
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

/**
 * 分享群或者个人二维码界面
 */

open class ShareQRCodeFragment: BaseBindingFragment<CommonFragmentShareQrcodeBinding>() {
    private val TAG = "ShareQRCodeFragment"
    protected var data: ShareQRCodeData? = null
    private var qrContent:String? = null
    private var shareProfilePrefix = ""
    private var isDecideToBack = false
    private val permissionLauncher = fragment.requestPermissionLauncher{
        if (it.not()){
            toast(R.string.common_save_qrcode_without_permission_tip)
        }else{
            saveQRCodeToGallery(binding.root.context)
        }
    }

    companion object {
        fun newInstance(data: ShareQRCodeData?): ShareQRCodeFragment {
            val args = Bundle()
            args.putParcelable(RouterParamKey.ShareQRCode.KEY_SHARE_DATA, data)
            val fragment = ShareQRCodeFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun initView() {
        data = arguments?.getParcelable(RouterParamKey.ShareQRCode.KEY_SHARE_DATA)
        if (data.isNull()) {
            logInfo(TAG, "data can not be null")
            return
        }

        shareProfilePrefix = when(data!!.type){
            2 -> AppConfigRequestManager.groupShareText + " "
            else -> AppConfigRequestManager.systemShareText+": "
        }

        binding.iftvScan.click {
            if (data?.source == ShareQRCodeSource.Scan.value) {
                activity?.finish()
                return@click
            }
            startActivityByRouter(PATH_COMMON_ACTIVITY_QR_CODE, {
                withInt(RouterParamKey.Common.KEY_SOURCE, QRCodeScanSource.QRCode.value)
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }
        binding.spaceStatusBar.initStatusBarHeight()
        binding.clvLoading.startLoading()
        binding.clvLoading.alpha = 0.3f
        binding.iftvStartBack.click {
            isDecideToBack = true
            activity?.finish()
        }

        combineView(binding.roundRetry,binding.iftvRetry).click {
            onRetry()
        }

        combineView(binding.iftvShareProfile, binding.tvShareProfile).click {
            trackShareProfileClickEvent()
            shareTextBySystemDialog(binding.root.context, (shareProfilePrefix + qrContent))
        }

        combineView(binding.iftvDownload, binding.tvDownload).click {
            trackDownloadClickEvent()
            saveQRCodeToGallery(binding.root.context)
        }

        binding.apply {
            val combineView = combineView(
                iftvShareProfile,
                tvShareProfile,
                iftvDownload,
                tvDownload,
                iftvCopyLink,
                tvCopyLink
            )
            combineView.alpha = 0.3f
            combineView.isClickable = false
            combineView.isEnabled = false
        }
        combineView(binding.iftvCopyLink, binding.tvCopyLink).click {
            trackCopyClickEvent()
            qrContent?.copyToClipboard()
            toastRegularCorrect(R.string.common_copy_link_to_clipboard_tip)
        }
        combineView(binding.tvBuzId, binding.vBuzIdClickArea).click(vibrate = true) {
            toastSolidCorrect(
                R.string.ftue_v3_buzIdCopied.asString(),
                IconToastStyle.ICON_TOP_TEXT_BOTTOM
            )
            data!!.buzId?.copyToClipboard()
            CommonTracker.onClickCopyBuzId(true)
        }

        initPortraitAndName()
    }

    override fun initData() {
        super.initData()
        getQrContent(data!!)
    }

    open fun getQrContent(data: ShareQRCodeData){}

    open fun trackShareProfileClickEvent(){}

    open fun trackCopyClickEvent(){}

    open fun trackDownloadClickEvent(){}

    fun onLoadQRCodeSuccess(content: ShareLink) {
        lifecycleScope.launch(Dispatchers.IO) {
            qrContent = content.link
            val bitmap = generateQRCode(
                text = content.link,
                size = 200.dp,
                qrCodeColor = R.color.color_background_1_default.asColor(),
                isDottedQRCode = true,
                showLogo = true
            )
            launch(Dispatchers.Main) {
                if(bindingIsNull) return@launch
                binding.ivQRCode.setImageBitmap(bitmap)
                binding.ivDownloadQRCode.setImageBitmap(bitmap)
                binding.clvLoading.gone()
                binding.apply {
                    val combineView = combineView(
                        iftvShareProfile,
                        tvShareProfile,
                        iftvDownload,
                        tvDownload,
                        iftvCopyLink,
                        tvCopyLink
                    )
                    combineView.alpha = 1f
                    combineView.isClickable = true
                    combineView.isEnabled = true
                    clvLoading.stopLoading()
                }

                binding.tvExpireTip.visible()
                binding.tvExpireTip.text = ResUtil.getString(
                    R.string.common_qrcode_expire_tip,
                    TimeUtils.formatTimestampToDayMonthYear(content.expireTime)
                )
                binding.tvDownloadExpireTip.visible()
                binding.tvDownloadExpireTip.text = ResUtil.getString(
                    R.string.common_qrcode_expire_tip,
                    TimeUtils.formatTimestampToDayMonthYear(content.expireTime)
                )
            }
        }
    }

    private fun getExpireTime():String{
        if (data.isNull()) return ""
        return when(data!!.type){

            1 ->{
                val expireTime = System.currentTimeMillis() + 29* millsOneDay
                TimeUtils.formatTimestampToDayMonthYear(expireTime)
            }

            2 ->{
                val expireTime = System.currentTimeMillis() + 6* millsOneDay
                TimeUtils.formatTimestampToDayMonthYear(expireTime)
            }
            else ->""
        }
    }

    fun onLoadQRCodeFail(){
        doInMainThread {
            log(TAG, "onLoadQRCodeFail: ")
            if (bindingIsNull) return@doInMainThread
            binding.clvLoading.gone()
            binding.clvLoading.stopLoading()
            binding.groupRetry.visible()
        }
    }

    private fun onRetry(){
        if(bindingIsNull) return
        binding.groupRetry.gone()
        binding.clvLoading.visible()
        binding.clvLoading.startLoading()
        getQrContent(data!!)
    }

    private fun initPortraitAndName() {
        log(TAG, "decodeParams: arguments = $arguments")
        if (data.isNull()) {
            logInfo(TAG, "data is null")
            return
        }
        binding.tvName.text = data!!.name
        combineView(
            binding.tvBuzId,
            binding.vBuzIdClickArea
        ).goneIf(data!!.buzId.isNullOrEmpty())
        if(data!!.buzId.isNullOrEmpty().not()) {
            binding.tvBuzId.text = buildSpannedString {
                append(R.string.common_symbol_at.asString())
                append(data!!.buzId)
                appendSpace(4.dp)
                size(14.dp) {
                    iconFontAlign {
                        typeface(FontUtil.fontIcon!!) {
                            append(R.string.ic_copy.asString())
                        }
                    }
                }
            }
        }
        binding.tvDownloadName.text =data!!.name
        val portraits = data!!.portrait
        if (portraits.isEmpty()) {
            when(data!!.type){
                1->{
                    binding.ivPortrait.load(R.drawable.common_pic_portrait_user_default)
                    binding.ivDownloadPortrait.load(R.drawable.common_pic_portrait_user_default)
                }
                2->{
                    binding.ivPortrait.load(R.drawable.common_pic_portrait_group_default)
                    binding.ivDownloadPortrait.load(R.drawable.common_pic_portrait_group_default)
                }
            }
        } else if (portraits.size == 1) {
            binding.ivPortrait.setPortrait(portraits[0])
            binding.ivDownloadPortrait.setPortrait(portraits[0])
        } else {
            binding.ivPortrait.setPortraitList(portraits)
            binding.ivDownloadPortrait.setPortraitList(portraits, allowHardware = false)
        }
    }

    private fun saveQRCodeToGallery(context: Context) {
        if (isDecideToBack) return
        if (Build.VERSION.SDK_INT < 29 && isPermissionGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE).not()) {
            permissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            return
        }

        val bitmap = Bitmap.createBitmap(
            binding.clDownLoadQRCode.width,
            binding.clDownLoadQRCode.height,
            Bitmap.Config.RGB_565
        )

        val onSuccess: () -> Unit = {
            if (bitmap.isNotNull() && bitmap.isRecycled.not()) {
                bitmap.recycle()
            }
            doInMainThread {
                toastSolidCorrect(R.string.saved)
            }
        }
        val onFailed: () -> Unit = {
            if (bitmap.isNotNull() && bitmap.isRecycled.not()) {
                bitmap.recycle()
            }
            doInMainThread {
                toast(R.string.common_save_qrcode_fail_tip)
            }
        }
        val canvas = Canvas(bitmap)
        binding.clDownLoadQRCode.draw(canvas)
        saveBitmapToGallery(
            context,
            bitmap,
            scope = lifecycleScope,
            onSuccess = onSuccess,
            onFail = onFailed
        )
    }
}


/**
 * type: 1:个人 2:群
 * name: 个人名字或者群名字
 * extraData:额外的数据
 * source:打点来源，看ShareQRCodeSource
 */
@Parcelize
data class ShareQRCodeData(
    val portrait: List<String?>,
    val name: String,
    val buzId: String? = null,
    val type: Int,
    val extraData: String?,
    val source: Int?
) : Parcelable