package com.interfun.buz.chat.common.view.item

import android.content.Context
import android.view.ViewGroup
import androidx.core.view.doOnDetach
import androidx.viewbinding.ViewBinding
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.simpleName
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.chat.common.entity.ChatMsgItemPayloadType
import com.interfun.buz.chat.common.entity.ChatMsgSendMediaItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.common.base.binding.BaseBindingDelegate

/**
 * Author: ChenYouSheng
 * Date: 2023/6/6
 * Email: <EMAIL>
 * Desc:
 */
abstract class ChatMsgSendMediaItemView<T : ChatMsgSendMediaItemBean, VB : ViewBinding>(
    itemCallback: ChatItemCallback
) : BaseChatItemView<T, VB>(itemCallback) {

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup
    ): ChatMsgSendMediaViewHolder<VB> {
        val bindingViewHolder = createViewHolder(parent).apply {
            //初始化 ViewHolder，绑定生命周期
            initHolder(BaseBindingDelegate::class.java.simpleName)
            parent.doOnDetach {
                //监听RV销毁时，将 Holder 生命周期解绑
                this.unbindHolder( "${BaseBindingDelegate::class.java.simpleName} doOnDetach ${parent.simpleName} ")
            }
            onViewHolderCreated(this)
        }
        return bindingViewHolder
    }

    override fun onBindViewHolder(holder: BindingViewHolder<VB>, item: T) {
        super.onBindViewHolder(holder, item)
        (holder as? ChatMsgSendMediaViewHolder)?.setItem(item)
    }

    abstract fun createViewHolder(parent: ViewGroup):ChatMsgSendMediaViewHolder<VB>


    override fun handlePayload(
        holder: BindingViewHolder<VB>,
        item: T,
        type: ChatMsgItemPayloadType
    ) {
        super.handlePayload(holder, item, type)
        when (type) {
            ChatMsgItemPayloadType.UpdateMediaSendStatus -> {
                (holder as? ChatMsgSendMediaViewHolder)?.onUpdateMediaSendStatus(
                    holder.binding,
                    item
                )
            }

            ChatMsgItemPayloadType.UpdateDownloadingLoadingStatus -> {
                (holder as? ChatMsgSendMediaViewHolder)?.let {
                    it.getIftvDownloadView(holder.binding)?.gone()
                    it.getCpiDownloadProgressView(holder.binding)?.visible()
                }
            }

            else -> {}
        }
    }

    override fun onViewRecycled(holder: BindingViewHolder<VB>) {
        super.onViewRecycled(holder)
        (holder as? ChatMsgSendMediaViewHolder)?.onViewRecycled()
    }
}