package com.interfun.buz.chat.common.entity

import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.common.ktx.SingletonDiskCache
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.model.IM5ImageMessage
import com.yibasan.lizhifm.lzlogan.Logz

/**
 * Author: ChenYouSheng
 * Date: 2023/6/6
 * Email: chenyoush<PERSON>@lizhi.fm
 * Desc: 图片接收消息
 */
class ChatMsgReceiveImageItemBean(
    override var msg: IMessage,
    override val msgType: ChatMsgType = ChatMsgType.Image,
    private var remainingBytes: Long = 0L,
) : ChatMsgReceiveMediaItemBean() {

    init {
        if (msg.content is IM5ImageMessage) {
            val message = msg.content as IM5ImageMessage
            val url = message.remoteUrl
            SingletonDiskCache.getSnapshot(url) { cache ->
                remainingBytes = if (cache.isNotNull()) {
                    0
                } else {
                    (msg.content as IM5ImageMessage).totalBytes
                }
                Logz.tag("ChatMsgReceiveImageItemBean")
                    .d("url: $url, leftSize: $remainingBytes, cache: ${cache?.data?.toFile()}")
            }
        } else {
            remainingBytes = 0L
        }
    }

    override fun getRemainingBytes(): Long {
        return remainingBytes
    }

    override fun setRemainingBytes(bytes: Long) {
        remainingBytes = bytes
    }
}
