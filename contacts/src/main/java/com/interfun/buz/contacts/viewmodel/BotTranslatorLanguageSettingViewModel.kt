package com.interfun.buz.contacts.viewmodel

import androidx.annotation.StringRes
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.common.constants.KEY_TYPE
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterParamValues
import com.interfun.buz.common.constants.USER_ID
import com.interfun.buz.social.db.entity.TranslatorLanguageItem
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.utils.TranslationAIUtils
import com.interfun.buz.contacts.viewmodel.BotTranslatorLanguageSettingViewModel.TranslatorSettingItem.SelectableLanguageItem
import com.interfun.buz.social.repo.BotRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 这个是翻译机器人的原语言跟目标语言设置，[BotLanguageSettingViewModel] 是机器人通用的语言设置
 */
@HiltViewModel
class BotTranslatorLanguageSettingViewModel @Inject constructor(
    private val botRepository: BotRepository,
    private val savedStateHandle: SavedStateHandle,
) : ViewModel() {

    private val botUserId = savedStateHandle.get<Long>(RouterParamKey.Common.USER_ID)
        ?: throw RuntimeException("botUserId cannot be null")

    /**
     * 0 sourceLanguageSetting [RouterParamValues.AI.TYPE_SOURCE_LANGUAGE]
     * 1 targetLanguageSetting [RouterParamValues.AI.TYPE_TARGET_LANGUAGE]
     */
    val type = savedStateHandle.get<Int>(RouterParamKey.AI.KEY_TYPE)
        ?: throw RuntimeException("type cannot be null")
    val showLoadingFlow = MutableStateFlow(false)
    val saveTranslationSetting = MutableSharedFlow<Boolean>()

    fun getData(): Flow<List<TranslatorSettingItem>> =
        combine(
            botRepository.getBotExtraFlow(botUserId),
            botRepository.getBotSettingsCacheFlow(botUserId),
            botRepository.getRecentlyUsedTranslatorLanguage(botUserId)
        ) { botInfo, botSetting, recentUsedList ->
            val selectedItem = botInfo.getTranslatorLanguageOrDefault(
                botSetting?.sourceLanguage,
                botSetting?.targetLanguage
            )
            val selectedCode = when (type) {
                RouterParamValues.AI.TYPE_SOURCE_LANGUAGE -> {
                    selectedItem.sourceLanguage.code
                }

                RouterParamValues.AI.TYPE_TARGET_LANGUAGE -> {
                    selectedItem.targetLanguage.code
                }

                else -> {
                    throw RuntimeException("type cannot be null")
                }
            }
            val normalLanguageList = when (type) {
                RouterParamValues.AI.TYPE_SOURCE_LANGUAGE -> {
                    botInfo.options?.translateSourceLanguage
                }

                RouterParamValues.AI.TYPE_TARGET_LANGUAGE -> {
                    botInfo.options?.translateTargetLanguage
                }

                else -> {
                    throw RuntimeException("type cannot be null")
                }
            }?.map {
                SelectableLanguageItem(
                    it,
                    it.languageCode == selectedCode,
                    false
                )
            }
            val normalLanguageMap =
                hashMapOf<String, SelectableLanguageItem>()
            normalLanguageList?.forEach {
                normalLanguageMap[it.language.languageCode] = it
            }
            val filteredRecentUsedList =
                recentUsedList?.mapNotNull {
                    val item = normalLanguageMap[it]
                    if (item != null) {
                        SelectableLanguageItem(
                            item.language,
                            item.isSelected,
                            true
                        )
                    } else {
                        null
                    }
                }
            if (normalLanguageList.isNullOrEmpty()) {
                return@combine emptyList()
            }
            val result = ArrayList<TranslatorSettingItem>(
                normalLanguageList.size + (filteredRecentUsedList?.size ?: 0) + 2
            )
            if (!filteredRecentUsedList.isNullOrEmpty()) {
                result.add(TranslatorSettingItem.Divider(R.string.translator_recent_used))
                result.addAll(filteredRecentUsedList)
            }
            result.add(TranslatorSettingItem.Divider(R.string.translator_language_list))
            result.addAll(normalLanguageList)
            result
        }

    fun selectLanguage(item: SelectableLanguageItem) {
        if (item.isSelected) {
            return
        }
        val languageItem = item.language
        viewModelScope.launch {
            showLoadingFlow.emit(true)
            val newSetting = if (type == RouterParamValues.AI.TYPE_SOURCE_LANGUAGE) {
                botRepository.updateLocalBotSettings(
                    botUserId,
                    sourceLanguage = languageItem.languageCode
                )
            } else {
                botRepository.updateLocalBotSettings(
                    botUserId,
                    targetLanguage = languageItem.languageCode
                )
            }
            if (languageItem.languageCode != TranslatorLanguageItem.AUTO_LANGUAGE_CODE) {
                sendTranslationCenteredMsg(botUserId)
            }
            saveTranslationSetting.emit(true)
            showLoadingFlow.emit(false)
        }
    }


    private fun sendTranslationCenteredMsg(botUserId: Long) {
        TranslationAIUtils.sendTranslationCenteredMsg(botUserId)
    }

    sealed interface TranslatorSettingItem {
        data class SelectableLanguageItem(
            val language: TranslatorLanguageItem,
            val isSelected: Boolean,
            val isLastUsed: Boolean
        ) : TranslatorSettingItem

        data class Divider(@StringRes val stringRes: Int) : TranslatorSettingItem
    }
}