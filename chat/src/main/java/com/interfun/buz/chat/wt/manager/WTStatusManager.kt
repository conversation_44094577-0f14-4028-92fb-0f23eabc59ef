package com.interfun.buz.chat.wt.manager

import android.content.Intent
import com.interfun.buz.base.coroutine.CloseableCoroutineScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.wt.entity.RealTimeMessage
import com.interfun.buz.chat.wt.entity.WTItemBean
import com.interfun.buz.chat.wt.service.WalkieTalkieService
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.manager.ActivityLifecycleManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.im.signal.HeartBeatManager
import com.interfun.buz.im.signal.HeartBeatType
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.BuzTracker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * @Desc wt status manager
 * @Author:<EMAIL>
 * @Date: 2022/10/26
 */
object WTStatusManager {

    private val TAG = "WTStatusManager"
    private val scope = CloseableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    private val switchFlow_ = MutableStateFlow(false)
    private val isRvWTScrollingFlow_ = MutableStateFlow(false)
    var showBlindBoxDialogForNewUser = false
    private var wtList: List<WTItemBean>? = null
    val isPlayingFlow =
        WTMessageManager.messageFlow
            .transform<Pair<RealTimeMessage?, MessageState>, Boolean>
            { emit(it.first!= null && it.second == MessageState.PLAYING) }
            .stateIn(
                scope,
                SharingStarted.Eagerly,
                WTMessageManager.messageFlow.value.first != null && WTMessageManager.messageFlow.value.second == MessageState.PLAYING
            )
    val switchFlow: StateFlow<Boolean> = switchFlow_
    val isRvWTScrollingFlow: StateFlow<Boolean> = isRvWTScrollingFlow_

    val isOn get() = switchFlow.value
    var currentSelectedItem: WTItemBean? = null
        set(value) {
            field = value
            currentSelectItemFlow.emitInScopeIfDifferent(scope, value)
        }
    val currentSelectItemFlow = MutableStateFlow(currentSelectedItem)

    // 当前首页选中的item id
    val wtTargetId: Long?
        get() {
            return currentSelectItemFlow.value?.targetId
        }

    var firstItemInList: WTItemBean? = null

    private val _dndInQuitTips = MutableStateFlow(false)
    val dndInQuitTipState = _dndInQuitTips.asStateFlow()


    init {
        observeCurrentStatusFlow()
        observeVoiceCallStatus()
        doInMainThread {
            ActivityLifecycleManager.doOnNonEntryFirstDrawOnAppResume {
                //case when open app if process is the same as that didn't be killed before.
                if (UserSessionManager.hasSession()){
                    requestChangeSwitchStatus(true)
                    if (isOn){
                        startForegroundService()
                    }
                }
            }
        }
    }

    private fun observeCurrentStatusFlow() {
        scope.launch {
            switchFlow.collect { isOn ->
                log("WTStatusManager", "switch state changed,isOn:$isOn")
                logInfo(TAG,"observeCurrentStatusFlow startForegroundService isOn:${isOn}")
                if (isOn) {
                    startForegroundService()
                    HeartBeatManager.subscribe(HeartBeatType.GLOBAL, null)
                } else {
                    stopForegroundService()
                    HeartBeatManager.unsubscribe(HeartBeatType.GLOBAL)
                    HeartBeatManager.unsubscribe(HeartBeatType.WT_ONLINE_GROUP_SELECTED)
                }
            }
        }
    }

    private fun observeVoiceCallStatus() {
        //之前的旧需求 现在没有对讲机开关了
        /*scope.launch {
            VoiceCallPendStatusManager.statusFlow.collect { status ->
                log("WTStatusManager", "VoiceCallStatus changed,$status")
                when (status.first) {
                    CallPendStatus.IDLE -> {
                        if (pausedByVoiceCall) {
                            requestChangeSwitchStatus(true)
                        }
                        pausedByVoiceCall = false
                    }
                    CallPendStatus.CONNECTING -> {
                        requestChangeSwitchStatus(false)
                        pausedByVoiceCall = true
                    }
                    else -> {
                    }
                }
            }
        }*/
    }

    fun startForegroundService(isFromOverlay : Boolean = false) {
        logInfo(TAG, "startForegroundService,isAppInForeground:${isAppInForeground},isOn:$isOn,isAppInForeground:$isAppInForeground")
        if (isOn.not()) {
            return
        }
        if (isAppInForeground || isFromOverlay) {
            try {
                appContext.startService(Intent(appContext, WalkieTalkieService::class.java).apply {
                    putExtra(RouterParamKey.Common.KEY_IS_FROM_OVERLAY, isFromOverlay)
                })
            } catch (t: Throwable) {
                BuzTracker.onForegroundServiceStartFailed(isAppInForeground, t, "1")
            }
        }
    }

    fun stopForegroundService() {
        logInfo(TAG,"stopForegroundService ${isAppInForeground}")
        appContext.stopService(Intent(appContext, WalkieTalkieService::class.java))
    }

    private fun requestWTSwitch(switch: Boolean) {
        changeSwitchState(switch)
    }

    private fun changeSwitchState(turnOn: Boolean) {
        logInfo(TAG, "changeSwitchState = $turnOn")
        switchFlow_.emitInScopeIfDifferent(scope, turnOn)
    }

    fun requestChangeSwitchStatus(turnOn: Boolean) {
        if (turnOn == isOn) {
            return
        }
        requestWTSwitch(turnOn)
    }

    fun setRvWTScrollingStatus(isScrolling: Boolean) {
        this.isRvWTScrollingFlow_.emitInScopeIfDifferent(scope, isScrolling)
    }

    fun getWtList(): List<WTItemBean>? {
        return wtList?.toList()
    }

    fun updateWtList(list: List<WTItemBean>?) {
        this.wtList = list
    }

    fun updateDndInQuitTipState(isInQuitTipState:Boolean){
        _dndInQuitTips.emitInScopeIfDifferent(scope,isInQuitTipState)
    }

}
