<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="64dp"
    android:layout_height="84dp"
    android:layout_marginEnd="10dp"
    tools:background="@color/color_background_1_default">

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="50dp"
        android:layout_height="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/common_portrait_placeholder" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftClose"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="31dp"
        android:layout_marginBottom="31dp"
        android:background="@drawable/common_circle_30dp_with_border_4dp_bg"
        android:gravity="center"
        android:text="@string/ic_exit"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintStart_toStartOf="@+id/ivPortrait" />

</androidx.constraintlayout.widget.ConstraintLayout>