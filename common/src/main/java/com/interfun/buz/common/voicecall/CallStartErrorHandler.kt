package com.interfun.buz.common.voicecall

import android.view.Gravity
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.IconToastStyle
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.toastIconFontMsg
import com.interfun.buz.common.R
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.eventbus.voicecall.ExcGroupOnCallDialogEvent
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.utils.showAskCameraPermissionDialog
import com.interfun.buz.common.utils.showAskRecordPermission

open class CallStartErrorHandler(private val fragment: Fragment) {
    fun handle(
        error: CallConflictState,
        actionType: ActionType,
        targetId: Long,
        callType: @CallType Int
    ) {
        when (error) {
            CallConflictState.NETWORK_ERROR -> {
                toastError(R.string.rtc_start_call_fail.asString(), callType)
            }

            CallConflictState.NOT_FRIEND -> {
                toastError(R.string.rtc_add_friend_first.asString(), callType)
            }

            CallConflictState.NOT_IN_GROUP -> {
                toastError(R.string.rtc_can_not_call_because_not_in_group.asString(), callType)
            }

            CallConflictState.BEING_INVITED -> {
                if (ChannelPendStatusManager.statusFlow.value.second?.targetId != targetId && actionType == ActionType.JOIN) {
                    toastError(R.string.incoming_call_try_again.asString(), callType)
                }
            }

            CallConflictState.ON_CALL -> {
                if (actionType!=ActionType.JOIN) {
                    toastError(R.string.rtc_retry_after_leave_call.asString(), callType)
                }
            }

            CallConflictState.GROUP_CALL_STARTED -> {
                createGroupCallExistDialog(targetId)
            }

            CallConflictState.NO_RECORD_PERMISSION -> {
                onNoRecordPermission()
            }

            CallConflictState.NO_CAMERA_PERMISSION -> {
                onNoCameraPermission()
            }

            CallConflictState.ON_AIR -> {
                if (actionType != ActionType.JOIN) {
                    toastError(R.string.rtc_retry_for_using_liveplace_now.asString(), callType)
                }
            }

            CallConflictState.IS_LIVE_PLACE_CHANNEL->{
                if (actionType != ActionType.JOIN) {
                    toastError(R.string.rtc_onlyone_of_liveplace_and_call_canbeuse.asString(), callType)
                }
            }

            CallConflictState.ON_AIR_CONFLICT -> {
                toastError(R.string.rtc_onlyone_of_liveplace_and_call_canbeuse.asString(), callType)
            }

            CallConflictState.ON_INVITING_AIR -> {
                toastError(R.string.incoming_call_try_again.asString(), callType)
            }

            CallConflictState.RECORD_IN_USE -> {
                toastError(R.string.rtc_retry_for_mic_is_taken.asString(), callType)
            }

            CallConflictState.CAMERA_IN_USE -> {
                toastError(R.string.rtc_retry_for_camera_is_taken.asString(), callType)
            }

            else -> {}
        }
    }

    open fun onNoRecordPermission() {
        fragment.activity?.let {
            showAskRecordPermission(
                context = it,
                tips = R.string.rtc_ask_mic_permission_description.asString()
            )
        }
    }

    open fun onNoCameraPermission() {
        fragment.activity?.let {
            showAskCameraPermissionDialog(
                context = it,
                tips = R.string.rtc_ask_video_permission_description.asString()
            )
        }
    }

    open fun createGroupCallExistDialog(targetId: Long) {
        fragment.activity?.let {
            val groupChannelInfo = ChannelStatusManager.getConvChannelInfo(targetId) ?: return
            val channelId = groupChannelInfo.channelId
            val callType = groupChannelInfo.vcCallType
            ExcGroupOnCallDialogEvent.post(
                activity = it,
                type = ExcGroupOnCallDialogEvent.TYPE_JOIN_SAME_ROOM,
                channelId = channelId,
                targetId = targetId,
                callType = callType
            )
        }
    }

    private fun toastError(message: String, callType: @CallType Int) {
        toastIconFontMsg(
            message = message,
            textColor = R.color.text_white_main.asColor(),
            iconFont = if (CallType.isVideoCall(callType)) R.string.ic_video.asString() else R.string.ic_tel.asString(),
            iconFontColor = R.color.text_white_main.asColor(),
            gravity = Gravity.CENTER,
            duration = Toast.LENGTH_SHORT,
            style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
        )
    }

}