package com.interfun.buz.onair.standard

import android.os.Parcelable
import androidx.annotation.IntDef
import androidx.compose.runtime.Immutable
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.manager.UserSessionManager
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@IntDef(
    LivePlaceSource.SOURCE_PERSON_PROFILE,
    LivePlaceSource.SOURCE_KNOCK_MSG,
    LivePlaceSource.SOURCE_INVITE_NOTIFY,
    LivePlaceSource.SOURCE_INTRODUCE_DLG,
    LivePlaceSource.SOURCE_NOTIFY,
    LivePlaceSource.SOURCE_SHARE_CARD_MSG,
    LivePlaceSource.SOURCE_CONVERSATION_HOME,
    LivePlaceSource.SOURCE_CONVERSATION_SEARCH_HOME
)
@Retention(AnnotationRetention.SOURCE)
annotation class LivePlaceSource {

    companion object {
        //个人资料页面
        const val SOURCE_PERSON_PROFILE = 1
        //敲门的时候居中消息
        const val SOURCE_KNOCK_MSG = SOURCE_PERSON_PROFILE + 1
        //邀请通知
        const val SOURCE_INVITE_NOTIFY = SOURCE_KNOCK_MSG + 1
        //第一次弹出介绍弹出的时候进场
        const val SOURCE_INTRODUCE_DLG = SOURCE_INVITE_NOTIFY + 1
        //通知栏进入比如官号推送等
        const val SOURCE_NOTIFY = SOURCE_INTRODUCE_DLG + 1
        //消息邀请卡片
        const val SOURCE_SHARE_CARD_MSG = SOURCE_NOTIFY + 1
        //主页联系人列表
        const val SOURCE_CONVERSATION_HOME = SOURCE_SHARE_CARD_MSG + 1
        //主页搜索
        const val SOURCE_CONVERSATION_SEARCH_HOME = SOURCE_CONVERSATION_HOME + 1
        //聊天页入口
        const val SOURCE_CHAT_HEADER = SOURCE_CONVERSATION_SEARCH_HOME + 1

    }

}

@Parcelize
@Immutable
data class RoomParam(
    val livePlaceType: LivePlaceType,
    val activityReason: ActivityReason,
    val targetId: Long,
    val openMic: Boolean,
    /**
     * 仅[activityReason]为[ActivityReason.JOIN_ROOM]才有数值
     */
    val joinROOMParam: JoinRoomParam?,
    @LivePlaceSource
    val source: Int
) : Parcelable {
    fun isSameRoom(param: RoomParam): Boolean {
        return isSameRoom(param.livePlaceType, param.targetId)
    }

    fun isSameRoom(convType: LivePlaceType, targetId: Long): Boolean {
        if (convType == this.livePlaceType && targetId == this.targetId) {
            return true
        }
        return false
    }

    @IgnoredOnParcel
    val imConvType = when (livePlaceType) {
        LivePlaceType.PRIVATE -> IM5ConversationType.PRIVATE
        LivePlaceType.GROUP -> IM5ConversationType.GROUP
    }

    fun obtainCorrespondingRoom(): OnAirRoom? {
        val curPresentOnAirRoom = routerServices<IGlobalOnAirController>().value?.curOnAirContext()
        return if (curPresentOnAirRoom?.createParam?.isSameRoom(this) == true) {
            curPresentOnAirRoom
        } else {
            null
        }
    }

    fun isPrivateType(): Boolean {
        return livePlaceType == LivePlaceType.PRIVATE
    }

    /**
     * 当前房间是我的个人房间
     */
    fun isMineLp(): Boolean {
        return isPrivateType() && targetId == UserSessionManager.getSessionUid()
    }

    class TargetIdWrapper(val gid: Long?, val uid: Long?)

    @IgnoredOnParcel
    val targetIdWrapper: TargetIdWrapper by lazy {
        val uid = if (livePlaceType == LivePlaceType.PRIVATE) {
            targetId
        } else {
            null
        }
        val gid = if (livePlaceType == LivePlaceType.GROUP) {
            targetId
        } else {
            null
        }
        TargetIdWrapper(gid, uid)
    }


}

sealed class MicStatus(val serverNum: Int) {
    data object OPEN : MicStatus(1)
    data object CLOSE : MicStatus(2)

    companion object {
        fun convertMicStatus(open: Boolean): MicStatus {
            return if (open) {
                OPEN
            } else {
                CLOSE
            }
        }

        fun convertMicStatus(flag: Int): MicStatus {
            return if (OPEN.serverNum == flag) {
                OPEN
            } else {
                CLOSE
            }
        }
    }
}