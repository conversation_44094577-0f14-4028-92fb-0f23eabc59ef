<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="50dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.interfun.buz.base.widget.round.RoundConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="12dp"
        android:background="@color/white"
        app:round_radius="@dimen/guide_layout_radius_min"
        app:layout_constraintBottom_toTopOf="@+id/anchor"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <org.libpag.PAGView
            android:id="@+id/pagView"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginStart="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tvTips"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:textColor="@color/text_black_main"
            android:textSize="14sp"
            android:text="@string/chat_guide_two_desc"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/pagView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constrainedWidth="true"
            />

    </com.interfun.buz.base.widget.round.RoundConstraintLayout>

    <ImageView
        android:id="@+id/anchor"
        android:layout_width="16dp"
        android:layout_height="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/common_tool_tip_anchor_top"/>


</androidx.constraintlayout.widget.ConstraintLayout>