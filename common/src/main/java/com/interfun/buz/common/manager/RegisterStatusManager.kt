package com.interfun.buz.common.manager

import com.interfun.buz.common.constants.CommonMMKV

/**
 * <AUTHOR>
 * @date 2025/1/23
 * @desc 实现需求：
 * 1. 注册成功后，标记该用户为新注册用户
 * 2. 退出登录后、或者重启APP，都不再认为是新用户
 */
object RegisterStatusManager {

    // 实现需求：
    //  1. 注册成功后，标记该用户为新注册用户
    //  2. 退出登录后、或者重启APP，都不再认为是新用户
    var isNewUserForOneTime: Boolean = false
        private set

    fun setIsNewUser() {
        CommonMMKV.isNewUser = true
        CommonMMKV.isUserRegister = true
        isNewUserForOneTime = true
    }

    fun logOut() {
        CommonMMKV.isNewUser = false
        isNewUserForOneTime = false
    }

    fun onServiceDestroy() {
        isNewUserForOneTime = false
    }
}