package com.interfun.buz.biz.center.voicefilter.datasource

import com.buz.idl.bot.request.RequestGetVoiceFilterList
import com.buz.idl.bot.response.ResponseGetVoiceFilterList
import com.buz.idl.bot.service.BuzNetBotServiceClient
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig
import javax.inject.Inject

class RemoteVoiceFilterDataSource @Inject constructor() : IRemoteVoiceFilterDataSource {
    companion object {
        const val TAG = "RemoteVoiceFilterDataSource"
    }

    private val client by lazy {
        BuzNetBotServiceClient().withConfig()
    }

    override suspend fun getVoiceFilterList(): Resp<ResponseGetVoiceFilterList> {
        val serverResp = client.getVoiceFilterList(RequestGetVoiceFilterList())
        val data = serverResp.data
        return if (serverResp.isSuccess && data != null) {
            Resp.Success(data, data.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
        }
    }
}