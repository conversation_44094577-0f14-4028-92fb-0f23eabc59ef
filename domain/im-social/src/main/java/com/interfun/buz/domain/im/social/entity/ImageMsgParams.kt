package com.interfun.buz.domain.im.social.entity

import androidx.exifinterface.media.ExifInterface
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.lizhi.im5.sdk.base.LocalFileOption
import com.lizhi.im5.sdk.conversation.IM5ConversationType

data class ImageMsgParams(
    val targetId: String,
    val convType: IM5ConversationType,
    val imagePath: String,
    val imageWidth: Int,
    val imageHeight: Int,
    val isSendOriginImage: Boolean = false,
    val orientation: Int = ExifInterface.ORIENTATION_NORMAL,
    val commonMsgParams: CommonMsgParams? = null,
    val localFileOption: LocalFileOption = LocalFileOption.Copy,
)