<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:background="@color/neutral_black"
    tools:layout_height="@dimen/home_preview_bar_height"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
        android:id="@+id/clPreviewUnreadCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <View
            android:id="@+id/viewUnreadBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/common_r26_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/viewUnreadMarginStart"
            android:layout_width="1.5dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iftvUnreadMentionMe"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvUnreadMentionMe"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/common_oval_black"
            android:gravity="center"
            android:text="@string/ic_at_icon"
            android:textColor="@color/color_background_light_default"
            android:textSize="14sp"
            android:visibility="gone"
            android:layout_marginStart="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintHeight_percent="0.75"
            app:layout_constraintEnd_toStartOf="@+id/tvPreviewUnreadCount"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@+id/viewUnreadMarginStart"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvPreviewUnreadCount"
            style="@style/text_label_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="2dp"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/color_text_black_secondary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvUnread"
            app:layout_constraintHeight_min="26dp"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@+id/iftvUnreadMentionMe"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_min="14dp"
            app:layout_goneMarginEnd="6dp"
            app:layout_goneMarginStart="4.5dp"
            tools:text="2" />

        <TextView
            android:id="@+id/tvUnread"
            style="@style/text_label_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/home_preview_unread_noti"
            android:textColor="@color/color_text_black_secondary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iftvUnreadArrow"
            app:layout_constraintStart_toEndOf="@+id/tvPreviewUnreadCount"
            app:layout_constraintTop_toTopOf="parent" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvUnreadArrow"
            style="@style/iconfont_14"
            android:layout_marginEnd="4dp"
            app:autoRTL="true"
            android:text="@string/ic_arrow_right"
            android:textColor="@color/color_text_black_secondary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvUnread"
            app:layout_constraintTop_toTopOf="parent" />

    </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

    <com.interfun.buz.home.view.widget.AdaptiveWidthView
        android:id="@+id/viewPreviewUnread"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</merge>