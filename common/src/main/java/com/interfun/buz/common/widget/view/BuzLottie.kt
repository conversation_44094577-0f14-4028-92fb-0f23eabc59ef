package com.interfun.buz.common.widget.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.core.view.isVisible
import com.airbnb.lottie.LottieAnimationView
import com.interfun.buz.base.ktx.NoParamOneReturnCallback

/**
 * 自动处理了在onDetachedFromWindow时停止动画的逻辑
 * 但注意在 RecyclerView 中，请实现在onAttachedToWindow时恢复动画的逻辑！！！否则喜提 bug
 */
open class BuzLottie @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : LottieAnimationView(context, attrs) {

    var checkIfNeedRePlayWhenAttached: NoParamOneReturnCallback<Boolean>? = null

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // logMine("this:${this.hashCode()},onDetachedFromWindow")
        //a leak in lottie,when use system window to show anim in Overlay,animation will not shop
        cancelAnimation()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (isVisible && checkIfNeedRePlayWhenAttached?.invoke() == true) {
            playAnimation()
        }
        // logMine("this:${this.hashCode()},onAttachedToWindow")
    }

    override fun onWindowVisibilityChanged(visibility: Int) {
        super.onWindowVisibilityChanged(visibility)
        // logMine("this:${this.hashCode()},onWindowVisibilityChanged:$visibility")
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        // logMine("this:${this.hashCode()},onVisibilityChanged:$visibility,changeView:$changedView")
    }
}