package com.interfun.buz.biz.center.voicemoji.datasource.collection

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.buz.idl.voicemoji.response.ResponseGetVoiceEmojiCollections
import com.google.common.reflect.TypeToken
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.biz.center.voicemoji.manager.VoiceEmojiMemoryCacheManager
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionModifyType
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionState
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionState.Offline.toState
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionStateTypeAdapter
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType.Unknown.toType
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionTypeAdapter
import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionData
import com.interfun.buz.common.manager.cache.userPreferencesDataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * Author: ChenYouSheng
 * Date: 2024/12/16
 * Email: <EMAIL>
 * Desc: VE收藏夹本地数据
 */
class LocalVECollectionDataSource(private val context: Context = appContext) {

    private val Context.veCollectionDataStore: DataStore<Preferences> by userPreferencesDataStore("voice-emoji-collection")
    private val KEY_VE_COLLECTION_LIST = stringPreferencesKey("ve_collection-list")
    private val KEY_VE_HAS_BEEN_FAVORITED = booleanPreferencesKey("ve_has_been_favorited")
    private val KEY_VE_COLLECTION_FIRST = "KEY_VE_COLLECTION_FIRST"
    val gson: Gson = GsonBuilder()
        .registerTypeAdapter(CollectionType::class.java, CollectionTypeAdapter())
        .registerTypeAdapter(CollectionState::class.java, CollectionStateTypeAdapter())
        .create()

    /**
     * 是否是首次请求，默认是true
     */
    fun isFirstRequest(): Boolean {
        return VoiceEmojiMemoryCacheManager.get(KEY_VE_COLLECTION_FIRST) ?: true
    }

    /**
     * 设置是否首次请求
     */
    fun setFirstRequest(isFirst: Boolean) {
        VoiceEmojiMemoryCacheManager.put(KEY_VE_COLLECTION_FIRST, isFirst)
    }

    /**
     * 更新保存收藏夹列表
     */
    suspend fun updateVoiceEmojiCollections(data: ResponseGetVoiceEmojiCollections) {
        withContext(Dispatchers.IO) {
            val collectionList = data.voiceEmojiCollections?.map { veCollection ->
                VECollectionData(
                    id = veCollection.id,
                    type = veCollection.type.toType(),
                    objectId = veCollection.objectId ?: "",
                    data = veCollection.dataJson ?: "",
                    status = veCollection.status.toState()
                )
            }
            context.veCollectionDataStore.updateData { pref ->
                pref.toMutablePreferences().apply {
                    this[KEY_VE_COLLECTION_LIST] = gson.toJson(collectionList)
                }
            }
        }
        if (!data.voiceEmojiCollections.isNullOrEmpty()) {
            setFirstRequest(false)
        }
    }

    /**
     * 获取收藏列表
     */
    fun getVECollectionList(): Flow<List<VECollectionData>?> {
        return context.veCollectionDataStore.data.map { sp ->
            val json = sp[KEY_VE_COLLECTION_LIST]
            try {
                val collectionList: MutableList<VECollectionData> =
                    gson.fromJson(json, object : TypeToken<MutableList<VECollectionData>>() {}.type)
                collectionList
            } catch (ignore: Exception) {
                null
            }
        }.flowOn(Dispatchers.Default)
    }

    /**
     * 修改本地缓存
     */
    suspend fun modifyLocalCache(type: CollectionModifyType, data: VECollectionData) {
        withContext(Dispatchers.IO) {
            context.veCollectionDataStore.edit { preferences ->
                try {
                    val json = preferences[KEY_VE_COLLECTION_LIST]
                    val collectionList: MutableList<VECollectionData> = json?.let {
                        gson.fromJson(it, typeOf<List<VECollectionData>>())
                    } ?: mutableListOf()

                    when (type) {
                        CollectionModifyType.AddToFirst -> { // 插入到首个位置
                            preferences[KEY_VE_HAS_BEEN_FAVORITED] = collectionList.removeIf { it.id == data.id }
                            collectionList.add(0, data)
                        }

                        CollectionModifyType.Remove -> {}  // 在 deleteLocalCache中实现

                        CollectionModifyType.MoveToFirst -> {  // 移动到首个位置
                            collectionList.removeIf { it.id == data.id }
                            collectionList.add(0, data)
                        }
                    }
                    if (collectionList.isEmpty()) {
                        preferences.remove(KEY_VE_COLLECTION_LIST)
                    } else {
                        // 更新本地缓存
                        preferences[KEY_VE_COLLECTION_LIST] = gson.toJson(collectionList)
                    }

                } catch (ignore: Exception) {

                }
            }
        }
    }

    /**
     * 当前VE/VG是否已经在收藏夹里
     */
    fun hasBeenFavoritedFlow(): Flow<Boolean> {
        return context.veCollectionDataStore.data.map { sp ->
            sp[KEY_VE_HAS_BEEN_FAVORITED] ?: false
        }.flowOn(Dispatchers.Default)
    }

    /**
     * 删除缓存
     */
    suspend fun deleteLocalCache(collectId: Long) {
        withContext(Dispatchers.IO) {
            context.veCollectionDataStore.edit { preferences ->
                val json = preferences[KEY_VE_COLLECTION_LIST] ?: return@edit
                try {
                    val collectionList: MutableList<VECollectionData> = gson.fromJson(
                        json, typeOf<List<VECollectionData>>()
                    )
                    collectionList.removeIf { it.id == collectId }
                    if (collectionList.isEmpty()) {
                        preferences.remove(KEY_VE_COLLECTION_LIST)
                    } else {
                        preferences[KEY_VE_COLLECTION_LIST] = gson.toJson(collectionList)
                    }
                } catch (ignore: Exception) {
                }
            }
        }
    }

    private inline fun <reified T> typeOf() = object : TypeToken<T>() {}.type
}