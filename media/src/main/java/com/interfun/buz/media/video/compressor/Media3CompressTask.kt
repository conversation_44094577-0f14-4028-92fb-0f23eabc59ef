@file:UnstableApi
package com.interfun.buz.media.video.compressor

import android.net.Uri
import androidx.annotation.MainThread
import androidx.media3.common.Effect
import androidx.media3.common.MediaItem
import androidx.media3.common.MimeTypes
import androidx.media3.common.util.UnstableApi
import androidx.media3.effect.Presentation
import androidx.media3.transformer.Composition
import androidx.media3.transformer.EditedMediaItem
import androidx.media3.transformer.Effects
import androidx.media3.transformer.ExportException
import androidx.media3.transformer.ExportResult
import androidx.media3.transformer.Transformer
import androidx.media3.transformer.VideoEncoderSettings
import com.google.common.collect.ImmutableList
import com.interfun.buz.assertutil.buzAssertMain
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.base.ktx.resumeIfActive
import com.interfun.buz.base.ktx.withIOContext
import com.interfun.buz.media.video.compressor.BuzVideoCompressorHelper.CompressTask
import com.interfun.buz.media.video.compressor.BuzVideoCompressorHelper.FailureInfo
import com.interfun.buz.media.video.compressor.BuzVideoCompressorHelper.SuccessInfo
import com.interfun.buz.media.video.compressor.BuzVideoCompressorHelper.TaskState
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import java.io.File
import kotlin.coroutines.resumeWithException

/**
 * 使用Media3组件的视频压缩任务，Media3内部压缩失败会使用VideoEdit组件进行二次压缩
 */
class Media3CompressTask(
    override var success: Boolean,
    override var state: TaskState,
    override var inputFilePath: String,
    override var outFilePath: String,
    override var originFileSize: Long,
    override var successInfo: SuccessInfo,
    override var failureInfo: FailureInfo,
    override var supportCompressSwitch: Boolean
): CompressTask(CompressMode.MEDIA3, success, state, inputFilePath, outFilePath, originFileSize, successInfo, failureInfo) {

    companion object {
        const val TAG = "Media3CompressTask"
    }

    private var transformer: Transformer? = null
    private var videoEditCompressTask: VideoEditCompressTask? = null

    @MainThread
    override suspend fun start() = suspendCancellableCoroutine<CompressTask> { con ->
        buzAssertMain()
        if (this.state != TaskState.DOING) {
            con.resumeWithException(Throwable("Task state must be DOING, current state is ${this.state}"))
            return@suspendCancellableCoroutine
        }
        con.invokeOnCancellation { err ->
            this.success = false
            this.state = TaskState.CANCEL
            this.failureInfo.exportException = err
            logInfo(TAG, "cancel compress on invokeOnCancellation")
            transformer?.cancel()
        }
        val startTime = android.os.SystemClock.elapsedRealtime()
        val effects = ImmutableList.Builder<Effect>()
        effects.add(Presentation.createForHeight(720))
        val inFile = File(inputFilePath)
        this.originFileSize = inFile.length()
        val processFileItem = MediaItem.fromUri(Uri.fromFile(inFile))
        val editedMediaItem = EditedMediaItem
            .Builder(processFileItem)
            .setEffects(Effects(ImmutableList.of(), effects.build())).build()
        val transformerBuilder = Transformer
            .Builder(ApplicationContext.getContext())
            .setVideoMimeType(MimeTypes.VIDEO_H264)
            .setAudioMimeType(MimeTypes.AUDIO_AAC)
//                .setMuxerFactory(DefaultMuxer.Factory())
            .setEnsureFileStartsOnVideoFrameEnabled(true)
        transformerBuilder.setEncoderFactory(
            /**
             * https://stackoverflow.com/questions/21120717/h-264-video-wont-play-on-ios
             * 此处因为IOS无法保存high@v6压缩的视频，因此才取自定义的做法
             */
            com.interfun.buz.media.video.compressor.encoder.DefaultEncoderFactory.Builder( /* context= */
                ApplicationContext.getContext()
            )
                .setRequestedVideoEncoderSettings(
                    VideoEncoderSettings.Builder()
                        .build()
                )
                .setEnableFallback(true)
                .build()
        )
        val transformerTask = transformerBuilder.build()
        transformerTask.addListener(object : Transformer.Listener {
            override fun onCompleted(composition: Composition, exportResult: ExportResult) {
                buzAssertMain()
                logInfo(TAG, "onCompleted")
                super.onCompleted(composition, exportResult)

                val outputFile = File(outFilePath)
                if (!outputFile.exists() || !File(inputFilePath).exists()) {
                    <EMAIL> = false
                    <EMAIL> = TaskState.ERR
                    <EMAIL> = exportResult
                    <EMAIL> = composition
                    val endTime = android.os.SystemClock.elapsedRealtime()
                    <EMAIL> = endTime - startTime
                    con.resumeIfActive(this@Media3CompressTask)
                    logInfo(TAG, "onCompleted , file not exists")
                } else {
                    <EMAIL> = true
                    <EMAIL> = TaskState.DONE
                    <EMAIL> = exportResult
                    <EMAIL> = composition
                    val endTime = android.os.SystemClock.elapsedRealtime()
                    <EMAIL> = endTime - startTime
                    <EMAIL> = outputFile.length()
                    con.resumeIfActive(this@Media3CompressTask)
                    logInfo(TAG, "onCompleted , file  exists")
                }

                StatisticsCompressHelper.printSuccess(this@Media3CompressTask)
            }


            override fun onError(
                composition: Composition,
                exportResult: ExportResult,
                exportException: ExportException
            ) {
                buzAssertMain()
                logError(TAG, "onError: ${exportException.message}")
                super.onError(composition, exportResult, exportException)
                if (supportCompressSwitch) {
                    //如果支持二次压缩，则使用VideoEdit组件 强制软解码进行二次压缩
                    videoEditCompressTask = VideoEditCompressTask(
                        false,
                        TaskState.INIT,
                        inputFilePath,
                        outFilePath,
                        originFileSize,
                        SuccessInfo(),
                        FailureInfo(),
                        forceSoftwareDecode = true
                    )
                    MainScope().launch {
                        videoEditCompressTask?.let {
                            try {
                                hasCompressSwitched = true
                                it.state = TaskState.DOING
                                val result = it.startWithTimeOut()
                                if (it.success) {
                                    // Fallback succeeded, use its result
                                    <EMAIL> = true
                                    <EMAIL> = TaskState.DONE
                                    <EMAIL> = result.successInfo
                                    StatisticsCompressHelper.printSuccess(this@Media3CompressTask)
                                    con.resumeIfActive(this@Media3CompressTask)
                                    logInfo(TAG, "Fallback compression succeeded")
                                } else {
                                    // Both primary and fallback failed
                                    <EMAIL> = false
                                    <EMAIL> = TaskState.ERR
                                    <EMAIL> = result.failureInfo.exportException?: exportException
                                    <EMAIL> = composition
                                    <EMAIL> = exportResult
                                    StatisticsCompressHelper.printFailure(this@Media3CompressTask, true)
                                    con.resumeIfActive(this@Media3CompressTask)
                                    logWarn(TAG, "Both Media3 and fallback compression failed")
                                }
                            } catch (e: Exception) {
                                // Handle fallback exception
                                <EMAIL> = false
                                <EMAIL> = TaskState.ERR
                                <EMAIL> = e
                                <EMAIL> = composition
                                <EMAIL> = exportResult
                                StatisticsCompressHelper.printFailure(this@Media3CompressTask, true)
                                con.resumeIfActive(this@Media3CompressTask)
                                logError(TAG, "Fallback compression threw exception", e)
                            } finally {
                                it.release()
                            }
                        }

                    }
                }else{
                    //如果不支持二次压缩，则直接返回错误
                    <EMAIL> = false
                    <EMAIL> = TaskState.ERR
                    <EMAIL> = exportException
                    <EMAIL> = composition
                    <EMAIL> = exportResult
                    StatisticsCompressHelper.printFailure(this@Media3CompressTask)
                    con.resumeIfActive(this@Media3CompressTask)
                }
            }

        })
        try {
            logInfo(
                TAG,
                " transformerTask.start inputFilePath=[${inputFilePath}] outFilePath=[${outFilePath}]"
            )
            this.transformer = transformerTask
            transformerTask.start(editedMediaItem, outFilePath)
        } catch (err: Exception) {
            this.success = false
            this.state = TaskState.ERR
            this.failureInfo.exportException = err
            con.resumeIfActive(this)
        }
    }

    /**
     * 开始压缩但是约束必须在规定时间内完成
     */
    @MainThread
    override suspend fun startWithTimeOut(maxTimeMillisecond: Long): CompressTask {
        try {
            buzAssertMain()
            logInfo(TAG, "startWithTimeOut ${maxTimeMillisecond}")
            withTimeout(maxTimeMillisecond) {
                //这里加断言是防止未来kotlin对withTimeout的更改
                buzAssertMain()
                videoParameters = withIOContext {
                    VideoParameters.detachVideoModel(inputFilePath)
                }
                start()
            }
        } catch (e: TimeoutCancellationException) {
            if (videoEditCompressTask != null && videoEditCompressTask?.state == TaskState.DOING) {
                videoEditCompressTask?.cancel()
            }
            this.state = TaskState.TIME_OUT
            this.success = false
            this.failureInfo.composition = null
            this.failureInfo.exportResult = null
            this.failureInfo.exportException = e
            logError(TAG, e)
        }
        return this
    }

    /**
     * 取消压缩
     */
    @MainThread
    override fun cancel() {
        buzAssertMain()
        if (this.state == TaskState.DOING) {
            transformer?.cancel()
            videoEditCompressTask?.cancel()
        }
        this.success = false
        this.state = TaskState.CANCEL
        this.failureInfo.exportException = RuntimeException("manual call cancel")
        this.failureInfo.composition = null
        this.failureInfo.exportResult = null
        logWarn(TAG, "cancel ${this.inputFilePath}")
    }

    @MainThread
    override fun release() {
    }
}