package com.interfun.buz.demo.videocall

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewDelegate
import com.google.android.flexbox.*
import com.google.android.flexbox.FlexboxLayoutManager.LayoutParams
import com.interfun.buz.base.ktx.*
import com.interfun.buz.demo.videocall.VideoPreviewCardItemView.VH
import kotlin.random.Random

/**
 * Author: ChenYouSheng
 * Date: 2025/2/21
 * Email: <EMAIL>
 * Desc:
 */
class FlexBoxLayoutDemoActivity : AppCompatActivity() {

    private val mAdapter by lazy {
        MultiTypeAdapter {
            register(VideoPreviewCardItemView(mRecyclerView))
        }
    }

    private val mRecyclerView by lazy {
        RecyclerView(this).apply {
            layoutManager = FlexboxLayoutManager(this@FlexBoxLayoutDemoActivity).apply {
                flexDirection = FlexDirection.ROW// 主轴横向布局
                setFlexWrap(FlexWrap.WRAP)// 允许换行
                justifyContent = JustifyContent.CENTER// 主轴居中
                alignItems = AlignItems.CENTER// 交叉轴居中
            }
        }
    }


    private val containerId by lazy {
        View.generateViewId()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mRecyclerView.adapter = mAdapter
        mAdapter.items = emptyList()
        setContentView(LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            id = containerId
            setBackgroundColor(Color.WHITE)

            addView(LinearLayout(this@FlexBoxLayoutDemoActivity).apply {
                orientation = LinearLayout.HORIZONTAL
                addView(Button(this@FlexBoxLayoutDemoActivity).apply {
                    text = "Join Item"
                    isAllCaps = false
                    click {
                        doOnAddItem()
                    }
                })

                addView(Button(this@FlexBoxLayoutDemoActivity).apply {
                    text = "Leave Item"
                    isAllCaps = false
                    click {
                        doOnRemoveItem()
                    }
                })

                addView(Button(this@FlexBoxLayoutDemoActivity).apply {
                    text = "enterPiPMode"
                    isAllCaps = false
                    click {
                        enterPiPMode()
                    }
                })
            })

            addView(
                mRecyclerView, LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT, 0, 1f
                )
            )
        })

        mRecyclerView.doOnGlobalLayout {
            mAdapter.items = initItems()
            mAdapter.notifyDataSetChanged()
            true
        }

    }

    private fun initItems() = mutableListOf<VideoPreviewItemBean>().apply {
        repeat(10) {
            val preview = createPreview(it + 1)
            val itemBean = VideoPreviewItemBean(
                id = it.toLong(), preview = preview
            )
            add(itemBean)
        }
    }


    private fun doOnRemoveItem() {
        val originalList = mAdapter.items.toMutableList()
        if (originalList.isNotEmpty()) {
            val removedIndex = originalList.lastIndex
            originalList.removeLast()

            mAdapter.items = originalList
            mAdapter.notifyItemRemoved(removedIndex) // 先通知移除
            mAdapter.notifyItemRangeChanged(0, originalList.size)
        }
    }

    private fun doOnAddItem() {
        val originalList = mAdapter.items.toMutableList()

        // 限制最大 item 数量为 12
        if (originalList.size >= 12) {
            "最多支持12人".toast()
            return
        }

        val newItemIndex = originalList.size

        // 创建新 item
        val newItem = VideoPreviewItemBean(
            id = newItemIndex.toLong(),
            preview = createPreview(newItemIndex + 1),
        )

        // 添加新 item
        originalList.add(newItem)

        mAdapter.items = originalList
        // 先插入
        mAdapter.notifyItemInserted(newItemIndex)
        // 在变更剩下的
        mAdapter.notifyItemRangeChanged(0, originalList.size)
    }

    private fun createPreview(newItemIndex: Int) = TextView(this).apply {
        text = "Item $newItemIndex"
        setTextColor(Color.WHITE)
        gravity = Gravity.CENTER
    }


    private fun enterPiPMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val intent = Intent(this, com.interfun.buz.demo.videocall.PipDemoActivity::class.java)
            // 保存返回时的Activity，当重最小化返回的时候能够返回回来
            val backIntent = Intent(this, FlexBoxLayoutDemoActivity::class.java)
            intent.putExtra("PREVIOUS_ACTIVITY", backIntent)

            startActivity(intent)
        }
        finish() // 关闭当前房间
    }

    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean,
        newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)
        if (isInPictureInPictureMode) {
            findViewById<LinearLayout>(containerId).getChildAt(0).gone()
        } else {
            findViewById<LinearLayout>(containerId).getChildAt(0).visible()
        }
        mAdapter.notifyDataSetChanged()
    }
}


class VideoPreviewCardItemView(private val mRecyclerView: RecyclerView) :
    ItemViewDelegate<VideoPreviewItemBean, VH>() {
    class VH(itemView: View) : RecyclerView.ViewHolder(itemView)

    override fun onBindViewHolder(holder: VH, item: VideoPreviewItemBean) {
        holder.itemView.apply {
            val container = this as? ViewGroup ?: return@apply
            container.removeAllViews()

            item.preview.removeFromParent()
            val (width, height) = calculateItemSize(
                adapterItems.size,
                mRecyclerView.width,
                mRecyclerView.height
            )
            container.addView(
                item.preview, ViewGroup.LayoutParams(width, height)
            )
        }
    }

    override fun onCreateViewHolder(context: Context, parent: ViewGroup): VH {
        return VH(FrameLayout(context).apply {
            layoutParams = LayoutParams(
                LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT
            )
            setBackgroundColor(getRandomColor())
        })
    }
}

data class VideoPreviewItemBean(
    val id: Long,
    val preview: View,
)


fun calculateItemSize(
    itemCount: Int, recyclerViewWidth: Int, recyclerViewHeight: Int
): Pair<Int, Int> {
    val columns = when {
        itemCount == 1 -> 1
        itemCount <= 2 -> 1
        itemCount <= 6 -> 2
        else -> 3
    }
    val rows = when {
        itemCount == 1 -> 1
        itemCount <= 4 -> 2
        itemCount <= 9 -> 3
        else -> 4
    }
    val itemWidth = recyclerViewWidth / columns
    val itemHeight = recyclerViewHeight / rows
    return Pair(itemWidth, itemHeight)
}

fun getRandomColor(): Int {
    return Color.rgb(Random.nextInt(256), Random.nextInt(256), Random.nextInt(256))
}

