package com.interfun.buz.onair.viewmodel

import android.net.Uri
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavHostController
import com.interfun.buz.common.database.entity.LivePlaceBaseInfo
import com.interfun.buz.compose.ktx.navHostControllerChangeFlow
import com.interfun.buz.liveplace.viewmodel.ILivePlaceViewModel
import com.interfun.buz.liveplace.viewmodel.LivePlaceViewModelHelp
import com.interfun.buz.onair.standard.LivePlaceBaseInfoRepository
import com.interfun.buz.onair.standard.LivePlaceType
import com.interfun.buz.onair.ui.state.LivePlaceNavType
import com.yibasan.lizhifm.lzlogan.Logz
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

data class RouteBackground(val route: String, val uri: Uri, val isCustom: Boolean = false)

data class BgImgData(val uri: Uri, val isCustom: Boolean = false)
@HiltViewModel
class LivePlaceBackgroundViewmodel @javax.inject.Inject constructor(
    private val handle: SavedStateHandle,
    private val livePlaceInfoRepository: LivePlaceBaseInfoRepository
) : ViewModel(),
    ILivePlaceViewModel by LivePlaceViewModelHelp() {
    private val _navigatorDesFlow = MutableSharedFlow<String?>(replay = 1)
    val navigatorDesFlow = _navigatorDesFlow.asSharedFlow()
    private val _routeBackgroundFlow = MutableStateFlow<RouteBackground?>(null)
    suspend fun updateNavigateDes(nav: NavHostController) {
        nav.navHostControllerChangeFlow().collect { des ->
            _navigatorDesFlow.emit(des.route)
        }
    }

    fun requestUpdateBg(data: RouteBackground) = viewModelScope.launch {
        _routeBackgroundFlow.emit(data)
    }

    val TAG = "LivePlaceBackgroundViewmodel"

    /**
     * 获取当前有效的背景去设置
     */
    val availableBgImgFlow by lazy {
        combine(
            _navigatorDesFlow, obtainOriginBaseInfo(), _routeBackgroundFlow
        ) { curDes, baseInfo, routeBackground ->
            Logz.tag(TAG).d(
                "availableBgImgFlow  called with: curDes = $curDes, baseInfo = $baseInfo, routeBackground = $routeBackground"
            )
            if (curDes == routeBackground?.route && routeBackground!=null) {
                BgImgData(routeBackground.uri,routeBackground.isCustom)
            } else if (isShowBgDes(curDes) || routeBackground == null) {
                if (baseInfo?.bgImgUrl.isNullOrEmpty()) {
                    BgImgData(Uri.EMPTY,true)
                } else {
                    BgImgData(Uri.parse(baseInfo?.bgImgUrl),baseInfo?.isCustomizeBg==true)
                }
            } else {
                BgImgData(routeBackground.uri,routeBackground.isCustom)
            }
        }.stateIn(this.viewModelScope, SharingStarted.Eagerly,  BgImgData(Uri.EMPTY,true))
    }

    private fun isShowBgDes(des: String?) = des == LivePlaceNavType.Home.route
            || des == LivePlaceNavType.InitSettingPendingLive.route

    private fun obtainOriginBaseInfo(): Flow<LivePlaceBaseInfo?> {
        val param = obtainLivePlaceParam()
        return if (param.livePlaceType == LivePlaceType.PRIVATE) {
            livePlaceInfoRepository.livePlaceForUserFlow(param.targetId)
        } else {
            livePlaceInfoRepository.livePlaceForGroupFlow(param.targetId)
        }
    }


    //这里sample是用避免闪烁
    val bgmPlayerStatusFlow: StateFlow<Int> by lazy {
        obtainLivePlaceRoom()?.obtainPlayBgmStatusFlow()
            ?: MutableStateFlow(androidx.media3.common.Player.STATE_IDLE)
    }

}

