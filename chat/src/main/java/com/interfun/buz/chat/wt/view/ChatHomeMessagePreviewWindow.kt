package com.interfun.buz.chat.wt.view

import android.content.Context
import android.text.SpannedString
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import com.interfun.buz.base.ktx.appendCenterImage
import com.interfun.buz.base.ktx.appendSpace
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asDrawable
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.cropLength
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.iconFontAlign
import com.interfun.buz.base.ktx.isVisible
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.launchMain
import com.interfun.buz.base.ktx.size
import com.interfun.buz.base.ktx.typeface
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.utils.AsrTracker
import com.interfun.buz.chat.databinding.ChatHomeMessagePreviewWindowBinding
import com.interfun.buz.chat.wt.entity.MessagePreviewState
import com.interfun.buz.chat.wt.entity.WTItemBean
import com.interfun.buz.chat.wt.entity.isGroup
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.ktx.alphaAnim
import com.interfun.buz.im.ktx.asrText
import com.interfun.buz.im.ktx.translateText
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.im.ktx.isGroup
import com.interfun.buz.im.ktx.updateCloseAsrState
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay

class ChatHomeMessagePreviewWindow @JvmOverloads constructor(
    context: Context, attr: AttributeSet? = null, def: Int = 0
) : ConstraintLayout(context, attr, def) {

    companion object {
        const val TAG = "ChatHomeMessagePreviewWindow"
        const val SWITCH_MSG_ANIM_DURATION = 7 * 100L
        const val SHOW_ANIM_DURATION = 2 * 100L
    }

    val binding: ChatHomeMessagePreviewWindowBinding =
        ChatHomeMessagePreviewWindowBinding.inflate(LayoutInflater.from(context), this)

    private var currentMessagePreviewState = MessagePreviewState.Idle
    private var currentBuzVoiceMsg: IMessage? = null

    var closePreviewCallback: ((IMessage?) -> Unit)? = null

    private var currentTarget: WTItemBean? = null

    private var hiding = false

    private val transcribingTextArray = arrayOf(".","..","...")

    private var transcribingAnimJob : Job? = null

    init {
        initView()
    }

    fun isCurrentMsg(msg: IMessage):Boolean {
        return  msg.serMsgId == currentBuzVoiceMsg?.serMsgId
    }

    private fun initView() {
        switchQuietMode()
        binding.ivClose.click {
            hidePreview(currentBuzVoiceMsg, WTStatusManager.currentSelectedItem)
            closePreviewCallback?.invoke(currentBuzVoiceMsg)
            AsrTracker.onClickCloseMessagePreview(
                !currentTarget.isGroup,
                currentTarget?.targetId ?: -1,
                currentBuzVoiceMsg?.msgTraceId ?: ""
            )
        }
    }

    fun switchQuietMode() {
        binding.imPreviewBg.setImageDrawable(
            (if (WTQuietModeManager.isQuietModeEnable)
                R.drawable.chat_bg_message_preview_off_auto_play
            else
                R.drawable.chat_bg_message_preview_on_auto_play)
                .asDrawable()
        )
    }

    fun hidePreview(msg: IMessage? = currentBuzVoiceMsg, target: WTItemBean? = currentTarget) {
        currentTarget = target
        currentBuzVoiceMsg = msg
        currentMessagePreviewState = MessagePreviewState.Idle
        binding.tvTranslateContent.gone()
        binding.groupTranscribing.gone()
        if (isVisible() && !hiding) {
            hiding = true
            downToHide(binding.root) {
                hiding = false
            }
        }
    }

    fun switchToTranscribing(msg: IMessage, target: WTItemBean?) {
        switchState(target, msg, MessagePreviewState.Transcribing)
    }

    fun switchToTranscribeFail(msg: IMessage, target: WTItemBean?) {
        if (preTaskFilter(msg, MessagePreviewState.TranscribeFail)) {
            currentBuzVoiceMsg = msg
            return
        }
        switchState(target, msg, MessagePreviewState.TranscribeFail)
    }

    fun switchToTranscribeSuccess(msg: IMessage, target: WTItemBean?) {
        if (preTaskFilter(msg, MessagePreviewState.TranscribeSuccess)) {
            currentBuzVoiceMsg = msg
            return
        }
        switchState(target, msg, MessagePreviewState.TranscribeSuccess)
    }

    fun switchToTranslating(msg: IMessage, target: WTItemBean?) {
        if (preTaskFilter(msg, MessagePreviewState.Translating)) {
            currentBuzVoiceMsg = msg
            return
        }
        switchState(target, msg, MessagePreviewState.Translating)
    }

    fun switchToTranslateSuccess(msg: IMessage, target: WTItemBean?) {
        if (preTaskFilter(msg, MessagePreviewState.TranslateSuccess)) {
            currentBuzVoiceMsg = msg
            return
        }
        switchState(target, msg, MessagePreviewState.TranslateSuccess)
    }

    fun switchToTranslateFail(msg: IMessage, target: WTItemBean?) {
        if (preTaskFilter(msg, MessagePreviewState.TranslateFail)) {
            currentBuzVoiceMsg = msg
            return
        }
        switchState(target, msg, MessagePreviewState.TranslateFail)
    }

    private fun preTaskFilter(msg: IMessage,targetState:MessagePreviewState):Boolean {
        transcribingAnimJob?.cancel()
        if (currentBuzVoiceMsg?.serMsgId == msg.serMsgId) {
            // 如果当前状态已经是翻译成功了，但是需要变成转义成功的话，那将会默认过滤，因为状态不已应该往回跑
            if (targetState == MessagePreviewState.TranscribeSuccess) {
                return currentMessagePreviewState == targetState || currentMessagePreviewState == MessagePreviewState.TranslateSuccess
            }
            return currentMessagePreviewState == targetState
        }
        return false
    }

    private fun switchState(target: WTItemBean?,msg: IMessage,state:MessagePreviewState) {
        switchTarget(target) {
            when(state) {
                MessagePreviewState.Transcribing -> {
                    updateTranscribingContent(msg, target)
                }
                MessagePreviewState.TranscribeFail -> {
                    updateTranscribeFailContent(msg, target)
                }
                MessagePreviewState.TranscribeSuccess -> {
                    updateTranscribeSuccessContent(msg, target)
                }
                MessagePreviewState.Translating -> {
                    updateTranslatingContent(msg, target)
                }
                MessagePreviewState.TranslateFail -> {
                    updateTranslateFailContent(msg, target)
                }
                MessagePreviewState.TranslateSuccess -> {
                    updateTranslateSuccessContent(msg, target)
                }
                else -> {}
            }
            currentBuzVoiceMsg = msg
            currentTarget = target
            currentMessagePreviewState = state
        }
    }

    private fun switchTarget(currentSelectedItem: WTItemBean?, callback: (() -> Unit)? = null) {
        // 防止因为动画没有返回callback导致的hiding状态一直不对而做的一个处理。
        hiding = false
        if (currentTarget?.targetId != currentSelectedItem?.targetId) {
            if (isVisible()) {
                downToHide(binding.root) {
                    callback?.invoke()
                    topToShow(binding.root)
                }
            } else {
                callback?.invoke()
                topToShow(binding.root)
            }
        } else {
            if (isVisible()) {
                callback?.invoke()
            } else {
                callback?.invoke()
                topToShow(binding.root)
            }
        }
    }

    private fun updateTranscribingContent(msg: IMessage, target: WTItemBean?) {
        if (currentMessagePreviewState == MessagePreviewState.Transcribing) {
            return
        }
        switchMsgAnim(msg, target)
        binding.tvTranscribeContent.gone()
        binding.tvTranslateContent.gone()
        binding.groupTranscribing.visible()
        binding.itTranslationLogo.gone()
        binding.ltTranslatingLogo.gone()
        binding.ltTranslatingLogo.cancelAnimation()
        binding.tvTranscribing.text = buildSpannedString {
            iconFontAlign(color = R.color.text_white_disable.asColor()) {
                size(18.dp) {
                    typeface(FontUtil.fontIcon!!) {
                        append(R.string.ic_voice_to_text.asString())
                    }
                }
            }
            appendSpace(5.dp)
            append(R.string.transcribing.asString())
        }
        var index = 0
        val lastJob = transcribingAnimJob
        transcribingAnimJob = userLifecycleScope?.launchMain {
            lastJob?.cancelAndJoin()
            while (index < 120) {
                binding.tvTranscribingAnim.text = transcribingTextArray[index % 3]
                delay(300)
                index++
            }
        }
    }

    private fun updateTranscribeFailContent(msg: IMessage, target: WTItemBean?) {
        switchMsgAnim(msg, target)
        showAsrResultAnim(currentTarget?.targetId == target?.targetId)
        binding.itTranslationLogo.gone()
        binding.ltTranslatingLogo.gone()
        binding.ltTranslatingLogo.cancelAnimation()
        binding.tvTranscribeContent.text = buildSpannedString {
            iconFontAlign(color = R.color.text_white_disable.asColor()) {
                size(18.dp) {
                    typeface(FontUtil.fontIcon!!) {
                        append(R.string.ic_voice_to_text.asString())
                    }
                }
            }
            appendSpace(5.dp)
            append(R.string.unable_transcribe.asString())
            iconFontAlign(color = R.color.text_white_disable.asColor()) {
                size(16.dp) {
                    typeface(FontUtil.fontIcon!!) {
                        append(R.string.ic_arrow_right_rtl.asString())
                    }
                }
            }
        }
        binding.tvTranscribeContent.setTextColor(R.color.text_white_disable.asColor())
    }

    private fun updateTranscribeSuccessContent(msg: IMessage, target: WTItemBean?) {
        switchMsgAnim(msg, target)
        showAsrResultAnim(currentTarget?.targetId == target?.targetId)
        binding.itTranslationLogo.gone()
        binding.ltTranslatingLogo.gone()
        binding.tvTranscribeContent.text = buildContent(msg,"${msg.content?.asrText}")
        binding.tvTranscribeContent.setTextColor(R.color.text_white_default.asColor())
        AsrTracker.onHomePageTranscribeExposed(
            isPrivate = !target.isGroup,
            targetId = target?.targetId ?: -1,
            traceId = msg.msgTraceId ?: "")
    }

    private fun updateTranslatingContent(msg: IMessage, target: WTItemBean?) {
        switchMsgAnim(msg, target)
        showAsrResultAnim(currentTarget?.targetId == target?.targetId)
        binding.itTranslationLogo.gone()
        binding.ltTranslatingLogo.visible()
        binding.ltTranslatingLogo.playAnimation()
        binding.tvTranscribeContent.text = buildContent(msg,"${msg.content?.asrText}")
        binding.tvTranscribeContent.setTextColor(R.color.text_white_default.asColor())
        AsrTracker.onHomePageTranscribeExposed(
            isPrivate = !target.isGroup,
            targetId = target?.targetId ?: -1,
            traceId = msg.msgTraceId ?: "")
    }

    private fun updateTranslateFailContent(msg: IMessage, target: WTItemBean?) {
        switchMsgAnim(msg, target)
        binding.tvTranslateContent.gone()
        binding.tvTranscribeContent.visible()
        binding.ltTranslatingLogo.gone()
        binding.ltTranslatingLogo.cancelAnimation()
        binding.itTranslationLogo.visible()
        binding.itTranslationLogo.text = R.string.ic_warning.asString()
        binding.tvTranscribeContent.text = buildContent(msg, "${msg.content?.asrText}")
        binding.tvTranscribeContent.setTextColor(R.color.text_white_default.asColor())
    }

    private fun updateTranslateSuccessContent(msg: IMessage, target: WTItemBean?) {
        switchMsgAnim(msg, target)
        showTranslateResultAnim(currentTarget?.targetId == target?.targetId)
        binding.groupTranscribing.gone()
        binding.ltTranslatingLogo.gone()
        binding.ltTranslatingLogo.cancelAnimation()
        binding.itTranslationLogo.visible()
        binding.itTranslationLogo.text = R.string.ic_translate.asString()
        binding.tvTranslateContent.text = buildContent(msg,"${msg.content?.translateText ?: msg.content?.asrText}")
    }

    private fun buildContent(msg: IMessage, content: String): SpannedString {
        return buildSpannedString {
            R.drawable.chat_ic_transcribing_success.asDrawable()
                ?.let { appendCenterImage(it, 20.dp, 20.dp) }
            appendSpace(5.dp)
            if (msg.isGroup) {
                val userName = msg.userInfo.nickName.cropLength(16)
                color(R.color.text_white_important.asColor()) {
                    append("${userName}:")
                }
                appendSpace(2.dp)
            }
            append(content)
        }
    }

    private fun switchMsgAnim(msg: IMessage, target: WTItemBean?) {
        if (currentTarget != target) return
        if (msg.serMsgId == currentBuzVoiceMsg?.serMsgId) return
        if (!isVisible()) return
        binding.viewAnim.visible()
        binding.viewAnim.alphaAnim(SWITCH_MSG_ANIM_DURATION, 0f, 1.0f, 0f) {
            binding.viewAnim.gone()
        }.start()
    }

    fun reportClickEvent() {
        AsrTracker.onClickMessagePreview(
            !currentTarget.isGroup,
            currentTarget?.targetId ?: -1,
            currentBuzVoiceMsg?.msgTraceId ?: ""
        )
    }

    private fun showAsrResultAnim(needAnim: Boolean) {
        if (needAnim) {
            var currentView:View = binding.groupTranscribing
            if (currentMessagePreviewState == MessagePreviewState.Transcribing) {
                currentView = binding.groupTranscribing
            } else if (currentMessagePreviewState == MessagePreviewState.TranslateSuccess) {
                currentView = binding.tvTranslateContent
            }
            showAlphaAnim(currentView, binding.tvTranscribeContent).apply {
                doOnEnd {
                    if (currentMessagePreviewState == MessagePreviewState.Transcribing) {
                        binding.groupTranscribing.visible()
                    }
                    if (currentMessagePreviewState == MessagePreviewState.TranslateSuccess) {
                        binding.tvTranslateContent.visible()
                    }
                }
            }
        } else {
            binding.tvTranscribeContent.visible()
            binding.groupTranscribing.gone()
            binding.groupTranscribing.alpha = 1.0f
            binding.tvTranslateContent.gone()
            binding.tvTranslateContent.alpha = 1.0f
        }
    }

    private fun showTranslateResultAnim(needAnim: Boolean) {
        if (needAnim) {
            showAlphaAnim(binding.tvTranscribeContent, binding.tvTranslateContent).apply {
                doOnEnd {
                    if (currentMessagePreviewState == MessagePreviewState.TranscribeSuccess
                        || currentMessagePreviewState == MessagePreviewState.Translating
                    ) {
                        binding.tvTranscribeContent.visible()
                    }
                }
            }
        } else {
            binding.tvTranslateContent.visible()
            binding.groupTranscribing.gone()
            binding.groupTranscribing.alpha = 1.0f
            binding.tvTranscribeContent.gone()
            binding.tvTranscribeContent.alpha = 1.0f
        }
    }

    fun updateClosePreview() {
        userLifecycleScope?.launchIO {
            currentBuzVoiceMsg?.updateCloseAsrState(true)
        }
    }
}
