package com.interfun.buz.im.message

import com.interfun.buz.im.entity.IMType
import com.lizhi.im5.sdk.message.model.IM5MsgContent
import com.lizhi.im5.sdk.message.model.MessageTag
import org.json.JSONObject

@MessageTag(type = IMType.TYPE_ON_AIR_SOUNDBOARD, flag = MessageTag.NOUPDATECONV or MessageTag.ONLY_ONLINE)
class SoundBoardEmojiMsg : IM5MsgContent {

    var soundBoardId: Long = 0
    var displayValue: String = ""
    var displayType: Int = 0
    var superscript: String = ""
    var voiceUrl: String = ""
    var mExtra: String = ""

    override fun encode(): String {
        val json = JSONObject()
        json.put("soundBoardId", soundBoardId)
        json.put("displayValue", displayValue)
        json.put("displayType", displayType)
        json.put("superscript", superscript)
        json.put("voiceUrl", voiceUrl)
        json.put("extra", mExtra)
        return json.toString()
    }

    override fun decode(contentStr: String?): Boolean {
        if (contentStr == null) {
            return false
        }
        val json = JSONObject(contentStr)
        soundBoardId = json.optLong("soundBoardId", 0L)
        displayValue = json.optString("displayValue", "")
        displayType = json.optInt("displayType", 0)
        superscript = json.optString("superscript", "")
        voiceUrl = json.optString("voiceUrl", "")
        mExtra = json.optString("extra", "")
        return true
    }

    override fun getDigest(): String {
        return ""
    }

    override fun setExtra(extra: String?) {
        this.mExtra = extra ?: ""
    }

    override fun getExtra(): String {
        return this.mExtra
    }
}