package com.interfun.buz.common.database

import android.content.Context
import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.interfun.buz.common.constants.APP_DATABASE_NAME
import com.interfun.buz.common.database.dao.BuzMediaCacheDao
import com.interfun.buz.common.database.dao.SessionDao
import com.interfun.buz.common.database.entity.*

@Database(
    entities = [
        Session::class,
        BuzMediaCacheEntity::class
    ],
    version = 2,
    autoMigrations = [
        AutoMigration(from = 1, to = 2),
    ],
    exportSchema = true
)
abstract class BuzDatabase : RoomDatabase() {

    abstract fun getSessionDao(): SessionDao

    abstract fun getMediaCacheDao(): BuzMediaCacheDao

    companion object {
        @Volatile
        private var INSTANCE: BuzDatabase? = null

        fun getInstance(context: Context): BuzDatabase {
            if (INSTANCE == null) {
                synchronized(BuzDatabase::class) {
                    if (INSTANCE != null) return INSTANCE!!
                    INSTANCE = Room.databaseBuilder(
                        context.applicationContext,
                        BuzDatabase::class.java,
                        APP_DATABASE_NAME
                    )
                        .allowMainThreadQueries()
                        .build()
                }
            }
            return INSTANCE!!
        }
    }
}


