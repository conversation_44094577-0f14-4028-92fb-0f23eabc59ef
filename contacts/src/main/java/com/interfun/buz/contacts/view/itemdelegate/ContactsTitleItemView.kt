package com.interfun.buz.contacts.view.itemdelegate

import android.view.Gravity
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.layoutPaddingHorizontal
import com.interfun.buz.contacts.databinding.ContactsItemTitleBinding
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.interfaces.BaseContactsDelegate
import com.interfun.buz.contacts.interfaces.DefaultContactsItemCallback

/**
 * <AUTHOR>
 * @date 2023/5/9
 * @desc 通用标题类型
 * General Title Type
 */
class ContactsTitleItemView(val paddingHorizontal:Int = 20.dp) :
    BaseContactsDelegate<ContactsItemTitleBinding>(DefaultContactsItemCallback()) {

    override fun onBindViewHolder(
        binding: ContactsItemTitleBinding,
        item: ContactsItemBean,
        position: Int
    ) {
        binding.tvTitle.apply {
            layoutPaddingHorizontal(paddingHorizontal)
            text = item.content
            gravity = if (item.extra.isTitleInCenter) {
                Gravity.CENTER
            } else {
                Gravity.CENTER_VERTICAL
            }
        }
    }
}