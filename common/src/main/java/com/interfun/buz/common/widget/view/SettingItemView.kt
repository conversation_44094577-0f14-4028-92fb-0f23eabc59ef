package com.interfun.buz.common.widget.view

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.view.Gravity
import android.widget.TextView
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.getString
import com.interfun.buz.base.widget.round.RoundLinearLayout
import com.interfun.buz.common.R
import com.interfun.buz.common.ktx.getText
import com.interfun.buz.common.ktx.setStyleBodyMedium
import com.interfun.buz.common.ktx.setStyleLabelLarge

class SettingItemView @JvmOverloads constructor(
    context: Context, val attrs: AttributeSet? = null
) : RoundLinearLayout(context, attrs) {


    private lateinit var tvTitle: TextView
    private lateinit var tvSetting: TextView
    private lateinit var tvIcon: IconFontTextView

    init {
        orientation = HORIZONTAL
        gravity = Gravity.CENTER
        initChild()
        initAttr()
    }

    private fun initChild() {
        tvTitle = TextView(context)
        tvSetting = TextView(context)
        tvSetting.gravity = Gravity.END or Gravity.CENTER_VERTICAL
        tvIcon = IconFontTextView(context)
        addView(tvTitle, LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        addView(
            tvSetting,
            LayoutParams(0, LayoutParams.WRAP_CONTENT).apply {
                weight = 1f
            })
        val iconLp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        iconLp.leftMargin = 6.dp
        addView(tvIcon, iconLp)
    }

    private fun initAttr() {
        val typedArray: TypedArray =
            context.obtainStyledAttributes(attrs, R.styleable.SettingItemView)
        val titleText = typedArray.getText(context,R.styleable.SettingItemView_title)
        val settingText = typedArray.getText(context,R.styleable.SettingItemView_settingText)
        val icon = typedArray.getText(context,R.styleable.SettingItemView_iconfont)
            ?: getString(R.string.ic_arrow_right)
        val colorTextWhiteSecondary = R.color.color_text_white_secondary.asColor()
        tvTitle.setStyleLabelLarge()
        tvTitle.setTextColor(R.color.color_text_white_primary.asColor())
        titleText?.let { setTitle(titleText) }
        tvSetting.setStyleBodyMedium()
        tvSetting.setTextColor(colorTextWhiteSecondary)
        settingText?.let { setSettingText(settingText) }
        tvSetting.text = settingText
        tvIcon.textSize = 16f
        tvIcon.setTextColor(colorTextWhiteSecondary)
        setIcon(icon)
        typedArray.recycle()
        setBackgroundResource(R.drawable.common_selector_press_overlay_white_4)
    }

    fun setTitle(title: CharSequence) {
        tvTitle.text = title
    }

    fun setSettingText(settingText: CharSequence) {
        tvSetting.text = settingText
    }

    fun setIcon(iconFont: CharSequence) {
        tvIcon.text = iconFont
    }
}