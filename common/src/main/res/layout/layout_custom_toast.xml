<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/overlay_grey_20"
    android:paddingHorizontal="@dimen/toast_padding_horizontal"
    android:minWidth="@dimen/icon_font_toast_style_2_min_width"
    app:round_radius="@dimen/toast_style_1_radius"
    tools:layout_marginHorizontal="60dp"
    tools:layout_gravity="center">

    <com.interfun.buz.common.widget.view.IconFontTextView
        style="@style/iconfont_24"
        android:id="@+id/iftvIcon"
        android:layout_width="@dimen/toast_icon_font_size"
        android:layout_height="@dimen/toast_icon_font_size"
        android:visibility="gone"
        android:textColor="@color/text_white_important"
        app:layout_constraintBottom_toBottomOf="@+id/tvText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvText"
        tools:text="@string/ic_correct_solid"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvText"
        style="@style/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/toast_icon_font_and_text_margin"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/toast_padding_vertical"
        android:textColor="@color/text_white_default"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHeight_min="44dp"
        app:layout_constraintStart_toEndOf="@+id/iftvIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_goneMarginStart="0dp"
        tools:text="Network is unavailable. Please check your network and try again." />

</com.interfun.buz.base.widget.round.RoundConstraintLayout>