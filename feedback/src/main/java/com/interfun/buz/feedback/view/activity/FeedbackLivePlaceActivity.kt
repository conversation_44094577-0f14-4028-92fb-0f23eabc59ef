package com.interfun.buz.feedback.view.activity

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.widget.FrameLayout
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.PATH_FEEDBACK_LIVEPLACE
import com.interfun.buz.common.constants.PATH_FEEDBACK_SUGGESTION
import com.interfun.buz.feedback.R
import com.interfun.buz.feedback.view.dialog.LivePlaceFeedbackDialog
import com.interfun.buz.feedback.view.fragment.FeedbackType
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Route(path = PATH_FEEDBACK_LIVEPLACE)
class FeedbackLivePlaceActivity : SimpleContainerActivity() {

    companion object {
        private val TAG = "FeedbackLivePlaceActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        CommonMMKV.hadShownLPFeedback = true
        lifecycleScope.launch {
            val fragmentTag = "LivePlaceFeedbackDialog"
            val oldFragment = supportFragmentManager.findFragmentByTag(fragmentTag)

            supportFragmentManager.beginTransaction().apply {
                if (oldFragment == null) {
                    add(LivePlaceFeedbackDialog.newInstance(FeedbackType.LIVEPLACE), fragmentTag)
                    commitAllowingStateLoss()
                }
            }
        }
    }
}