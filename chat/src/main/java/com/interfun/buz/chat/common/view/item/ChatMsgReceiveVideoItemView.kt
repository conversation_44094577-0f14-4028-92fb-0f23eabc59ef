package com.interfun.buz.chat.common.view.item

import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.asDimension
import com.interfun.buz.base.ktx.asDimensionInt
import com.interfun.buz.base.ktx.isVisible
import com.interfun.buz.base.utils.ViewBindingUtil
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.BaseChatMsgItemBean
import com.interfun.buz.chat.common.entity.ChatMsgReceiveImageItemBean
import com.interfun.buz.chat.common.entity.ChatMsgReceiveVideoItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.common.utils.ReplyItemViewHelper
import com.interfun.buz.chat.common.view.widget.ReplyItemView
import com.interfun.buz.chat.common.view.widget.ReplyPreviewData
import com.interfun.buz.chat.databinding.ChatItemReceiveImageBinding
import com.interfun.buz.chat.databinding.ChatItemReceiveVideoBinding
import com.interfun.buz.im.ktx.isPrivate
import com.interfun.buz.im.ktx.isSend
import kotlinx.coroutines.CoroutineScope

/**
 *
 */
class ChatMsgReceiveVideoItemView(
    itemCallback: ChatItemCallback
) : ChatMsgReceiveMediaItemView<ChatMsgReceiveVideoItemBean, ChatItemReceiveVideoBinding>(
    itemCallback
) ,IChatSupportReplyView<ChatMsgReceiveVideoItemBean,ChatItemReceiveVideoBinding>{
    private val replyItemViewHelper = ReplyItemViewHelper<ChatMsgReceiveVideoItemBean, ChatItemReceiveVideoBinding>()

    override fun createViewHolder(parent: ViewGroup): ChatMsgReceiveMediaViewHolder<ChatItemReceiveVideoBinding> {
        return ChatMsgReceiveVideoViewHolder(ViewBindingUtil.inflateWithGeneric(this, parent))
    }

    override fun getPortraitImageView(binding: ChatItemReceiveVideoBinding) = binding.ivPortrait

    override fun getAnchorView(binding: ChatItemReceiveVideoBinding) = binding.clContent

    override fun getGroupMemberNameTextView(binding: ChatItemReceiveVideoBinding) = binding.tvGroupMemberName

    override fun onViewHolderCreated(holder: BindingViewHolder<ChatItemReceiveVideoBinding>) {
        super.onViewHolderCreated(holder)
        replyItemViewHelper.handleClickNew(this,holder,getReplyItemView(holder.binding),itemCallback)
    }

    override fun onBindViewHolder(
        scope: CoroutineScope?,
        binding: ChatItemReceiveVideoBinding,
        item: ChatMsgReceiveVideoItemBean,
        position: Int
    ) {
        super.onBindViewHolder(scope, binding, item, position)
        replyItemViewHelper.handleReply(binding,getReplyItemView(binding),item,this)
    }

    override fun getReplyItemView(binding: ChatItemReceiveVideoBinding): ReplyItemView? {
        return binding.replyView
    }

    override fun extraHandle(
        itemView: ReplyItemView,
        binding: ChatItemReceiveVideoBinding,
        item: BaseChatMsgItemBean,
        data: ReplyPreviewData
    ) {
        if (item.msg.isSend || item.msg.isPrivate) {
            return
        }
        replyItemViewHelper.quickHandleReceiveAvatarMarginTop(this,binding,binding.clContent,data)
    }

    override fun updateReply(
        binding: ChatItemReceiveVideoBinding,
        replyItemView: ReplyItemView?,
        item: ChatMsgReceiveVideoItemBean,
        supportView: IChatSupportReplyView<ChatMsgReceiveVideoItemBean, ChatItemReceiveVideoBinding>
    ) {
        replyItemViewHelper.handleReply(binding, replyItemView, item, supportView)
    }

}