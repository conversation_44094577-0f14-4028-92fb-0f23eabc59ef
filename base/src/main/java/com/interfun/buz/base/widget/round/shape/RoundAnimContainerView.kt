package com.interfun.buz.base.widget.round.shape

import android.content.Context
import android.graphics.Canvas
import android.graphics.Shader
import android.util.AttributeSet
import com.interfun.buz.base.widget.round.IRoundLayout
import com.interfun.buz.base.widget.round.RoundLayout
import com.interfun.buz.base.widget.view.animContainer.AnimContainerView

/**
 * <AUTHOR>
 *
 * @date 2022/7/6
 *
 * @desc
 */
class RoundAnimContainerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : Anim<PERSON>ontainerView(context, attrs, defStyleAttr), IRoundLayout {

    private val roundHelper = RoundLayout(this, attrs)

    override fun draw(canvas: Canvas) {
        roundHelper.drawBefore(canvas)
        super.draw(canvas)
        roundHelper.drawAfter(canvas)
    }

    override fun setBorder(color: Int, gradient: Shader?, width: Float) {
        roundHelper.strokeWidth = width
        roundHelper.strokeColor = color
        gradient?.let{ roundHelper.strokeGradient = gradient }
        invalidate()
    }

    override fun setRadius(tlR: Float?, trR: Float?, brR: Float?, blR: Float?) {
        roundHelper.setRadius(tlR, trR, brR, blR)
        invalidate()
    }
}