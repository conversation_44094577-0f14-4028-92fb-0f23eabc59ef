package com.interfun.buz.common.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "contacts")
class ContactsBean {
    @PrimaryKey(autoGenerate = true)
    var id: Int = 0

    @ColumnInfo(name = "phone")
    var phone: String = "" //非格式化的手机号，可能存在中间有空格 如:183 1234 5678

    @ColumnInfo(name = "firstName")
    var firstName: String? = "" //通讯录中备注的firstName，非好友用户注册的firstName

    @ColumnInfo(name = "lastName")
    var lastName: String? = "" //通讯录中备注的lastName，非好友用户注册的lastName

    @ColumnInfo(name = "firstLetter")
    @Deprecated("按需获取")
    var firstLetter: String = "" //首字母

    @ColumnInfo(name = "displayName")
    var displayName: String? = "" //通讯录默认显示名称

    @ColumnInfo(name = "contactId")
    var contactId: Long? = 0L

    @ColumnInfo(name = "filteredPhone")
    var filteredPhone: String? = ""

    //该手机号是否能格式化成功，不能被格式化代表它可能不是一个正确的手机号，或者不属于当前默认的区号
    @ColumnInfo(name = "formatResult")
    @Deprecated("no more use")
    var formatResult: Int? = 0

    override fun toString(): String {
        return "ContactsBean[ phone: $phone, displayName: \"$displayName\", " +
            "firstName: \"$firstName\", lastName: \"$lastName\" contactId: $contactId"
    }
}