package com.interfun.buz.common.web.functions

import android.net.Uri
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.web.bean.CAMPAIGN_FOLDER_PATH
import com.interfun.buz.common.web.manager.UploadManager
import com.lizhi.itnet.upload.model.UploadStatus
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail
import org.json.JSONObject
import java.io.File

class UploadFileFunction: JSFunction() {
    private val TAG = "UploadMediaFunction"

    companion object{
        const val METHOD_NAME = "uploadFile"
    }

    override fun invoke(
        activity: BaseActivity,
        webView: LJavaScriptWebView,
        data: JSFunctionData
    ): JsCallbackDetail? {
        logInfo(TAG,"invoke:activity = $activity, webView = $webView, data = $data")
        val fileId = optStringParam(data, "fileId")
        if (fileId.isNullOrEmpty()) return getFailedCallback(data)

        val mediaFile = File(CAMPAIGN_FOLDER_PATH + fileId)
        if (!mediaFile.exists()) return getFailedCallback(data)

        val fileUrl = Uri.fromFile(mediaFile).toString()
        val fileUri = Uri.parse(fileUrl)
        val taskId = UploadManager.uploadLocalFileAndObserve(fileUri){ state, url->
            val params = JSONObject()
            params.put("state",getUploadStateCode(state) )
            params.put("msg",state.msg?:"")
            params.put("url",url)
            webView.triggerJsEvent("uploadStateListener", params.toString())
        }

        logInfo(TAG,"invoke:taskId = $taskId")
        val result = getSuccessCallback(data)
        result.put("taskId",taskId?:"")

        return result
    }

    private fun getUploadStateCode(status:UploadStatus):Int{
        return when(status){
            UploadStatus.INVALID -> 0
            UploadStatus.SUSPENDED -> 1
            UploadStatus.WAITING -> 2
            UploadStatus.UPLOADING -> 3
            UploadStatus.CANCELED -> 4
            UploadStatus.FAIL -> 5
            UploadStatus.SUCCESS -> 6
        }
    }
}