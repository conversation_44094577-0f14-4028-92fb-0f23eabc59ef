package com.interfun.buz.onair.ui.action

import androidx.compose.runtime.staticCompositionLocalOf
import com.interfun.buz.common.bean.voicecall.DeviceInfoItemOption

/**
 * <AUTHOR>
 * @date 2024/8/23
 * @desc
 */
val LocalOnAirInnerAction =
    staticCompositionLocalOf<(OnAirInnerAction) -> Unit> { error("no provided") }
val LocalOnAirPageAction =
    staticCompositionLocalOf<(OnAirPageAction) -> Unit> { error("no provided") }

sealed interface OnAirInnerAction {
    data object HandleSpeaker : OnAirInnerAction
    data object SwitchOverlay : OnAirInnerAction
//    data object OpenShare : OnAirInnerAction
    data object OpenPlaceSetting : OnAirInnerAction
    data object OpenBGM : OnAirInnerAction
    data class ShowOrHideSoundBoardPage(val isShow: Boolean) : OnAirInnerAction
    data class ShowOrHideGiftBottomSheet(val isShow: Boolean) : OnAirInnerAction
    data class ShowOrHideShareBottomSheet(val isShow: Boolean) : OnAirInnerAction
    data class OnSpeakerSelect(val selectItem: DeviceInfoItemOption) : OnAirInnerAction
    data class MicrophoneAdjustment(val status: Boolean) : OnAirInnerAction
}

sealed interface OnAirPageAction {
    data object End : OnAirPageAction
    data object Leave : OnAirPageAction
    data object BackStack : OnAirPageAction
    data object Dismiss : OnAirPageAction
    data object OpenProfilePage : OnAirPageAction
    data object OpenChatPage : OnAirPageAction
    data object InviteToJoin : OnAirPageAction
    data object OpenPlaceSettingPage : OnAirPageAction
//    data object OpenSharedPage : OnAirPageAction
}