package com.interfun.buz.chat.privy.view.item

import android.view.View
import com.interfun.buz.chat.common.entity.BaseChatMsgItemBean
import com.interfun.buz.chat.common.entity.ChatMsgReceivePrivateVoiceItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.common.view.item.BaseChatVoiceMsgItemView
import com.interfun.buz.chat.common.view.item.IChatSupportReplyView
import com.interfun.buz.chat.common.view.widget.ChatTranslationItemView
import com.interfun.buz.chat.common.view.widget.ReplyItemView
import com.interfun.buz.chat.common.view.widget.ReplyPreviewData
import com.interfun.buz.chat.databinding.ChatItemVoiceMsgReceivePrivateBinding
import com.interfun.buz.chat.databinding.ChatItemVoiceMsgSendBinding

/**
 * <AUTHOR>
 * @date 2022/11/26
 * @desc
 */
class PrivateChatVoiceMsgReceiveVoiceItemView(itemCallback: ChatItemCallback) :
    BaseChatVoiceMsgItemView<ChatMsgReceivePrivateVoiceItemBean, ChatItemVoiceMsgReceivePrivateBinding>(
        itemCallback
    ) {

    override fun getAnchorView(binding: ChatItemVoiceMsgReceivePrivateBinding) = binding.clBubble


    override fun getTranslationView(binding: ChatItemVoiceMsgReceivePrivateBinding): ChatTranslationItemView? {
        return binding.translateContainer
    }

    override fun getReplyItemView(binding: ChatItemVoiceMsgReceivePrivateBinding): ReplyItemView? {
        return binding.replyView
    }

    override fun extraHandle(
        itemView: ReplyItemView,
        binding: ChatItemVoiceMsgReceivePrivateBinding,
        item: BaseChatMsgItemBean,
        data: ReplyPreviewData
    ) {

    }


}