package com.interfun.buz.common.view.other.titleBar

import android.view.View

/**
 * TitleBar点击监听
 * Created by ch<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * Date: 2021/2/7
 */
interface IOnTitleBarListener {
    /**
     * 左项被点击
     *
     * @param v     被点击的左项View
     */
    fun onLeftClick(v: View?)

    /**
     * 标题被点击
     *
     * @param v     被点击的标题View
     */
    fun onTitleClick(v: View?)

    /**
     * 右项被点击
     *
     * @param v     被点击的右项View
     */
    fun onRightClick(v: View?)

    /***
     * 右边第二个icon点击
     */
    fun onSecRightClick(v : View?)
}