package com.interfun.buz.chat.wt.preview

import com.interfun.buz.chat.voicemoji.ktx.convertToVoiceEmojiCategoryType
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.IMsgPreview
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.UnicodeVoiceEmojiPreview
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.lizhi.im5.sdk.message.IMessage

class UnicodeEmojiPreviewCreator : MessagePreviewCreator<WTVoiceEmojiMsg>() {
    override val type: Int = IMType.TYPE_VOICE_EMOJI

    override fun createMsgPreview(
        msg: IMessage,
        content: WTVoiceEmojiMsg,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): IMsgPreview {
        return UnicodeVoiceEmojiPreview(
            content.emojiIcon,
            content.emojiSuperscript,
            content.emojiCategoryType.convertToVoiceEmojiCategoryType(),
            baseMsgInfo = baseMsgInfo,
            duration = content.duration,
            isPlaying = false,
            voiceFilterName = null,
            voiceFilterColor = null
        )
    }
}