package com.interfun.buz.common.utils

import android.content.Context
import android.os.Build
import androidx.annotation.Keep
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.google.gson.Gson
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.utils.ScreenUtil
import com.lizhi.component.tekiapm.utils.CpuUtils
import com.lizhi.component.tekiapm.utils.DeviceUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

@Keep
data class DeviceScore(
    val cpuFreq: Long,
    val memSize: Long,
    val diskSize: Long,
    val widthPixel: Int,
    val heightPixel: Int,
    val score: Int,
    val version: Int = 1 //设备评分版本号，因为评分规则可能会变化，所以需要版本号做递增标识
)

object DeviceScoreUtil {
    // 权重配置
    private const val CPU_WEIGHT = 0.35f        // CPU权重35%
    private const val RAM_WEIGHT = 0.30f        // 运行内存权重30%
    private const val STORAGE_WEIGHT = 0.15f    // 存储空间权重15%
    private const val SCREEN_WEIGHT = 0.20f     // 屏幕分辨率权重20%

    // 存储空间值，单位Byte，实际获取到的值要低于1024的倍数，所以这里粗略取整数值
    private const val DISK_64GB = 50000000000
    private const val DISK_128GB = 100000000000
    private const val DISK_256GB = 200000000000
    private const val DISK_512GB = 450000000000
    private const val DISK_1TB = 900000000000

    // 运行内存值，单位Byte, 实际获取到的值要低于1024的倍数，所以这里粗略取整数值
    private const val RAM_12GB = 11000000000
    private const val RAM_8GB = 7000000000
    private const val RAM_6GB = 5000000000
    private const val RAM_4GB = 3000000000

    private val Context.deviceScoreDataStore: DataStore<Preferences> by preferencesDataStore(name = "buz_device_score")
    private val DEVICE_SCORE_KEY = stringPreferencesKey("device_score")

    private suspend fun saveDeviceScore(context: Context, score: DeviceScore) {
        context.deviceScoreDataStore.edit { preferences ->
            preferences[DEVICE_SCORE_KEY] = Gson().toJson(score)
        }
    }

    private suspend fun getDeviceScoreFromDataStore(context: Context): DeviceScore? {
        return context.deviceScoreDataStore.data.map { preferences ->
            preferences[DEVICE_SCORE_KEY]?.fromJson<DeviceScore>()
        }.first()
    }

    /**
     * 设备等级枚举
     */
    enum class DeviceLevel(val description: String) {
        HIGH("高端机"),
        MEDIUM("中端机"),
        LOW("低端机")
    }

    /**
     * 计算设备总评分
     */
    suspend fun calculateDeviceScore(context: Context): DeviceScore = withContext(Dispatchers.IO) {
        try {
            getDeviceScoreFromDataStore(context)?.let {
                logDebug("DeviceScore", "从本地缓存获取设备评分信息: $it")
                return@withContext it
            }
            val bigCpuMaxFreq = CpuUtils.getBigCpuMaxFreq()
            val ramGB = DeviceUtil.getAppMemoryInfo(context).totalMem
            val diskSize = DeviceUtil.getDiskTotalSpace(context)
            val widthPixel = ScreenUtil.getScreenWidth(context)
            val heightPixel = ScreenUtil.getScreenHeight(context)

            val cpuScore = calCpuScore(bigCpuMaxFreq)
            val ramScore = calculateRamScore(ramGB)
            val storageScore = calculateStorageScore(diskSize)
            val screenScore = calculateScreenScore(widthPixel, heightPixel)
            val score = cpuScore * CPU_WEIGHT +
                    ramScore * RAM_WEIGHT +
                    storageScore * STORAGE_WEIGHT +
                    screenScore * SCREEN_WEIGHT
            logDebug("DeviceScore",
                "设备评分: $score \n" +
                        "设备等级: ${getDeviceLevel(score)}\n" +
                        "设备信息:\n" +
                        "- CPU最大频率: $bigCpuMaxFreq KHz,[评分：$cpuScore]\n" +
                        "- 运行内存: $ramGB Byte, [评分：$ramScore] \n" +
                        "- 存储空间: $diskSize Byte, [评分：$storageScore]\n" +
                        "- 屏幕分辨率: $widthPixel x $heightPixel, [评分：$screenScore]"
            )
            val deviceScore =
                DeviceScore(bigCpuMaxFreq, ramGB, diskSize, widthPixel, heightPixel, score.toInt())
            saveDeviceScore(context, deviceScore)
            return@withContext deviceScore
        } catch (e: Exception) {
            logError("DeviceScore", "计算设备评分失败: ${e.message}")
            return@withContext DeviceScore(0, 0, 0, 0, 0, -1)
        }
    }

    /**
     * @param cpuMaxFreq CPU最大频率 (单位KHz)
     * 计算CPU得分 = ((最大频率 - 1000 MHz) / (3300MHz - 1000MHz) * 架构得分 * 100
     */
    private fun calCpuScore(cpuMaxFreq: Long): Float {
        val abiFactor = if (Build.SUPPORTED_64_BIT_ABIS.isEmpty()) 0.5f else 1f
        return (cpuMaxFreq / 1000 - 1000) / 2300f * abiFactor * 100
    }

    /**
     * 计算运行内存得分
     */
    private fun calculateRamScore(ramGB: Long): Float = when {
        ramGB >= RAM_12GB -> 100f
        ramGB >= RAM_8GB -> 80f
        ramGB >= RAM_6GB -> 60f
        ramGB >= RAM_4GB -> 40f
        else -> 20f
    }

    /**
     * 计算存储空间得分
     */
    private fun calculateStorageScore(storageGB: Long): Float = when {
        storageGB >= DISK_512GB -> 100f
        storageGB >= DISK_256GB -> 80f
        storageGB >= DISK_128GB -> 60f
        storageGB >= DISK_64GB -> 40f
        else -> 20f
    }

    /**
     * 计算屏幕分辨率得分
     */
    private fun calculateScreenScore(width: Int, height: Int): Float {
        val pixels = width.toLong() * height
        return when {
            pixels >= 3686400 -> 100f  // 2K及以上 (如2960x1440)
            pixels >= 2073600 -> 80f   // 1080P (1920x1080)
            pixels >= 1440000 -> 60f   // 720P (1280x720)
            else -> 40f
        }
    }

    /**
     * 获取设备等级
     */
    fun getDeviceLevel(score: Float): DeviceLevel = when {
        score > 80 -> DeviceLevel.HIGH
        score >= 50 -> DeviceLevel.MEDIUM
        else -> DeviceLevel.LOW
    }
}