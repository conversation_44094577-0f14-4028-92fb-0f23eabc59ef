package com.interfun.buz.chat.map.send.util

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.tracker.trackerString
import com.interfun.buz.common.utils.BuzTracker
import com.lizhi.im5.sdk.conversation.IM5ConversationType

object MapTracker {
    fun appViewScreenAVS2024062501(
        source: String,
        conversation: IM5ConversationType,
        target: String
    ) {
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024062501")
            put(TrackConstant.KEY_TITLE, "sender_map_home_page")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
            put(TrackConstant.KEY_SOURCE, source)
        }
    }

    fun appClickAC2024062508(
        conversation: IM5ConversationType,
        target: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062508")
            put(TrackConstant.KEY_TITLE, "map")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "check_nearby_place")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
        }
    }
    fun appClickAC2024062507(
        conversation: IM5ConversationType,
        target: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062507")
            put(TrackConstant.KEY_TITLE, "map")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "choose_nearby_location")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
        }
    }
    fun appClickAC2024062506(
        conversation: IM5ConversationType,
        target: String,
        type:String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062506")
            put(TrackConstant.KEY_TITLE, "map")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "recenter_location")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
            put(TrackConstant.KEY_SOURCE, type)
        }
    }
    fun appClickAC2024062505(
        conversation: IM5ConversationType,
        target: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062505")
            put(TrackConstant.KEY_TITLE, "map")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "search_location")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
        }
    }
    fun appClickAC2024062509(
        conversation: IM5ConversationType,
        target: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062509")
            put(TrackConstant.KEY_TITLE, "map")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "open_maps_in_other")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
        }
    }

    fun appClickAC2024062510(
        conversation: IM5ConversationType,
        target: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062510")
            put(TrackConstant.KEY_TITLE, "map")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "send_location")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
        }
    }

    fun appViewScreenAVS2024062502(
        conversation: IM5ConversationType,
        target: String
    ) {
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024062502")
            put(TrackConstant.KEY_TITLE, "receive_map_home_page")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversation.trackerString)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, target)
        }
    }

}