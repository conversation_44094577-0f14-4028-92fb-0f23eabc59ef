package com.interfun.buz.demo.audio

import android.content.Context
import android.media.AudioManager
import android.media.AudioRecordingConfiguration
import android.os.Build
import android.os.Bundle
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.GridLayoutManager
import com.interfun.buz.base.ktx.MultiTypeAdapter
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.demo.databinding.ActivityAudioTestBinding
import com.interfun.buz.demo.entry.DemoEntryFragment

class AudioFocusTestActivity : BaseBindingActivity<ActivityAudioTestBinding>() {

    companion object {
        const val TAG = "AudioFocusTest"
    }

    private val audioFocusManager by lazy { PlayerAudioFocusTestManager(this) }
    private val audioManager by lazy { getSystemService(Context.AUDIO_SERVICE) as AudioManager }
    private val audioRecordingCallback = @RequiresApi(Build.VERSION_CODES.N)
    object : AudioManager.AudioRecordingCallback() {
        override fun onRecordingConfigChanged(configs: MutableList<AudioRecordingConfiguration>?) {
            super.onRecordingConfigChanged(configs)
            log(TAG,"onRecordingConfigChanged configSize:${configs?.size}")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val btnListAdapter = MultiTypeAdapter { register(DemoEntryFragment.ButtonDelegate()) }
        binding.rvContent.apply {
            layoutManager = GridLayoutManager(this@AudioFocusTestActivity, 2)
            adapter = btnListAdapter
        }
        btnListAdapter.items = listOf(
            DemoEntryFragment.ButtonInfo("获取音频焦点") {
                audioFocusManager.request()
            },
            DemoEntryFragment.ButtonInfo("释放音频焦点") {
                audioFocusManager.abandon()
            },
            DemoEntryFragment.ButtonInfo("获取系统当前音频模式") {
                val mode = audioManager.mode
                val modeStr = when (mode) {
                    AudioManager.MODE_NORMAL -> "normal"
                    AudioManager.MODE_RINGTONE -> "ringtone"
                    AudioManager.MODE_IN_CALL -> "in call"
                    AudioManager.MODE_IN_COMMUNICATION -> "in communication"
                    AudioManager.MODE_CALL_SCREENING -> "call_screening"
                    AudioManager.MODE_CALL_REDIRECT -> "redirect"
                    AudioManager.MODE_COMMUNICATION_REDIRECT -> "communication_redirect"
                    else -> "other"
                }
                log(TAG, "sysAudioMode:${modeStr}")
            },
            DemoEntryFragment.ButtonInfo("注册录音监听") {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    audioManager.unregisterAudioRecordingCallback(audioRecordingCallback)
                    audioManager.registerAudioRecordingCallback(audioRecordingCallback, null)
                }
            },
            DemoEntryFragment.ButtonInfo("获取系统活跃录音配置") {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    val list = audioManager.activeRecordingConfigurations
                    log(TAG, "activeRecordingConfigurations size:${list.size}")
                    list.forEachIndexed { index, config ->
                        val clientSource = config.getClientAudioSource()
                        val audioSource = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            config.audioSource
                        } else {
                            null
                        }
                        log(TAG,"active config$index,source:${audioSource},clientSource:${clientSource}")
                    }
                }
            },
        )
    }
}