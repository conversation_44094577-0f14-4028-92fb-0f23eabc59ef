package com.interfun.buz.notification.usecase

import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.appStringContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.feature.notification.R
import com.interfun.buz.im.IMAgent
import com.interfun.buz.notification.constants.LocalNotificationId
import com.interfun.buz.notification.model.NotificationModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map

class UnreadMsgNotificationUseCase(private val imRepository: IMAgent) {
    operator fun invoke(): Flow<NotificationModel> =
        imRepository.msgEndLoadingFlow.map { unreadCount ->
            logInfo("UnreadMsgNotificationUseCase  $unreadCount")
            val channel = if (VERSION.SDK_INT >= VERSION_CODES.O) {
                NotificationChannelUtils.getSystemNotificationChannel()
            } else {
                null
            }
            val content = if (unreadCount == 1) appStringContext.getString(R.string.have_one_unread_message)
                            else appStringContext.getString(R.string.have_x_unread_messages, unreadCount.toString())
            val title = R.string.app_name.asString()
            val model = NotificationModel(
                logModel = null,
                notificationId = LocalNotificationId.MessageUnreadCountNotificationId.id,
                pendingRouter = "",
                number = unreadCount,
                content = content,
                title = title,
                channel = channel
            )

            return@map model
        }.flowOn(Dispatchers.IO).filterNotNull()
}