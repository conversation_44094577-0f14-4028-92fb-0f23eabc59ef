package com.interfun.buz.floating.manager

import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.text.buildSpannedString
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.interfun.buz.base.ktx.appendClickable
import com.interfun.buz.base.ktx.appendSpace
import com.interfun.buz.base.ktx.application
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.autoEnterLine
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.iconFontAlign
import com.interfun.buz.base.ktx.size
import com.interfun.buz.base.ktx.sp
import com.interfun.buz.base.ktx.typeface
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.utils.FloatPermissionUtils
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.service.FloatModuleService
import com.interfun.buz.floating.fragment.OverlayGuidanceDialogFragment
import com.interfun.buz.floating.guide.R
import com.interfun.buz.floating.guide.databinding.FloatPopupGuidanceBinding
import com.interfun.buz.floating.log.FloatTracker
import com.interfun.buz.floating.storage.FloatGuideMMKV
import com.interfun.buz.floating.viewmodel.FloatGuidanceViewModel

object WTFloatGuidanceManager {

    fun showGuidancePopupFlowIfNecessary(
        activity: FragmentActivity,
        container: ViewGroup,
        callback: FloatModuleService.GuideFlowCallback
    ) {
        if (FloatPermissionUtils.checkPermission(application)) {
            callback.onFlowEnd(FloatModuleService.GuideFlowCallback.FLOW_RESULT_GRANTED)
            return
        }
        if (FloatGuideMMKV.hasFloatNewUserGuidanceBeenShown) {
            callback.onFlowEnd(FloatModuleService.GuideFlowCallback.FLOW_RESULT_ALREADY_SHOWN)
            return
        }
        container.visible()
        val binding =
            FloatPopupGuidanceBinding.inflate(LayoutInflater.from(activity), container, true)

        binding.tvPrompt.text = buildSpannedString {
            append(R.string.wt_overlay_tips.asString())
            appendSpace(6.dp)
            autoEnterLine(14f.sp, R.color.basic_primary.asColor()) {
                appendClickable(
                    R.string.wt_try_it.asString(),
                    R.color.basic_primary.asColor(),
                    false
                ) {
                    FloatTracker.onOverlayTryItButtonClick()
                    if (FloatPermissionUtils.checkPermission(application)) {
                        callback.onFlowEnd(FloatModuleService.GuideFlowCallback.FLOW_RESULT_GRANTED)
                        return@appendClickable
                    }
                    val viewModel = ViewModelProvider(activity)[FloatGuidanceViewModel::class.java]
                    viewModel.flowResult.removeObservers(activity)
                    viewModel.flowResult.value = null
                    viewModel.flowResult.observe(activity, object : Observer<Int?> {
                        override fun onChanged(t: Int?) {
                            if (t != null) {
                                callback.onFlowEnd(t)
                                viewModel.flowResult.removeObserver(this)
                            }
                        }
                    })
                    OverlayGuidanceDialogFragment().showDialog(activity)
                    FloatTracker.onOverlayGuidanceDialogExpose()
                    callback.onFlowEnd(FloatModuleService.GuideFlowCallback.FLOW_RESULT_OPEN_SETTING)
                }

                iconFontAlign(color = R.color.basic_primary.asColor()) {
                    size(16.dp) {
                        typeface(FontUtil.fontIcon!!) {
                            append(R.string.ic_arrow_right_rtl.asString())
                        }
                    }
                }
            }
        }

        binding.tvPrompt.movementMethod = LinkMovementMethod.getInstance()
        binding.root.click {
            //do nothing,just for preventing user touch the view below it.
        }
        callback.onViewShow()
        FloatGuideMMKV.hasFloatNewUserGuidanceBeenShown = true
        FloatTracker.onOverlayTryItDialogExpose()
        binding.icTvCancel.click {
            callback.onFlowEnd(FloatModuleService.GuideFlowCallback.FLOW_RESULT_CLICK_CANCEL)
        }
    }
}