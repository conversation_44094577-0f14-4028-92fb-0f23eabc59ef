package com.interfun.buz.common.manager.voicecall

import com.interfun.buz.base.ktx.collectLatestIn
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager

object VoiceCallNotificationConflictManager {
    private val TAG = "VoiceCallNotificationConflictManager"
    private var isFirstCollect = true
    @Volatile
    var lastShowChannelId:Long = 0L

    init {
        ChannelPendStatusManager.isPendingFlow.collectLatestIn(VoiceCallPortal.voiceCallScope){
            if (isFirstCollect){
                isFirstCollect = false
                return@collectLatestIn
            }
            if (it.second != CallPendStatus.BEING_INVITED){
                logInfo(TAG,"change : lastShowChannelId 0")
                lastShowChannelId = 0L
            }
        }
    }

    fun onServiceDestroy(){
        logInfo(TAG,"onServiceDestroy: ")
        lastShowChannelId = 0L
    }
}