package com.interfun.buz.voicecall.common.view.itemdelegate

import android.content.Context
import android.text.TextUtils
import android.view.ViewGroup
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import androidx.recyclerview.widget.RecyclerView
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo.Companion.MY_MICROPHONE_OFF
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo.Companion.MY_POOR_NETWORK
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo.Companion.TARGET_BUSY
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo.Companion.TARGET_NO_ANSWER
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo.Companion.TARGET_MICROPHONE_OFF
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo.Companion.TARGET_POOR_NETWORK
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.databinding.VoicecallStateToastItemViewBinding
import com.interfun.buz.voicecall.privatecall.viewmodel.StateToastInformation

class RealTimeCallStateToastItemView
    : BaseBindingDelegate<StateToastInformation, VoicecallStateToastItemViewBinding>() {

    private lateinit var context: Context

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup
    ): BindingViewHolder<VoicecallStateToastItemViewBinding> {
        this.context = context
        return super.onCreateViewHolder(context, parent)
    }

    override fun onBindViewHolder(
        holder: BindingViewHolder<VoicecallStateToastItemViewBinding>,
        item: StateToastInformation
    ) {
        if (holder.bindingAdapterPosition != RecyclerView.NO_POSITION) {
            setState(
                binding = holder.binding,
                state = item.toastStyle,
                username = item.username,
                portrait = item.portrait
            )
        }
        setState(
            binding = holder.binding,
            state = item.toastStyle,
            username = item.username,
            portrait = item.portrait
        )
        super.onBindViewHolder(holder, item)
    }

    private fun setState(
        binding: VoicecallStateToastItemViewBinding,
        state: String,
        username: String?,
        portrait: String?
    ) {
        when (state) {
            MY_POOR_NETWORK -> {
                setIcon(binding, R.drawable.ic_poor_network)
                val text = R.string.rtc_weak_network.asString()
                setText(binding, text)
            }
            MY_MICROPHONE_OFF -> {
                setIconFont(binding, R.string.ic_mic_close.asString())
                val text = R.string.mic_off_tip.asString()
                setText(binding, text)
            }
            TARGET_POOR_NETWORK -> {
                setIcon(binding, R.drawable.ic_poor_network)
                username?.let {
                    val text = ellipsizeUsername(binding, state, it, R.string.rtc_x_weak_network)
                    setText(binding, text)
                }
            }
            TARGET_MICROPHONE_OFF -> {
                setIconFont(binding, R.string.ic_mic_close.asString())
                username?.let {
                    val text = ellipsizeUsername(binding, state, it, R.string.rtc_x_mic_is_off)
                    setText(binding, text)
                }
            }
            TARGET_BUSY -> {
                setPortrait(binding, portrait)
                username?.let {
                    val text = ellipsizeUsername(binding, state, it, R.string.rtc_x_busy)
                    setText(binding, text)
                }
            }
            TARGET_NO_ANSWER -> {
                setPortrait(binding, portrait)
                username?.let {
                    val text = ellipsizeUsername(binding, state, it, R.string.rtc_x_no_answer)
                    setText(binding, text)
                }
            }
        }
    }

    private fun ellipsizeUsername(
        binding: VoicecallStateToastItemViewBinding,
        state: String,
        username: String,
        stateStringResId: Int
    ): String {
        val maxContainerSize = context.screenWidth - binding.root.marginStart - binding.root.marginEnd
        val iconSizeAndSpacing = when (state) {
            TARGET_MICROPHONE_OFF -> 16.dp + 12.dp + 4.dp + 12.dp
            TARGET_POOR_NETWORK -> 14.dp + 12.dp + 4.dp + 12.dp
            TARGET_BUSY,
            TARGET_NO_ANSWER -> 32.dp + 4.dp + 8.dp + 12.dp
            else -> 0.dp
        }
        val stateString = stateStringResId.asString().replace("%s", "")
        val textPaint = binding.tvStatus.paint
        val stateStringSize = textPaint.measureText(stateString)
        val maxUsernameSize = maxContainerSize - iconSizeAndSpacing - stateStringSize
        val ellipsizeUsername = TextUtils.ellipsize(username, textPaint, maxUsernameSize, TextUtils.TruncateAt.END).toString()
        return context.getString(stateStringResId, ellipsizeUsername)
    }

    private fun setText(binding: VoicecallStateToastItemViewBinding, text: String) {
        binding.tvStatus.text = text
    }

    private fun setPortrait(binding: VoicecallStateToastItemViewBinding, url: String?) {
        combineView(
            binding.ivIcon,
            binding.iftvIcon
        ).gone()
        binding.ivPortrait.apply {
            setPortrait(url)
            visible()
        }
    }

    private fun setIcon(binding: VoicecallStateToastItemViewBinding, icon: Int) {
        combineView(
            binding.ivPortrait,
            binding.iftvIcon
        ).gone()
        binding.ivIcon.apply {
            setImageResource(icon)
            visible()
        }
    }

    private fun setIconFont(binding: VoicecallStateToastItemViewBinding, text: String) {
        combineView(
            binding.ivPortrait,
            binding.ivIcon
        ).gone()
        binding.iftvIcon.apply {
            setText(text)
            visible()
        }
    }
}