package com.interfun.buz.chat.map.send.model

import android.location.Address
import android.os.Parcelable
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.chat.R
import kotlinx.parcelize.Parcelize

@Parcelize
data class BuzAddressBean(val featureName: String, var addressLines: String) : Parcelable {
    companion object {
        fun formatLocationMsg(lan: Double, lon: Double): String {
            return String.format(
                "${R.string.map_lng.asString()}%f ${R.string.map_lat.asString()}%f",
                lon,
                lan
            )
        }
    }
}

fun Address.convertBuzAddressBean(position: BuzLocation): BuzAddressBean {
    val addressLine = if (this.maxAddressLineIndex >= 0) {
        this.getAddressLine(maxAddressLineIndex)
    } else {
        BuzAddressBean.formatLocationMsg(position.lat, position.lon)
    }

    var featureName = this.featureName
    featureName = if (featureName == null || featureName.trim().isBlank()) {
        R.string.location.asString()
    } else {
        featureName.trim()
    }
    return BuzAddressBean(featureName, addressLine)
}


