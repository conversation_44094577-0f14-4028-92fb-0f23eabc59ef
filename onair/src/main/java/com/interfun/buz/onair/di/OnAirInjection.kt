package com.interfun.buz.onair.di

import android.annotation.SuppressLint
import com.interfun.buz.onair.datasource.LocalOnAirGiftDataSource
import com.interfun.buz.onair.datasource.RemoteOnAirGiftDataSource
import com.interfun.buz.onair.repository.OnAirGiftRepository
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext

object OnAirInjection {
//    @SuppressLint("StaticFieldLeak")
//    private val onAirGiftRepository = OnAirGiftRepository(
//        LocalOnAirGiftDataSource(ApplicationContext.getApplication()),
//        RemoteOnAirGiftDataSource()
//    )


//    fun provideOnAirGiftRepository(): OnAirGiftRepository {
//        return onAirGiftRepository
//    }
}