package com.interfun.buz.biz.center.voicemoji.repository.tabnavigation

import android.annotation.SuppressLint
import com.interfun.buz.biz.center.voicemoji.datasource.tabnavigation.LocalTabNavigationDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.tabnavigation.RemoteTabNavigationDataSource
import com.interfun.buz.biz.center.voicemoji.model.tabnavigation.VETabNavigation
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.manager.GlobalEventManager
import com.interfun.buz.common.manager.UserSessionManager
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

/**
 * Author: ChenYouSheng
 * Date: 2024/12/31
 * Email: <EMAIL>
 * Desc:
 */
object TabNavigationRepository {
    @SuppressLint("StaticFieldLeak")
    private val localDS = LocalTabNavigationDataSource()
    private val remoteDS = RemoteTabNavigationDataSource()

    /**
     * 获取一级导航栏列表
     */
    fun getTabNavigationList() = localDS.getVENavigationTabList()

    /**
     * 获取二级导航栏列表
     */
    fun getSubTabNavigationList(id: Long) = getTabNavigationList().map { navigationList ->
        navigationList?.find { it.id == id }?.subNavigationTab
    }

    /**
     * 同步导航栏列表（一二级）
     */
    suspend fun syncTabNavigationList() {
        val noCache = localDS.getVENavigationTabList().firstOrNull().isNullOrEmpty()
        if (localDS.isFirstRequest() || noCache) {
            val resp = remoteDS.getNavigationTabList()
            if (resp is Success) {
                localDS.updateLocalCache(resp.data)
            }
        }
    }

    /**
     * 保存选中的tab
     */
    fun saveSelectTab(tab: VETabNavigation) {
        localDS.saveSelectTab(tab)
    }

    /**
     * 获取已选中的tab
     */
    fun getSelectedTab() = localDS.getSelectedTab()

    /**
     * 获取已选中的tab
     */
    fun getSelectedTabWithoutFlow() = localDS.getSelectedTabWithoutFlow()

}