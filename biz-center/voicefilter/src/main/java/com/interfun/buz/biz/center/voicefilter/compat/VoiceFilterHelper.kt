package com.interfun.buz.biz.center.voicefilter.compat

import com.interfun.buz.biz.center.voicefilter.model.VoiceFilterData

/**
 * VoiceFilter 数据访问助手类
 * 提供简单的 API 来获取 VoiceFilter 相关数据
 */
@Deprecated("为了兼容旧代码而保留，请不要再使用这个类")
object VoiceFilterHelper : VoiceFilterDataCompat{

    lateinit var delegate: VoiceFilterDataCompat

    override suspend fun getCachedVoiceFilterDataById(filterId: Long): VoiceFilterData? {
        return delegate.getCachedVoiceFilterDataById(filterId)
    }

    override fun getCachedVoiceFilterById(filterId: Long): VoiceFilterData {
        return delegate.getCachedVoiceFilterById(filterId)
    }

    override fun getCachedVoiceFilterNameById(filterId: Long): String {
        return delegate.getCachedVoiceFilterNameById(filterId)
    }
}