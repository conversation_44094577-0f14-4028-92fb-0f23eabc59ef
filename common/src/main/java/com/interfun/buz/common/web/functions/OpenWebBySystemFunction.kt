package com.interfun.buz.common.web.functions

import android.content.Intent
import android.net.Uri
import com.interfun.buz.base.ktx.equalsIgnoreCase
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.base.BaseActivity
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail
import org.json.JSONObject

class OpenWebBySystemFunction : JSFunction() {
    companion object {
        const val METHOD_NAME = "openWebBySystem"
        const val TAG = "OpenWebBySystemFunction"
    }

    override fun invoke(
        activity: BaseActivity,
        webView: LJavaScriptWebView,
        data: JSFunctionData
    ): JsCallbackDetail? {
        logInfo(TAG, "invoke OpenWebBySystemFunction data ${data}")
        val params = JSONObject(data.params)
        val url = params.optString("url", "")
        if (url.isNullOrEmpty() || "null".equalsIgnoreCase(url)) {
            return getSuccessCallback(data)
        }
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        activity.startActivity(intent)
        return getSuccessCallback(data)
    }
}