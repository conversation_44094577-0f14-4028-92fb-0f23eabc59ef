package com.interfun.buz.onair.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.SplitEllipsisText
import com.interfun.buz.compose.components.haze.hazeEffect
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_liveplace.ui.LivePlaceTag
import com.interfun.buz.onair.R
import com.interfun.buz.onair.viewmodel.LivePlaceOwnerInfo

@Composable
fun LivePlaceTitleBar(
    topic: String,
    ownerName: String,
    ownerInfo: LivePlaceOwnerInfo = LivePlaceOwnerInfo(),
    isLive: () -> Boolean = { true },
    showTagTurnOnAnim: () -> Boolean = { true },
    onNameClick: () -> Unit = {},
    onOverlayClick: () -> Unit = {},
    onCloseClick: () -> Unit,
    closeComposable: @Composable (
        ownerInfo: LivePlaceOwnerInfo,
        onCloseClick: () -> Unit,
    ) -> Unit = DefaultCloseLambda,
    overlappedComposable: @Composable (onOverlayClick: () -> Unit) -> Unit = DefaultOverlappedLambda,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .height(90.dp)
            .padding(horizontal = 20.dp)
    ) {
        VerticalSpace(10.dp)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(28.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            LivePlaceTag(isLive = isLive(), showTurnOnAnim = showTagTurnOnAnim())
            WeightSpace(1f)
            overlappedComposable(onOverlayClick)
            HorizontalSpace(12.dp)
            closeComposable(ownerInfo, onCloseClick)

        }
        Row(
            modifier = Modifier
                .padding(end = 40.dp)
                .debouncedClickable(enabled = ownerInfo.canEditInfo, onClick = onNameClick),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = topic,
                style = TextStyles.titleMedium(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = R.color.color_foreground_neutral_important_default.asColor(),
                modifier = Modifier.weight(1f, fill = false)
            )
            if (ownerInfo.canEditInfo) {
                IconFontText(
                    modifier = modifier.rotateWhenArLanguage(),
                    iconRes = R.string.ic_arrow_right,
                    iconSize = 20.dp,
                    iconColor = R.color.alpha_white_60.asColor(),
                    size = 20.dp,
                )
            }
        }
        VerticalSpace(1.dp)
        if (ownerName.notEmpty()) {
            SplitEllipsisText(
                pattern = R.string.live_place_owner_title.asString(),
                content = ownerName,
                style = TextStyles.bodySmall(),
                color = R.color.alpha_white_60.asColor(),
                modifier = Modifier.padding(end = 40.dp)
            )
        }
    }
}

/**
 * <AUTHOR>
 * @date 2024/12/27
 * @desc
 */
@Composable
fun LivePlaceSimpleTitleBar(
    topic: String,
    ownerName: String,
    ownerInfo: LivePlaceOwnerInfo = LivePlaceOwnerInfo(),
    isLive: () -> Boolean = { false },
    onNameClick: () -> Unit = {},
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    LivePlaceTitleBar(
        topic = topic,
        ownerName = ownerName,
        ownerInfo = ownerInfo,
        isLive = isLive,
        onNameClick = onNameClick,
        onCloseClick = onCloseClick,
        closeComposable = { _, onCloseClick ->
            IconFontText(
                modifier = Modifier
                    .size(24.dp)
                    .debouncedClickable(onClick = onCloseClick),
                iconText = R.string.ic_exit.asString(),
                iconColor = R.color.white.asColor()
            )
        },
        overlappedComposable = {},
        modifier = modifier
    )
}

@Composable
private fun DefaultClose(
    ownerInfo: LivePlaceOwnerInfo,
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .height(28.dp)
            .clip(shape = RoundedCornerShape(14.dp))
            .hazeEffect(state = LocalHazeState.current)
            .debouncedClickable(onClick = onCloseClick)
    ) {
        Text(
            text = if (ownerInfo.isOwner) {
                R.string.end_live_place.asString()
            } else {
                R.string.leave_live_place.asString()
            },
            style = TextStyles.labelMedium(),
            color = R.color.color_text_white_important.asColor(),
            modifier = Modifier
                .align(Alignment.Center)
                .padding(horizontal = 10.dp)
        )
    }
}

private val DefaultCloseLambda =
    @Composable { ownerInfo: LivePlaceOwnerInfo, onCloseClick: () -> Unit ->
        DefaultClose(
            ownerInfo,
            onCloseClick,
        )
    }

private val DefaultOverlappedLambda =
    @Composable { onOverlayClick: () -> Unit ->
        DefaultOverlapped(onOverlayClick = onOverlayClick)
    }

@Composable
private fun DefaultOverlapped(modifier: Modifier = Modifier, onOverlayClick: () -> Unit) {
    Box(
        modifier = modifier
            .size(28.dp)
            .clip(shape = CircleShape)
            .hazeEffect(state = LocalHazeState.current)
            .debouncedClickable(onClick = onOverlayClick),
        contentAlignment = Alignment.Center
    ) {
        IconFontText(
            iconRes = R.string.ic_minimize,
            iconSize = 16.dp,
            modifier = Modifier.rotateWhenArLanguage()
        )
    }
}

@Preview
@Composable
fun PreviewLivePlaceTitleBar() {
    PreviewLivePlaceLocalProvider {
        LivePlaceTitleBar(
            topic = "Super Long Place Name Super Long Place Name",
            ownerName = "Super Long Owner Name Super Long Owner Name",
            onNameClick = {},
            onOverlayClick = {},
            onCloseClick = {},
        )
    }
}
