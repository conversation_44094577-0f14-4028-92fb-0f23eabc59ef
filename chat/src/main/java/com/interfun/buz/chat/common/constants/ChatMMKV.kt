package com.interfun.buz.chat.common.constants

import com.interfun.buz.base.ktx.MMKVOwner
import com.interfun.buz.base.ktx.mmkvBool
import com.interfun.buz.base.ktx.mmkvLong
import com.interfun.buz.base.ktx.mmkvString
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.ktx.userMMKVBool
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.MmkvUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * MMKV常量记录类，请保持规范：以模块名作为开头、避免重名
 * 使用方式：
 * 1. 通过by mmkv...的属性委托方式添加及使用，使用驼峰式命名
 * 2. 添加const val String作为Key，手动调用mmkv.encode\decode，规范为全大写+下划线
 */
object ChatMMKV : MMKVOwner {

    /**----------------- 常量区域 ------------------**/
    /** group invite exposure record, dynamic format key: chat_key_group_invite_exposure_uid_groupId */
    private const val CHAT_KEY_GROUP_INVITE_EXPOSURE_BY_UID = "chat_key_group_invite_exposure_"


    /**--------------- 委托属性区域 -----------------**/
    val a by mmkvBool()

    /**
     * Get flag for Whether the user invitation has been exposed
     */
    fun getGroupInviteExposureFlag(groupId: String): Boolean{
        if (UserSessionManager.hasSession().not()){
            return false
        }
        val key = CHAT_KEY_GROUP_INVITE_EXPOSURE_BY_UID + UserSessionManager.uid + "_" + groupId
        return MmkvUtil.getMmkvBoolean(key,  false)
    }

    /**
     * set flag for the user invitation has been exposed or reset
     * reset when 1.been kicked exposed 2.leave group actively
     */
    fun putGroupInviteExposureFlag(groupId: String, exposed: Boolean = true){
        GlobalScope.launch(Dispatchers.IO) {
            if (UserSessionManager.hasSession()){
                val key = CHAT_KEY_GROUP_INVITE_EXPOSURE_BY_UID + UserSessionManager.uid + "_" + groupId
                MmkvUtil.putMmkvBoolean(key, exposed)
            }
        }
    }

    /** the flag for notify setting show time */
    var lastNotifySettingShowTime by mmkvLong(0L)

    //the flag for had shown 'Get Started' guide
    var hadShownRegisterWTGuideStepOne by mmkvBool(false)

    //the flag for had shown 'Hold Speaking' guide
    var hadShownRegisterWTGuideStepTwo by mmkvBool(false)

    //the flag for had shown 'wt msg send success' guide
    var hadShownRegisterWTGuideStepThird by mmkvBool(false)

    //true indicate that "get started" guidance for robot have been shown,otherwise false
    var hadShownRobotSayHelloGuide by userMMKVBool(false)
    //true indicate that introduction popup for robot have been handled,otherwise false
    var hadHandledRobotGuideFlow by userMMKVBool(false)

    //为了兼容之前版本三星设备部分机型使用了intent的方式设置桌面角标，目前不用这个方案了，但是有些旧用户可能设置了后，
    //无论重启手机还是卸载重装，新的notification设置number的方案都无效，所以需要清空一下
    var hasClearedBadgeCount by mmkvBool(false)


    // the flag for whether show guide user to tap ai from homepage
    var hadShownTapAiToSelectGuide by mmkvBool(false)

    // the flag for whether show guide user current address an ai in homepage
    var hadShownYouAreAddressAiGuide by mmkvBool(false)

    // the flag for whether show guide user current address can speak with in homepage
    var hadShowPushToTalkAiGuide by mmkvBool(false)

    var hadClickCloseVoiceMojiPreviewTips by mmkvBool(false)

    var hadChatListClickCloseVoiceMojiPreviewTips by mmkvBool(false)
    var cacheLocation by mmkvString("")

    var hadClickChatHeaderLivePlaceEntry by mmkvBool(false)

}