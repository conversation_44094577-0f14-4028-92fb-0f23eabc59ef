package com.interfun.buz.common.anim

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.transition.Transition
import android.transition.TransitionValues
import android.view.ViewGroup
import com.interfun.buz.base.ktx.log

class ScaleTransition(val endScaleX: Float, val endScaleY: Float) : Transition() {

    companion object {
        private const val PROPNAME_SCALE_X = "com.interfun.buz.scaleX"
        private const val PROPNAME_SCALE_Y = "com.interfun.buz.scaleY"
        const val TAG = "ScaleTransition"
    }

    override fun captureStartValues(transitionValues: TransitionValues) {
        val scaleX = transitionValues.view.scaleX
        val scaleY = transitionValues.view.scaleY
        transitionValues.values.put(PROPNAME_SCALE_X, scaleX)
        transitionValues.values.put(PROPNAME_SCALE_Y, scaleY)
    }

    override fun captureEndValues(transitionValues: TransitionValues) {
        transitionValues.values.put(PROPNAME_SCALE_X, endScaleX)
        transitionValues.values.put(PROPNAME_SCALE_Y, endScaleY)
    }

    override fun createAnimator(
        sceneRoot: ViewGroup,
        startValues: TransitionValues?,
        endValues: TransitionValues?
    ): Animator? {
        if (startValues == null || endValues == null) {
            return null
        }
        val view = endValues.view
        val startScaleX = startValues.values.get(PROPNAME_SCALE_X) as? Float ?: return null
        val startScaleY = startValues.values.get(PROPNAME_SCALE_Y) as? Float ?: return null
        val endScaleX = endValues.values.get(PROPNAME_SCALE_X) as? Float ?: return null
        val endScaleY = endValues.values.get(PROPNAME_SCALE_Y) as? Float ?: return null
        val animatorSet = AnimatorSet()
        val scaleXAnim = ObjectAnimator.ofFloat(view, "scaleX", startScaleX, endScaleX)
        val scaleYAnim = ObjectAnimator.ofFloat(view, "scaleY", startScaleY, endScaleY)
        animatorSet.playTogether(scaleXAnim, scaleYAnim)
        log(
            TAG,
            "createAnimator,startScaleX:$startScaleX,startScaleY:${startScaleY},endScaleX:${endScaleX},endScaleY:$endScaleY,view:$view"
        )
        return animatorSet
    }
}