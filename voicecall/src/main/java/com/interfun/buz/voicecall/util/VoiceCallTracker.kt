package com.interfun.buz.voicecall.util

import com.interfun.buz.base.ktx.getIfTrueOrFalse
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.common.bean.voicecall.AudioDevice
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.IncomingRealTimeCallSource
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.utils.BuzTracker

object VoiceCallTracker {

    fun onCallWaitingPageView(
        traceId: String?,
        channelType: Int,
        callType: @CallType Int,
        targetId: Long
    ) {
        val pageBusinessType = when(channelType){
            ChannelType.TYPE_VOICE_CALL_GROUP -> "group"
            ChannelType.TYPE_VOICE_CALL_1V1 -> "private"
            else -> "unknown"
        }
        val pageStatus = when(callType){
            CallType.TYPE_VOICE -> "voice_call"
            CallType.TYPE_VIDEO -> "video_call"
            else -> "unknown"
        }
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024031301")
            put(TrackConstant.KEY_TITLE, "语音呼叫中页面")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_PAGE_STATUS, pageStatus)
            if (null != traceId) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, traceId)
            }
        }
    }

    fun onCallConnectingPageView(
        channelId: Long,
        channelType: Int,
        callType: @CallType Int
    ) {
        val pageBusinessType = when(channelType){
            ChannelType.TYPE_VOICE_CALL_GROUP -> "group"
            ChannelType.TYPE_VOICE_CALL_1V1 -> "private"
            else -> "unknown"
        }
        val contentName = when(callType){
            CallType.TYPE_VOICE -> "voice_call"
            CallType.TYPE_VIDEO -> "video_call"
            else -> "unknown"
        }
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2025031201")
            put(TrackConstant.KEY_TITLE, "call_connecting_page")
            put(TrackConstant.KEY_PAGE_TYPE, "call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_CONTENT_NAME, contentName)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, channelId.toString())
        }
    }

    fun onCallConnectedPageView(
        channelId: Long,
        channelType: Int,
        callType: @CallType Int,
        targetId: Long
    ) {
        val pageBusinessType = when(channelType){
            ChannelType.TYPE_VOICE_CALL_GROUP -> "group"
            ChannelType.TYPE_VOICE_CALL_1V1 -> "private"
            else -> "unknown"
        }
        val contentName = when(callType){
            CallType.TYPE_VOICE -> "voice_call"
            CallType.TYPE_VIDEO -> "video_call"
            else -> "unknown"
        }
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024031302")
            put(TrackConstant.KEY_TITLE, "语音通话中页面")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_CONTENT_NAME, contentName)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, channelId.toString())
        }
    }

    fun onCallIncomingCallPageView(
        channelId: Long,
        channelType: Int,
        targetId: Long,
        callType: @CallType Int,
        source: IncomingRealTimeCallSource
    ) {
        val pageBusinessType = when(channelType){
            ChannelType.TYPE_VOICE_CALL_GROUP -> "group"
            ChannelType.TYPE_VOICE_CALL_1V1 -> "private"
            else -> "unknown"
        }
        val contentName = when (callType) {
            CallType.TYPE_VOICE -> "voice_call"
            CallType.TYPE_VIDEO -> "video_call"
            else -> "unknown"
        }
        val elementBusinessType = when (source) {
            IncomingRealTimeCallSource.Notification -> "notification"
            IncomingRealTimeCallSource.InAppNotification -> "in_app_notification"
            IncomingRealTimeCallSource.CallKit -> "callkit"
        }
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024031303")
            put(TrackConstant.KEY_TITLE, "语音来电页面")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_SOURCE, if (isAppInForeground) "front" else "back")
            put(TrackConstant.KEY_CONTENT_NAME, contentName)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, channelId.toString())
        }
    }

    fun onClickPendAnswerEvent(
        channelId: Long,
        channelType: Int,
        callType: Int,
        isAnswer:Boolean,
        source: IncomingRealTimeCallSource
    ){
        val pageBusinessType = when(channelType){
            ChannelType.TYPE_VOICE_CALL_GROUP -> "group"
            ChannelType.TYPE_VOICE_CALL_1V1 -> "private"
            else ->return
        }
        val contentName = when (source) {
            IncomingRealTimeCallSource.Notification -> "notification"
            IncomingRealTimeCallSource.InAppNotification -> "in_app_notification"
            IncomingRealTimeCallSource.CallKit -> "callkit"
        }
        val elementBusinessType = when (callType) {
            CallType.TYPE_VOICE -> "voice_call"
            CallType.TYPE_VIDEO -> "video_call"
            else -> "unknown"
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031303")
            put(TrackConstant.KEY_TITLE, "语音来电页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "接听选择按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_SOURCE, if (isAppInForeground) "front" else "back")
            put(TrackConstant.KEY_CONTENT_NAME, contentName)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (isAnswer) "accept" else "reject")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, channelId.toString())
        }
    }

    fun onClickMicSwitchEvent(isMicOff:Boolean){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031305")
            put(TrackConstant.KEY_TITLE, "语音通话中页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "开闭麦")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (isMicOff) "off" else "on")
        }
    }

    fun onClickDeviceSwitchEvent(deviceType:Int){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031306")
            put(TrackConstant.KEY_TITLE, "语音通话中页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "输出设备选项")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, getDeviceTrackerName(deviceType))
        }
    }

    private fun getDeviceTrackerName(deviceType:Int): String{
        return when(AudioDevice.fromRouting(deviceType)){
            AudioDevice.AUDIO_ROUTE_HANDSET -> {
                "earpiece"
            }
            AudioDevice.AUDIO_ROUTE_SPEAKER -> {
                "speaker"
            }
            AudioDevice.AUDIO_ROUTE_WIRED_EARPHONE -> {
                "earphone"
            }
            AudioDevice.AUDIO_ROUTE_BLUETOOTH_DEVICE -> {
                "bluetooth"
            }
            else ->{
                "other"
            }
        }
    }

    fun onClickHangUpEvent(channelType: Int, trackId: String? = null){
        val pageBusinessType = when(channelType){
            ChannelType.TYPE_VOICE_CALL_GROUP -> "group"
            ChannelType.TYPE_VOICE_CALL_1V1 -> "private"
            else ->return
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031307")
            put(TrackConstant.KEY_TITLE, "语音通话中页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "挂断按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE,pageBusinessType)
            if (trackId != null){
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, trackId)//通话的唯一标识id,使用channelId
            }
        }
    }

    fun onClickChatEntranceEvent(){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031308")
            put(TrackConstant.KEY_TITLE, "语音通话中页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "聊天页按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
        }
    }


    /** APP页面浏览-群通话选人页 */
    fun onGroupRealTimeCallSelectMember(groupId: String, isAdd: Boolean) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024031304")
            put(TrackConstant.KEY_TITLE, "群通话选人页")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
            val content = isAdd.getIfTrueOrFalse("add", "first")
            put(TrackConstant.KEY_SOURCE, content)
        }
    }

    /** APP元素点击-语音通话页-最小化 */
    fun onClickVoiceCallMinimize(isGroup: Boolean, isSpeaking: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031304")
            put(TrackConstant.KEY_TITLE, "语音通话页")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "最小化")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, isGroup.getIfTrueOrFalse("group", "private"))
            put(TrackConstant.KEY_PAGE_STATUS, isSpeaking.getIfTrueOrFalse("speaking", "waiting"))
        }
    }

    /** APP元素点击-群通话选人页-下一步 */
    fun onClickGroupRealTimeCallNextStep(groupId: String, selectedCount: Int, isAdd: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031309")
            put(TrackConstant.KEY_TITLE, "群通话选人页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "下一步")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
            val content = isAdd.getIfTrueOrFalse("add", "first")
            put(TrackConstant.KEY_SOURCE, content)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, selectedCount.toString())
        }
    }

    fun appClickAC2025031205(@CallType type:Int,source:String,channelId:String){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025031205")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "join_group_call")
            put(TrackConstant.KEY_PAGE_TYPE, "call")
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if(CallType.isVoiceCall(type)) "voice_call" else "video_call")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, channelId)
        }
    }
    /** APP元素点击-首页-群通话入口 */
    fun onClickGroupRealTimeCallHome(groupId: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031310")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "群通话入口")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
        }
    }

    /** APP元素点击-群聊天页-群通话入口 */
    fun onClickGroupRealTimeCallChat(groupId: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031311")
            put(TrackConstant.KEY_TITLE, "群聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "群通话入口")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
        }
    }

    /** APP 元素曝光-首页-群通话入口 */
    fun onHomeGroupJoinExposure(groupId: String) {
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024031301")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "群通话入口")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
        }
    }

    /** APP 元素曝光-群聊天-群通话入口 */
    fun onChatGroupJoinExposure(groupId: String) {
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024031302")
            put(TrackConstant.KEY_TITLE, "群聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "群通话入口")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
        }
    }

    fun resultBackRB2025031205(groupId: String,@CallType callType: Int,channelId:String,rcode:Int){
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025031205")
            put(TrackConstant.KEY_RESULT_TYPE, "oncall_invite_group_member_result")
            put(TrackConstant.KEY_PAGE_TYPE, "call")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
            put(TrackConstant.KEY_BUSINESS_TYPE, if (CallType.isVoiceCall(callType)) "voice_call" else "video_call")
            put(TrackConstant.KEY_CONTENT_ID, channelId)
            put(TrackConstant.KEY_IS_SUCCESS, if (rcode.isSuccess) "success" else "fail")
            put(TrackConstant.KEY_FAIL_REASON, "${rcode}")
        }
    }

    fun resultBackRB2025031206(
        channelType: @ChannelType Int,
        callType: @CallType Int,
        channelId: Long,
        memberCount: Int = 1,
        hasOpenedCamera: Boolean = false,
        duration: Long = 0, // ms
        rating: Int = 0, // 1: good , 0 : not_good
        endReason: String = ""
    ) {
        val pageBusinessType = when (channelType) {
            ChannelType.TYPE_VOICE_CALL_GROUP -> "group"
            ChannelType.TYPE_VOICE_CALL_1V1 -> "private"
            else -> "unknown"
        }
        val businessType = when (callType) {
            CallType.TYPE_VOICE -> "voice_call"
            CallType.TYPE_VIDEO -> "video_call"
            else -> "unknown"
        }
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025041702")
            put(TrackConstant.KEY_RESULT_TYPE, "result_rating_call")
            put(TrackConstant.KEY_PAGE_TYPE, "call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_CONTENT_ID, channelId.toString())
            put(TrackConstant.KEY_CONTENT_NAME, if (rating == 1) "good" else "not_good")
            put(TrackConstant.KEY_LOG_TIME, "$duration")
            put(TrackConstant.KEY_BUSINESS_NUM, memberCount)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, if (hasOpenedCamera) "yes" else "no")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_PAGE, endReason)
        }
    }
}