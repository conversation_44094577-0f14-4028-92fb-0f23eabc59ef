package com.interfun.buz.chat.common.entity

import android.graphics.Bitmap
import com.interfun.buz.compose.components.QRCodeUIState


class ChatGroupInviteItemBean(val groupId: Long) : ChatItem {

    var qrInfo: QRCodeUIState = QRCodeUIState(state = QRCodeUIState.State.LOADING)
        private set

    var expireTip: String = ""
        private set

    fun updateQrInfo(qrInfo: QRCodeUIState) {
        this.qrInfo = qrInfo
    }
    fun updateExpireTip(expiredTip: String) {
        this.expireTip = expiredTip
    }
}
