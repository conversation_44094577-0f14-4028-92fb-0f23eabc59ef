package com.interfun.buz.chat.group.view.itemdelegate

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.interfun.buz.chat.R
import com.interfun.buz.chat.group.viewmodel.GroupSelectMemberViewModel

/**
 * @Desc 空列表显示
 * @Author:<EMAIL>
 * @Date: 2022/7/26
 */
class GroupSearchEmptyItemView(): ItemViewBinder<GroupSelectMemberViewModel.EmptySearchItem, GroupSearchEmptyItemView.MyViewHolder>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): MyViewHolder {
        return MyViewHolder(inflater.inflate(R.layout.group_item_contact_search_empty, parent, false))
    }

    override fun onBindViewHolder(holder: MyViewHolder, item: GroupSelectMemberViewModel.EmptySearchItem) {

    }

    inner class MyViewHolder constructor(holderItemView: View): RecyclerView.ViewHolder(holderItemView){

    }
}