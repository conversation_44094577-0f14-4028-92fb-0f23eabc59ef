# Buz Android项目结构与功能模块

Buz是一个模块化构建的Android应用程序，采用分层架构设计，主要用于语音社交和即时通讯功能。

## 项目架构分层

### 1. 应用层 (Application Layer)
- [app](mdc:app) - 应用主模块
    - 应用程序入口和全局配置
    - 包含Application类、主Activity等
    - 渠道配置: [app_channel.txt](mdc:app/app_channel.txt)
    - 混淆规则: [proguard-rules.pro](mdc:app/proguard-rules.pro)

### 2. 基础层 (Base Layer)
- [base](mdc:base) - 基础功能模块
    - 提供应用程序的基础组件和工具类
    - 包含通用的Widget、工具类、扩展函数等
- [base-float](mdc:base-float) - 悬浮窗基础组件
- [base-photopreview](mdc:base-photopreview) - 图片预览基础组件

### 3. 通用层 (Common Layer)
- [common](mdc:common) - 通用功能模块
    - 跨模块共享的工具和组件
    - 包含资源文件、样式、字符串等
- [common-compose](mdc:common-compose) - Compose通用组件
    - Jetpack Compose相关的通用UI组件
    - 提供可复用的Compose组件库

### 4. 组件层 (Component Layer)
- [component:hilt](mdc:component/hilt) - Dagger Hilt依赖注入组件
    - 提供全应用的依赖注入配置
    - 管理模块间的依赖关系

### 5. 核心层 (Core Layer)
- [core:widget_record](mdc:core/widget_record) - 录音相关核心UI组件
- [core:widget_liveplace](mdc:core/widget_liveplace) - 直播场景相关核心UI组件

### 6. 业务中心层 (Business Center Layer)
- [biz-center:voicemoji](mdc:biz-center/voicemoji) - 语音表情业务逻辑
- [biz-center:voicefilter](mdc:biz-center/voicefilter) - 声音滤镜业务逻辑
- [biz-center:voicerecord](mdc:biz-center/voicerecord) - 语音录制业务逻辑
- [biz-center:liveplace](mdc:biz-center/liveplace) - 直播场景业务逻辑
- [biz-center:auth](mdc:biz-center/auth) - 认证业务逻辑
- [biz-center:social](mdc:biz-center/social) - 社交业务逻辑
- [biz-center:translator](mdc:biz-center/translator) - 翻译业务逻辑

### 7. 领域层 (Domain Layer)
- [domain:im-social](mdc:domain/im-social) - 即时通讯社交领域
- [domain:voiceemoji-im](mdc:domain/voiceemoji-im) - 语音表情即时通讯领域

### 8. 领域UI层 (Domain UI Layer)
- [domain-ui:social](mdc:domain-ui/social) - 社交相关UI
- [domain-ui:chat](mdc:domain-ui/chat) - 聊天相关UI
- [domain-ui:record](mdc:domain-ui/record) - 录音相关UI

### 9. 功能层 (Feature Layer)
- [feature:voicepreview](mdc:feature/voicepreview) - 语音预览功能
- [feature:notification](mdc:feature/notification) - 通知功能

### 10. 业务模块层 (Business Module Layer)

#### 核心功能模块
- [login](mdc:login) - 登录模块
    - 用户登录、注册、验证等功能
- [chat](mdc:chat) - 聊天模块
    - 私聊、群聊、语音消息等功能
    - 子模块包括: privy(私聊)、group(群聊)、voicemoji(语音表情)等
- [im](mdc:im) - 即时通讯模块
    - 消息传输、连接管理等底层通讯功能
- [contacts](mdc:contacts) - 联系人模块
    - 好友管理、联系人列表等功能
- [user](mdc:user) - 用户信息模块
    - 用户资料、设置等功能
- [voicecall](mdc:voicecall) - 语音通话模块
    - 语音通话、视频通话功能
- [onair](mdc:onair) - LivePlace模块
    - 个人liveplace空间
    - 群聊liveplace空间
- [push](mdc:push) - 推送模块

#### 媒体相关模块
- [media](mdc:media) - 媒体处理模块
- [album](mdc:album) - 相册模块
- [pictureselector](mdc:pictureselector) - 图片选择器模块
- [sharedmedia](mdc:sharedmedia) - 媒体分享模块
- [storage](mdc:storage) - 存储模块

#### 辅助功能模块
- [home](mdc:home) - 主页模块
- [startup](mdc:startup) - 启动模块
- [float](mdc:float) - 悬浮窗模块
- [feedback](mdc:feedback) - 反馈模块
- [campaign](mdc:campaign) - 活动模块
- [handleReceiveShare](mdc:handleReceiveShare) - 分享接收处理模块
- [demo](mdc:demo) - 演示模块
- [utils](mdc:utils) - 工具模块

#### 性能与测试模块
- [benchmark-startup](mdc:benchmark-startup) - 启动性能测试模块

## 项目配置文件

### 构建配置
- [settings.gradle](mdc:settings.gradle) - 项目模块配置
- [build.gradle](mdc:build.gradle) - 项目级构建文件
- [config.gradle](mdc:config.gradle) - 依赖版本和构建配置
- [build_config.gradle](mdc:build_config.gradle) - 构建变体配置
- [base_config.gradle](mdc:base_config.gradle) - 基础构建配置

### 编译命令
- 调试版本编译: `./gradlew app:assembleDebug`
- 清理编译: `./gradlew clean app:assembleDebug --no-build-cache`

### CI/CD配置
- [.gitlab-ci.yml](mdc:.gitlab-ci.yml) - GitLab CI/CD配置

### 多渠道打包
- 项目使用VasDolly实现多渠道打包，渠道配置在[app/app_channel.txt](mdc:app/app_channel.txt)

### 国际化配置
- [localizable_config.json](mdc:localizable_config.json) - 本地化配置
- [lokalise_download.py](mdc:lokalise_download.py) - Lokalise下载脚本
- [localizable_download.py](mdc:localizable_download.py) - 本地化下载脚本

## 模块依赖关系

### 依赖层次结构
1. **应用主模块** (app) 依赖所有功能模块
2. **功能模块** 依赖对应的领域UI模块和业务中心模块
3. **领域UI模块** 依赖业务中心模块和核心层模块
4. **业务中心模块** 包含具体的业务逻辑实现
5. **基础层模块** 被所有上层模块依赖

### 模块间通信
- 使用Dagger Hilt进行依赖注入
- 通过service包进行跨模块通信
- 遵循单向数据流原则

## 核心模块结构

### 通用包结构模式示例
每个功能模块内部采用标准化包结构：
```
module-name/
├── common/          # 公共模块
│   ├── view/
│   ├── viewmodel/
│   ├── entity/
│   ├── utils/
│   └── service/
└── feature-name/    # 具体功能
    ├── view/
    ├── viewmodel/
    └── manager/
```

### Base模块 ([base](mdc:base))
基础功能模块的包结构：

```
base/
├── widget/                   # 基础UI控件
├── ktx/                      # Kotlin扩展函数
│   └── Log.kt               # 日志工具类
├── utils/                    # 工具类集合
├── coroutine/               # 协程相关工具
├── manager/                 # 基础管理类
├── basis/                   # 基础抽象类
├── constants/               # 常量定义
├── ime/                     # 输入法相关
├── anim/                    # 动画相关
└── qrcodereaderview/        # 二维码扫描组件
```

### Common模块 ([common](mdc:common))
通用功能模块，包含跨模块共享的资源和组件：

```
common/
├── src/main/
│   ├── java/                # Java/Kotlin源码
│   └── res/                 # 资源文件
│       ├── values/          # 默认资源(英语)
│       ├── values-zh/       # 中文资源
│       ├── drawable/        # 图片资源
│       ├── layout/          # 布局文件
│       └── color/           # 颜色资源
└── build.gradle            # 模块构建配置
```

### Common-Compose模块 ([common-compose](mdc:common-compose))
Jetpack Compose通用组件库：

```
common-compose/
├── components/              # 可复用Compose组件
│   └── PreviewUtils.kt     # Preview工具函数
├── theme/                   # 主题相关
├── animation/               # 动画效果
└── utils/                   # Compose工具函数
```

## 业务模块结构示例

### Chat模块 ([chat](mdc:chat))
聊天功能模块的详细结构：

```
chat/
├── common/                  # 聊天通用功能
│   ├── entity/             # 消息实体、聊天实体等
│   ├── viewmodel/          # 通用聊天ViewModel
│   ├── utils/              # 聊天工具类
│   ├── manager/            # 聊天管理器
│   ├── service/            # 聊天服务接口
│   └── compose/            # 聊天Compose组件
├── privy/                   # 私聊功能
│   ├── view/
│   ├── viewmodel/
│   └── manager/
├── group/                   # 群聊功能
│   ├── view/
│   ├── viewmodel/
│   └── manager/
├── voicemoji/              # 语音表情功能
├── ai/                      # AI聊天功能
├── media/                   # 媒体消息处理
├── forward/                 # 消息转发
├── e2ee/                    # 端到端加密
├── dnd/                     # 消息拖拽
├── voicepanel/             # 语音面板
├── map/                     # 位置消息
├── online/                  # 在线状态
├── targetselect/           # 目标选择
├── wt/                      # 对讲功能
├── di/                      # 依赖注入模块
└── ChatNavigation.kt       # 聊天导航配置
```

### Login模块 ([login](mdc:login))
用户认证模块结构：

```
login/
├── common/                  # 登录通用功能
│   ├── entity/             # 用户实体、认证信息等
│   ├── viewmodel/          # 登录基础ViewModel
│   └── service/            # 认证服务接口
├── signin/                  # 登录功能
├── signup/                  # 注册功能
├── verify/                  # 验证功能
└── reset/                   # 密码重置功能
```

## 业务中心模块结构

### Biz-Center模块组织
业务中心模块专注于业务逻辑实现：

```
biz-center/
├── voicemoji/              # 语音表情业务
│   ├── repository/         # 数据仓库
│   ├── usecase/           # 业务用例
│   ├── model/             # 业务模型
│   └── service/           # 业务服务
├── voicefilter/           # 声音滤镜业务
├── voicerecord/           # 语音录制业务
├── liveplace/             # 直播场景业务
├── auth/                  # 认证业务
├── social/                # 社交业务
└── translator/            # 翻译业务
```

## 开发规范

### 技术栈
- Kotlin 作为主要开发语言
- Jetpack Compose 用于UI开发
- Room 用于本地数据存储
- Dagger Hilt 用于依赖注入
- MVVM 架构模式
- 遵循Clean Architecture原则

### 文件命名规范
- **Activity**: `*Activity.kt` (如: `ChatActivity.kt`)
- **Fragment**: `*Fragment.kt` (如: `MessageFragment.kt`)
- **ViewModel**: `*ViewModel.kt` (如: `ChatViewModel.kt`)
- **Repository**: `*Repository.kt` (如: `MessageRepository.kt`)
- **Entity**: `*Entity.kt` 或 `*.kt` (如: `MessageEntity.kt`)
- **Utils**: `*Utils.kt` 或 `*Util.kt` (如: `DateUtils.kt`)
- **Manager**: `*Manager.kt` (如: `ChatManager.kt`)
- **Service**: `*Service.kt` (如: `NotificationService.kt`)

### 包命名规范
- 使用小写字母和下划线
- 按功能分组，保持包结构清晰
- 避免过深的包层次结构

### 依赖注入模式
使用Dagger Hilt进行依赖注入：

```kotlin
// 模块定义
@Module
@InstallIn(SingletonComponent::class)
object ChatModule {
    @Provides
    @Singleton
    fun provideChatRepository(): ChatRepository = ChatRepositoryImpl()
}

// 使用依赖注入
@AndroidEntryPoint
class ChatActivity : BaseComposeActivity() {
    @Inject
    lateinit var chatRepository: ChatRepository
}
```

### Compose组件规范
- 所有新的UI组件使用Jetpack Compose开发
- 为每个Compose组件提供@Preview
- Preview函数首行调用`InitPreview()`函数
- 组件文件放在对应的compose包下

### 数据流架构
遵循MVVM架构和单向数据流：

```
View (Compose) → ViewModel → Repository → DataSource
     ↑                                        ↓
     ←←←←←←← StateFlow/LiveData ←←←←←←←←←←←←←←←←
```

## 模块构建配置

### 标准build.gradle结构
每个模块的构建文件遵循统一模式：

```gradle
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'dagger.hilt.android.plugin'

// 应用通用配置
apply from: "${rootProject.projectDir}/base_config.gradle"

dependencies {
    // 基础依赖
    api project(':base')
    api project(':common')
    
    // 业务依赖
    implementation project(':biz-center:social')
    
    // 第三方库依赖
    api rootProject.ext.dependencies["hilt"]
    kapt rootProject.ext.dependencies["hilt_compiler"]
}
```


## 参考链接
[Buz Android 项目结构概览](https://vocalbeats.sg.larksuite.com/wiki/VhAOwPNP7iv3UdkNnYzlmv0WgDg)

[tikeplay使用指南](https://lizhi2021.feishu.cn/wiki/wikcnhL3066f1PG1egB5acCADGe)

[tikeplay使用指南2](https://lizhi2021.feishu.cn/wiki/wikcnwOw4ZxAeccEZbjrooeu6ec?utm_source=gold_browser_extension)

[Buz Android 基础组件文档](https://vocalbeats.sg.larksuite.com/wiki/CjGpwiUAgizpvpkUIhflCN8kgfu)

[VX/IM5文档](https://lizhi2021.feishu.cn/wiki/wikcnxPJhNdr1jyfmC6I1hG9Dpd)
