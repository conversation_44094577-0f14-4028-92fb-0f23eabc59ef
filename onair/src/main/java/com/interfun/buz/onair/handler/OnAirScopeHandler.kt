package com.interfun.buz.onair.handler

import com.interfun.buz.base.ktx.buzCollect
import com.interfun.buz.onair.standard.OnAirLifecycle
import com.interfun.buz.onair.standard.OnAirRoom
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlin.coroutines.coroutineContext

class OnAirScopeHandler(val room: OnAirRoom) {

    fun initHandler(){
        room.obtainOnAirLifecycleFLow().buzCollect(MainScope()){ lifecycle->
            if(lifecycle is OnAirLifecycle.DESTROY){
                destroy()
                coroutineContext[Job]?.cancel()
            }
        }
    }

    private fun destroy() {
        room.onAirScope().cancel()
    }
}