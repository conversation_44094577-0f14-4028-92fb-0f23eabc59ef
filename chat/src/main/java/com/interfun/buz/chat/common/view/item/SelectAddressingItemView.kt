package com.interfun.buz.chat.common.view.item

import android.content.Context
import android.view.ViewGroup
import coil.load
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatItemAddressAiBinding
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.database.entity.UserRelationInfo

class SelectAddressingItemView : BaseBindingDelegate<UserRelationInfo,ChatItemAddressAiBinding>() {

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup
    ): BindingViewHolder<ChatItemAddressAiBinding> {
        val holder = super.onCreateViewHolder(context, parent)
        holder.binding.ivAIFlag.load(R.drawable.common_icon_ai_flag)
        return holder
    }

    override fun onBindViewHolder(
        holder: BindingViewHolder<ChatItemAddressAiBinding>,
        item: UserRelationInfo
    ) {
        super.onBindViewHolder(holder, item)
        holder.binding.ivContactPortrait.setPortrait(item.portrait)
        holder.binding.tvName.text = item.userName ?: ""
    }

    override fun useItemClickListener(item: UserRelationInfo): Boolean {
        return true
    }
}