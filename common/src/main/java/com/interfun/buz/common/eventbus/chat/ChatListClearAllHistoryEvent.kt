package com.interfun.buz.common.eventbus.chat

import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.eventbus.BaseEvent
import com.interfun.buz.common.eventbus.album.UpdateAlbumListDataEvent
import com.jeremyliao.liveeventbus.LiveEventBus

class ChatListClearAllHistoryEvent(): BaseEvent() {
    companion object {
        fun post() {
            BusUtil.post(ChatListClearAllHistoryEvent())
        }
    }
}