package com.interfun.buz.contacts.view.itemdelegate

import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.contacts.databinding.ContactsAutoTranslateItemTitleBinding
import com.interfun.buz.contacts.viewmodel.AutoTranslatorSettingItem.AutoTranslateTitleItemBean

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @desc
 */
class AutoTranslateTitleItemView() :
    BaseBindingDelegate<AutoTranslateTitleItemBean, ContactsAutoTranslateItemTitleBinding>() {

    override fun onBindViewHolder(
        binding: ContactsAutoTranslateItemTitleBinding,
        item: AutoTranslateTitleItemBean,
        position: Int
    ) {
        binding.tvTitle.text = item.title
    }
}