package com.interfun.buz.chat.ai.topic.faq


enum class FAQStyle {
    TOP_DESC,
    NORMAL_DESC,
    LINK_DESC,
    FEEDBACK_DESC,
}

open class FAQItemBean(val id: Long, val styleId: FAQStyle, val title: String, val content: String)
open class FAQTopDescItemBean(id: Long, title: String, content: String) :
    FAQItemBean(id, FAQStyle.TOP_DESC, title, content)

open class FAQNormalDescItemBean(id: Long, title: String, content: String) :
    FAQItemBean(id, FAQStyle.NORMAL_DESC, title, content)

open class FAQLinkDescItemBean(id: Long, title: String, content: String, val url: String) :
    FAQItemBean(id, FAQStyle.LINK_DESC, title, content)

open class FAQFeedbackDescItemBean(id: Long, title: String, content: String) :
    FAQItemBean(id, FAQStyle.FEEDBACK_DESC, title, content)