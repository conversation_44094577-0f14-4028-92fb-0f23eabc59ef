<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item app:state_disable="true">
        <shape android:shape="oval">
            <solid android:color="@color/color_background_highlight_1_disable"/>
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/color_background_highlight_1_pressed"/>
        </shape>
    </item>

    <item>
        <shape android:shape="oval">
            <solid android:color="@color/color_background_highlight_1_default"/>
        </shape>
    </item>

</selector>