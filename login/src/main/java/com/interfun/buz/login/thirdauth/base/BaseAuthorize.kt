package com.interfun.buz.login.thirdauth.base

import android.app.Activity
import android.content.Context
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultCaller
import androidx.fragment.app.FragmentActivity
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.utils.TraceIdGenerator
import com.interfun.buz.login.thirdauth.bean.AuthResult
import com.interfun.buz.login.thirdauth.bean.AuthUserInfoBean
import com.interfun.buz.login.utils.BuzAuthTracker

/**
 * 文件名：BaseAuthorize
 * 作用：封装授权的基本操作，如打点
 */
abstract class BaseAuthorize(val activityResultCaller: ActivityResultCaller) : IAuthorize {
    private val TAG = "BaseAuthorize"


    override suspend fun authorize(activity: Activity): AuthResult {
        val traceId = TraceIdGenerator.getTraceIdUUID()
        postAuthCallEvent(traceId)
        val authResult = getAuthUserInfoBean(activity, traceId)
        return authResult
    }

    abstract suspend fun getAuthUserInfoBean(activity: Activity, traceId: String): AuthResult

    override fun clearAccount(context: Context) {

    }

    /**
     * 打点授权拉起事件
     */
    private fun postAuthCallEvent(traceId: String) {
        BuzAuthTracker.onBuzAuthCall(getPlatformType(), traceId)
    }

    fun postAuthSuccessEvent(traceId: String, openId: String?, isEmptyToken:Boolean) {
        BuzAuthTracker.onBuzAuthSuccess(getPlatformType(), traceId, openId, isEmptyToken)
    }

    fun postAuthFailEvent(traceId: String, errCode: Int, errMsg: String) {
        BuzAuthTracker.onBuzAuthFail(getPlatformType(),traceId, errCode, errMsg)
    }

     fun buildErrorAuthResult(errCode: Int): AuthResult {
        return AuthResult(AuthUserInfoBean(), errCode)
    }
}