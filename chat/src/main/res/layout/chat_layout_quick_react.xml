<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:id="@+id/viewBg"
        android:layout_width="0dp"
        android:layout_height="@dimen/chat_dimen_qr_bg_height"
        android:layout_marginTop="8dp"
        android:background="@drawable/chat_bg_quick_react"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/spaceEnd"
        app:layout_constraintStart_toStartOf="@+id/spaceStart"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStart"
        android:layout_width="6dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

    <TextView
        android:id="@+id/tvEmoji"
        style="@style/text_label_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/color_text_white_secondary"
        android:gravity="center_vertical"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintStart_toEndOf="@+id/spaceStart"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        app:layout_constraintWidth_min="16dp"
        tools:text="😄😄2" />

    <ImageView
        android:id="@+id/ivPlaying"
        android:layout_width="12dp"
        android:layout_height="20dp"
        android:gravity="center"
        android:layout_marginStart="3dp"
        android:src="@drawable/common_ic_voice_moji_play"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintStart_toEndOf="@+id/tvEmoji"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

    <org.libpag.PAGView
        android:id="@+id/pagPlaying"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/ivPlaying"
        app:layout_constraintEnd_toEndOf="@+id/ivPlaying"
        app:layout_constraintStart_toStartOf="@+id/ivPlaying"
        app:layout_constraintTop_toTopOf="@+id/ivPlaying" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceEnd"
        android:layout_width="8dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintStart_toEndOf="@+id/ivPlaying"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

</merge>