package com.interfun.buz.chat.group.view.itemdelegate

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.view.fragment.InviteDialogFragment

/**
 * @Desc 底部占位
 * @Author:<EMAIL>
 * @Date: 2022/9/15
 */
class InviteEmptyPlaceholderItemView: ItemViewBinder<InviteDialogFragment.BottomSpace, InviteEmptyPlaceholderItemView.MyViewHolder>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): MyViewHolder {
        return MyViewHolder(inflater.inflate(R.layout.online_chat_invite_empty_placeholder, parent, false))
    }

    override fun onBindViewHolder(holder: MyViewHolder, item: InviteDialogFragment.BottomSpace) {

    }

    inner class MyViewHolder constructor(holderItemView: View): RecyclerView.ViewHolder(holderItemView){

    }
}