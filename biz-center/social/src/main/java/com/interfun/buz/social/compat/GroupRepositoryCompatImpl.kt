package com.interfun.buz.social.compat

import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.eventbus.QueryGroupInfoSuccessEvent
import com.interfun.buz.common.manager.cache.group.GroupRepositoryCompat
import com.interfun.buz.social.SocialBizCenterMediator
import com.interfun.buz.social.di.SocialSingletonEntryPoint
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.launch

@Deprecated("请使用 GroupRepository 代替")
class GroupRepositoryCompatImpl : GroupRepositoryCompat {
    companion object {
        /**
         * @deprecated Use hilt to inject a GroupRepository in UserComponent instead.
         *
         * Don't keep the reference of this object,because it will be change when user has been changed.
         * Should always use [groupRepository] to get the UserRepository.
         */
        @Deprecated("兼容旧代码，新代码请使用hilt inject GroupRepository")
        val groupRepository
            get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
                SocialBizCenterMediator.appContext
            ).groupRepository()
    }

    override fun getGroupInfoBeanById(groupId: Long): GroupInfoBean? {
        val groupRepository = groupRepository
        val cache = groupRepository.getGroupCompositeNotSuspend(groupId)?.toOldGroupInfoBean()
        if (cache == null){
            //旧逻辑，需要处理没有缓存的情况,要拉数据，然后拉到后抛出这个事件：QueryGroupInfoSuccessEvent
            groupRepository.getUserScope().launch {
                groupRepository.getGroupCompositeFlow(groupId).collect { groupComposite ->
                    //抛出事件，结束协程
                    QueryGroupInfoSuccessEvent.post(arrayListOf(groupComposite.toOldGroupInfoBean()))
                    throw CancellationException("done")
                }
            }
        }
        return cache
    }

    override suspend fun getGroupInfoBeanByIdSync(groupId: Long): GroupInfoBean? {
        return groupRepository.getGroupComposite(groupId)?.toOldGroupInfoBean()
    }

    override fun getGroupInfoBeanFromCache(groupId: Long): GroupInfoBean? {
        return groupRepository.getGroupCompositeNotSuspend(groupId)?.toOldGroupInfoBean()
    }

    override fun getGroupInfoFromMem(groupId: Long): GroupInfoBean? {
        return groupRepository.getGroupCompositeFromMem(groupId)?.toOldGroupInfoBean()
    }

    override suspend fun getGroupInfoListFromDb(): List<GroupInfoBean>? {
        return groupRepository.getAllJoinedGroupsFromCache().map { it.toOldGroupInfoBean() }
    }
}