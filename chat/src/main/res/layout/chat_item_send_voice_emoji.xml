<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:paddingVertical="@dimen/chat_item_between_same_padding_top"
    tools:background="@color/overlay_grey_10"
    tools:paddingHorizontal="@dimen/chat_list_horizontal_padding">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/emoji"
        app:layout_constraintEnd_toEndOf="@+id/emoji"
        app:layout_constraintStart_toStartOf="@+id/emoji"
        app:layout_constraintTop_toTopOf="@+id/emoji" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvSendFailed"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="@dimen/chat_item_send_failed_loading_icon_margin"
        android:background="@drawable/common_oval_secondary_error"
        android:gravity="center"
        android:text="@string/ic_refresh"
        android:textColor="@color/text_black_main"
        android:textSize="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/emoji"
        app:layout_constraintTop_toTopOf="@+id/emoji"
        tools:ignore="SpUsage"
        app:layout_constraintEnd_toStartOf="@id/emoji"
        tools:visibility="visible" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieLoading"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="@dimen/chat_item_send_failed_loading_icon_margin"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/emoji"
        app:layout_constraintEnd_toStartOf="@+id/emoji"
        app:layout_constraintTop_toTopOf="@+id/emoji"
        app:lottie_rawRes="@raw/chat_lottie_msg_sending_loading"
        app:lottie_loop="true"
        tools:visibility="visible" />

    <com.interfun.buz.chat.common.view.widget.ReplyItemView
        android:layout_width="wrap_content"
        android:id="@+id/replyView"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/chat_dimen_reply_height_at_history_top"
        android:paddingTop="@dimen/chat_dimen_reply_height_at_history_top"
        android:layout_marginStart="@dimen/chat_item_text_horizontal_margin"
        app:layout_constraintWidth_min="@dimen/chat_dimen_mim_width_reply"
        app:layout_constrainedWidth="true"
        android:background="@color/color_background_1_default"
        app:layout_constraintEnd_toEndOf="@id/emoji"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:round_top_left_radius="@dimen/chat_item_bg_radius"
        app:round_top_right_radius="@dimen/chat_item_bg_radius"
        app:round_bottom_left_radius="@dimen/chat_item_bg_radius"
        app:round_bottom_right_radius="@dimen/chat_item_bg_radius_min"/>

    <com.interfun.buz.chat.voicemoji.view.widget.VoiceEmojiChatView
        android:id="@+id/emoji"
        android:layout_width="wrap_content"
        app:layout_constraintWidth_min="124dp"
        app:voiceEmojiMirrorEffect="true"
        android:layout_height="wrap_content"
        app:layout_constraintHeight_min="90dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/replyView"
        app:layout_goneMarginTop="0dp"
         />




    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:id="@+id/replyViewArrows"
        android:layout_marginEnd="5dp"
        android:layout_marginTop="4dp"
        app:srcCompat="@drawable/common_corner_arrow"
        android:visibility="gone"
        android:rotationY="@integer/rtl_rotation"
        app:layout_constraintTop_toTopOf="@id/emoji"
        app:layout_constraintEnd_toStartOf="@id/emoji"
        android:layout_height="wrap_content"/>

</com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout>