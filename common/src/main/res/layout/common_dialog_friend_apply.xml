<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginEnd="10dp"
    app:round_radius="6dp">

    <View
        android:id="@+id/viewBg"
        android:layout_width="0dp"
        android:layout_height="110dp"
        android:background="@color/overlay_grey_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvNickname"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="16dp"
        android:textColor="@color/text_white_main"
        android:textSize="16sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvContent"
        app:layout_constraintEnd_toStartOf="@+id/iftDenied"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Jenny Wilson" />

    <TextView
        android:id="@+id/tvContent"
        style="@style/body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="16dp"
        android:ellipsize="middle"
        android:maxLines="2"
        android:text="@string/invite_you_to_online_chat"
        android:textColor="@color/text_white_default"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintEnd_toStartOf="@+id/iftDenied"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@+id/tvNickname"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftAccept"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/common_oval_basic_primary"
        android:gravity="center"
        android:text="@string/ic_add"
        android:textColor="@color/text_black_main"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftDenied"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/common_oval_secondary_button_secondary"
        android:gravity="center"
        android:text="@string/ic_delete"
        android:textColor="@color/text_white_main"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iftAccept"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />


</com.interfun.buz.base.widget.round.RoundConstraintLayout>