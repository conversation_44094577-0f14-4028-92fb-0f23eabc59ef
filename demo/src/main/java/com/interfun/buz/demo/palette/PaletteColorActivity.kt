package com.interfun.buz.demo.palette

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.View
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.palette.graphics.Palette
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.widget.behavior.SwipeUpDismissBehavior
import com.interfun.buz.common.widget.behavior.SwipeUpDismissBehavior.OnDismissListener
import com.interfun.buz.demo.R
import com.interfun.buz.demo.databinding.ActivityPaletteColorBinding

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2024/4/8
 */
class PaletteColorActivity: BaseBindingActivity<ActivityPaletteColorBinding>() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.btn.click {
            val inputText = binding.etInput.text.toString().trim()
            val drawableName = "palette$inputText"
            val resourceId = getResourceIdByName(context, drawableName)
            if (resourceId == null || resourceId == 0){
                "找不到对应的图片资源".toast()
                return@click
            }
            val bitmap = BitmapFactory.decodeResource(resources, resourceId)
            binding.imageView.setImageBitmap(bitmap)
            Palette.from(bitmap).generate { palette ->
                val defaultColor = appContext.getCompatColor(R.color.transparent)
                val dominantColor = palette?.getDominantColor(defaultColor)
                val mutedColor = palette?.getMutedColor(defaultColor)
                val darkMutedColor = palette?.getDarkMutedColor(defaultColor)
                val vibrantColor = palette?.getVibrantColor(defaultColor)
                binding.viewDominantColor.setBackgroundColor(dominantColor?: defaultColor)
                binding.viewMutedColor.setBackgroundColor(mutedColor?: defaultColor)
                binding.viewDarkMutedColor.setBackgroundColor(darkMutedColor?: defaultColor)
                binding.viewVibrantColor.setBackgroundColor(vibrantColor?: defaultColor)

                binding.tvDominantColorRBG.text = "R:${dominantColor?.red}, G:${dominantColor?.green}, B:${dominantColor?.blue}"
                binding.tvMutedColorRBG.text = "R:${mutedColor?.red}, G:${mutedColor?.green}, B:${mutedColor?.blue}"
                binding.tvDarkMutedColorRBG.text = "R:${darkMutedColor?.red}, G:${darkMutedColor?.green}, B:${darkMutedColor?.blue}"
                binding.tvVibrantColorRBG.text = "R:${vibrantColor?.red}, G:${vibrantColor?.green}, B:${vibrantColor?.blue}"
            }
        }

        val swipeUpDismissBehavior = SwipeUpDismissBehavior.from(binding.dragLayout)
        swipeUpDismissBehavior.listener = object : OnDismissListener{
            override fun onDismiss(view: View) {
                log("PaletteColorActivity", "onDismiss")
                finish()
            }
        }

    }

    private fun getResourceIdByName(context: Context, imageName: String?): Int? {
        return context.resources.getIdentifier(imageName, "drawable", context.packageName)
    }


}