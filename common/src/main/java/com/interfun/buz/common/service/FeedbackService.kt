package com.interfun.buz.common.service

import android.content.Context
import com.alibaba.android.arouter.facade.template.IProvider
import com.buz.idl.common.bean.PopWindow
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.common.widget.dialog.BasePriorityBottomSheetDialogFragment

interface FeedbackService : IProvider {
    fun checkNeedShowFeedbackDialog(): Boolean

    fun getFeedbackDialog(priority: Int = 0): BasePriorityBottomSheetDialogFragment

    fun checkNeedShowCollectEmailDialog(): Boolean

    fun getCollectEmailDialog(): BaseBottomSheetDialogFragment

    fun getResearchStartDialog(priority: Int = 0): BasePriorityBottomSheetDialogFragment

    fun checkNeedShowBuzResearchDialog(): Boolean

    fun getSurveyDialog(priority: Int = 0): BasePriorityBottomSheetDialogFragment

    fun setSurveyData(surveyDialog: BasePriorityBottomSheetDialogFragment,popWindow: PopWindow)

    fun startLivePlaceFeedbackActivity(context: Context): Unit
}