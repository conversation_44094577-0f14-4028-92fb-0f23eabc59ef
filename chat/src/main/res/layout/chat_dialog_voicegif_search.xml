<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background_4_default"
    app:round_top_left_radius="@dimen/bottom_sheet_dialog_top_radius"
    app:round_top_right_radius="@dimen/bottom_sheet_dialog_top_radius">

    <View
        android:id="@+id/vHandle"
        android:layout_width="46dp"
        android:layout_height="6dp"
        android:layout_marginTop="7dp"
        android:background="@drawable/common_rect_secondary_thirdly_radius_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/llSearchBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vHandle">

        <com.interfun.buz.base.widget.round.RoundLinearLayout
            android:id="@+id/llSearch"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:background="@color/color_background_3_default"
            android:orientation="horizontal"
            app:round_radius="16dp">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="20dp"
                android:text="@string/ic_search_input"
                android:textColor="@color/text_white_secondary"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/etSearch"
                style="@style/body"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:background="@color/transparent"
                android:gravity="center_vertical"
                android:hint="@string/search"
                android:imeOptions="actionSearch"
                android:maxLength="50"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/text_white_main"
                android:textColorHint="@color/text_white_disable"
                android:textCursorDrawable="@drawable/common_edittext_cursor" />


            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvClear"
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/ic_clear_input_solid"
                android:textColor="@color/text_white_secondary"
                android:textSize="16sp"
                android:visibility="gone" />
        </com.interfun.buz.base.widget.round.RoundLinearLayout>

        <TextView
            android:id="@+id/tvCancel"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center"
            android:paddingHorizontal="10dp"
            android:text="@string/cancel"
            android:textColor="@color/text_white_default"
            tools:visibility="visible" />
    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginHorizontal="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llSearchBar"
        app:srlEnableNestedScrolling="false">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvVoiceGifList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:splitMotionEvents="false" />

        <com.interfun.buz.common.widget.view.loading.CircleLoadingWithNoMoreRefreshFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>


    <com.interfun.buz.common.widget.view.network.BuzLoadingView
        android:id="@+id/vLoading"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:visibility="invisible"
        android:layout_marginTop="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llSearchBar" />

    <com.interfun.buz.common.widget.view.EmptyDataView
        android:id="@+id/emptyDataView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        android:layout_marginTop="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llSearchBar" />

    <FrameLayout
        android:id="@+id/flPreview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

</com.interfun.buz.base.widget.round.RoundConstraintLayout>