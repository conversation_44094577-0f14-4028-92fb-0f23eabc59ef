<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <!--通用button内部元素的颜色-->
    <!--primary类型下各种状态颜色-->
    <item android:color="@color/color_foreground_onColor_1_disable" app:state_color_primary="true" app:state_disable="true"/>
    <item android:color="@color/color_foreground_onColor_1_pressed" app:state_color_primary="true" android:state_pressed="true"/>
    <item android:color="@color/color_foreground_onColor_1_default" app:state_color_primary="true"/>

    <!--main类型下各种状态颜色-->
    <item android:color="@color/color_foreground_onLight_disable" app:state_color_main="true" app:state_disable="true"/>
    <item android:color="@color/color_foreground_onLight_pressed" app:state_color_main="true" android:state_pressed="true"/>
    <item android:color="@color/color_foreground_onLight_default" app:state_color_main="true" />

    <!--secondary类型下各种状态颜色-->
    <item android:color="@color/color_foreground_onLight_disable" app:state_color_secondary="true" app:state_disable="true"/>
    <item android:color="@color/color_foreground_onLight_pressed" app:state_color_secondary="true" android:state_pressed="true"/>
    <item android:color="@color/color_foreground_onLight_default" app:state_color_secondary="true" />

    <!--tertiary类型下各种状态颜色-->
    <item android:color="@color/color_foreground_neutral_important_disable" app:state_color_tertiary="true" app:state_disable="true"/>
    <item android:color="@color/color_foreground_neutral_important_pressed" app:state_color_tertiary="true" android:state_pressed="true"/>
    <item android:color="@color/color_foreground_neutral_important_default" app:state_color_tertiary="true" />

    <!--consequential类型下各种状态颜色-->
    <item android:color="@color/color_foreground_consequential_disable" app:state_color_consequential="true" app:state_disable="true"/>
    <item android:color="@color/color_foreground_consequential_pressed" app:state_color_consequential="true" android:state_pressed="true"/>
    <item android:color="@color/color_foreground_consequential_default" app:state_color_consequential="true" />

    <!--兜底-->
    <item android:color="@color/color_foreground_onColor_1_default" />

</selector>