<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp"
    android:layout_marginVertical="10dp"
    android:background="@color/overlay_white_4"
    android:padding="16dp"
    app:round_radius="@dimen/guide_layout_radius_min"
    tools:background="@color/black_100">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLogoTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_security"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        style="@style/text_body_medium"
        android:id="@+id/tvText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:text="@string/group_add_ai_tip"
        android:textColor="@color/text_white_default"
        android:textSize="14sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivLogoTag"
        app:layout_constraintTop_toTopOf="parent" />
</com.interfun.buz.base.widget.round.RoundConstraintLayout>