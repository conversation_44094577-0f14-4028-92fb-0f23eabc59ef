package com.interfun.buz.common.bean.push

import androidx.annotation.IntDef
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.logLineInfo
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.deviceId
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserManager
import org.json.JSONObject
import kotlin.annotation.AnnotationRetention.SOURCE

/**
 * @Desc 通知用户登出
 * "{\"op\":1001,\"sendTimestamp\":1234,\"data\":{\"reason\":1}}"
 * @Author:<EMAIL>
 * @Date: 2022/6/30
 */
class PushGoodBye: PushDataBase() {
    private val TAG = "PushGoodBye"

    override fun handlePushEvent(op: Int, sendTimestamp: Long, data: JSONObject?) {
        data?.let {
            if (it.has("reason")){
                logLineInfo(TAG, logLine = LogLine.LOGIN, "handlePushEvent: data = $data")
                try {
                    val reason = it.getInt("reason")
                    if (it.has("prompt")){
                        val prompt = it.getString("prompt")
                        logInfo(TAG,"handlePushEvent: $prompt")
                        CommonMMKV.loginOutPrompt = prompt
                    }
                    if (UserManager.getLoginNTPTime() > sendTimestamp){
                        logInfo(TAG,"handlePushEvent: sendTimestamp is expire and return")
                        return
                    }
                    //账号被封禁的情况直接退出登录
                    if (reason == LogoutReason.REASON_ACCOUNT_BANNED){
                        logLineInfo(TAG, logLine = LogLine.LOGIN, "handlePushEvent: handle logout by REASON_ACCOUNT_BANNED")
                        UserManager.logout(reason)
                        return
                    }
                    if (it.has("deviceId") && reason == LogoutReason.REASON_MULTI_KICK_OUT){
                        val newDeviceId = it.getString("deviceId")
                        if (newDeviceId == deviceId){
                            logInfo(TAG,"handlePushEvent:deviceId is mySelf and return")
                            return
                        }
                    }
                    logLineInfo(TAG, logLine = LogLine.LOGIN, "handlePushEvent: handle logout")
                    UserManager.logout()
                } catch (e: Exception) {
                    e.log(TAG)
                }
            }
        }
    }

    @Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.TYPE)
    @IntDef(
        LogoutReason.REASON_ON_OWN,
        LogoutReason.REASON_MULTI_KICK_OUT,
        LogoutReason.REASON_ACCOUNT_BANNED
        )
    @Retention(SOURCE)
    annotation class LogoutReason{
        companion object {
            const val REASON_ON_OWN = 1 //主动退出
            const val REASON_MULTI_KICK_OUT = 2 //多设备互踢
            const val REASON_ACCOUNT_BANNED = 3 //账号被封禁
        }
    }
}



