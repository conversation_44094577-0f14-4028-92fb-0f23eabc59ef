package com.interfun.buz.chat.voicepanel.model

import coil.size.Size
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.TwoParamCallback
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.chat.voicepanel.view.widget.previewview.VoiceItemPreviewView

/**
 * Represents necessary data for previewing different types of Voicemoji
 */
sealed interface PreviewData

class VoiceEmojiPreview(
    val voiceEmojiEntity: VoiceEmojiEntity, // VE/盲盒表情
    val sendTo: String?, // 发送对象/群名称
    val collectId: Long? = null, // 来自收藏夹时，长按收藏的VE/VG，需要传入收藏id
    val location: IntArray, // 记录预览页展示时，出现的动画位置和关闭预览时的动画
    val size: Size, // 记录预览页展示时，出现的动画大小和关闭预览时的大小
    val onSendClickCallback: OneParamCallback<SendVoiceItemData>, // 发送表情点击回调
    val onOperateCollectCallback: TwoParamCallback<CollectVoiceItemData, VoiceItemPreviewView>// 收藏表情增删操作回调
) : PreviewData

class OpenBlindBoxPreview(
    val voiceEmojiEntity: VoiceEmojiEntity, // 盲盒表情
    val openBoxAnimUrl: String?, // 打开盲盒动画
    val location: IntArray, // 记录预览页展示时，出现的动画位置和关闭预览时的动画
    val size: Size, // 记录预览页展示时，出现的动画大小和关闭预览时的大小
    val startedDays: Int // 盲盒激活任务天数
) : PreviewData


class VoiceGifPreview(
    val voiceGifEntity: VoiceGifEntity, // VE/盲盒表情
    val sendTo: String?, // 发送对象/群名称
    val collectId: Long? = null, // 来自收藏夹时，长按收藏的VE/VG，需要传入收藏id
    val location: IntArray, // 记录预览页展示时，出现的动画位置和关闭预览时的动画
    val size: Size, // 记录预览页展示时，出现的动画大小和关闭预览时的大小
    val onSendClickCallback: OneParamCallback<SendVoiceItemData>, // 发送表情点击回调
    val onOperateCollectCallback: TwoParamCallback<CollectVoiceItemData, VoiceItemPreviewView> // 收藏表情增删操作回调
) : PreviewData