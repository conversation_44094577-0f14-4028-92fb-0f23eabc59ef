package com.interfun.buz.im.ktx

import android.net.Uri
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.interfun.buz.assertutil.buzAssertMain
import com.interfun.buz.assertutil.buzAssertNotMain
import com.interfun.buz.base.ktx.TypefaceSpanCompat
import com.interfun.buz.base.ktx.asMillisecond
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.asUri
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.secondsToTime
import com.interfun.buz.base.ktx.toSafeLong
import com.interfun.buz.base.ktx.topActivity
import com.interfun.buz.base.manager.download.CommonDownloadManager
import com.interfun.buz.base.manager.download.MediaType
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.common.MentionDisplaySpanOption
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.im.PrepareMsgFailedReason
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.getDisplayName
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.ktx.isRobotSuspend
import com.interfun.buz.common.ktx.supportEncrypt
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.ktx.userName
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.AsrSettingManager.Companion.defaultAsrFunction
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.utils.language.TranslateLanguageManager
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.R
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.im.entity.*
import com.interfun.buz.im.entity.translation.TranslateConstant
import com.interfun.buz.im.entity.translation.TranslateState
import com.interfun.buz.im.ktx.BuzSendingState.Canceled
import com.interfun.buz.im.ktx.BuzSendingState.Compressing
import com.interfun.buz.im.ktx.BuzSendingState.CompressingCanceled
import com.interfun.buz.im.ktx.BuzSendingState.Failed
import com.interfun.buz.im.ktx.BuzSendingState.Sending
import com.interfun.buz.im.ktx.BuzSendingState.Succeed
import com.interfun.buz.im.ktx.BuzSendingState.UploadPaused
import com.interfun.buz.im.ktx.BuzSendingState.Uploading
import com.interfun.buz.im.message.*
import com.interfun.buz.im.util.IMMsgIdentity
import com.interfun.buz.im.util.toMsgIdentity
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.conversation.IConversation
import com.lizhi.im5.sdk.conversation.IM5Conversation
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.CryptStatus
import com.lizhi.im5.sdk.message.IM5CheckPerferredUrl
import com.lizhi.im5.sdk.message.IM5Message
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MessageStatus
import com.lizhi.im5.sdk.message.MessageStatus.CANCEL
import com.lizhi.im5.sdk.message.MessageStatus.SENDING
import com.lizhi.im5.sdk.message.MessageStatus.SUCCESS
import com.lizhi.im5.sdk.message.MsgDirection
import com.lizhi.im5.sdk.message.model.*
import com.lizhi.im5.sdk.message.model.UploadState.FAILED
import com.lizhi.im5.sdk.message.model.UploadState.INIT
import com.lizhi.im5.sdk.message.model.UploadState.IN_PROGRESS
import com.lizhi.im5.sdk.message.model.UploadState.PAUSED
import com.lizhi.im5.sdk.utils.IM5MsgUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.internal.toLongOrDefault
import org.json.JSONArray
import java.io.File
import java.util.concurrent.TimeUnit

fun IMessage.dispatch(
    wtVoiceMessage: (IM5VoiceMessage.() -> Unit)? = null
) {
    content?.dispatch(wtVoiceMessage)
}

fun IM5MsgContent.dispatch(
    wtVoiceMessage: (IM5VoiceMessage.() -> Unit)? = null
) {
    when (this) {
        is IM5VoiceMessage -> {
            wtVoiceMessage?.invoke(this)
        }
    }
}

val IMessage.isPrivate get() = conversationType == IM5ConversationType.PRIVATE
val IMessage.isGroup get() = conversationType == IM5ConversationType.GROUP
val IMessage.isChannel get() = conversationType == IM5ConversationType.CHANNEL

/**
 * IM发送人是否为机器人。仅针对私聊
 * 因为getUserRelationInfoByUid会调用到数据库禁止主线程调用
 */
suspend fun IMessage.targetIsRobotOnPrivate(): Boolean {
    if (this.isGroup) {
        return false
    } else {
        val uid = IM5MsgUtils.getConvTargetId(this).toLongOrNull()
        if (uid != null) {
            buzAssertNotMain()
            if (UserRelationCacheManager.getUserRelationInfoByUid(uid)?.isRobot == true) {
                return true
            }
        }
    }
    return false
}

val IMessage.isSend get() = messageDirection == MsgDirection.SEND
val IMessage.isReceive get() = messageDirection == MsgDirection.RECEIVE

val IMessage.isWTVoiceMessage
    get() = msgType == IMType.TYPE_VOICE_MSG
            || msgType == IMType.TYPE_WALKIE_TALKIE_VOICE
            || msgType == IMType.TYPE_VOICE_TEXT
            || msgType == IMType.TYPE_VOICE_TEXT_NEW

fun IMessage.isRecallWTAutoPlayMessage(type: Int): Boolean {
    return type == IMType.TYPE_VOICE_MSG
            || type == IMType.TYPE_WALKIE_TALKIE_VOICE
            || type == IMType.TYPE_VOICE_TEXT
            || type == IMType.TYPE_VOICE_TEXT_NEW
            || type == IMType.TYPE_VOICE_EMOJI
            || type == IMType.TYPE_VOICE_GIF
}


val IMessage.isCommandMessage get() = msgType == IMType.TYPE_COMMAND

val IMessage.isLeaveVoiceMessage get() = this.msgType == 2
val IMessage.isLocationMsg get() = this.msgType == IMType.TYPE_LOCATION_MSG
val IMessage.isOnAirDanmaku get() = this.msgType == IMType.TYPE_ONIAR_DANMAKU ||this.msgType == IMType.TYPE_LIVEPLACE_DANMAKU

val IMessage.isVoiceMsg get() = content is IM5VoiceMessage
val IMessage.isLivePlaceShareMsg get() = content is BuzLivePlaceShareMessage


// see [MediaTextMsg]
val IMessage.isMediaTextMsg get() = this.msgType == IMType.TYPE_MEDIA_TEXT || this.msgType == IMType.TYPE_MEDIA_TEXT_NEW

val IMessage.isRealTimeCallMsg get() = this.msgType == IMType.TYPE_REAL_TIME_CALL

val IMessage.isRecallMsg get() = msgType == IMType.TYPE_RECALL_MSG
val IMessage.isTextMsg get() = content is IM5TextMessage

// 是否需要气泡（目前文本和语音消息带气泡）
val IMessage.hasBubble get() = isMediaTextMsg || isVoiceMsg || isTextMsg

fun IMessage.getConversationId(): String {
    return IM5MsgUtils.getConvTargetId(this) ?: "0"
}

fun IMessage.getConvTargetIdLong(): Long {
    return getConversationId().toLongOrDefault(0L)
}

fun List<IMessage>.toListString(): String {
    return "[${
        this.joinToString {
            "IMessage:{msgId:${it.msgId},serMsgId:${it.serMsgId},msgType:${it.msgType}}"
        }
    }]"
}

val IMessage.userId get() = fromId?.toLongOrNull()


val IMessage.localExtraKey get() = "${conversationType.value}-$msgId"

val MediaMessageContent.url: String
    get() {
        val localFile = if (localPath?.isNotEmpty() == true) File(localPath) else null
        val useLocalPath = localFile?.exists() ?: false
        return if (useLocalPath) Uri.fromFile(localFile).toString() else (remoteUrl ?: "")
    }

val MediaMessageContent.urlPair: Pair<String, IM5CheckPerferredUrl>
    get() {
        val perferredUrl = checkPerferredUrl()
        val path = when (perferredUrl) {
            IM5CheckPerferredUrl.Local -> {
                Uri.fromFile(File(localPath)).toString()
            }
            IM5CheckPerferredUrl.Remote -> {
                remoteUrl
            }
            else -> {
                ""
            }
        }
        return Pair<String, IM5CheckPerferredUrl>(path, perferredUrl)
    }

fun IMessage.getAudioUri(): Pair<Uri, IM5CheckPerferredUrl>? {
    val (audioUrl, urlType) = (content as? IM5VoiceMessage)?.urlPair ?: return null
    val uri = if (urlType == IM5CheckPerferredUrl.Remote) {
        if (isVoiceMojiMessage) {
            CommonDownloadManager.getDownloadCache(audioUrl, MediaType.VoiceEmojiSound)?.let {
                Uri.fromFile(it)
            } ?: audioUrl.asUri()
        } else audioUrl.asUri()
    } else audioUrl.asUri()
    return uri to urlType
}

//local extra 这里太乱了，用一个锁也不一定能保证没问题，现在localextra都是全量更新，没有统一，im内部还是异步的,先简单处理一部分异步情况
suspend fun IMessage.setPlayedMessage() {
    if (isSendType) {
        return
    }
    if (localExtraModel().isSetMsgPlayed) {
        return
    }

    IMAgent.setPlayedMessage(
        conversationType,
        getConversationId(),
        msgId.toString()
    )
    logInfo("IM-updateLocalExtraField", "setPlayedMessage true,msgId:${msgId}")
    updateLocalExtraField {
        isSetMsgPlayed = true
    }
}
 private suspend fun IMessage.updateLocalExtraField(apply:IMMessageLocalExtra.() -> Unit) {
    val updateFieldExtra = IMMessageLocalExtra()
    apply(updateFieldExtra)
    IMAgent.updateLocalExtraField(this, updateFieldExtra)

}
/**
 * make IMessage localExtra field mark as 'LISTENED' state
 */
suspend fun IMessage.makeVoiceMsgListened() {
    makeVoiceMsgReceivedStatus(VoiceMsgReceivedStatus.LISTENED)
}

suspend fun IMessage.makeVoiceMsgReceivedStatus(
    receivedStatus: VoiceMsgReceivedStatus,
) {
    if (this.content !is IM5VoiceMessage) {
        return
    }

    updateLocalExtraField {
        showAsrText = defaultAsrFunction(this@makeVoiceMsgReceivedStatus)
        voiceMsgReceivedStatus = receivedStatus.value
    }
}

suspend fun IMessage.updateLocalSendNtpTime(
    ntpTime: Long
) {
    updateLocalExtraField {
        msgSendNtpTime = ntpTime
    }
}

suspend fun IMessage.updateReply(
    reply: Boolean
) {
    updateLocalExtraField {
        isReply = reply
    }
}


suspend fun IMessage.updateLocalAsrInfo(
    asrText: String? = null,
    showAsrText: Boolean? = null,
    from: String = ""
) {
    logInfo("updateLocalAsrInfo", "msgId: ${serMsgId}, from: $from, showAsrText:$showAsrText")
    updateLocalExtraField {
        if (asrText != null) {
            this.asrText = asrText
            this.asrTimeout = false
        }
        if (showAsrText != null) {
            this.showAsrText = showAsrText
        }
    }
}


suspend fun IMessage.updateCloseAsrState(close: Boolean) {
    logInfo(
        "updateLocalAsrInfo",
        "msgId: ${serMsgId}, close: $close"
    )
    updateLocalExtraField {
        this.closeAsrPreview = close
    }
}

suspend fun IMessage.setAsrTimeout(timeout: Boolean) {
    logInfo(
        "updateLocalAsrInfo",
        "setAsrTimeout msgId: ${serMsgId}, timeout: $timeout"
    )
    updateLocalExtraField {
        this.asrTimeout = timeout
    }
}

fun IMessage.isAsrTimeout(): Boolean {
    return if (content is IM5VoiceMessage) {
        localExtraModel().asrTimeout
    } else {
        false
    }
}

suspend fun IMessage.setTranslateState(state: TranslateState) {
    logInfo(
        "TranslateMessageManager",
        "setTranslateState msgId: ${serMsgId}, state: $state"
    )
    updateLocalExtraField {
        this.translateState = state.ordinal
    }
}

val IMessage.isTranslateFail
    get() =
        when (content) {
            is BuzVoiceMsg -> {
                localExtraModel().translateState == TranslateState.TranslateFail.ordinal
            }

            is BuzTextMsg -> {
                localExtraModel().translateState == TranslateState.TranslateFail.ordinal
            }

            else -> {
                false
            }
        }

suspend fun IMessage.updateShowTranslateText(@ShowTranslateTextOp op: Int) {
    logInfo(
        "updateShowTranslateText",
        "msgId: ${serMsgId}, isShow: $op"
    )
    updateLocalExtraField {
        this.showTranslateText = op
    }
}


fun IMessage.isManuOpenTranslation(): Boolean {
    return localExtraModel().showTranslateText == ShowTranslateTextOp.MANUAL_OPEN
}

fun IMessage.isManuCloseTranslation(): Boolean {
    return localExtraModel().showTranslateText == ShowTranslateTextOp.MANUAL_CLOSE
}


val IMessage.isCloseAsrPreview
    get() =
        if (content is IM5VoiceMessage) {
            localExtraModel().closeAsrPreview
        } else {
            false
        }

val IMessage.showAsrText
    get() =
        if (content is IM5VoiceMessage) {
            localExtraModel().showAsrText
        } else {
            false
        }


val IM5MsgContent.detectLanguageCode: String?
    get() {
        return when (this) {
            is BuzVoiceMsg -> {
                detectLanguageCode
            }

            is BuzTextMsg -> {
                detectLanguageCode
            }

            else -> null
        }
    }

val IM5MsgContent.isDetectTranslateCodeEdit: Boolean
    get() {
        return when (this) {
            is BuzVoiceMsg -> editType == MessageEditType.DETECT_LANGUAGE_CODE_EDIT
            is BuzTextMsg -> editType == MessageEditType.DETECT_LANGUAGE_CODE_EDIT
            else -> false
        }
    }

fun IMessage.isVoiceListened(): Boolean {
    return if (content is IM5VoiceMessage) {
        localExtraModel().voiceMsgReceivedStatus == VoiceMsgReceivedStatus.LISTENED.value
    } else {
        true
    }
}

fun IMessage.isRead(conv: IConversation): Boolean {
    return if (this is IM5Message) {
        if (conv is IM5Conversation) {
            this.createTime <= conv.readTime
        } else {
            this.seq <= conv.readSeq
        }
    } else this.isVoiceListened()
}

/**
 * 如果是语音文本消息,判断是否需要自动播放, 默认返回需要自动播放
 */
fun IMessage.isNeedAutoPlay(): Boolean {
    return if (content is VoiceTextMsg) {
        (content as VoiceTextMsg).autoPlay
    } else true
}

fun IMessage.setEncryptMessage() {
    if (isPrivate && targetId.toSafeLong().supportEncrypt()) {
        enableEncrypt(isPrivate && ABTestManager.getE2EESendEnable())
    }
}

val IMessage.isSendType get() = this.messageDirection == MsgDirection.SEND


fun IMessage.isSameMessage(message: IMessage?): Boolean {
    if (message == null) {
        return false
    }
    if (this.msgId == message.msgId && this.conversationType == message.conversationType) {
        return true
    }
    return false
}

fun IMessage.obtainAsrText(): String?{
    val asrText = (this.content as? BuzVoiceMsg)?.asrText
    return if (!asrText.isNullOrEmpty()) {
        asrText
    }else{
        localExtraModel().asrText
    }
}

fun IMessage.localExtraModel(): IMMessageLocalExtra {
    return IMAgent.getLocalExtraModel(this)
}

/**
 * get im conversation unreadCount
 */
suspend fun getMsgUnreadCountByTargetId(lifecycleOwner: LifecycleOwner, targetId: String): Int {
    return withContext(Dispatchers.IO) {
        val convUnreadCount = IMAgent.getUnreadCount(lifecycleOwner, targetId)
        logDebug("IMKtx", "getMsgUnreadCountByTargetId targetId = $targetId, convUnreadCount = $convUnreadCount")
        convUnreadCount
    }
}

val IMessage.isTextMessage: Boolean get() = this.msgType == IMType.TYPE_TEXT_MSG
val IMessage.isImageMessage: Boolean get() = this.msgType == IMType.TYPE_IMAGE_MSG
val IMessage.isVideoMessage: Boolean get() = this.msgType == IMType.TYPE_VIDEO_MSG
val IMessage.isFileMessage: Boolean get() = this.msgType == IMType.TYPE_FILE_MSG
val IMessage.isVoiceMojiMessage: Boolean get() = this.msgType == IMType.TYPE_VOICE_EMOJI || this.msgType == IMType.TYPE_VOICE_EMOJI_IMG
val IMessage.isVoiceGifMessage: Boolean get() = this.msgType == IMType.TYPE_VOICE_GIF

val IMessage.isRecallVideoMessage: Boolean
    get() = this.msgType == IMType.TYPE_RECALL_MSG
            && this.content is IM5RecallMessage
            && (this.content as IM5RecallMessage).orgType == IMType.TYPE_VIDEO_MSG.toLong()

val IMessage.isRecallImageMessage: Boolean
    get() = this.msgType == IMType.TYPE_RECALL_MSG
            && this.content is IM5RecallMessage
            && (this.content as IM5RecallMessage).orgType == IMType.TYPE_IMAGE_MSG.toLong()

val IMessage.hasMentionedMe: Boolean
    get() {
        return content?.hasMentionedMe ?: false
    }

fun IMessage.mentionedInfoText(option: MentionDisplaySpanOption = MentionDisplaySpanOption()): CharSequence? {
    return content?.mentionedInfoText(option)
}

val IM5MsgContent.hasMentionedMe: Boolean
    get() {
        return when (this) {
            is BuzTextMsg -> isMentionedMeByMap(mentionedMaps)
            is VoiceTextMsg -> isMentionedMeByList(mentionedUsers)
            is BuzVoiceMsg -> isMentionedMeByList(mentionedUsers)
            else -> false
        }
    }

suspend fun IM5MsgContent.mentionedRobotId(): String? {
    return when (this) {
        is BuzTextMsg -> {
            this.mentionedMaps?.entries
                ?.firstOrNull { it.value.userId.isRobotSuspend() }
                ?.value?.userId?.toString()
        }
        is VoiceTextMsg -> {
            this.mentionedUsers
                ?.firstOrNull { it.userId.isRobotSuspend() }
                ?.userId?.toString()
        }
        is BuzVoiceMsg -> {
            this.mentionedUsers
                ?.firstOrNull { it.userId.isRobotSuspend() }
                ?.userId?.toString()
        }
        else -> null
    }
}

val IM5MsgContent.hasMentionedRobot: Boolean
    get() {
        return mentionedUserIdsList.any { userId ->
            UserRelationCacheManager.getUserRelationInfoByUid(userId)?.isRobot.getBooleanDefault()
        }
    }

/**
 * Replaces all mentioned userIds in the text with @username
 *
 * Example:
 * * "Hello, 【52684129451321411】! Come to my house 【5123545217239171231】"
 *
 * Results:
 * * "Hello, @Harry! Come to my house @Lily"
 *
 * The replaced name will be remark if exists
 *
 * @param option To style the text span (e.g. clickable, colour, background)
 * @see MentionDisplaySpanOption
 */
fun IM5MsgContent.mentionedInfoText(option: MentionDisplaySpanOption = MentionDisplaySpanOption()): CharSequence {
    val msgContent = (this as? BuzTextMsg) ?: return ""
    val mentionedText = msgContent.textMentioned
    val mentionedMap = msgContent.mentionedMaps
    val content = if (mentionedText.isNullOrEmpty() || mentionedMap.isNullOrEmpty()) {
        msgContent.text
    } else {
        val spannableStringBuilder = SpannableStringBuilder()
        var i = 0
        while (i < mentionedText.length) {
            var replaced = false
            mentionedMap.forEach { (beReplacedText, info) ->
                if (mentionedText.startsWith(beReplacedText, i)) {
                    val isMentioningAll = info.isMentioningAll == true
                    val isMyself = info.userId.isMe()
                    val displayName = when {
                        isMentioningAll -> "All"
                        isMyself && option.displayAsYou -> R.string.you.asString()
                        isMyself -> UserSessionManager.userName
                        else -> {
                            val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(info.userId)
                            userInfo?.getDisplayName() ?: info.userName
                        }
                    }
                    val displayText = "@$displayName"
                    val formattedDisplayText = if (LanguageManager.isArLanguage()) {
                        // Wrap mention with left-to-right marker (LRM) to maintain correct order
                        // To ensure @demo is always displayed as LTR even inside RTL setting
                        "\u200E$displayText\u200E"
                    } else {
                        displayText
                    }
                    if (option.allowClick) {
                        val clickableSpan = object : ClickableSpan() {
                            override fun onClick(widget: View) {
                                if (isMyself || isMentioningAll) return
                                val activity = when (val ctx = option.context) {
                                    is FragmentActivity -> ctx
                                    else -> topActivity as? FragmentActivity ?: return
                                }
                                routerServices<ContactsService>().value!!.getProfileDialog(
                                    userId = info.userId,
                                    source = FriendApplySource.CLICK_ADDRESSED_USER,
                                    businessId = null,
                                    trackerSource = option.trackerSource,
                                    isFromGroup = true
                                ).showDialog(activity)
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                ds.color = option.mentionTextColor
                            }
                        }
                        spannableStringBuilder.append(
                            formattedDisplayText,
                            clickableSpan,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    } else {
                        spannableStringBuilder.append(formattedDisplayText)
                    }
                    val isBold = isMyself || isMentioningAll || option.isSendingSide == true
                    if (isBold) {
                        // Extra bold, if the message mentioning me, or if the received message is mentioning all users
                        val isExtraBold = isMyself || (isMentioningAll && option.isSendingSide == false)
                        val boldTypeFace = if (isExtraBold) FontUtil.fontExtraBold else FontUtil.fontBold
                        spannableStringBuilder.setSpan(
                            TypefaceSpanCompat(boldTypeFace!!),
                            spannableStringBuilder.length - formattedDisplayText.length,
                            spannableStringBuilder.length,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    // RoundedBackgroundSpan is temporarily disabled because handling @username ellipsize
                    // for long usernames within a background span is technically difficult.
                    // If re-enabled, ensure proper text truncation while maintaining background visibility.
//                    if (isMyself && null != option.roundedBg) {
//                        spannableStringBuilder.setSpan(
//                            RoundedBackgroundSpan(
//                                option.roundedBg!!,
//                                option.roundedBgTextColor,
//                                0.9f
//                            ),
//                            spannableStringBuilder.length - displayText.length,
//                            spannableStringBuilder.length,
//                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
//                        )
//                    }
                    i += beReplacedText.length
                    replaced = true
                    return@forEach
                }
            }
            if (!replaced) {
                spannableStringBuilder.append(mentionedText[i])
                i++
            }
        }
        return spannableStringBuilder
    }
    return content
}

val IMessage.mentionedInfoWithPlaceholders: String?
    get() {
        return content?.mentionedInfoWithPlaceholders
    }

/**
 * Replaces all mentioned usernames in the text with indexed placeholders
 *
 * Example:
 * * Given:
 *  textMentioned = "【5375127826238362751】 hello 【5363630561707587199】 welcome to 【5375127826238362751】 his house!"
 *
 * The result will be:
 *  "<span class='notranslate'>【%0】</span> hello <span class='notranslate'>【%1】</span> welcome to <span class='notranslate'>【%2】</span> his house!"
 *
 * The string inside the <span> tag will not be translated
 * - If `textMentioned` or `mentionedMaps` is empty, returns the original text
 *
 * @return A string where all mentioned usernames are replaced with indexed placeholders
 */
val IM5MsgContent.mentionedInfoWithPlaceholders: String
    get() {
        val msgContent = (this as? BuzTextMsg) ?: return ""
        val mentionedText = msgContent.textMentioned ?: msgContent.text
        val mentionedMap = msgContent.mentionedMaps
        if (mentionedText.isNullOrEmpty() || mentionedMap.isNullOrEmpty()) {
            return msgContent.text ?: ""
        }
        val spannableStringBuilder = SpannableStringBuilder()
        var i = 0
        var index = 0

        while (i < mentionedText.length) {
            var replaced = false
            for ((key, _) in mentionedMap) {
                if (mentionedText.startsWith(key, i)) {
                    val displayText = "<span class='notranslate'>[${"%$index"}]</span>"
                    spannableStringBuilder.append(displayText)
                    i += key.length
                    replaced = true
                    index++
                    break
                }
            }

            if (!replaced) {
                spannableStringBuilder.append(mentionedText[i])
                i++
            }
        }
        return spannableStringBuilder.toString()
    }

val IMessage.mentionedUserIdsList: List<Long>?
    get() {
        return content?.mentionedUserIdsList
    }

val IM5MsgContent.mentionedUserIdsList: List<Long>
    get() {
        return when (this) {
            is BuzTextMsg -> mentionedMaps?.values?.map { it.userId } ?: emptyList()
            is VoiceTextMsg -> mentionedUsers?.map { it.userId } ?: emptyList()
            is BuzVoiceMsg -> mentionedUsers?.map { it.userId } ?: emptyList()
            else -> emptyList()
        }
    }

val IMessage.mentionedUsers: List<MentionedUser>?
    get() {
        return content?.mentionedUsers
    }

val IM5MsgContent.mentionedUsers: List<MentionedUser>?
    get() {
        return when (this) {
            is BuzTextMsg -> mentionedMaps?.map { it.value }
            is VoiceTextMsg -> mentionedUsers?.map { it }
            is BuzVoiceMsg -> mentionedUsers?.map { it }
            else -> emptyList()
        }
    }

val IMessage.mentionedUserIdsJsonArray: JSONArray?
    get() {
        val ids = mentionedUserIdsList
        return if (ids == null) {
            null
        } else {
            val array = JSONArray()
            ids.forEach {
                array.put(it)
            }
            array
        }
    }

private fun isMentionedMeByList(list: List<MentionedUser>?): Boolean {
    if (list == null) {
        return false
    }
    for (item in list) {
        if (item.userId == UserSessionManager.uid || item.isMentioningAll == true) {
            return true
        }
    }
    return false
}

private fun isMentionedMeByMap(map: Map<String, MentionedUser>?): Boolean {
    val entries = map?.entries ?: return false
    for ((key, value) in entries) {
        if (value.userId == UserSessionManager.uid || value.isMentioningAll == true) {
            return true
        }
    }
    return false
}

val IMessage.isStatusSuccess get() = serMsgId != null && status == SUCCESS

val IMessage.isSupportForwardType get() = isTextMessage || isImageMessage || isVideoMessage || isLocationMsg || isVoiceGifMessage
        || (isVoiceMojiMessage && !isBlindBox) || isFileMessage

val IMessage.isBlindBox get() = (this.content as? WTVoiceEmojiMsg)?.emojiCategoryType == ImVoiceEmojiCategoryType.BlindBox

fun IMessage.canForwardMsg(): Boolean {
    return isSupportForwardType
            && AppConfigRequestManager.enableMsgForward == true
            && status == SUCCESS
            && !isRecallMsg
}

val IMessage.isSupportReplyType
    get() = isTextMessage
        || isVoiceMojiMessage
        || isVoiceMsg
        || isImageMessage
        || isVideoMessage
        || isLocationMsg
        || isMediaTextMsg
        || isRealTimeCallMsg
        || isFileMessage

val IMessage.isSupportAddFavoritesType
    get() = (isVoiceMojiMessage || isVoiceGifMessage)
            && (this.content as? WTVoiceEmojiMsg)?.emojiCategoryType != ImVoiceEmojiCategoryType.BlindBox

val IMessage.isNonsupportAIThinkingType
    get() = isVoiceGifMessage || isVoiceMojiMessage || isBlindBox

// 是否可以引用回复
fun IMessage.canReplyMsg(): Boolean {
    return isSupportReplyType
            && AppConfigRequestManager.enableReferMsg != false
            && status == SUCCESS
            && !isRecallMsg
}
// 是否可以引用回复
fun IMessage.canAddToFavoritesMsg(): Boolean {
    return isSupportAddFavoritesType
            && !isRecallMsg
}
fun IMessage.getVoiceDurationSecond(): Int {
    val msgContent = (content as? IM5VoiceMessage) ?: return 0
    val duration = msgContent.duration.asMillisecond()
    return if (duration.value in 60000 until 61000) 60 else duration.ceilToSecond
}

fun IMessage.getVoiceDuration(): Int {
    val msgContent = (content as? IM5VoiceMessage) ?: return 0
    val duration = msgContent.duration
    return duration
}


/**
 * 格式化为 "0:00" 的时间格式
 */
fun IMessage.getVoiceDurationFormatString(): String {
    return getVoiceDurationSecond().secondsToTime()
}

fun IMessage.isGroupOfflineMessage(): Boolean {
    return isGroup && (NtpTime.nowForce() - createTime) > TimeUnit.SECONDS.toMillis(
        AppConfigRequestManager.groupMsgAutoPlayTime.toLong())
}

fun IMessage.isRoamMessage(): Boolean {
    return IMAgent.isRoamMessage(this)
}
/**
 * 上传状态
 * https://vocalbeats.sg.larksuite.com/wiki/SJviwalxlikn5QklpsPley69gQh#part-HrRsdjYJdoy0UIxKOLQlzxVzg5f
 */
fun IMessage.mediaUploadInfo(): MediaUploadInfo {
    return when (content) {
        is IM5ImageMessage -> {
            val info = (content as IM5ImageMessage).uploadInfo
            MediaUploadInfo(
                info.getUploadState(),
                info.getUploadedBytes(),
                info.getTotalBytesToUpload()
            )
        }

        is IM5VideoMessage -> {
            val info = (content as IM5VideoMessage).uploadInfo
            MediaUploadInfo(
                info.getUploadState(),
                info.getUploadedBytes(),
                info.getTotalBytesToUpload()
            )
        }

        is BuzFileMessage -> {
            val info = (content as BuzFileMessage).uploadInfo
            MediaUploadInfo(
                info.getUploadState(),
                info.getUploadedBytes(),
                info.getTotalBytesToUpload()
            )
        }

        else -> {
            MediaUploadInfo(UploadState.COMPLETED, 0L, 0L)
        }
    }
}

val IMessage.asrText: String? get() = (this.content as? BuzVoiceMsg)?.asrText



val IMessage.isBuzVoiceMsg get() = content is BuzVoiceMsg

val IM5MsgContent.translateText: String?
    get() {
        return when (this) {
            is BuzVoiceMsg -> {
                val targetLanguage = TranslateLanguageManager.getTargetLanguageCode()
                if (targetLanguage == translateInfo?.targetLanguage) {
                    translateInfo?.translateText
                } else {
                    null
                }
            }
            is BuzTextMsg -> {
                val targetLanguage = TranslateLanguageManager.getTargetLanguageCode()
                if (targetLanguage == translateInfo?.targetLanguage) {
                    translateInfo?.translateText
                } else {
                    null
                }
            }

            else -> null
        }
    }

val IM5MsgContent.isTargetLanguage: Boolean
    get() {
        return when (this) {
            is BuzVoiceMsg -> {
                val targetLanguage = TranslateLanguageManager.getTargetLanguageCode()
                targetLanguage == translateInfo?.targetLanguage
            }
            is BuzTextMsg -> {
                val targetLanguage = TranslateLanguageManager.getTargetLanguageCode()
                targetLanguage == translateInfo?.targetLanguage
            }

            else -> false
        }
    }

val IM5MsgContent.isShow: Boolean
    get() {
        return when (this) {
            is BuzVoiceMsg -> {
                translateInfo?.translateCode == TranslateConstant.SHOW_TRANSLATION
            }
            is BuzTextMsg -> {
                translateInfo?.translateCode == TranslateConstant.SHOW_TRANSLATION
            }
            else -> false
        }
    }

val IM5MsgContent.asrText: String?
    get() {
        return when (this) {
            is BuzVoiceMsg -> asrText
            else -> null
        }
    }

val IM5MsgContent.isTranslateEdit: Boolean
    get() {
        return when (this) {
            is BuzVoiceMsg -> editType == MessageEditType.TRANSLATE_EDIT
            is BuzTextMsg -> editType == MessageEditType.TRANSLATE_EDIT
            else -> false
        }
    }

val IM5MsgContent.isASREdit: Boolean
    get() {
        return when (this) {
            is BuzVoiceMsg -> editType == MessageEditType.ASR_EDIT
            else -> false
        }
    }

val IM5Message.isDeleteBool get() = this.isDeleted == MessageTag.NOUPDATECONV

val IMessage.isDeleteBool get() = (this as? IM5Message)?.isDeleteBool ?: false

val IMessage.hasVoiceFilter : Boolean
    get() {
        return when (content) {
            is BuzVoiceMsg -> {
                (content as? BuzVoiceMsg)?.voiceFilterInfo?.filterId != null
            }
            is VoiceTextMsg -> {
                (content as? VoiceTextMsg)?.voiceFilterInfo?.filterId != null
            }
            else -> false
        }
    }

val IMessage.getVoiceFilterId : Long?
    get() {
        return when (content) {
            is BuzVoiceMsg -> {
                (content as? BuzVoiceMsg)?.voiceFilterInfo?.filterId
            }
            is VoiceTextMsg -> {
                (content as? VoiceTextMsg)?.voiceFilterInfo?.filterId
            }
            else -> null
        }
    }

val IMessage.businessType: Int?
    get() {
        return when (content) {
            is MediaTextMsg -> {
                (content as? MediaTextMsg)?.businessType
            }
            else -> null
        }
    }

suspend fun IMessage.getCommandMsgNotifyText(): String? {
    if (msgType == IMType.TYPE_COMMAND) {
        val commandMsg = content as? CommandMsg
        val command = commandMsg?.decode()
        if (command?.isVoiceCallStartedType == true
            || command?.isVoiceCallEndType == true
            || command?.isVoiceCallMissedType == true
            || command?.isVoiceCallBusyType == true
        ) {
            return (command as VoiceCallCommand).tipMsg
        } else if (command?.isLpMiss == true
            || command?.isLpEnd == true
            || command?.isLpBusy == true
        ) {
            return (command as OnLiveCommand).obtainTip(this)
        } else if (command?.isOnAirType == true) {
            return (command as OnAirCommand).tipMsg
        }
        return null
    } else {
        return null
    }
}

/**
 * 居中消息默认是不会在notification和全局pop弹窗中显示出来的，如果需要显示特定的居中消息类型需要在这里做定义
 * 还需要在 getCommandMsgNotifyText 方法中处理要显示的内容
 */
suspend fun IMessage.isNotifyShowCommandMsg(): Boolean {
    if (msgType == IMType.TYPE_COMMAND) {
        val commandMsg = content as? CommandMsg
        val command = commandMsg?.decode()
        if (command?.isVoiceCallStartedType == true
            || command?.isVoiceCallEndType == true
            || command?.isVoiceCallMissedType == true
            || command?.isVoiceCallBusyType == true
            || command?.isOnAirType == true
            || command?.isLpMiss == true
            || command?.isLpEnd == true
            || command?.isLpBusy == true
        ) {
            return true
        }
        return false
    } else {
        return false
    }
}

data class MediaUploadInfo(val state: UploadState, val uploadBytes: Long, val totalBytes: Long) {
    val progress: Int get() = if (totalBytes > 0L) (uploadBytes * 100 / totalBytes).toInt() else 0
}

val IMessage.buzSendingState: BuzSendingState
    get() {
        val uploadInfo = mediaUploadInfo()
        return when (status) {
            SENDING -> {
                when (uploadInfo.state) {
                    //init状态认为是压缩中
                    INIT -> Compressing
                    IN_PROGRESS -> if (uploadInfo.progress == 100) Sending else Uploading(uploadInfo)
                    //如果上传过程调用pauseUpload，会回调这个
                    PAUSED -> Uploading(uploadInfo)
                    //如果上传过程中断网，会回调这个
                    FAILED -> Uploading(uploadInfo.copy(uploadBytes = 0))
                    else -> Sending
                }
            }

            MessageStatus.FAILED -> {
                when (uploadInfo.state) {
                    INIT -> {
                        val content = content
                        if (content is IM5VideoMessage && content.reason == PrepareMsgFailedReason.REASON_CANCEL) {
                            //主动取消压缩的话，会调用IM的prepareVideoMessageFailed接口,传递reason为cancel，然后消息状态会变为failed，这种情况需要判断reason来判断是否是用户主动取消
                            CompressingCanceled
                        } else {
                            Failed
                        }
                    }

                    PAUSED -> UploadPaused

                    else -> Failed
                }
            }

            null, SUCCESS -> Succeed
            CANCEL -> Canceled
        }
    }



val IMessage.isDecryptFail: Boolean get() = this.cryptStatus == CryptStatus.DECRYPT_FAILED

val IMessage.isOnlineMessage
    get() = when (this.msgType) {
        IMType.TYPE_ON_AIR_SOUNDBOARD,
        IMType.TYPE_ON_AIR_GIFT,
        IMType.TYPE_LIVEPLACE_DANMAKU,
        IMType.TYPE_ONIAR_DANMAKU -> true

        else -> false
    }

sealed interface BuzSendingState {
    data object Compressing : BuzSendingState
    data object CompressingCanceled : BuzSendingState
    data class Uploading(val uploadInfo: MediaUploadInfo) : BuzSendingState
    data object UploadPaused : BuzSendingState
    data object Sending : BuzSendingState
    data object Canceled : BuzSendingState
    data object Succeed : BuzSendingState
    data object Failed : BuzSendingState
}

fun IMSendStateEvent.toBuzSendingState(): BuzSendingState {
    return when (this) {
        is IMSendStateEvent.OnAttach -> msg.buzSendingState
        is IMSendStateEvent.OnSuccess -> msg.buzSendingState
        is IMSendStateEvent.OnError -> msg.buzSendingState
        is IMSendStateEvent.OnCanceled -> msg.buzSendingState
        is IMSendStateEvent.OnProgress -> msg.buzSendingState
    }
}

fun IMSendStateEvent.getMsgIdentity(): IMMsgIdentity {
    return when (this) {
        is IMSendStateEvent.OnAttach -> msg.toMsgIdentity()
        is IMSendStateEvent.OnSuccess -> msg.toMsgIdentity()
        is IMSendStateEvent.OnError -> msg.toMsgIdentity()
        is IMSendStateEvent.OnCanceled -> msg.toMsgIdentity()
        is IMSendStateEvent.OnProgress -> msg.toMsgIdentity()
    }
}