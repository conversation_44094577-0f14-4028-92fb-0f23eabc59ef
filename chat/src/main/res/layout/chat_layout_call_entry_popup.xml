<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_rect_background_5_default_radius_16"
    tools:layout_height="88dp"
    tools:layout_width="228dp">

    <!-- 语音通话选项 -->
    <TextView
        android:id="@+id/tvVoiceCall"
        style="@style/text_label_large"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="40dp"
        android:text="@string/rtc_voicecall"
        android:textColor="@color/color_text_white_important"
        app:layout_constraintBottom_toTopOf="@+id/viewDivider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvVoiceCall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/ic_tel"
        android:textColor="@color/color_text_white_important"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvVoiceCall"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvVoiceCall" />

    <!-- 分隔线 -->
    <View
        android:id="@+id/viewDivider"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="16dp"
        android:background="@color/color_foreground_neutral_important_disable"
        app:layout_constraintBottom_toTopOf="@+id/tvVideoCall"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvVoiceCall" />

    <!-- 视频通话选项 -->
    <TextView
        android:id="@+id/tvVideoCall"
        style="@style/text_label_large"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="40dp"
        android:text="@string/rtc_videocall"
        android:textColor="@color/color_text_white_important"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewDivider" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvVideoCall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/ic_video"
        android:textColor="@color/color_text_white_important"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvVideoCall"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvVideoCall" />

</androidx.constraintlayout.widget.ConstraintLayout>