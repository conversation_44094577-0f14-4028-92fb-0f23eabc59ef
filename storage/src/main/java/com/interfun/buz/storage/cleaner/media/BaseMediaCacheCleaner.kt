package com.interfun.buz.storage.cleaner.media

import com.interfun.buz.common.database.entity.BuzMediaCacheEntity
import com.interfun.buz.storage.cleaner.ICacheCleaner

/**
 * Author: ChenYouSheng
 * Date: 2024/6/21
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 当前用户媒体消息缓存清理基类
 */
abstract class BaseMediaCacheCleaner(val list: List<BuzMediaCacheEntity>) : ICacheCleaner {

    /**
     * 自动清理判断是否可以删除
     */
    open fun canAutoDelete(
        mediaCacheEntity: BuzMediaCacheEntity,
        day: Int = 0
    ): <PERSON><PERSON><PERSON> {
        return System.currentTimeMillis() - mediaCacheEntity.updateTime > (day * 24 * 3600 * 1000)
    }

    /**
     * 手动清理判断是否可以删除
     */
    open fun canDelete(mediaCacheEntity: BuzMediaCacheEntity): Boolean {
        return true
    }

}