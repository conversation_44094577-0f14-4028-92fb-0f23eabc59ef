package com.interfun.buz.onair.bean

import com.buz.idl.liveplace.bean.LivePlaceChannel
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType

/**
 *@param channelStatus:0-未开启 1：发起中 2:通话中
 */
 interface ConvChannelInfo{
    val convTargetId: Long//会话id
    val convType: Int//会话类型：1-私聊 2-群聊
    val channelId: Long//VC或OA的channelId
    val channelStatus: Int//0-未开启 1-发起中 2:通话中
    val channelType:@ChannelType Int//1-VC_PRIVATE 2-VC_GROUP 3-OA_PRIVATE 4-OA_GROUP
    val updateTimestamp:Long//channel 更新时间戳
    var channelInfo:ChannelInfo?
    var vcCallType: @CallType Int // 1-VOICE_CALL 2-VIDEO_CALL

 }

class CloseChannelInfo(
    override val convTargetId: Long,
    override val convType: Int = 0,
    override val channelId: Long = 0L,
    override val channelStatus: Int = 0 ,
    override val channelType: Int = 0,
    override val updateTimestamp: Long,
    override var channelInfo: ChannelInfo? = null,
    override var vcCallType: Int = CallType.TYPE_VOICE,
):ConvChannelInfo

class LivePlaceChannelInfo(
    var topic:String?,
    override val convTargetId: Long,
    override val convType: Int,
    override val channelId: Long,
    override val channelStatus: Int,
    override val channelType: Int,
    override val updateTimestamp: Long,
    override var channelInfo: ChannelInfo?,
    override var vcCallType: Int = CallType.TYPE_VOICE
):ConvChannelInfo

class VoiceCallChannelInfo(
    override val convTargetId: Long,
    override val convType: Int,
    override val channelId: Long,
    override val channelStatus: Int,
    override val channelType: Int,
    override val updateTimestamp: Long,
    override var channelInfo: ChannelInfo?,
    override var vcCallType: Int
):ConvChannelInfo


fun ConvChannelInfo.isLivePlaceChannelInfo():Boolean{
    return channelType == ChannelType.TYPE_LIVE_PLACE_PRIVATE
            || channelType == ChannelType.TYPE_LIVE_PLACE_GROUP
}

fun ConvChannelInfo.isVoiceCallChannelInfo():Boolean{
    return ChannelType.isVoiceCallType(channelType) && vcCallType == CallType.TYPE_VOICE
}

fun ConvChannelInfo.isVideoCallChannelInfo():Boolean{
    return ChannelType.isVoiceCallType(channelType) && vcCallType == CallType.TYPE_VIDEO
}

fun ConvChannelInfo.isChannelOpen():Boolean{
    return channelStatus != 0
}

fun ConvChannelInfo.isVoiceCallOpen():Boolean{
    return isVoiceCallChannelInfo() && isChannelOpen()
}

fun ConvChannelInfo.isVideoCallOpen():Boolean{
    return isVideoCallChannelInfo() && isChannelOpen()
}

fun ConvChannelInfo.isLivePlaceOpen():Boolean{
    return isLivePlaceChannelInfo() && isChannelOpen()
}



data class ChannelInfo(
    val memberCount:Int,//channel内成员数
    val topMembers:List<String>?,//channel内前n个成员头像
)

fun LivePlaceChannel.convert2LPChannelInfo(status: Int): LivePlaceChannelInfo {
    return LivePlaceChannelInfo(
        topic,
        targetId,
        placeType,
        channelId,
        status,
        if (placeType == 1) ChannelType.TYPE_LIVE_PLACE_PRIVATE else ChannelType.TYPE_LIVE_PLACE_GROUP,
        dataTimestamp,
        null
    )
}


