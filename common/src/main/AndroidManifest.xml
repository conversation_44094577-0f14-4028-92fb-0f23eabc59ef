<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name='android.permission.ACCESS_NOTIFICATION_POLICY' />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name='android.permission.WAKE_LOCK' />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="*"/>
        </intent>
    </queries>
    <application>

        <activity
            android:name=".view.activity.ShareQRCodeActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"/>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/buz_file_paths" />
        </provider>

        <provider
            android:name="com.yibasan.lizhifm.permission.FileProvider"
            android:authorities="${applicationId}.file.path.share"
            android:exported="false"
            tools:node="remove"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/permission_file_paths" />
        </provider>

        <provider
            android:name="com.lizhi.component.share.lzsharesdk.provider.LzShareProvider"
            android:authorities="${applicationId}.sharefileprovider"
            android:exported="false"
            tools:node="remove"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths"
                tools:replace="android:resource" />
        </provider>

        <activity
            android:name=".web.WebViewActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".crop.UCropActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity android:name=".qrcode.QRCodeScanActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".manager.router.RouterFragmentHostActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:theme="@style/AppNoTitleTheme.transparent" />

        <activity android:name=".view.activity.ImagePreViewActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:exported="false"/>

        <receiver android:name=".broadcast.DeclineChannelReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.interfun.buz.closeVoiceCall" />
            </intent-filter>
        </receiver>
        <receiver android:name=".broadcast.IgnoreDeleteVoiceCallReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.interfun.buz.ignoreVoiceCall" />
            </intent-filter>
        </receiver>


    </application>

</manifest>