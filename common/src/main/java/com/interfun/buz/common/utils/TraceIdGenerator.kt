package com.interfun.buz.common.utils

import com.interfun.buz.common.constants.channelId
import com.interfun.buz.common.constants.deviceId
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import com.yibasan.lizhifm.sdk.platformtools.Md5Util
import java.util.UUID

object TraceIdGenerator {
    fun getTraceId(userId: Long = 0) =
        Md5Util.getMD5String(channelId + deviceId + userId + ApplicationContext.getAppSTime())
    fun getTraceIdUUID() = Md5Util.getMD5String(UUID.randomUUID().toString())
}