package com.interfun.buz.common.widget.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.AttributeSet
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.sp
import com.interfun.buz.common.R


class SideBar(context: Context, attributeSet: AttributeSet) : View(context, attributeSet) {
    val letters = ArrayList<String>()
    private val letterPaint = Paint().apply { this.isAntiAlias = true }
    private val bgPaint = Paint()
    private var touchIndex = -1
    private var selectWordChangeListener: ((String)->Unit)? = null
    private var actionUpListener:(()->Unit)? = null
    private val padding = 20.dp
    private val rect = Rect()

    val defaultParam = Params(
        10f.sp,
        R.color.text_white_secondary.asColor(),
        R.color.text_black_main.asColor(),
        R.color.basic_primary.asColor(),
        8.dp,
        8.dp,
        R.color.transparent.asColor()
    )

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var height = 0
        letterPaint.textSize = defaultParam.letterSize
        letters.forEach {
            letterPaint.getTextBounds(it, 0, 1, rect)
            height += rect.height() + defaultParam.itemSpace
        }

        height -= defaultParam.itemSpace
        height += padding * 2
        val heightMeasure = MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
        super.onMeasure(widthMeasureSpec, heightMeasure)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        var startY = padding
        for (i in letters.indices) {
            letterPaint.textSize = defaultParam.letterSize
            letterPaint.getTextBounds(letters[i], 0, 1, rect)

            if (touchIndex == i) {
                bgPaint.color = defaultParam.selectBackgroundColor
                canvas.drawCircle(
                    width / 2f,
                    startY.toFloat() + rect.height() / 2,
                    defaultParam.selectBackgroundRadius.toFloat(),
                    bgPaint
                )
                letterPaint.color = defaultParam.letterSelectColor
            } else {
                letterPaint.color = defaultParam.letterColor
            }

            val wordX = (width / 2 - rect.width() / 2).toFloat()
            canvas.drawText(
                letters[i],
                wordX,
                startY.toFloat() + rect.height() / 2 + getBaseline(letterPaint),
                letterPaint
            )
            startY += rect.height() + defaultParam.itemSpace
        }
    }

    /**
     * 当手指触摸按下的时候改变字母背景颜色
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                val y = event.y
                val index = getTouchWordIndex(y)
                if (index != touchIndex){
                    touchIndex = index
                    if (selectWordChangeListener != null && 0 <= touchIndex && touchIndex <= letters.size - 1) {
                        selectWordChangeListener?.invoke(letters[touchIndex])
                    }
                    invalidate()
                }

            }
            MotionEvent.ACTION_UP -> {
                actionUpListener?.invoke()
            }
        }
        return true
    }

    fun setSelectText(text:String){
        touchIndex = letters.indexOf(text)
        invalidate()
    }

    fun clearSelectState(){
        touchIndex = -1
        invalidate()
    }

    private fun getTouchWordIndex(y: Float): Int {
        var index = -1
        var height = padding
        letterPaint.textSize = defaultParam.letterSize
        letters.forEach {
            letterPaint.getTextBounds(it, 0, 1, rect)
            height += rect.height() + defaultParam.itemSpace
            if (height >= y) return index + 1
            index++
        }

        return letters.size - 1
    }

    private fun getBaseline(p: Paint): Float {
        val fontMetrics = p.fontMetrics
        return (fontMetrics.descent - fontMetrics.ascent) / 2 - fontMetrics.descent
    }

    fun setLetters(letters: List<String>) {
        this.letters.clear()
        this.letters.addAll(letters)
        requestLayout()
        invalidate()
    }

    /*设置手指按下字母改变监听*/
    fun setOnWordsChangeListener(listener: (String)->Unit) {
        this.selectWordChangeListener = listener
    }



    data class Params(
        var letterSize: Float,
        var letterColor: Int,
        var letterSelectColor: Int,
        var selectBackgroundColor: Int,
        var selectBackgroundRadius: Int,
        var itemSpace: Int,
        var bgColor: Int
    )

}