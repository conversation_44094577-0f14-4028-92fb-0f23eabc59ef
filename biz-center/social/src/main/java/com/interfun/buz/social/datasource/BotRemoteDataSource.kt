package com.interfun.buz.social.datasource

import com.buz.idl.bot.bean.BotInfo
import com.buz.idl.bot.bean.BotUserSetting
import com.buz.idl.bot.response.ResponseAddChat
import com.buz.idl.bot.response.ResponseBotGroupChange
import com.interfun.buz.common.bean.Resp

internal interface BotRemoteDataSource {
    suspend fun getBotInfo(botUserId: Long): Resp<BotInfo?>
    suspend fun getBotInfoBatch(botUserIds: List<Long>): Resp<List<BotInfo>?>
    suspend fun getMarketAllBots(): Resp<List<BotInfo>?>
    suspend fun updateBotSetting(
        currentUserId: Long,
        botUserId: Long,
        languageCode: String?,
        voiceStyleId: Long?,
    ): Resp<BotUserSetting?>

    suspend fun getBotSettings(botUserId: Long): Resp<BotUserSetting?>
    suspend fun addBotToChat(botUserId: Long): Resp<ResponseAddChat?>
    suspend fun addBotToChat(botUserIds: List<Long>): Resp<List<BotInfo>?>
} 