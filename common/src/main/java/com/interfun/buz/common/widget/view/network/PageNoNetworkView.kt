package com.interfun.buz.common.widget.view.network

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.interfun.buz.common.databinding.CommonPageNoNetworkLayoutBinding
import com.interfun.buz.common.widget.view.EmptyDataViewListener

interface  PageNoNetworkViewListener {
    fun onRetry(view: PageNoNetworkView)
}

class PageNoNetworkBean

class PageNoNetworkView @JvmOverloads constructor
    (context: Context, attrs: AttributeSet? = null) : ConstraintLayout(context, attrs) {

    private var listener: PageNoNetworkViewListener? = null
    private var binding: CommonPageNoNetworkLayoutBinding? = null

    fun setRetryListener(listener: PageNoNetworkViewListener) {
        this.listener = listener
    }

    init {
        val layoutInflater = LayoutInflater.from(context)
        binding = CommonPageNoNetworkLayoutBinding.inflate(layoutInflater, this)
        binding?.emptyDataView?.setListener(object : EmptyDataViewListener {
            override fun onButtonClicked() {
                listener?.onRetry(this@PageNoNetworkView)
            }
        })
    }
}