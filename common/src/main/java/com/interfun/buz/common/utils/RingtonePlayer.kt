package com.interfun.buz.common.utils

import android.media.SoundPool
import androidx.annotation.IdRes
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.common.R
import com.yibasan.lizhifm.lzlogan.Logz
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.abs

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/9/1
 * https://blog.csdn.net/lovewaterman/article/details/38021305
 */
class RingtonePlayer private constructor() {

    private var mSp: SoundPool = SoundPool.Builder().setMaxStreams(7).build()
    private var sSpMap: MutableMap<Int, Int> = mutableMapOf<Int, Int>().apply {
        put(TYPE_ONLINE_CHAT_USER_JOIN, mSp.load(appContext, R.raw.online_chat_user_join, 1))
        put(TYPE_ONLINE_CHAT_HANG_UP, mSp.load(appContext, R.raw.online_chat_hang_up, 1))
        put(TYPE_CHAT_PLAY_MASSAGE_END, mSp.load(appContext, R.raw.chat_audio_play_massage_end, 1))
        put(TYPE_CHAT_LEAVE_MESSAGE_RECEIVED, mSp.load(appContext, R.raw.chat_receive_new_message, 1))
        put(TYPE_CHAT_PLAY_WT_MESSAGE_START, mSp.load(appContext, R.raw.chat_audio_walkie_talkie_play_start, 1))
        put(TYPE_CHAT_PLAY_WT_MESSAGE_END, mSp.load(appContext, R.raw.chat_audio_walkie_talkie_play_end, 1))
        put(TYPE_CHAT_MESSAGE_POPUP,mSp.load(appContext,R.raw.chat_message_receive,1))
        put(TYPE_CHAT_SEND_VOICE_CANCEL,mSp.load(appContext,R.raw.chat_audio_send_voice_cancel,1))
        put(TYPE_CHAT_SEND_TEXT,mSp.load(appContext,R.raw.chat_audio_send_text,1))

        put(TYPE_VOICE_CALL_NO_ANSWER, mSp.load(appContext,R.raw.voicecall_calling_no_answer,1))
        put(TYPE_VOICE_CALL_LEAVE, mSp.load(appContext,R.raw.voicecall_call_leave,1))
        put(TYPE_VOICE_CALL_JOIN, mSp.load(appContext,R.raw.voicecall_call_join,1))

        put(TYPE_FRIEND_ONLINE_POPUP, mSp.load(appContext, R.raw.chat_friend_online, 1))
        put(TYPE_EDIT_PORTRAIT, mSp.load(appContext, R.raw.login_profile_aura, 1))
        put(TYPE_VERIFY_CODE_SUCCESS, mSp.load(appContext, R.raw.login_onboarding_completed_new, 1))

        put(TYPE_KNOCK, mSp.load(appContext, R.raw.live_place_knock, 1)) // Register the knock sound
        put(TYPE_LIVE_PLACE_JOIN, mSp.load(appContext, R.raw.live_place_enter, 1))
        put(TYPE_LIVE_PLACE_LEAVE, mSp.load(appContext, R.raw.live_place_leave, 1))
    }

    private var sSpMapState = ConcurrentHashMap<Int, Boolean>()
    private var loadComplete = false
    private val playIntervals : Long = 500
    private var lastPlayTime: Long = 0L

    companion object {

        const val TYPE_ONLINE_CHAT_USER_JOIN = 2
        const val TYPE_ONLINE_CHAT_HANG_UP = 3
        const val TYPE_CHAT_PLAY_MASSAGE_END = 4 //这个音频太短 无法在tekiPlayer中播放
        const val TYPE_CHAT_LEAVE_MESSAGE_RECEIVED = 5
        const val TYPE_CHAT_PLAY_WT_MESSAGE_START = 6
        const val TYPE_CHAT_PLAY_WT_MESSAGE_END = 7
        const val TYPE_CHAT_MESSAGE_POPUP = 8
        const val TYPE_CHAT_SEND_VOICE_CANCEL = 9
        const val TYPE_CHAT_SEND_TEXT = 10
        const val TYPE_VOICE_CALL_NO_ANSWER = 11
        const val TYPE_VOICE_CALL_LEAVE = 12
        const val TYPE_VOICE_CALL_JOIN = 13
        const val TYPE_FRIEND_ONLINE_POPUP = 14
        const val TYPE_VERIFY_CODE_SUCCESS = 15
        const val TYPE_EDIT_PORTRAIT = 16
        const val TYPE_KNOCK = 17 // Add a new type for the knock sound
        const val TYPE_LIVE_PLACE_JOIN = 18
        const val TYPE_LIVE_PLACE_LEAVE = 19

        private var ringtonePlayer: RingtonePlayer? = null

        fun getInstance(): RingtonePlayer {
            return ringtonePlayer ?: RingtonePlayer().also { ringtonePlayer = it }
        }
    }

    init {
        // 这里要提前注册下，监听sSpMap初始化load资源
        initLoadCompleteListener(null)
    }

    private fun initLoadCompleteListener(callback: ((Int) -> Unit)?) {
        mSp.setOnLoadCompleteListener { soundPool, sampleId, status ->
            sSpMapState[sampleId] = 0 == status
            callback?.invoke(sampleId)
        }
    }

    fun updateAlertSound(@IdRes alertSoundStartResId: Int, @IdRes alertSoundEndResId: Int) {
        sSpMap[TYPE_CHAT_PLAY_WT_MESSAGE_START] = mSp.load(appContext, alertSoundStartResId, 1)
        sSpMap[TYPE_CHAT_PLAY_WT_MESSAGE_END] = mSp.load(appContext, alertSoundEndResId, 1)
        Logz.tag("RingtonePlayer").i("update sSpMap ==> $sSpMap")
    }

    fun play(type: Int) {
        val currentTime = System.currentTimeMillis()
        if (sSpMap.containsKey(type) && abs(currentTime - lastPlayTime) > playIntervals) {
            lastPlayTime = currentTime
            sSpMap[type]?.let {
                var play = 0
                if (sSpMapState[it] == true) {
                    play = mSp.play(it, 1f, 1f, 0, 0, 1f)
                    Logz.tag("RingtonePlayer").i("RingtonePlayer play = $play")
                } else {
                    val loadCompletePlayLimit = AtomicBoolean()
                    initLoadCompleteListener { sampleId ->
                        Logz.tag("RingtonePlayer") .i("RingtonePlayer setOnLoadCompleteListener sampleId = $sampleId， soundId = $it")
                        if (sampleId == it && loadCompletePlayLimit.getAndSet(true).not()) {
                            play = mSp.play(it, 1f, 1f, 0, 0, 1f)
                            Logz.tag("RingtonePlayer")
                                .i("type $type, sampleId = $sampleId, Complete RingtonePlayer play = $play")
                        }
                    }
                }
            }
        }
    }
}