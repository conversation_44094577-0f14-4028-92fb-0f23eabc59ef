package com.interfun.buz.common.bean.push

import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.eventbus.contact.PushNewContactRegisteredEvent
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2024/6/13
 * @desc 收到推送后，显示[com.interfun.buz.contacts.view.dialog.AddFriendsGuideDialog]
 *
 * - op=2009
 * - sendTimestamp
 *   - 推送时间戳
 * - hasRegisteredFriend
 *   - 是否存在已注册的好友推荐
 *   - true/false
 */
class PushNewContactRegistered : PushDataBase() {

    companion object {
        const val TAG = "PushNewContactRegistered"
    }

    override fun handlePushEvent(op: Int, sendTimestamp: Long, data: JSONObject?) {
        log(TAG, "sendTime:$sendTimestamp, data = $data")
        if (CommonMMKV.hasShownAddFriendsGuideDialog || CommonMMKV.isUserRegister.not()) return
        PushNewContactRegisteredEvent.post(TAG)
    }
}