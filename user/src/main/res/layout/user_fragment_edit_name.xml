<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background">


    <Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />


    <RelativeLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar"
        android:orientation="horizontal">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            android:layout_width="64dp"
            app:autoRTL="true"
            android:layout_alignParentStart="true"
            android:text="@string/ic_back"
            android:textSize="24sp"
            android:gravity="center"
            android:textColor="@color/text_white_main"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/edit_fullname"
            android:textColor="@color/text_white_main" />
    </RelativeLayout>


    <EditText
        android:id="@+id/edtFirstName"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/common_rect_overlay_white_6_radius_8"
        android:gravity="center_vertical"
        android:maxLength="50"
        android:hint="@string/first_name"
        android:paddingStart="20dp"
        android:textColor="@color/text_white_main"
        android:textColorHint="@color/text_white_disable"
        android:singleLine="true"
        android:layout_marginHorizontal="40dp"
        android:lines="1"
        android:paddingEnd="45dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar"
       />
    
    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvClearFirstName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        android:text="@string/ic_clear_input_solid"
        app:layout_constraintTop_toTopOf="@id/edtFirstName"
        app:layout_constraintBottom_toBottomOf="@id/edtFirstName"
        app:layout_constraintEnd_toEndOf="@id/edtFirstName"
        android:layout_marginEnd="20dp"
        android:visibility="gone"/>


    <EditText
        android:id="@+id/edtLastName"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/common_rect_overlay_white_6_radius_8"
        android:gravity="center_vertical"
        android:maxLength="50"
        android:paddingStart="20dp"
        android:textColor="@color/text_white_main"
        android:singleLine="true"
        android:hint="@string/last_name"
        android:textColorHint="@color/text_white_disable"
        android:layout_marginHorizontal="40dp"
        android:lines="1"
        android:paddingEnd="45dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edtFirstName"
        />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvClearLastName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        android:text="@string/ic_clear_input_solid"
        app:layout_constraintTop_toTopOf="@id/edtLastName"
        app:layout_constraintBottom_toBottomOf="@id/edtLastName"
        app:layout_constraintEnd_toEndOf="@id/edtLastName"
        android:layout_marginEnd="20dp"
        android:visibility="gone"/>

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/cbSave"
        app:type="secondary_larger"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="40dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="20dp"
        app:text="@string/save"/>

</androidx.constraintlayout.widget.ConstraintLayout>