package com.interfun.buz.album.di

import com.interfun.buz.album.repository.MediaCacheRepository
import com.interfun.buz.component.hilt.UserComponent
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class AlbumModuleDelegate {

    @Provides
    fun provideMediaCacheRepository(userComponent: UserComponent): MediaCacheRepository {
        return EntryPoints.get(userComponent, AlbumSocialEntryPoint::class.java)
            .getMediaCacheRepository()
    }
}