plugins {
    id "com.android.application"
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin'

}
apply plugin: 'com.huawei.agconnect'

apply plugin: 'com.google.gms.google-services'
apply from: "../base_config.gradle"
apply from: "../build_config.gradle"
apply plugin: 'com.tencent.vasdolly'
//apply plugin: 'walle'

if (QA_JENKINS_BUILD.toBoolean()) {
    logger.log(LogLevel.WARN,"ARouterPlugin启用")
    //for Third-Party Arouter plugin(Support AGP8) https://github.com/JailedBird/ArouterGradlePlugin?tab=readme-ov-file
    apply plugin: "io.github.JailedBird.ARouterPlugin"
}else {
    logger.log(LogLevel.WARN,"ARouterPlugin禁用")
}

// TODO: 这里涉及插桩可能会影响编译速度，有需要可以改为 jenkins 打包再启用
apply from: "config_stump.gradle"
secrets {
    // A properties file containing default secret values. This file can be
    // checked in version control.
    defaultPropertiesFileName = "local.defaults.properties"
    // Configure which keys should be ignored by the plugin by providing regular expressions.
    // "sdk.dir" is ignored by default.
    ignoreList.add("keyToIgnore") // Ignore the key "keyToIgnore"
    ignoreList.add("sdk.*")       // Ignore all keys matching the regexp "sdk.*"
}
android {
    defaultConfig {
        manifestPlaceholders =[HW_APP_ID:"110599309"]
        applicationId "com.interfun.buz"
        multiDexEnabled true
        ndk {
            abiFilters "armeabi-v7a"
            abiFilters "arm64-v8a"
        }
    }

    packagingOptions{
        exclude 'META-INF/idlkit_release.kotlin_module'
        //same files found whth path itnet-push-lib-5.3.1 and module push
        exclude 'META-INF/push_release.kotlin_module'
        pickFirst '**/liblog.so'
        pickFirst 'lib/*/libbasetool.so'
        pickFirst 'lib/*/libc++_shared.so'
    }

    bundle {
        // 是否分割多语言包
        language {
            enableSplit = false
        }

        // 是否分割资源
        density {
            enableSplit = false
        }

        // 是否分割二进制 so 包资源
        abi {
            enableSplit = true
        }
    }
    namespace 'com.interfun.buz'

    packagingOptions {
        pickFirst 'lib/*/libshadowhook.so'
        pickFirst '**/libshadowhook.so'
        pickFirst '**/libavcodec.so'
        pickFirst '**/libavutil.so'

    }

    experimentalProperties["android.experimental.r8.dex-startup-optimization"] = true
}

//VasDolly插件配置
//gradle channelDebug/channelRelease 编译生成apk后，再根据生成的Apk生成渠道包
channel{
    channelFile = file("app_channel.txt")
    //多渠道包的输出目录，默认为new File(project.buildDir,"channel")
    outputDir = new File(project.buildDir,"channels")
    //多渠道包的命名规则，默认为：${appName}-${versionName}-${versionCode}-${flavorName}-${buildType}-${buildTime}
    apkNameFormat ='${appName}-${versionName}-${versionCode}-${flavorName}-${buildType}'
    //快速模式：生成渠道包时不进行校验（速度可以提升10倍以上，默认为false）
    fastMode = false
    //buildTime的时间格式，默认格式：yyyyMMdd-HHmmss
    buildTimeDateFormat = 'yyyyMMdd-HH:mm:ss'
    //低内存模式（仅针对V2签名，默认为false）：只把签名块、中央目录和EOCD读取到内存，不把最大头的内容块读取到内存，在手机上合成APK时，可以使用该模式
    lowMemory = false
}

/*walle {
    // 指定渠道包的输出路径
    apkOutputFolder = new File("${project.buildDir}/outputs/channels")
    // 定制渠道包的APK的文件名称
    apkFileNameFormat = '${appName}-v${versionName}-${versionCode}-${channel}-${buildType}.apk'
    // 渠道配置文件
    channelFile = new File("${project.getProjectDir()}/app_channel.txt")
}*/

configurations.all {
    //全局移除腾讯X5内核相关依赖，因为GP过审不能有x5
    exclude group:'com.tencent.tbs.tbssdk', module:"sdk"
    resolutionStrategy {
        force("androidx.emoji2:emoji2-views-helper:1.3.0")
        force("androidx.emoji2:emoji2:1.3.0")
    }
}

dependencies {
    implementation project(':common')
    implementation project(':common-compose')
    implementation project(path: ':login')
    implementation project(path: ':startup')
    implementation project(path: ':chat')
    implementation project(path: ':contacts')
    implementation project(path: ':im')
    implementation project(path: ':push')
    implementation project(path: ':handleReceiveShare')
    implementation project(path: ':user')
    implementation project(path: ':feedback')
    implementation project(path: ':float')
    implementation project(path: ':campaign')
    implementation project(path: ':voicecall')
    implementation project(path: ':sharedmedia')

    implementation 'com.huawei.hms:push:6.12.0.300'
    implementation project(path: ':media')
    implementation project(path: ':album')
    implementation project(path: ':storage')
    implementation project(path: ':onair')
    implementation project(path: ':home')
    implementation project(path: ':feature:voicepreview')
    implementation project(path: ':feature:notification')
    implementation project(path: ':domain-ui:social')
    implementation project(path: ':biz-center:social')
    implementation project(path: ':biz-center:auth')
    implementation project(path: ':biz-center:voicefilter')
    implementation project(path: ':biz-center:translator')
    implementation project(path: ':domain:im-social')

    implementation("androidx.profileinstaller:profileinstaller:1.3.1")
    debugImplementation rootProject.ext.dependencies["dokit_debug"]
    releaseImplementation rootProject.ext.dependencies["dokit_release"]
    releaseLogImplementation rootProject.ext.dependencies["dokit_debug"]
    benchmarkImplementation rootProject.ext.dependencies["dokit_release"]
    debugImplementation "com.bytedance.tools.codelocator:codelocator-lancet-all:2.0.3"
    // lizhi-leakcanary只能在debug中引入，请勿在正式包中引入
    debugImplementation "fm.lizhi.testing:lizhi-leakcanary:3.0.20-SNAPSHOT"
    releaseImplementation "fm.lizhi.testing:leakcanary-android-no-op:3.0.20-SNAPSHOT"
}