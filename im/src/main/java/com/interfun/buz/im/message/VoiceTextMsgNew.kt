package com.interfun.buz.im.message

import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.im.entity.VoiceFilterInfo
import com.lizhi.im5.sdk.message.model.MessageTag

/**
 * for compatibility with older versions of IM messages, because there can be some issues with old messages in groups.
 */
@MessageTag(
    type = IMType.TYPE_VOICE_TEXT_NEW, flag = MessageTag.COUNTANDPERSIST
)
class VoiceTextMsgNew : VoiceTextMsg(){
    companion object {
        fun obtain(
            localPath: String,
            duration: Int,
            mentionedUsers: List<MentionedUser>?,
            autoPlay : Boolean,
            voiceFilterInfo: VoiceFilterInfo? = null
        ): VoiceTextMsgNew {
            return VoiceTextMsgNew().apply {
                setLocalPath(localPath)
                setDuration(duration)
                this.mentionedUsers = mentionedUsers
                this.autoPlay = autoPlay
                this.voiceFilterInfo = voiceFilterInfo
            }
        }
    }

    override fun prepareToForward() {
        super.prepareToForward()
        val contentExtra = IMMessageContentExtra.parseFromJson(extra)
        val eventExtra = contentExtra.eventTrackExtra
        val newExtra = if (eventExtra == null) {
            contentExtra.copy(eventTrackExtra = EventTrackExtra(isForwardMsg = true))
        } else {
            contentExtra.copy(eventTrackExtra = eventExtra.copy(isForwardMsg = true))
        }
        this.extra = newExtra.toJson()
    }
}