package com.interfun.buz.contacts.viewmodel

import android.Manifest.permission
import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.doInMutex
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.base.ktx.screenHeightWithoutTopBottom
import com.interfun.buz.common.constants.AddFriendSource
import com.interfun.buz.common.ktx.isFriend
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsItemType
import com.interfun.buz.contacts.entity.ContactsListContainer
import com.interfun.buz.contacts.entity.ContactsPayloadType
import com.interfun.buz.social.repo.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.sync.Mutex
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2023/5/6
 * @desc
 */
@HiltViewModel
class ContactsRecommendViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val mutex = Mutex()

    val recommendChangeLiveData = MutableLiveData<Pair<Int, ContactsPayloadType>>()
    val recommendListLiveData = MutableLiveData<List<ContactsItemBean>>()
    val recommendListContainer = ContactsListContainer()

    fun requestAddFriend(
        source: Int,
        eventSource: Int = AddFriendSource.AddFriendPage.value,
        userId: Long,
        isFromBlindBox: Boolean = false
    ){
        requestAddFriend(userRepository, source, eventSource, userId, isFromBlindBox)
    }

    fun getRecommendList(
        isViewAll: Boolean,
        isFirstRequest: Boolean,
        contextLambda: () -> Context
    ) = doInMutex(mutex) {
        if (!isPermissionGranted(permission.READ_CONTACTS)) {
            recommendListContainer.clear()
            recommendListLiveData.postValue(recommendListContainer.list)
            return@doInMutex
        }
        val recommendList = getRecommendFriendsList(isFirstRequest)
        recommendListContainer.clear()
        recommendListContainer.addAll(recommendList)
        if (recommendList.notEmpty() && isViewAll.not()) {
            recommendListContainer.add(ContactsItemBean(type = ContactsItemType.RecommendEntry))
        }
        if (recommendList.isEmpty() && isViewAll) {
            val emptyItem = ContactsItemBean(type = ContactsItemType.Empty).apply {
                content = R.string.contacts_no_suggestions.asString()
                extra.emptyHeight = contextLambda().screenHeightWithoutTopBottom - 72.dp
            }
            recommendListContainer.clear()
            recommendListContainer.add(emptyItem)
        }
        recommendListLiveData.postValue(recommendListContainer.list)
    }

    fun updateRecommendRelation(userId: Long, contextLambda: () -> Context) = doInMutex(mutex) {
        val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(userId) ?: return@doInMutex
        if (userInfo.isFriend) {
            removeByUserId(userId, contextLambda)
            return@doInMutex
        }
        recommendListContainer.update(
            userId,
            action = { pos, item ->
                item.extra.isLoading = false
                item.userInfo = userInfo
                recommendChangeLiveData.postValue(pos to ContactsPayloadType.UpdateUserRelation)
            })
    }

    fun requestDeleteRecommend(userId: Long, contextLambda: () -> Context) = doInMutex(mutex) {
        removeByUserId(userId, contextLambda)
        requestIgnoreRecommendFriends(userId)
    }

    private fun removeByUserId(userId: Long, contextLambda: () -> Context) {
        recommendListContainer.removeIf { it.targetId == userId }
        if (recommendListContainer.size == 1) {
            if (recommendListContainer.get(0)?.type == ContactsItemType.RecommendEntry) {
                recommendListContainer.clear()
            }
        }
        if (recommendListContainer.isEmpty()) {
            val emptyItem = ContactsItemBean(type = ContactsItemType.Empty).apply {
                content = R.string.contacts_no_request.asString()
                extra.emptyHeight = contextLambda().screenHeightWithoutTopBottom - 72.dp
            }
            recommendListContainer.add(emptyItem)
        }
        recommendListLiveData.postValue(recommendListContainer.list)
    }
}