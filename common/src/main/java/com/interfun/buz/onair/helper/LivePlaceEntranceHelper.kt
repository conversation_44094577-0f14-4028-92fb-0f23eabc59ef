package com.interfun.buz.onair.helper

import androidx.fragment.app.FragmentActivity
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.NavManager
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.block.LivePlaceInviteBlock.Companion.TAG
import com.interfun.buz.common.eventbus.chat.CloseConvSearchEvent
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.onair.bean.isLivePlaceOpen
import com.interfun.buz.onair.standard.*

object LivePlaceEntranceHelper {

    /*
    * 如果当前已经加入过该空间（最小化状态），那么直接拉起空间。否则打开资料页
    * */
    fun reopenLivePlaceOrJumpToProfile(
        activity: FragmentActivity?,
        targetId: Long?,
        isPrivate:Boolean,
        profileSource:Int
    ){
        activity ?: return
        targetId ?: return
        val isReopen = reopenLivePlace(
            activity = activity,
            targetId = targetId,
            livePlaceType = if (isPrivate) LivePlaceType.PRIVATE else LivePlaceType.GROUP,
        )
        if (!isReopen) {
            if (isPrivate) {
                NavManager.showPersonalProfile(
                    activity,
                    targetId,
                    source = FriendApplySource.LP_INVITE_CARD,
                    traceSource = profileSource
                )
            } else {
                NavManager.showGroupProfile(activity, targetId, profileSource)
            }
        } else {
            if (profileSource == FriendApplySource.search) {
                CloseConvSearchEvent.post()
            }
        }
    }

    fun handleEnterLivePlace(
        activity: FragmentActivity?,
        targetId: Long,
        livePlaceType: LivePlaceType,
        traceId: String,
        @LivePlaceSource
        source:Int
    ) : Boolean {
        activity ?: return false
        val isReopenOnAirSuccess = reopenLivePlace(activity, targetId, livePlaceType)

        if (!isReopenOnAirSuccess) {

            if (routerServices<IGlobalOnAirController>().value?.isInOnAir() == true) {
                toast(R.string.exit_current_call_and_try)
                return false
            }
            // routerServices<IGlobalOnAirController>().value?.showOnAirPreviewDialog(
            //     targetId, convType, source
            // )
            val channelInfo = ChannelStatusManager.getConvChannelInfo(targetId)
            if (channelInfo?.isLivePlaceOpen() == true) {
                joinLivePlace(
                    activity = activity,
                    targetId = targetId,
                    channelId = channelInfo.channelId,
                    livePlaceType = livePlaceType,
                    source = source
                )
            } else {
                enterLivePlace(
                    activity = activity,
                    targetId = targetId,
                    livePlaceType = livePlaceType,
                    source
                )
            }
        }

        CommonTracker.onClickOnAirEntry(livePlaceType, targetId, traceId)
        return true
    }

    fun reopenLivePlace(
        activity: FragmentActivity,
        targetId: Long,
        livePlaceType: LivePlaceType
    ): Boolean {
        val currentParam =
            routerServices<IGlobalOnAirController>().value?.curOnAirContext()?.createParam
        if (currentParam?.isSameRoom(livePlaceType, targetId) == true) {
            routerServices<IGlobalOnAirController>().value?.enterOnAir(
                currentParam,
                activity
            )
            return true
        }
        return false
    }

    fun createLivePlace(
        activity: FragmentActivity?,
        targetId: Long,
        livePlaceType: LivePlaceType,
        @LivePlaceSource
        source: Int
    ) : Boolean {
        activity ?: return false
        if (routerServices<IGlobalOnAirController>().value?.isInOnAir() == true) {
            toast(R.string.exit_current_call_and_try)
            return false
        }
        val roomParam = RoomParam(
            livePlaceType = livePlaceType,
            activityReason = ActivityReason.ESTABLISH_ROOM,
            targetId = targetId,
            openMic = true,
            source = source,
            joinROOMParam = null
        )
        routerServices<IGlobalOnAirController>().value?.enterOnAir(roomParam, activity)
        return true
    }

    fun enterLivePlace(
        activity: FragmentActivity?,
        targetId: Long,
        livePlaceType: LivePlaceType,
        @LivePlaceSource
        source: Int
    ) : Boolean {
        activity ?: return false
        val roomParam = RoomParam(
            livePlaceType = livePlaceType,
            activityReason = ActivityReason.OPEN_ROOM,
            targetId = targetId,
            openMic = true,
            joinROOMParam = null,
            source = source
        )
        routerServices<IGlobalOnAirController>().value?.enterOnAir(roomParam, activity)
        return true
    }

    fun joinLivePlace(
        activity: FragmentActivity?,
        targetId: Long,
        channelId: Long,
        livePlaceType: LivePlaceType,
        @LivePlaceSource
        source: Int
    ) : Boolean {
        activity ?: return false
        val isReopenOnAirSuccess = reopenLivePlace(activity, targetId, livePlaceType)
        if (isReopenOnAirSuccess) {
            return true
        }else{
            val currentParam =
                routerServices<IGlobalOnAirController>().value?.curOnAirContext()?.createParam
            val joinLambda = {
                val roomParam = RoomParam(
                    livePlaceType = livePlaceType,
                    activityReason = ActivityReason.JOIN_ROOM,
                    targetId = targetId,
                    openMic = true,
                    source = source,
                    joinROOMParam = JoinRoomParam(channelId, 1, 2)
                )
                routerServices<IGlobalOnAirController>().value?.enterOnAir(roomParam, activity)
            }


            //正在VC语音电话
            val vcRoom = VoiceCallPortal.currentRoom.value
            logInfo(TAG, "enterOnAir() vcRoom = ${vcRoom}")
            vcRoom?.hangUp()
            joinLambda.invoke()
        }



        return true
    }

}