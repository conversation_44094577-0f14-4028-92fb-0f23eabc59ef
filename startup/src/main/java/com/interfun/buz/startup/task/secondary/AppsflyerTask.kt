package com.interfun.buz.startup.task.secondary

import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.isDebug
import com.interfun.buz.base.ktx.isReleaseLogBuildType
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.channelId
import com.interfun.buz.common.constants.deviceId
import com.interfun.buz.common.constants.mediaChannel
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.af.AFManager
import com.interfun.buz.startup.task.MAIN_PROCESS_MODE
import com.interfun.buz.startup.task.Task
import com.yibasan.lizhi.tracker.LZTracker
import com.yibasan.lizhifm.lzlogan.Logz
import org.json.JSONObject

/**
 * @see: https://vocalbeats.sg.larksuite.com/wiki/BDqjwdYN1ieEqvkgMnflzB74gug
 */
@Deprecated("AppsFlyer已被Adjust替换，不在使用初始化")
class AppsflyerTask : Task(TASK_TAG) {
    companion object {
        const val TASK_TAG = "AppsflyerTask"
        const val AF_DEV_KEY = "GRdhm2LMCiwHzr54QbVXPj"
    }

    override fun run() {
        if (isDebug || isReleaseLogBuildType) {
            AppsFlyerLib.getInstance().setDebugLog(true)
        }
        AppsFlyerLib.getInstance().setCustomerUserId(deviceId)
        AppsFlyerLib.getInstance().setCollectOaid(true)
        AppsFlyerLib.getInstance().setOneLinkCustomDomain("invite.buz-app.com")
        AppsFlyerLib.getInstance().setAppInviteOneLink(AppConfigRequestManager.afTemplateId)
        AppsFlyerLib.getInstance().setOutOfStore(channelId)

        AppsFlyerLib.getInstance().init(
            AF_DEV_KEY, object : AppsFlyerConversionListener {
                override fun onConversionDataSuccess(map: MutableMap<String, Any>?) {
                    Logz.tag(TASK_TAG).d("onConversionDataSuccess:${map}")
                    val mediaSource = map?.get("media_source")
                    if (mediaSource != null){
                        mediaChannel = mediaSource as? String?
                    }
                    logInfo(TASK_TAG,"onConversionDataSuccess media_source = $mediaSource")
                    LZTracker.registerProperties(JSONObject().apply {
                        put("af_pid",mediaSource)
                    })

                    val isFirstLaunch = map?.get("is_first_launch")?:false
                    logInfo(TASK_TAG,"onConversionDataSuccess isFirstLaunch = $isFirstLaunch")
                    if (!(isFirstLaunch as Boolean)) return

                    val deepLink = map?.get("deep_link_sub1") as? String?
                    logInfo(TASK_TAG,"onConversionDataSuccess deep_link_sub1 = $deepLink")

                    val campaign = map?.get("campaign") as? String?
                    logInfo(TASK_TAG,"onConversionDataSuccess: campaign = $campaign")

                    if (deepLink != null) {
                        AFManager.handleOnConversionDataSuccessWithDeepLink(deepLink)
                    }else if (!campaign.isNullOrEmpty()){
                        AFManager.handleOnConversionDataSuccessWithCampaign(campaign)
                    }else{
                        AFManager.handleOnConversionDataSuccessWithoutData()
                    }
                }

                override fun onConversionDataFail(msg: String?) {
                }

                override fun onAppOpenAttribution(map: MutableMap<String, String>) {
                    logInfo(TASK_TAG,"AppsFlyerConversionListener onAppOpenAttribution: $map")
                    val deepLink = map["deep_link_sub1"]
                    logInfo(TASK_TAG,"onAppOpenAttribution deep_link_sub1 = $deepLink")

                    if (deepLink != null) {
                        AFManager.handleOnAppOpenAttributionDeepLink(deepLink)
                    }
                }

                override fun onAttributionFailure(p0: String?) {
                }
            },
            appContext
        )
        AppsFlyerLib.getInstance().start(appContext, AF_DEV_KEY)
    }

    override fun processModel(): Int {
        return MAIN_PROCESS_MODE
    }
}