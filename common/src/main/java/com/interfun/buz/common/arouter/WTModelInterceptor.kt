package com.interfun.buz.common.arouter

import android.content.Context
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.annotation.Interceptor
import com.alibaba.android.arouter.facade.callback.InterceptorCallback
import com.alibaba.android.arouter.facade.template.IInterceptor
import com.interfun.buz.common.bean.chat.GroupChatJumpInfo
import com.interfun.buz.common.bean.chat.PrivateChatJumpInfo
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.service.ChatService

/**
 * @Desc Interceptor jump into PrivateChatActivity or GroupChatActivity when wt model is open status
 * @Author:<EMAIL>
 * @Date: 2022/11/1
 */
@Deprecated("unused")
@Interceptor(priority = Int.MAX_VALUE - 1)
class WTModelInterceptor : IInterceptor {

    private val chatRouterService by lazy {
        routerServices<ChatService>().value
    }

    companion object{

        private val interceptPaths = listOf(
            PATH_CHAT_ACTIVITY_PRIVATE,
            PATH_CHAT_ACTIVITY_GROUP
        )

        internal fun isInterceptPath(path: String): Boolean{
            return interceptPaths.contains(path)
        }
    }

    override fun init(context: Context?) {
    }

    override fun process(postcard: Postcard, callback: InterceptorCallback) {
        /*if (isInterceptPath(postcard.path) && chatRouterService?.isWTOnlineStatus() == true){
            callback.onInterrupt(null)
            if (postcard.path == PATH_CHAT_ACTIVITY_PRIVATE){
                val privateChatJumpInfo =
                    postcard.extras.getParcelable<PrivateChatJumpInfo>(RouterParamKey.Chat.JUMP_INFO)
                privateChatJumpInfo?.let {
                    NavManager.startChatHomeActivityWithWTInvite(1, it.userId?:0L)
                }
            }else if (postcard.path == PATH_CHAT_ACTIVITY_GROUP){
                val groupChatJumpInfo =
                    postcard.extras.getParcelable<GroupChatJumpInfo>(RouterParamKey.Chat.JUMP_INFO)
                groupChatJumpInfo?.let {
                    NavManager.startChatHomeActivityWithWTInvite(2, it.groupId?:0L)
                }
            }
        }else{
            callback.onContinue(postcard)
        }*/
        callback.onContinue(postcard)
    }

}