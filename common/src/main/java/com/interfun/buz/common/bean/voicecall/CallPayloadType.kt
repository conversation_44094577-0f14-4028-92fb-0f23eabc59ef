package com.interfun.buz.common.bean.voicecall
import com.yibasan.lizhifm.liveinteractive.CameraStopReason

class CallPayload(val types: List<CallPayloadType>) {
    override fun toString(): String {
        return types.joinToString { it.toString() }
    }
}

enum class CallPayloadType {
    UPDATE_MIC_STATUS,
    UPDATE_CAMERA_STATUS,
    UPDATE_CAMERA_STATE,
    UPDATE_NETWORK_STATUS,
    UPDATE_USER_INFO,
    UPDATE_SPEAKING_STATUS,
    SHOW_USERNAME,
    HIDE_USERNAME,
    RELEASE_CAMERA,
    NO_ACTION,
}

interface CameraPayload
object CaptureStarted: CameraPayload
object DeviceChanged: CameraPayload
class CaptureStopped(val reason: CameraStopReason? = null): CameraPayload