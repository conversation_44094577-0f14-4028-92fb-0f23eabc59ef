package com.interfun.buz.home.view.dialog

import com.interfun.buz.album.R
import com.interfun.buz.album.databinding.AlbumListDialogBinding
import com.interfun.buz.album.ui.fragment.BaseAlbumDialogFragment
import com.interfun.buz.base.ktx.click

/**
 * Author: ChenYouSheng
 * Date: 2025/5/15
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 首页相册弹窗
 */
class HomeAlbumDialogFragment : BaseAlbumDialogFragment() {

    override val binding by lazy { AlbumListDialogBinding.inflate(layoutInflater) }

    override fun getContentId(): Int {
        return R.id.flAlbum
    }

    override fun initView() {
        super.initView()
        binding.ivHandle.click {
            dismiss()
        }
    }

    companion object {
        const val TAG = "HomeAlbumDialogFragment"

        fun newInstance(
            targetId: String,
            convType: Int,
            isFullScreen: Boolean = true,
            source: Int
        ) = HomeAlbumDialogFragment().apply {
            arguments = createArguments(targetId, convType, isFullScreen, source)
        }
    }
}