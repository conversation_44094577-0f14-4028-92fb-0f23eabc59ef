package com.interfun.buz.contacts.entity

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.facade.Postcard
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.ChatNavigation
import com.interfun.buz.chat.common.utils.GroupFlowTracker
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.database.entity.ContactsBean
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.user.FriendRequestCount
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.R.anim
import com.interfun.buz.contacts.interfaces.ContactsItemCallback
import com.interfun.buz.contacts.utils.ContactsTracker
import com.interfun.buz.contacts.view.itemdelegate.*

open class ContactsItemBean(
    var type: ContactsItemType,
    var content: String? = null,
    var desc: String? = null,
    val targetId: Long = 0L,
    var userInfo: UserRelationInfo? = null,
    var contactInfo: ContactsBean? = null,
    var groupInfo: GroupInfoBean? = null,
    var extra: ContactsExtra = ContactsExtra()
) {
    companion object {
        fun generateTitle(textRes: Int) =
            ContactsItemBean(type = ContactsItemType.Title, content = textRes.asString())

        fun generateTitleWithIcon(textRes: Int) =
            ContactsItemBean(type = ContactsItemType.TitleWithIcon, content = textRes.asString())

        fun generateSpace(size: Int) =
            ContactsItemBean(type = ContactsItemType.Space).apply {
                extra.spaceSize = size
            }
    }

    fun isGroup(): Boolean {
        return groupInfo != null
    }

    fun isContact(): Boolean {
        return contactInfo != null
    }

    fun isUser(): Boolean {
        return userInfo != null
    }

    fun dispatch(
        group: OneParamCallback<GroupInfoBean>? = null,
        contact: OneParamCallback<ContactsBean>? = null,
        user: OneParamCallback<UserRelationInfo>? = null
    ) {
        group?.let {
            groupInfo?.let { group.invoke(it) }
        }
        contact?.let {
            contactInfo?.let { contact.invoke(it) }
        }
        user?.let {
            userInfo?.let { user.invoke(it) }
        }
    }

    fun areItemsTheSame(other: ContactsItemBean?): Boolean {
        if (type != other?.type) return false
        return when (type) {
            ContactsItemType.Normal,
            ContactsItemType.Recommend,
            ContactsItemType.RecommendHome,
            ContactsItemType.Requests,
            ContactsItemType.SendRequests,
            ContactsItemType.Suggestion,
            ContactsItemType.Official -> targetId == other.targetId
            ContactsItemType.Title -> content == other.content
            ContactsItemType.Space -> extra.spaceSize == other.extra.spaceSize
            ContactsItemType.LargeTitle -> content == other.content
            ContactsItemType.HorizontalShareList -> true
            ContactsItemType.VerticalShareList -> true
            else -> this == other
        }
    }

    fun update(other: ContactsItemBean){
        if (targetId != other.targetId) return
        other.let {
            type = other.type
            content = other.content
            desc = other.desc
            userInfo = other.userInfo
            contactInfo = other.contactInfo
            groupInfo = other.groupInfo
            extra = other.extra
        }
    }
}

data class ContactsExtra(
    var source: Int? = null,
    var priority: Boolean = false,
    var isLoading: Boolean = false,
    var count: FriendRequestCount? = null,
    var spaceSize: Int = 10.dp,
    var emptyHeight: Int = 0,
    var isTitleInCenter: Boolean = false,
    var recommendList: MutableList<ContactsItemBean>? = null,
    var firstLetter: String = "#",
    var isInviteUser: Boolean = false,
    var showAddedWhenIsFriend: Boolean = false,
    var friendAppliesStatus: Int = FriendAppliesStatus.PENDING.value,
    var hasNewAi: Boolean = false,
)

enum class FriendAppliesStatus(val value: Int) {
    PENDING(0),
    EXPIRED(3)
}

enum class ContactsItemType {

    Loading,
    /** Universal Functional Types */
    Title,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsTitleItemView]*/
    Empty,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsEmptyItemView]*/
    Space,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsSpaceItemView]*/
    NoPermission,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsNoPermissionItemView]*/
    SearchHint,    /**[com.interfun.buz.contacts.view.itemdelegate.ContactsSearchHintItemView]*/
    SearchInvite,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsSearchInviteItemView]*/

    /** Types Entrance [com.interfun.buz.contacts.view.itemdelegate.ContactsEntryItemView] */
    EntryAddFriends,
    EntryCreateGroup,
    EntryAIMarketsPlace,
    EntryRequests,

    /** Types Recommend */
    RecommendList,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsRecommendListItemView]*/
    RecommendHome,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsRecommendItemView]*/
    RecommendEntry, /**[com.interfun.buz.contacts.view.itemdelegate.ContactsRecommendEntryItemView]*/

    /** User/Group/Contact/Official All use[com.interfun.buz.contacts.view.itemdelegate.ContactsItemView]*/
    Normal,
    Recommend,
    MightKnow,
    Contact,
    Requests,
    SendRequests,
    Official,
    UnRegister, /**[com.interfun.buz.contacts.view.itemdelegate.ContactsUnRegisterItemView]*/
    Suggestion,

    /** Types Share */
    LargeTitle,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsLargeTitleItemView]*/
    HorizontalShareList,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsHorizontalShareListItemView]*/
    NewHorizontalShareList,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsHorizontalShareListItemViewNew]*/
    VerticalShareList,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsVerticalShareListItemView]*/
    NewVerticalShareList,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsVerticalShareListItemViewNew]*/
    TitleWithIcon,  /**[com.interfun.buz.contacts.view.itemdelegate.ContactsTitleWithIconItemView]*/

}

fun getContactsListAdapter(
    callback: ContactsItemCallback,
    defaultEntryList: List<Any> = emptyList()
) = MultiTypeAdapter(defaultEntryList).apply {
    register(ContactsItemBean::class.java)
        .to(
            ContactsItemView(callback),
            ContactsNoPermissionItemView(callback),
            ContactsTitleItemView(),
            ContactsEntryItemView(callback),
            ContactsSpaceItemView(),
            ContactsEmptyItemView(),
            ContactsRecommendListItemView(callback),
            ContactsUnRegisterItemView(callback),
            ContactsSearchHintItemView(callback),
            ContactsSearchInviteItemView(callback),
            ContactsLargeTitleItemView(),
            ContactsHorizontalShareListItemView(callback),
            ContactsVerticalShareListItemView(callback),
            ContactsTitleWithIconItemView(callback = callback),
            ContactsHorizontalShareListItemViewNew(callback),
            ContactsVerticalShareListItemViewNew(callback)
        )
        .withKotlinClassLinker { _, item ->
            when (item.type) {
                ContactsItemType.EntryAddFriends,
                ContactsItemType.EntryCreateGroup,
                ContactsItemType.EntryRequests,
                ContactsItemType.EntryAIMarketsPlace -> ContactsEntryItemView::class

                ContactsItemType.Title -> ContactsTitleItemView::class
                ContactsItemType.Space -> ContactsSpaceItemView::class
                ContactsItemType.Empty -> ContactsEmptyItemView::class
                ContactsItemType.NoPermission -> ContactsNoPermissionItemView::class
                ContactsItemType.RecommendList -> ContactsRecommendListItemView::class

                ContactsItemType.SearchHint -> ContactsSearchHintItemView::class
                ContactsItemType.SearchInvite -> ContactsSearchInviteItemView::class
                ContactsItemType.UnRegister -> ContactsUnRegisterItemView::class

                ContactsItemType.LargeTitle -> ContactsLargeTitleItemView::class
                ContactsItemType.HorizontalShareList -> ContactsHorizontalShareListItemView::class
                ContactsItemType.VerticalShareList -> ContactsVerticalShareListItemView::class
                ContactsItemType.TitleWithIcon -> ContactsTitleWithIconItemView::class
                ContactsItemType.NewHorizontalShareList -> ContactsHorizontalShareListItemViewNew::class
                ContactsItemType.NewVerticalShareList -> ContactsVerticalShareListItemViewNew::class
                else -> ContactsItemView::class
            }
        }
}

fun ContactsItemBean.onItemClick(activity: Activity? = topActivity, from: Int? = null, currentPage: ContactsItemInPage) {
    if (activity == null || activity !is FragmentActivity) return
    activity.hideKeyboard()
    when (type) {
        ContactsItemType.EntryAddFriends -> {
            startActivity(PATH_CONTACTS_ACTIVITY_ADD_FRIENDS){
                withInt(RouterParamKey.Common.KEY_SOURCE, AddFriendPageSource.Contacts.value)
            }
            ContactsTracker.onClickAC2024120601()
        }
        ContactsItemType.EntryCreateGroup -> {
            if (ABTestManager.isFTUECreateGroupPlanB) {
                ChatNavigation.toCreateGroupActivity(activity,CreateGroupSource.Contact.value)
            } else {
                startActivity(PATH_CHAT_ACTIVITY_SELECT_GROUP_MEMBER) {
                    withInt(RouterParamKey.Common.KEY_SOURCE, CreateGroupSource.Contact.value)
                    withString(RouterParamKey.Group.KEY_MEMBER_USER_IDS, "${UserSessionManager.uid}")
                }
            }
            GroupFlowTracker.onClickCreateGroupEntrance(CreateGroupSource.Contact.value)
            ContactsTracker.onCreateGroupClick()
        }

        ContactsItemType.EntryRequests -> {
            startActivity(PATH_CONTACTS_ACTIVITY_REQUESTS)
        }

        ContactsItemType.RecommendEntry -> {
            startActivity(PATH_CONTACTS_ACTIVITY_RECOMMEND)
            ContactsTracker.onClickAC2024120604()
        }

        else -> {
            dispatch(
                group = {
                    routerServices<ChatService>().value?.getGroupInfoDialog(it.groupId)
                        ?.showDialog(activity)
                    ContactsTracker.onOpenGroupProfileDialog()
                },
                user = {
                    // 点击自己不打开个人资料页
                    if (it.userId.isMe()){
                        toast(R.string.vf_check_profile_in_setting_page)
                        return@dispatch
                    }
                    val trackerSource = if (currentPage == ContactsItemInPage.PAGE_CONTACT_HOME) {
                        ProfileSource.ON_CONTACTS.source
                    } else {
                        ProfileSource.SEARCH_PAGE_SUGGESTION.source // 除了通讯录页面上报ON_CONTACTS，其他上报SEARCH_PAGE_SUGGESTION保持之前逻辑，
                    }
                    routerServices<ContactsService>().value?.getProfileDialog(
                        userId = it.userId,
                        source = FriendApplySource.contact,
                        contactName = if (type.canShowPhoneContactName()) this.content else null,
                        businessId = null,
                        trackerSource = trackerSource,
                        isFromGroup = false,
                        isFromBlindBox = from == AddFriendPageSource.MissedBlindBox.value
                    )?.showDialog(activity)
                    ContactsTracker.onOpenUserProfileDialog()
                }
            )
        }
    }
}

val ContactsItemBean.isContentItem: Boolean
    get() = userInfo != null || groupInfo != null || contactInfo != null

private fun ContactsItemType.canShowPhoneContactName(): Boolean {
    return this == ContactsItemType.Recommend || this == ContactsItemType.RecommendHome || this == ContactsItemType.Contact
}

private fun startActivity(route: String, buildPostcardBlock: (Postcard.() -> Unit)? = null) =
    startActivityByRouter(route, {
        buildPostcardBlock?.invoke(this)
        withTransition(anim.anim_nav_enter, anim.anim_nav_exit)
    })

enum class ContactsItemInPage(page: Int) {
    PAGE_CONTACT_HOME(0), // 通讯录页面
    PAGE_SEARCH_CONTACT(1), // 通讯录搜索界面
    PAGE_CONTACT_ADD_FRIENDS(2), // 添加好友界面


}
