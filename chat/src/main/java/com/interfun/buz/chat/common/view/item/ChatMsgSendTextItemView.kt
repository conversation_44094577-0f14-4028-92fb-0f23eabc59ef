package com.interfun.buz.chat.common.view.item

import android.text.method.LinkMovementMethod
import androidx.core.text.buildSpannedString
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.appendSpace
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.centerVertical
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.dpFloat
import com.interfun.buz.base.ktx.getHolder
import com.interfun.buz.base.ktx.getItem
import com.interfun.buz.base.ktx.typeface
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.ChatMsgSendTextItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.common.view.widget.ChatTranslationItemView
import com.interfun.buz.chat.common.view.widget.ReplyItemView
import com.interfun.buz.chat.databinding.ChatItemSendTextBinding
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.utils.highLightLinkAndHandleClick
import kotlinx.coroutines.CoroutineScope

/**
 * Author: ChenYouSheng
 * Date: 2023/6/9
 * Email: <EMAIL>
 * Desc:
 */
class ChatMsgSendTextItemView(
    itemCallback: ChatItemCallback
) : BaseChatTextMsgItemView<ChatMsgSendTextItemBean, ChatItemSendTextBinding>(itemCallback) {


    override fun onViewHolderCreated(holder: BindingViewHolder<ChatItemSendTextBinding>) {
        super.onViewHolderCreated(holder)
        holder.binding.tvText.movementMethod = LinkMovementMethod.getInstance()
    }

    override fun onBindViewHolder(scope: CoroutineScope?, binding: ChatItemSendTextBinding, item: ChatMsgSendTextItemBean, position: Int) {
        super.onBindViewHolder(scope, binding, item, position)
        val text = createDisplayCharSequence(binding.root.context, item.msg)
        val topicText = if (item.isTopic) {
            buildSpannedString {
                typeface(FontUtil.fontIcon!!) {
                    centerVertical(16.dpFloat) {
                        append(R.string.ic_album_view_all.asString())
                    }
                }
                appendSpace(6.dp)
                append(text)
            }
        } else {
            text
        }
        binding.tvText.highLightLinkAndHandleClick(
            binding.clTextLayout,
            txtContent = topicText,
            linkColor = R.color.assistant_blue.asColor(),
            highlightLinkColor = R.color.overlay_black_14.asColor(),
            onLinkClickCallback = itemCallback::onLinkClick,
            onLinkLongClickCallback = { url ->
                itemCallback.onLinkLongClick(url = url, null)
            },
            onLongClickCallback = {
                var currentItem = item
                binding.getHolder()?.let { holder ->
                    if (holder.absoluteAdapterPosition in adapterItems.indices) {
                        currentItem = getItem(holder.absoluteAdapterPosition)
                    }
                }
                itemCallback.onLongClick(
                    anchorView = binding.clTextLayout,
                    portraitView = getPortraitImageView(binding),
                    item = currentItem
                )
            },
            onClickNoLink = {
                itemCallback.onItemClick(binding,item = item)
            }
        )
    }

    override fun getResendIcon(binding: ChatItemSendTextBinding) = binding.iftvSendFailed

    override fun getSendingLoading(binding: ChatItemSendTextBinding) = binding.lottieLoading

    override fun getAnchorView(binding: ChatItemSendTextBinding) = binding.clTextLayout
    override fun getTranslationView(binding: ChatItemSendTextBinding): ChatTranslationItemView? {
        return binding.translateContainer
    }

    override fun getReplyItemView(binding: ChatItemSendTextBinding): ReplyItemView? {
        return binding.replyView
    }
}