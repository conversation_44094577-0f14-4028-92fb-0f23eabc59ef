package com.interfun.buz.common.eventbus.wt

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc the eventbus event for friends wt online status update
 * @Author:lvzhen<PERSON><EMAIL>
 * @Date: 2022/10/25
 */
class WtFriendsOnlineStatusUpdateEvent(val onlineCount: Int, val userId: Long?) : BaseEvent() {

    companion object {
        fun post(onlineCount: Int, userId: Long? = null) {
            LiveEventBus.get(WtFriendsOnlineStatusUpdateEvent::class.java)
                .post(WtFriendsOnlineStatusUpdateEvent(onlineCount, userId))
        }
    }
}