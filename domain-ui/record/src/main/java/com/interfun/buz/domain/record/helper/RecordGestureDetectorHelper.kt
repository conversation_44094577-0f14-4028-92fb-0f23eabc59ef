package com.interfun.buz.domain.record.helper

import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.ui.input.pointer.AwaitPointerEventScope
import androidx.compose.ui.input.pointer.PointerInputChange
import com.interfun.buz.base.ktx.dpFloat
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.widget.area.IArea
import com.interfun.buz.core.widget_record.entity.RecordAreaType
import com.interfun.buz.core.widget_record.state.VoiceFilterRecordedBehavior
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.state.recordedBehavior
import com.interfun.buz.domain.record.entity.ReleaseReason
import kotlin.math.abs

/**
 * <AUTHOR>
 * @date 2024/12/4
 * @desc
 * 监听手势，判断是否为按下录音、滑动到取消或预览区域等用户行为
 * 需要注意的是：对手势的判断区域并非为某个单独的组件的区域，
 * 而是对整个BottomScreen（屏幕下半区）内的所有事件进行监听
 * 原因是声音滤镜模式开启下，除了区分手势是滑动还是按下，还要在按下时替换显示不同的组件
 * 当子组件消失时事件也无法连续，并且不同的子组件的坐标系也不同，计算很麻烦
 * 所以要监听root父组件，并将所有子组件的位置记录下来，同处于同一个坐标系内计算
 */
class RecordGestureDetectorHelper(
    private val touchSlop: Float,
    private val recordTransitionYWhenShowTabs: Float,
    private val getAreaLambda: (RecordAreaType) -> IArea,
    private val isRtlLambda: () -> Boolean,
    private val isShowVFTabs: () -> Boolean,
    private val isRecordingLambda: () -> Boolean,
    private val isLockingLambda: () -> Boolean,
    private val isVoiceFilterModeLambda: () -> Boolean,
    private val isVoiceFilterScrollingLambda: () -> Boolean,
    private val checkEnableAndToastWhenDisableLambda: () -> Boolean,
    private val currentVoiceFilterLambda: () -> VoiceFilterUiData?,
    private val onPressDown: () -> Unit,
    private val onLockRecord: () -> Unit,
    private val onPressUp: (reason: ReleaseReason) -> Unit,
    private val onPressAreaChange: (RecordAreaType) -> Unit,
    private val onLockGuideProgressChange: (Float) -> Unit,
) {

    companion object {
        const val TAG = "HomeBottomGestureDetector"
    }

    private val isRtl get() = isRtlLambda()
    private val isRecording get() = isRecordingLambda()
    private val isLocking get() = isLockingLambda()
    private val isVoiceFilterMode get() = isVoiceFilterModeLambda()
    private val currentVoiceFilter get() = currentVoiceFilterLambda()
    private val isVoiceFilterScrolling get() = isVoiceFilterScrollingLambda()
    private val recordTransitionY: Float
        get() {
            return if (isVoiceFilterMode && isShowVFTabs()) {
                recordTransitionYWhenShowTabs
            } else {
                0f
            }
        }
    private val recordArea by lazy { getAreaLambda(RecordAreaType.Record) }
    private val recordingArea by lazy { getAreaLambda(RecordAreaType.Recording) }
    private val recordingWhileLockingArea by lazy { getAreaLambda(RecordAreaType.RecordingWhileLocking) }
    private val cancelArea by lazy { getAreaLambda(RecordAreaType.Cancel) }
    private val previewArea by lazy { getAreaLambda(RecordAreaType.Preview) }
    private val lockArea by lazy { getAreaLambda(RecordAreaType.Lock) }
    private val voiceFilterRecordArea by lazy { getAreaLambda(RecordAreaType.VoiceFilterRecord) }
    private val topAreaOffset = 10.dpFloat

    fun create(): suspend AwaitPointerEventScope.() -> Unit = {
        val down = awaitFirstDown()
        logInfo(
            TAG,
            "Start: On finger down, "
                    + "isRecording: $isRecording, "
                    + "isLocking: $isLocking, "
                    + "isVoiceFilterMode: $isVoiceFilterMode, "
                    + "isVoiceFilterScrolling: $isVoiceFilterScrolling"
        )
        onLockGuideProgressChange(0f)
        if (isLocking) {
            // 在锁定状态下处理点击事件
            handleWhenLocked(down)
        } else {
            if (checkIsPressConfirmed(down)) {
                if (checkEnableAndToastWhenDisableLambda()) {
                    handleWhenPressConfirmed()
                } else {
                    logInfo(TAG, "Cancel: CheckIsCanRecord is false")
                }
            }
        }
    }

    /**
     * 确认当前是否按下（主要是需要判断VF模式下按下后并未滑动）
     */
    private suspend fun AwaitPointerEventScope.checkIsPressConfirmed(down: PointerInputChange): Boolean {
        val isInRecordArea = if (isVoiceFilterMode) {
            voiceFilterRecordArea.isInArea(down.position, offsetY = -recordTransitionY)
        } else {
            recordArea.isInArea(down.position)
        }
        if (!isInRecordArea) {
            logInfo(TAG, "Cancel: Not in button area")
            return false
        }
        return if (isVoiceFilterMode) {
            val firstDownX = down.position.x
            withTimeoutOrNull(200L) {
                // 短时间内持续监听手指是否移动或松开
                do {
                    val event = awaitPointerEvent()
                    // 如果手指松开，返回false
                    if (!event.changes.first().pressed) {
                        logInfo(TAG, "Cancel: Finger lifted before timeout")
                        return@withTimeoutOrNull false
                    }
                    // 如果手指移动，返回false
                    val gap = abs(event.changes.first().position.x - firstDownX)
                    if (gap > touchSlop) {
                        logInfo(TAG, "Cancel: Move detected, cancel press")
                        return@withTimeoutOrNull false
                    }
                    // 如果正在滚动，返回false
                    if (isVoiceFilterScrolling) {
                        logInfo(TAG, "Cancel: Scrolling detected, cancel press")
                        return@withTimeoutOrNull false
                    }
                } while (true)
                // 如果短时间内没有移动且未松开，确认按下
                true
            } ?: true
        } else {
            true
        }
    }

    private suspend fun AwaitPointerEventScope.handleWhenPressConfirmed() {
        logInfo(TAG, "Result: Press confirmed and can record, onPressChange return true")
        onPressDown()
        var isInCancel = false
        var isInPreview = false
        var isInLock = false
        var offsetYWhenRecording: Float? = null

        fun reset() {
            isInCancel = false
            isInPreview = false
            isInLock = false
            onPressAreaChange.invoke(RecordAreaType.None)
        }

        do {
            val event = awaitPointerEvent()
            if (isRecording) {
                val position = event.changes.first().position
                val currentY = position.y - recordTransitionY
                if (offsetYWhenRecording == null) {
                    offsetYWhenRecording = currentY
                } else {
                    if (currentY <= recordingArea.bottom && currentY > offsetYWhenRecording) {
                        offsetYWhenRecording = currentY
                    }
                }
                val startY = offsetYWhenRecording!!
                val endY = lockArea.bottom
                val progress = (currentY - startY) / (endY - startY)
                onLockGuideProgressChange(progress)

                val isInRecordingArea = recordingArea.isInArea(position, offsetY = -recordTransitionY)
                val isBelowHalf = currentY > recordingArea.centerY
                if (isInRecordingArea || isBelowHalf) {
                    reset()
                } else {
                    var isInCenterArea = false
                    val top = cancelArea.top - topAreaOffset
                    if (isRtl) {
                        val previewEndX = maxOf(previewArea.end, recordingArea.start)
                        val cancelStartX = minOf(cancelArea.start, recordingArea.end)
                        if (position.x < previewEndX && position.y > top) {
                            if (!isInPreview) {
                                reset()
                                isInPreview = true
                                onPressAreaChange.invoke(RecordAreaType.Preview)
                            }
                        } else if (position.x > cancelStartX && position.y > top) {
                            if (!isInCancel) {
                                reset()
                                isInCancel = true
                                onPressAreaChange.invoke(RecordAreaType.Cancel)
                            }
                        } else {
                            isInCenterArea = true
                        }
                    } else {
                        val cancelEndX = maxOf(cancelArea.end, recordingArea.start)
                        val previewStartX = minOf(previewArea.start, recordingArea.end)
                        if (position.x < cancelEndX && position.y > top) {
                            if (!isInCancel) {
                                reset()
                                isInCancel = true
                                onPressAreaChange.invoke(RecordAreaType.Cancel)
                            }
                        } else if (position.x > previewStartX && position.y > top) {
                            if (!isInPreview) {
                                reset()
                                isInPreview = true
                                onPressAreaChange.invoke(RecordAreaType.Preview)
                            }
                        } else {
                            isInCenterArea = true
                        }
                    }
                    if (isInCenterArea) {
                        if (!isInLock && progress > 0.5f) {
                            reset()
                            isInLock = true
                            onPressAreaChange.invoke(RecordAreaType.Lock)
                        }
                    }
                }
            }
        } while (event.changes.first().pressed)

        // 手指松开时，判断是否需要锁定录音
        logInfo(
            TAG,
            "End: On finger up. " +
                "isInCancel: $isInCancel, isInPreview: $isInPreview, " +
                "isInLock: $isInLock, isRecording: $isRecording"
        )

        if (isInLock && isRecording) {
            // 如果在锁定区域松手，不停止录音，保持锁定状态
            logInfo(TAG, "End: Lock recording because finger released in lock area")
            onLockRecord()
        } else {
            // 如果不在锁定区域松手，正常停止录音
            val reason = if (isInCancel) {
                ReleaseReason.USER_CANCEL
            } else if (isInPreview) {
                ReleaseReason.USER_PREVIEW
            } else {
                if (isVoiceFilterMode && currentVoiceFilter != null && currentVoiceFilter!!.recordedBehavior == VoiceFilterRecordedBehavior.Preview.value) {
                    ReleaseReason.USER_PREVIEW
                } else {
                    ReleaseReason.NORMAL
                }
            }
            logInfo(TAG, "End: onPressUp reason: $reason")
            onPressUp(reason)
        }
    }

    private suspend fun AwaitPointerEventScope.handleWhenLocked(down: PointerInputChange) {
        // 在锁定状态下，等待手指抬起后再根据位置触发对应的onPressUp(reason)
        logInfo(TAG, "Locked: Handle touch event in locked state")

        // 不需要在手指移动过程中实时更新状态，只在手指抬起时判断位置即可
        var finalPosition = down.position

        // 等待手指抬起，只记录最终位置，不做其他计算
        do {
            val event = awaitPointerEvent()
            finalPosition = event.changes.first().position
        } while (event.changes.first().pressed)

        // 手指抬起后，根据最后的位置判断是否在某个区域内
        // 只检查必要的区域，按优先级顺序检查，找到第一个包含位置的区域就返回
        when {
            // 先检查是否在取消区域
            cancelArea.isInArea(finalPosition, offsetY = -recordTransitionY) -> {
                logInfo(TAG, "Locked: Finger released in cancel area, cancel recording")
                onPressUp(ReleaseReason.USER_CANCEL)
            }
            // 再检查是否在预览区域
            previewArea.isInArea(finalPosition, offsetY = -recordTransitionY) -> {
                logInfo(TAG, "Locked: Finger released in preview area, preview recording")
                onPressUp(ReleaseReason.USER_PREVIEW)
            }
            // 最后检查是否在录音按钮区域
            recordingWhileLockingArea.isInArea(finalPosition, offsetY = -recordTransitionY) -> {
                logInfo(TAG, "Locked: Finger released in record area, stop and send")
                if (isVoiceFilterMode && currentVoiceFilter != null) {
                    if (currentVoiceFilter!!.recordedBehavior == VoiceFilterRecordedBehavior.Preview.value) {
                        onPressUp(ReleaseReason.USER_PREVIEW)
                        return
                    }
                }
                onPressUp(ReleaseReason.NORMAL)
            }
            // 不在任何区域，不执行操作
            else -> {
                logInfo(TAG, "Locked: Finger released outside all areas, do nothing")
            }
        }
    }
}