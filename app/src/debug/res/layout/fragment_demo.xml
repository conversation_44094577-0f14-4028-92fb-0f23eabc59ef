<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_90"
    android:paddingHorizontal="12dp">

    <Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="30dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceStatusBar"
        tools:itemCount="1"
        tools:listitem="@layout/item_button" />

    <EditText
        android:id="@+id/etUrl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:includeFontPadding="false"
        android:text="http://192.168.38.61:5173/feedback"
        android:textColor="@color/white"
        android:textSize="13sp"
        app:layout_constraintEnd_toStartOf="@+id/btnWebView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btnWebView" />

    <Button
        android:id="@+id/btnWebView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:text="WebView"
        android:textAllCaps="false"
        android:textColor="@color/black_90"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/etUrl"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />


</androidx.constraintlayout.widget.ConstraintLayout>