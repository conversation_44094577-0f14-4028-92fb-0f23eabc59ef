@file:Suppress("unused", "NOTHING_TO_INLINE")

package com.interfun.buz.base.ktx

import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListUpdateCallback
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.drakeet.multitype.ItemViewDelegate
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.coroutine.withReentrantLock
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withContext

typealias ItemsLiveData<T> = MutableLiveData<List<T>>

inline fun <reified T : Any> MultiTypeAdapter(delegate: ItemViewDelegate<T, *>) =
    MultiTypeAdapter { register(delegate) }

inline fun MultiTypeAdapter(block: MultiTypeAdapter.() -> Unit) =
    MultiTypeAdapter().apply(block)

inline fun <reified T : Any> MultiTypeAdapter.register(vararg delegate: ItemViewDelegate<T, *>) =
    register(T::class).to(*delegate)

inline fun <T : Any> MultiTypeAdapter.observeItemsChanged(
    owner: LifecycleOwner,
    items: LiveData<List<T>>,
    detectMoves: Boolean = true,
    noinline areItemsTheSame: (T, T) -> Boolean
) {
    items.observe(owner) {
        submitItems(it, detectMoves, areItemsTheSame)
    }
}

inline fun MultiTypeAdapter.getItem(position: Int) =
    items[position]

inline fun <T, VB : ViewBinding> ItemViewDelegate<T, BindingViewHolder<VB>>.getItem(position: Int): T =
    adapterItems[position] as T

inline fun <T, VH : RecyclerView.ViewHolder> ItemViewDelegate<T, VH>.safetyAdapter(): MultiTypeAdapter? {
    return try {
        adapter
    } catch (ignore: Exception) {
        null
    }
}

/**
 * this DiffUtil computing is on a main thread
 * recommend use 'AsyncListDiffer' to computing the difference between two lists via DiffUtil on a background thread.
 */
@Deprecated("This may cause main thread lag,use AsyncDiffMultiTypeAdapter/asyncSubmitItems instead")
fun <T : Any> MultiTypeAdapter.submitItems(
    newItems: List<T>,
    detectMoves: Boolean = true,
    areContentsTheSame: (T, T) -> Boolean = { oldItem, newItem ->
        oldItem == newItem
    },
    areItemsTheSame: (T, T) -> Boolean = { oldItem, newItem ->
        oldItem == newItem
    },
    changePayload: ((T, T) -> Any?)? = { _, _ -> null }
) {
    if (items.isEmpty()) {
        items = newItems
        notifyDataSetChanged()
        return
    }
    val oldItems = items
    val result = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
        @Suppress("UNCHECKED_CAST")
        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int) =
            areItemsTheSame(oldItems[oldItemPosition] as T, newItems[newItemPosition])

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int) =
            areContentsTheSame(oldItems[oldItemPosition] as T, newItems[newItemPosition])

        override fun getOldListSize() = oldItems.size
        override fun getNewListSize() = newItems.size

        //execution getChangePayload after  items = newItems
        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return changePayload?.invoke(oldItems[oldItemPosition] as T, newItems[newItemPosition])
        }
    }, detectMoves)
    items = newItems
    result.dispatchUpdatesTo(this)
}

fun <T : Any> MultiTypeAdapter.asyncSubmitItems(
    lifecycleCoroutineScope: LifecycleCoroutineScope,
    mutex: Mutex,
    newItems: List<T>,
    detectMoves: Boolean = true,
    areContentsTheSame: (T, T) -> Boolean = { oldItem, newItem ->
        oldItem == newItem
    },
    areItemsTheSame: (T, T) -> Boolean = { oldItem, newItem ->
        oldItem == newItem
    },
    changePayload: ((T, T) -> Any?)? = { _, _ -> null },
    finishCallback: DefaultCallback = {}
) {
    val adapter = this
    lifecycleCoroutineScope.launch {
        mutex.withReentrantLock {
            if (items.isEmpty()) {
                items = newItems
                notifyDataSetChanged()
                finishCallback.invoke()
                return@withReentrantLock
            }
            val oldItems = items
            val result = withIOContext {
                diffResult(
                    areItemsTheSame,
                    oldItems,
                    newItems,
                    areContentsTheSame,
                    changePayload,
                    detectMoves
                )
            }
            items = newItems
            result.dispatchUpdatesTo(adapter)
            finishCallback.invoke()
        }
    }
}

private fun <T : Any> diffResult(
    areItemsTheSame: (T, T) -> Boolean,
    oldItems: List<Any>,
    newItems: List<T>,
    areContentsTheSame: (T, T) -> Boolean,
    changePayload: ((T, T) -> Any?)?,
    detectMoves: Boolean
) = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
    @Suppress("UNCHECKED_CAST")
    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int) =
        areItemsTheSame(oldItems[oldItemPosition] as T, newItems[newItemPosition])

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int) =
        areContentsTheSame(oldItems[oldItemPosition] as T, newItems[newItemPosition])

    override fun getOldListSize() = oldItems.size
    override fun getNewListSize() = newItems.size

    //execution getChangePayload after  items = newItems
    override fun getChangePayload(
        oldItemPosition: Int,
        newItemPosition: Int
    ): Any? {
        return changePayload?.invoke(oldItems[oldItemPosition] as T, newItems[newItemPosition])
    }
}, detectMoves)

fun MultiTypeAdapter.addItemCountChangeListener(
    onItemCountChangeListener: OneParamCallback<Int>
): MultiTypeAdapter = apply {
    registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
        private fun onItemCountChange() {
            onItemCountChangeListener.invoke(<EMAIL>)
        }

        override fun onChanged() {
            super.onChanged()
            onItemCountChange()
        }

        override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
            super.onItemRangeInserted(positionStart, itemCount)
            onItemCountChange()
        }

        override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
            super.onItemRangeRemoved(positionStart, itemCount)
            onItemCountChange()
        }
    })
}

suspend fun <T : Any> List<T>.areListsTheSame(
    oldList: List<T>,
    areItemsTheSame: (T, T) -> Boolean = { oldItem, newItem -> oldItem == newItem },
    areContentsTheSame: (T, T) -> Boolean = { oldItem, newItem -> oldItem == newItem }
): Boolean = withContext(Dispatchers.IO) {
    var hasChanges = false

    val diffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
        override fun getOldListSize() = oldList.size
        override fun getNewListSize() = <EMAIL>

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int) =
            areItemsTheSame(oldList[oldItemPosition], this@areListsTheSame[newItemPosition])

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int) =
            areContentsTheSame(oldList[oldItemPosition], this@areListsTheSame[newItemPosition])

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return null
        }
    })
    diffResult.dispatchUpdatesTo(object : ListUpdateCallback {
        override fun onInserted(position: Int, count: Int) {
            hasChanges = true
        }

        override fun onRemoved(position: Int, count: Int) {
            hasChanges = true
        }

        override fun onMoved(fromPosition: Int, toPosition: Int) {
            hasChanges = true
        }

        override fun onChanged(position: Int, count: Int, payload: Any?) {
            hasChanges = true
        }
    })
    return@withContext !hasChanges
}

