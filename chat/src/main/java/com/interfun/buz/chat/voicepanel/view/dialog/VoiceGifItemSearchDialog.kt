package com.interfun.buz.chat.voicepanel.view.dialog

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import androidx.recyclerview.widget.RecyclerView.State
import coil.size.Size
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.BottomSheetCallback
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.ktx.isMsgPlayingOrOnCalling
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.databinding.ChatDialogVoicegifSearchBinding
import com.interfun.buz.chat.voicemoji.manager.VoiceMojiManager
import com.interfun.buz.chat.voicemoji.utils.VoiceMojiTracker
import com.interfun.buz.chat.voicepanel.callback.VoiceItemCollectResultCallback
import com.interfun.buz.chat.voicepanel.callback.VoiceItemPanelEventListener
import com.interfun.buz.chat.voicepanel.callback.itemview.VoiceGifItemClickCallBack
import com.interfun.buz.chat.voicepanel.model.CollectVoiceItemData
import com.interfun.buz.chat.voicepanel.model.SendVoiceGifData
import com.interfun.buz.chat.voicepanel.model.VoiceEmojiPanelType
import com.interfun.buz.chat.voicepanel.model.VoiceGifPreview
import com.interfun.buz.chat.voicepanel.view.itemview.holder.ItemViewType
import com.interfun.buz.chat.voicepanel.view.itemview.voicegif.VoiceGifItemView
import com.interfun.buz.chat.voicepanel.viewmodel.VoiceGifSearchViewModel
import com.interfun.buz.chat.wt.entity.isUser
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.common.eventbus.chat.CloseBottomPanelEvent
import com.interfun.buz.common.ktx.toastNetworkErrorTips
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.common.widget.view.EmptyDataType
import com.interfun.buz.common.widget.view.EmptyDataViewListener


class VoiceGifSearchDialog : BaseBottomSheetDialogFragment(), VoiceGifItemClickCallBack {

    companion object {
        const val TAG = "VoiceGifSearchDialog"
        private const val PANEL_TYPE = "panelType"
        fun newInstance(panelType: VoiceEmojiPanelType, listener: VoiceItemPanelEventListener) =
            VoiceGifSearchDialog().apply {
                setVoiceGifClickListener(listener)
                arguments = Bundle().apply {
                    putParcelable(PANEL_TYPE, panelType)
                }
            }
    }

    val binding: ChatDialogVoicegifSearchBinding by lazy {
        ChatDialogVoicegifSearchBinding.inflate(layoutInflater)
    }
    private val panelType: VoiceEmojiPanelType
        get() = arguments?.getParcelable(PANEL_TYPE) ?: VoiceEmojiPanelType.HomePagePanelType
    private lateinit var listener: VoiceItemPanelEventListener
    private var dismissAction: VoiceGifSearchDialogAction? = null

    private val mAdapter by lazy {
        MultiTypeAdapter {
            register(
                VoiceGifItemView(
                    this@VoiceGifSearchDialog,
                    VoiceEmojiPanelType.SearchListPanelType,
                    ItemViewType.SearchVG
                )
            )
        }
    }
    private val voiceGifSearchViewModel by fragmentViewModels<VoiceGifSearchViewModel>()

    override fun onCreateView(): View {
        return binding.root
    }

    override fun initView(view: View?) {
        mBehavior?.addBottomSheetCallback(object: BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    dismissAction = VoiceGifSearchDialogAction.SLIDE
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {}
        })
        binding.flPreview.layoutHeight(deviceHeight)
        initRecyclerView()
    }

    override fun getHeight(): Int {
        return requireContext.deviceHeightWithoutTopBottom - 32.dp
    }

    override fun initListener(view: View?) {
        binding.apply {
            etSearch.apply {
                setOnFocusChangeListener { _, hasFocus ->
                    if (hasFocus)
                        VoiceMojiTracker.onVoiceGifSearchDialogAction(VoiceGifSearchDialogAction.SEARCH)
                }
                doOnKeyActionSearch {
                    startLoading()
                    voiceGifSearchViewModel.startVoiceGifSearch(
                        content = text.toString().trim(),
                        isRefresh = true
                    )
                    hideKeyboard()
                }
            }
            tvCancel.click {
                dismissAction = VoiceGifSearchDialogAction.CANCEL
                etSearch.hideSoftInput()
                dismiss()
            }
            vHandle.click {
                dismissAction = VoiceGifSearchDialogAction.SLIDE
                etSearch.hideSoftInput()
                dismiss()
            }
        }
        mBehavior?.addBottomSheetCallback(object: BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_DRAGGING) {
                    binding.etSearch.hideSoftInput()
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
            }
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        voiceGifSearchViewModel.searchResultFlow.collectIn(fragment) { uiState ->
            if (uiState != null) {
                val positionStart = mAdapter.items.size
                if (uiState.isLoadMore) {
                    if (uiState.isReqError) {
                        toastNetworkErrorTips()
                    } else {
                        mAdapter.items = mAdapter.items.toMutableList().also {
                            it.addAll(uiState.dataList)
                        }
                        mAdapter.notifyItemRangeInserted(positionStart, uiState.dataList.size)
                    }
                    binding.refreshLayout.visibleIf(mAdapter.items.isNotEmpty())
                    if (uiState.isLastPage) {
                        R.string.unable_to_load.toast()
                        binding.refreshLayout.finishLoadMoreWithNoMoreData()
                        binding.refreshLayout.setNoMoreData(true)
                    }else{
                        binding.refreshLayout.finishLoadMore()
                    }
                } else {
                    stopLoading()
                    // 首次刷新
                    if (uiState.isReqError) {
                        showNoNetworkError()
                    } else if (uiState.dataList.isEmpty()) {
                        showEmptyDataError()
                    } else {
                        mAdapter.items = uiState.dataList
                        mAdapter.notifyDataSetChanged()
                        binding.emptyDataView.gone()
                        binding.refreshLayout.visible()
                        binding.etSearch.showKeyboardDelay()
                    }
                    binding.refreshLayout.setNoMoreData(uiState.isLastPage)
                }
                ChatTracker.onGifSearchResult(
                    isSuccess = !uiState.isReqError,
                    searchContent = uiState.queryContent,
                    errorCode = uiState.errorCode,
                )
            }
        }
        startLoading()
        voiceGifSearchViewModel.startVoiceGifSearch(isRefresh = true)
    }

    private fun initRecyclerView() {
        binding.rvVoiceGifList.apply {
            val spanSize = 3
            val itemSpacing = 5.dp
            adapter = mAdapter
            layoutManager = GridLayoutManager(<EMAIL>, spanSize)
            addItemDecoration(object : ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: State
                ) {
                    // Reset all offsets to 0
                    outRect.apply {
                        top = 0.dp
                        bottom = itemSpacing
                        left = itemSpacing / 2
                        right = itemSpacing / 2

                        val itemSpanPosition = parent.getChildAdapterPosition(view)
                        if (LanguageManager.isArLanguage()) {
                            when (itemSpanPosition % spanSize) {
                                // first column
                                0 -> {
                                    left = itemSpacing / 3 * 2
                                    right = 0
                                }

                                1, spanSize - 2 -> {
                                    // second column
                                    if (itemSpanPosition % spanSize == 1) {
                                        right = itemSpacing / 3
                                    }

                                    // second last column
                                    if (itemSpanPosition % spanSize == spanSize - 2) {
                                        left = itemSpacing / 3
                                    }
                                }

                                // last column
                                spanSize - 1 -> {
                                    left = 0
                                    right = itemSpacing / 3 * 2
                                }
                            }
                        } else {
                            when (itemSpanPosition % spanSize) {
                                // first column
                                0 -> {
                                    left = 0
                                    right = itemSpacing / 3 * 2
                                }

                                1, spanSize - 2 -> {
                                    // second column
                                    if (itemSpanPosition % spanSize == 1) {
                                        left = itemSpacing / 3
                                    }
                                    // second last column
                                    if (itemSpanPosition % spanSize == spanSize - 2) {
                                        right = itemSpacing / 3
                                    }
                                }

                                // last column
                                spanSize - 1 -> {
                                    left = itemSpacing / 3 * 2
                                    right = 0
                                }
                            }
                        }
                    }
                }
            })
        }
        binding.refreshLayout.setEnableRefresh(false)
        binding.refreshLayout.setEnableLoadMore(true)
        binding.refreshLayout.setOnLoadMoreListener {
            voiceGifSearchViewModel.startVoiceGifSearch(
                content = binding.etSearch.text.toString(),
                isRefresh = false
            )
        }
    }

    private fun showEmptyDataError() {
        binding.emptyDataView.apply {
            setType(EmptyDataType.NO_DATA)
            setText(R.string.no_results_found.asString())
        }
        binding.emptyDataView.visible()
        binding.refreshLayout.gone()
    }

    private fun showNoNetworkError() {
        val isNetworkAvailable = isNetworkAvailable
        binding.emptyDataView.apply {
            setType(EmptyDataType.NO_NETWORK)
            setText(if (isNetworkAvailable) R.string.network_is_unstable.asString() else R.string.voice_call_network_error.asString())
            setButton(if (isNetworkAvailable) R.string.retry.asString() else "")
            setListener(object : EmptyDataViewListener {
                override fun onButtonClicked() {
                    voiceGifSearchViewModel.startVoiceGifSearch(
                        binding.etSearch.text.toString(),
                        isRefresh = true
                    )
                }
            })
        }
        binding.emptyDataView.visible()
        binding.refreshLayout.gone()
    }

    fun setVoiceGifClickListener(listener: VoiceItemPanelEventListener) {
        this.listener = listener
    }

    override fun onVoiceGifClick(
        voiceGifItem: VoiceGifEntity,
        location: IntArray,
        size: Size,
        index: Int
    ) {
        VoiceMojiTracker.onVoiceGifSearchDialogAction(VoiceGifSearchDialogAction.PRESS)
        binding.etSearch.hideSoftInput()
        listener.sendVoiceItem(
            voiceItem = SendVoiceGifData(voiceGifItem, size, location)
        )
        CloseBottomPanelEvent.post()
        dismiss()
    }

    override fun onVoiceGifLongClick(
        voiceGifItem: VoiceGifEntity,
        collectId: Long?,
        location: IntArray,
        size: Size,
        viewType: ItemViewType,
        index: Int
    ) {
        VoiceMojiTracker.onVoiceGifSearchDialogAction(VoiceGifSearchDialogAction.LONG_PRESS)
        binding.etSearch.hideSoftInput()
        val sendToUser = WTStatusManager.currentSelectedItem?.userInfo?.userName
        val sendToGroup = WTStatusManager.currentSelectedItem?.groupInfo?.groupName
        showPreviewView(
            previewData = VoiceGifPreview(
                sendTo = if (WTStatusManager.currentSelectedItem.isUser) sendToUser else sendToGroup,
                voiceGifEntity = voiceGifItem,
                location = location,
                size = size,
                onSendClickCallback = { voiceItem ->
                    listener.sendVoiceItem(
                        voiceItem = voiceItem,
                        fromPreview = true,
                    )
                    CloseBottomPanelEvent.post()
                    dismiss()
                },
                onOperateCollectCallback = { collectionData, previewView ->
                    listener.collectionVoiceItem(
                        collectionData = collectionData,
                        fromPreview = true,
                        callback = object : VoiceItemCollectResultCallback {
                            override fun onResult(code: Int, collectionData: CollectVoiceItemData) {
                                previewView.playGoneAnim()
                            }
                        }
                    )
                }
            ),
            viewType = viewType
        )
    }

    private fun showPreviewView(previewData: VoiceGifPreview, viewType: ItemViewType) {
        if (isMsgPlayingOrOnCalling()) {
            return
        }
        VoiceMojiManager.showPreviewView(
            fragment = fragment,
            panelType = panelType,
            previewData = previewData,
            previewView = binding.flPreview,
            hasAddedToFavorite = viewType == ItemViewType.CollectionVG || viewType == ItemViewType.CollectionVE
        )
    }

    private fun startLoading() {
        binding.emptyDataView.gone()
        binding.rvVoiceGifList.invisible()
        binding.vLoading.apply {
            visible()
            binding.vLoading.startLoading()
        }
    }

    private fun stopLoading() {
        binding.rvVoiceGifList.visible()
        binding.vLoading.apply {
            invisible()
            stopLoading()
        }
    }

    override fun onDetach() {
        super.onDetach()
        if (dismissAction != null) {
            VoiceMojiTracker.onVoiceGifSearchDialogAction(dismissAction!!)
            dismissAction = null
        } else {
            VoiceMojiTracker.onVoiceGifSearchDialogAction(VoiceGifSearchDialogAction.BACK)
        }
    }
}

enum class VoiceGifSearchDialogAction {
    SEARCH,
    CANCEL,
    BACK,
    SLIDE,
    PRESS,
    LONG_PRESS
}
