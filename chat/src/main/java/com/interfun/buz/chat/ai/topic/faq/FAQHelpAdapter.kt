package com.interfun.buz.chat.ai.topic.faq

import com.drakeet.multitype.MultiTypeAdapter

class FAQHelpAdapter() : MultiTypeAdapter() {
    init {
        setHasStableIds(true)
        register(FAQTopDesDelegateItem())
        register(FAQNormalDescDelegateItem())
        register(FAQLinkDescDelegateItem())
        register(FAQFeedbackDelegateItem())
    }

    override fun getItemId(position: Int): Long {
        val id = (items[position] as FAQItemBean).id
        return id
    }
}