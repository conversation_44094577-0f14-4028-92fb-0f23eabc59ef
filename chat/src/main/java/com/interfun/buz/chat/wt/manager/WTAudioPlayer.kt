@file:OptIn(DelicateCoroutinesApi::class)

package com.interfun.buz.chat.wt.manager

import android.net.Uri
import android.os.Bundle
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.emitInScope
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.domain.record.helper.TraceUtils
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.manager.ZeroDataAudioHelper
import com.interfun.buz.common.utils.BuzTracker
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.PlayEventListener
import com.lizhi.component.tekiplayer.TekiPlayer
import com.lizhi.component.tekiplayer.analyzer.Reporter
import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.component.tekiplayer.datasource.cache.LruCacheEvictor
import com.lizhi.component.tekiplayer.util.aes.AESUtil
import com.lizhi.fm.e2ee.aes.AESUtils
import com.lizhi.fm.e2ee.aes.IAesComponent
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import java.lang.RuntimeException
import java.util.Collections
import java.util.LinkedList

object WTAudioPlayer {
    private val TAG = "WTAudioPlayer"
    var playerCallback: PlayEventListener? = null
    @Volatile
    var playerCurrItem: MediaItem? = null
    private val scope = GlobalScope
    @Volatile
    var isPlaying = false
    @Deprecated("use isPlaying or WTMessageManager.messageFlow instead")
    val isPlayingFlow = MutableStateFlow(false)
    private val itemList = Collections.synchronizedList(LinkedList<MediaItem>())
    private val playEventListener = object : PlayEventListener {

        override fun onBufferedPositionUpdate(index: Int, item: MediaItem?, bufferPosition: Long) {
            logInfo(TAG, "onBufferedPositionUpdate index=$index bufferPosition=$bufferPosition")
            playerCallback?.onBufferedPositionUpdate(index, item, bufferPosition)
        }

        override fun onError(errCode: Int, message: String) {
            logInfo(TAG,"PlayEventListener onError errcode $errCode message $message")
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID,"TT2024042201")
                put(TrackConstant.KEY_EVENT_NAME,"TekiplayerErr")
                put(TrackConstant.KEY_CONTENT_1,"1")
                put(TrackConstant.KEY_CONTENT_1,"code:${errCode} message:${message}")
            }
            playerCurrItem = null
            playerCallback?.onError(errCode, message)
            setPlayingState(false)
        }

        override fun onPlayListFinished() {
            logInfo(TAG, "onPlayListFinished")
            playerCurrItem = null
            playerCallback?.onPlayListFinished()
            setPlayingState(false)
        }

        override fun onPlayListUpdate() {
            logInfo(TAG, "onPlayListUpdate")
            playerCallback?.onPlayListUpdate()
        }

        override fun onPlayZeroItem(item: MediaItem?) {
            logInfo(TAG, "PlayEventListener onPlayZeroItem $item")
            if (item!=null) {
                ZeroDataAudioHelper.report(item,ZeroDataAudioHelper.SOURCE_WTAUDIOPLAYER)
            }
        }

        override fun onPlaybackChange(
            index: Int,
            item: MediaItem?,
            lastPosition: Long,
            reason: Int
        ) {
            playerCurrItem = item
            if (item == null){
                setPlayingState(false)
            }
            logInfo(TAG, "onPlaybackChange index=$index reason=$reason,tag:${item?.tag},url:${item?.uri?.toString()}")
            playerCallback?.onPlaybackChange(index, item, lastPosition, reason)
        }

        override fun onPlaybackRemoveOnList() {
            logInfo(TAG, "onPlaybackRemoveOnList")
            playerCallback?.onPlaybackRemoveOnList()
        }

        override fun onPlaybackStateChange(status: Int) {
            logInfo(TAG, "onPlaybackStateChange -- play state:$status")
            when (status) {
                PlayerState.STATE_PLAYING -> setPlayingState(true)
                PlayerState.STATE_ENDED,
                PlayerState.STATE_PAUSED,
                PlayerState.STATE_IDLE -> setPlayingState(false)
            }
            playerCallback?.onPlaybackStateChange(status)
        }

        override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
            if (index == -1) {
                setPlayingState(false)
            }
            playerCallback?.onPlayedPositionUpdate(index, item, position)
        }
    }
    private val audioPlayerLazy = lazy {
        TekiPlayer.Builder(appContext).build().apply {
            addPlayEventListener(playEventListener)
            pauseWhenAudioFocusLost = false
            autoHandleAudioFocus = false
        }
    }
    private val audioPlayer by audioPlayerLazy

    fun add(
        tag: String,
        audioUri: Uri,
        durationMs: Long,
        msgTraceId: String? = null,
        aesComponent: IAesComponent? = null,
        highPriority: Boolean = false,
        serMsgId:String? = "",
    ) {
        logInfo(TAG, "add originSize:${itemList.size},tekiPlayerSize:${audioPlayer.getMediaItemList().size}, audioUri = $audioUri")
        TraceUtils.receiveVoiceFileInfo("1",msgTraceId?:"",durationMs,audioUri.toString())
        val item =
            MediaItem.Builder()
                .setUri(audioUri)
                .apply {
                    if (durationMs>0){
                        setSpecifiedDuration(durationMs)
                    }
                }
                .setTag(tag)
                .setExtraData(Bundle().apply {
                    if (msgTraceId.isNullOrBlank().not()){
                        putString(Reporter.REPORT_KEY_TRACE_ID, msgTraceId)
                    }
                    if (highPriority) {
                        putBoolean(LruCacheEvictor.CACHE_PRIORITY, true)
                    }
                    putString(ZeroDataAudioHelper.KEY_SER_MSG_ID,serMsgId?:"")
                    aesComponent?.let {
                        putString(
                            AESUtil.AES_KEY, AESUtils.encodeToBase64(aesComponent.getSecretKeySpec().encoded))
                        putString(
                            AESUtil.AES_IV, AESUtils.encodeToBase64(aesComponent.getIvParameterSpec().iv))
                    }
                })
                .build()
        itemList.add(item)
        audioPlayer.addMediaItem(item)
    }

    fun remove(tag: String) {
        logInfo(
            TAG,
            "remove tag:${tag},origin size:${itemList.size},tekiPlayerSize:${audioPlayer.getMediaItemList().size}"
        )
        var removeItem: MediaItem? = null
        for (item in itemList) {
            if (item.tag == tag) {
                removeItem = item
                break
            }
        }
        removeItem?.let {
            logInfo(
                TAG,
                "remove internal item:${removeItem.tag}"
            )
            itemList.remove(it)
            audioPlayer.removeItem(it)
        }
    }
    private fun setPlayingState(isPlaying: Boolean) {
        synchronized(this.isPlayingFlow) {
            if (this.isPlaying != isPlaying) {
                this.isPlaying = isPlaying
                isPlayingFlow.emitInScope(scope, isPlaying)
            }
        }
    }

    fun getSize(): Int {
        if (audioPlayerLazy.isInitialized().not()){
            return 0
        }
        return audioPlayer.getMediaItemList().size
    }

    fun prepareAudio() {
        audioPlayer.prepare()
    }

    fun pauseAudio() {
        setPlayingState(false)
        if (audioPlayerLazy.isInitialized()){
            audioPlayer.pause()
        }
    }

    fun resumeAudio() {
        logInfo(TAG, "resumeAudio size:${audioPlayer.getMediaItemList().size}")
        setPlayingState(true)
        prepareAudio()
        audioPlayer.resume()
    }

    fun resumeOrPlay() {
        if (audioPlayer.getStatus() == PlayerState.STATE_PAUSED) {
            resumeAudio()
        } else {
            playAudio()
        }
    }

    fun playAudio() {
        val state = audioPlayer.getStatus()
        if (isPlaying && (state == PlayerState.STATE_PLAYING || state == PlayerState.STATE_BUFFERING)) {
            logInfo(TAG, "playAudio isPlaying,return")
            return
        }
        logInfo(TAG, "playAudio ,origin size:${itemList.size},tekiPlayerSize:${audioPlayer.getMediaItemList().size}")
        setPlayingState(true)
        prepareAudio()
        audioPlayer.play()
    }

    fun playAudio(index: Int) {
        setPlayingState(true)
        prepareAudio()
        audioPlayer.play(index)
    }

    fun seekTo(index: Int, progress: Float) {
        audioPlayer.seekTo(index, progress)
    }

    fun clear() {
        logInfo(TAG, "clear size:${getSize()},isInit:${audioPlayerLazy.isInitialized()}")
        itemList.clear()
        if (audioPlayerLazy.isInitialized()){
            audioPlayer.stop()
            audioPlayer.clear()
        }
        setPlayingState(false)
    }

    fun stop(){
        logInfo(TAG, "stop size:${audioPlayer.getMediaItemList().size}")
        if (audioPlayerLazy.isInitialized()){
            audioPlayer.stop()
        }
        setPlayingState(false)
    }

    fun getCurrentPosition():Long {
        return audioPlayer.getPosition()
    }
}