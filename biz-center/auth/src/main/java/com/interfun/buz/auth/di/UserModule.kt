package com.interfun.buz.auth.di

import com.interfun.buz.auth.repository.UserAuthRepository
import com.interfun.buz.component.hilt.UserComponentManager
import com.interfun.buz.auth.manager.UserComponentManagerImpl
import com.interfun.buz.auth.repository.UserAuthRepositoryImpl
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope

@Module
@InstallIn(SingletonComponent::class)
internal abstract class UserSingletonModule {

    companion object {


        /**
         * 如果谁修改，这里一定不要添加 singleton 注解,这个需要每次都执行，否则会一直缓存第一个用户
         */
        @Provides
        fun provideUserComponent(userComponentManager: UserComponentManager): UserComponent {
            return userComponentManager.getUserComponentFlow().value
        }
    }


    @Binds
    abstract fun provideUserComponentManager(manager: UserComponentManagerImpl): UserComponentManager

    @Binds
    abstract fun provideUserAuthManager(userAuthRepository: UserAuthRepositoryImpl): UserAuthRepository

}

@Module
@InstallIn(UserComponent::class)
internal abstract class UserModule {

    /**
     * CoroutineScope在UserComponent中默认提供
     */
    @Binds
    @UserQualifier
    abstract fun provideUserScope(coroutineScope: CoroutineScope): CoroutineScope


    /**
     * userId在UserComponent中默认提供
     */
    @Binds
    @UserQualifier
    abstract fun provideUserId(userId: Long): Long


    /**
     * UserAuth在UserComponent中默认提供
     */
    @Binds
    @UserQualifier
    abstract fun provideUserAuth(userAuth: UserAuth): UserAuth

}

