package org.jack.i18nimpor/*
 * Copyright 2022 The Android Open Source Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       https://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.TaskAction
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.Optional
import java.io.File
import java.io.FileWriter
import java.nio.file.Files
import java.nio.file.Paths
import org.w3c.dom.Document
import org.w3c.dom.Element
import org.w3c.dom.Node
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.transform.TransformerFactory
import javax.xml.transform.dom.DOMSource
import javax.xml.transform.stream.StreamResult
import groovy.json.JsonSlurper
import java.net.URL
import java.net.HttpURLConnection
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.nio.charset.StandardCharsets
import java.nio.file.StandardCopyOption
import org.gradle.api.tasks.options.Option

abstract class I18nTask: org.gradle.api.DefaultTask() {
    @get:Input
    @get:Option(option = "businessTag", description = "Business tag for filtering lokalise keys")
    abstract val businessTag: Property<String>
    
    @get:Input
    @get:Optional
    @get:Option(option = "configPath", description = "Path to localizable_config.json")
    abstract val configPath: Property<String>
    
    private val tmpDir = "temp"
    private val specialString = "~!@#$%^&*()+-*/<>,.[]/"
    
    @TaskAction
    fun printMessage() {
        logger.lifecycle("Starting I18n import task with business tag: ${businessTag.get()}")
        
        // 创建临时目录
        val tempDirPath = project.projectDir.resolve(tmpDir)
        if (Files.exists(tempDirPath.toPath())) {
            tempDirPath.deleteRecursively()
        }
        tempDirPath.mkdirs()
        
        // 读取配置文件
        val configFilePath = if (configPath.isPresent) {
            configPath.get()
        } else {
            project.rootProject.projectDir.resolve("localizable_config.json").absolutePath
        }
        
        logger.lifecycle("Reading config from: $configFilePath")
        val configFile = File(configFilePath)
        if (!configFile.exists()) {
            throw IllegalArgumentException("Config file not found: $configFilePath")
        }
        
        val configJson = JsonSlurper().parse(configFile) as Map<String, Any>
        val removeDefaultLanguage = configJson["removeDefaultLanguage"] as String
        val business = configJson["business"] as String
        val languageDic = configJson["languages"] as Map<String, String>
        val languageFileName = configJson["fileName"] as String
        val defaultLanguage = configJson["default"] as String
        
        val defaultTargetPath = project.projectDir
            .resolve("src/main/res/values")
        val defaultXmlPath = tempDirPath.resolve("default.xml")
        
        // 创建默认XML文件
        makeDefaultXml(defaultXmlPath)
        
        // 从Lokalise获取数据
        val originalData = fetchDataFromLokalise(businessTag.get())
        val allLanguage = parseData(originalData)
        
        // 处理每种语言的翻译
        for ((language, translations) in allLanguage) {
            writeTranslationInXml(language, translations, languageDic,
                                 languageFileName, defaultLanguage, tempDirPath, project.projectDir)
        }
        
        // 确保目标目录存在
        if (!defaultTargetPath.exists()) {
            defaultTargetPath.mkdirs()
        }
        
        // 移动生成的XML文件到目标目录
        for (language in languageDic.keys) {
            try {
                val filepath = tempDirPath.resolve("$language-language.xml")
                val targetPath = project.projectDir
                    .resolve("src/main/res/values-${languageDic[language]}")
                
                if (language == defaultLanguage) {
                    Files.copy(
                        filepath.toPath(),
                        defaultTargetPath.resolve(languageFileName).toPath(),
                        StandardCopyOption.REPLACE_EXISTING
                    )
                }
                
                if (removeDefaultLanguage == "true" && language == defaultLanguage) {
                    continue
                }
                
                if (!targetPath.exists()) {
                    targetPath.mkdirs()
                }
                
                Files.move(
                    filepath.toPath(),
                    targetPath.resolve(languageFileName).toPath(),
                    StandardCopyOption.REPLACE_EXISTING
                )
            } catch (e: Exception) {
                logger.error("Error processing language: $language", e)
                throw e
            }
        }
        
        logger.lifecycle("I18n import completed successfully")
    }
    
    /**
     * 从Lokalise获取数据
     */
    private fun fetchDataFromLokalise(businessTag: String): String {
        val url = URL("https://api.lokalise.com/api2/projects/5348674867eccf20d66141.84358942:branch/keys?include_translations=1&filter_tags=$businessTag")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "GET"
        connection.setRequestProperty("accept", "application/json")
        connection.setRequestProperty("X-Api-Token", "0ca1d12bc926a9273ebc3843dad0fb233beef8b4")
        
        val responseCode = connection.responseCode
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw RuntimeException("Failed to fetch data from Lokalise: HTTP error code $responseCode")
        }
        
        val reader = BufferedReader(InputStreamReader(connection.inputStream))
        val response = StringBuilder()
        var line: String?
        while (reader.readLine().also { line = it } != null) {
            response.append(line)
        }
        reader.close()
        
        return response.toString()
    }
    
    /**
     * 解析Lokalise返回的数据
     */
    private fun parseData(data: String): Map<String, List<Pair<String, String>>> {
        val result = JsonSlurper().parseText(data) as Map<String, Any>
        val keys = result["keys"] as? List<Map<String, Any>> ?: return emptyMap()
        
        val languageMaps = mutableMapOf<String, MutableList<Pair<String, String>>>()
        
        for (keyAndValue in keys) {
            val keyName = (keyAndValue["key_name"] as? Map<String, Any>)?.get("android") as? String ?: continue
            val translations = keyAndValue["translations"] as? List<Map<String, Any>> ?: continue
            
            for (translationItem in translations) {
                val language = translationItem["language_iso"] as String
                val value = translationItem["translation"] as String
                
                val reflectLanguage = getCorrectIsoLanguage(language)
                if (!languageMaps.containsKey(reflectLanguage)) {
                    languageMaps[reflectLanguage] = mutableListOf()
                }
                
                languageMaps[reflectLanguage]?.add(Pair(keyName, value))
            }
        }
        
        // 打印语言和键值对信息
        for ((language, pairs) in languageMaps) {
            logger.lifecycle("language = $language")
            for ((key, value) in pairs) {
                logger.lifecycle("key = $key, value = $value")
            }
        }
        
        return languageMaps
    }
    
    /**
     * 获取正确的ISO语言代码
     */
    private fun getCorrectIsoLanguage(languageIsoCode: String): String {
        return when (languageIsoCode) {
            "zh_CN" -> "zh"
            "zh_Hant_TW" -> "zh-Hant"
            "id" -> "in"
            else -> languageIsoCode
        }
    }
    
    /**
     * 读取XML文件
     */
    private fun readXml(language: String, fileName: String, defaultLanguage: String,
                       projectDir: File, defaultXmlPath: File): Document {
        val filePath = if (language == defaultLanguage) {
            projectDir.resolve("src/main/res/values/$fileName")
        } else {
            projectDir.resolve("src/main/res/values-$language/$fileName")
        }
        
        logger.lifecycle("\u001B[32mstart load language.xml:$language address:$filePath\u001B[0m")
        
        val docBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder()
        val doc = if (filePath.exists()) {
            docBuilder.parse(filePath)
        } else {
            docBuilder.parse(defaultXmlPath)
        }
        
        logger.lifecycle("\u001B[32mend load language.xml:$language address:$filePath\u001B[0m \u001B[34mSuccess\u001B[0m")
        return doc
    }
    
    /**
     * 创建默认XML文件
     */
    private fun makeDefaultXml(defaultXmlPath: File) {
        try {
            // 创建一个格式化的XML文档
            val docFactory = DocumentBuilderFactory.newInstance()
            val docBuilder = docFactory.newDocumentBuilder()
            val doc = docBuilder.newDocument()
            
            // 创建根元素
            val rootElement = doc.createElement("resources")
            rootElement.setAttribute("xmlns:tools", "http://schemas.android.com/tools")
            rootElement.setAttribute("tools:ignore", "MissingTranslation,TypographyEllipsis")
            doc.appendChild(rootElement)
            
            // 使用Transformer写入文件，保持格式
            val transformerFactory = TransformerFactory.newInstance()
            val transformer = transformerFactory.newTransformer()
            
            // 设置XML输出格式
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "yes")
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml")
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4")
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8")
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.DOCTYPE_PUBLIC, "yes")
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "no")
            
            val source = DOMSource(doc)
            val result = StreamResult(defaultXmlPath)
            transformer.transform(source, result)
            
            logger.lifecycle("生成了格式化的默认XML文件")
        } catch (e: Exception) {
            logger.error("\u001B[31makeDefaultXml error + ${e.message}\u001B[0m")
            throw e
        }
    }
    
    /**
     * 替换特殊字符
     */
    private fun replaceFirstSpecialLetter(string: String): String {
        if (string.isNotEmpty()) {
            val firstLetter = string[0]
            if (firstLetter == '@') {
                return "\\@${string.substring(1)}"
            }
        }
        return string
    }
    
    /**
     * 处理语言翻译
     */
    private fun handleLanguage(language: String, translations: List<Pair<String, String>>, doc: Document, filepath: File) {
        val resources = doc.documentElement
        val strings = resources.getElementsByTagName("string")
        
        // 移除已有的相同版本的字符串
        val nodesToRemove = mutableListOf<Node>()
        for (i in 0 until strings.length) {
            val element = strings.item(i) as Element
            if (element.hasAttribute("version") && element.getAttribute("version") == businessTag.get()) {
                nodesToRemove.add(element)
            }
        }
        
        for (node in nodesToRemove) {
            resources.removeChild(node)
        }
        
        // 清理空白文本节点，避免多余的空白行
        cleanupWhitespaceNodes(resources)
        
        // 添加新的翻译
        for ((key, value) in translations) {
            val node = doc.createElement("string")
            var processedValue = value.replace("\\'", "'")
            processedValue = processedValue.replace("'", "\\'")
            processedValue = replaceFirstSpecialLetter(processedValue)
            processedValue = processedValue.replace("\"", "\\\"")
            
            node.textContent = processedValue
            node.setAttribute("name", key)
            node.setAttribute("version", businessTag.get())
            resources.appendChild(node)
        }
        
        // 写入文件，添加格式化配置
        val transformerFactory = TransformerFactory.newInstance()
        val transformer = transformerFactory.newTransformer()
        
        // 设置XML输出格式
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "yes")
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml")
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4")
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8")
        
        val source = DOMSource(doc)
        val result = StreamResult(filepath)
        transformer.transform(source, result)
        
        logger.lifecycle("生成了格式化的XML文件，语言: $language")
    }
    
    /**
     * 清理空白文本节点，避免生成多余的空白行
     */
    private fun cleanupWhitespaceNodes(element: Element) {
        val childNodes = element.childNodes
        val nodesToRemove = mutableListOf<Node>()
        
        for (i in 0 until childNodes.length) {
            val node = childNodes.item(i)
            if (node.nodeType == Node.TEXT_NODE) {
                val textContent = node.textContent
                // 如果是只包含空白字符的文本节点，标记为删除
                if (textContent.isBlank()) {
                    nodesToRemove.add(node)
                }
            }
        }
        
        // 移除空白文本节点
        for (node in nodesToRemove) {
            element.removeChild(node)
        }
    }
    
    /**
     * 获取XML中的键
     */
    private fun xmlKeys(doc: Document): MutableSet<String> {
        val keyValues = mutableSetOf<String>()
        val resources = doc.documentElement
        val strings = resources.getElementsByTagName("string")
        
        val nodesToRemove = mutableListOf<Node>()
        for (i in 0 until strings.length) {
            val element = strings.item(i) as Element
            if (element.hasAttribute("version") && element.getAttribute("version") == businessTag.get()) {
                nodesToRemove.add(element)
                continue
            }
            
            keyValues.add(element.getAttribute("name"))
        }
        
        for (node in nodesToRemove) {
            resources.removeChild(node)
        }
        
        // 清理空白文本节点，避免多余的空白行
        cleanupWhitespaceNodes(resources)
        
        return keyValues
    }
    
    /**
     * 将翻译写入XML
     */
    private fun writeTranslationInXml(language: String, translations: List<Pair<String, String>>, 
                                     languageDic: Map<String, String>,
                                     languageFileName: String, defaultLanguage: String, 
                                     tempDir: File, projectDir: File) {
        logger.lifecycle("language: $language translations = $translations")
        
        try {
            val filepath = tempDir.resolve("$language-language.xml")
            val doc = readXml(languageDic[language] ?: "",
                            languageFileName, defaultLanguage, projectDir, tempDir.resolve("default.xml"))
            val treeKeys = xmlKeys(doc)
            
            // 检查键名是否包含特殊字符或重复
            for ((key, _) in translations) {
                for (i in specialString) {
                    if (key.contains(i)) {
                        throw Exception("contain invalidate key : $key")
                    }
                }
                
                if (key in treeKeys) {
                    throw Exception("duplicate key : $key")
                }
            }
            
            handleLanguage(language, translations, doc, filepath)
        } catch (e: Exception) {
            logger.error("\u001B[31mdeal language： $language, error + ${e.message}\u001B[0m")
            throw e
        }
    }
}

class I18nImportPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        // 注册i18nImport任务
        target.tasks.register("i18nImport",I18nTask::class.java)
    }
}
