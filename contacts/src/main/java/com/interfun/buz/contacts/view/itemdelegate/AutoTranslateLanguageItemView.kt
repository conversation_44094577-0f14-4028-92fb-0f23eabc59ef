package com.interfun.buz.contacts.view.itemdelegate

import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.invisible
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.databinding.ContactsAutoTranslateLanguageItemBinding
import com.interfun.buz.contacts.viewmodel.BotTranslatorLanguageSettingViewModel.TranslatorSettingItem.SelectableLanguageItem

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @desc
 */
class AutoTranslateLanguageItemView :
    BaseBindingDelegate<SelectableLanguageItem, ContactsAutoTranslateLanguageItemBinding>() {

    private val colorPrimary by lazy { R.color.basic_primary.asColor() }
    private val colorTextWhiteMain by lazy { R.color.text_white_main.asColor() }

    override fun onBindViewHolder(
        holder: BindingViewHolder<ContactsAutoTranslateLanguageItemBinding>,
        item: SelectableLanguageItem
    ) {
        super.onBindViewHolder(holder, item)
        val binding = holder.binding
        binding.tvOption.text = item.language.displayName
        if (item.isSelected) {
            binding.tvOption.setTextColor(colorPrimary)
            binding.iftvCheck.visible()
        } else {
            binding.tvOption.setTextColor(colorTextWhiteMain)
            binding.iftvCheck.invisible()
        }
    }

    override fun useItemClickListener(item: SelectableLanguageItem): Boolean {
        return true
    }
}