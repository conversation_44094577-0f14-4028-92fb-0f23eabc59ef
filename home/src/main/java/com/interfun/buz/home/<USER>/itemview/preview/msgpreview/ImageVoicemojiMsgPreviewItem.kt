package com.interfun.buz.home.view.itemview.preview.msgpreview

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.dpFloat
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.invisible
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiType
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.PreviewPayloadType
import com.interfun.buz.common.ktx.scaleXYAnim
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.ChatPreviewItemVoicemojiMessageBinding
import kotlinx.coroutines.CoroutineScope

/**
 * <AUTHOR>
 * @date 2022/10/25
 * @desc
 */
class ImageVoicemojiMsgPreviewItem @JvmOverloads constructor
    (context: Context, attrs: AttributeSet? = null) :
    BaseMsgPreviewItem<HomeMsgPreviewModel.ImageVoiceEmojiPreview>(context, attrs),
    BaseMsgPreviewItem.IPlayablePreviewItem{
    companion object {
        const val TAG = "ImageVoicemojiMsgPreviewItem"
    }

    private val binding by lazy {
        ChatPreviewItemVoicemojiMessageBinding.inflate(LayoutInflater.from(context), this)
    }
    private val vVoiceButton by lazy {
        combineView(
            binding.middleCircle,
            binding.bigCircle
        )
    }
    private val scale = 46.dpFloat /56.dpFloat

    override fun setMessage(previewData: HomeMsgPreviewModel.ImageVoiceEmojiPreview, scope: CoroutineScope?,convInfo: HomeMsgPreviewModel.ConvInfo) {
        logDebug(TAG, "setMessage: ")
        binding.ivVoiceMoji.setVoiceEmojiUrl(previewData.emojiUrl, VoiceEmojiType.Image)
        binding.tvTextMessage.text = R.string.ve_voiceemoji_tip_updated.asString()
        setPlayStatus(previewData.isPlaying)
        binding.clVoiceMoji.click {
            onPreviewClickListener?.invoke(previewData)
        }
        super.setMessage(previewData, scope,convInfo)
    }

    override fun updateContent(previewData: HomeMsgPreviewModel.ImageVoiceEmojiPreview, types: List<PreviewPayloadType>) {
        types.forEach {type ->
            when (type) {
                PreviewPayloadType.UPDATE_PLAYING_STATE -> setPlayStatus(previewData.isPlaying)
                else -> {}
            }
        }
        binding.clVoiceMoji.click {
            onPreviewClickListener?.invoke(previewData)
        }
    }

    override fun setPlayStatus(isPlaying: Boolean) {
        if (isPlaying) {
            playVoiceAnim()
        } else {
            stopVoiceAnim()
        }
    }

    override fun playVoiceAnim() {
        binding.apply {
            ivVoiceMoji.scaleXYAnim(150L, 1f, scale).start()
            vVoiceButton.background = ColorDrawable(R.color.color_background_highlight_2_default.asColor())
            iftvVoiceMojiLogo.setColorFilter(R.color.color_foreground_highlight_default.asColor())
            voiceMojiName.setTextColor(R.color.color_foreground_highlight_default.asColor())
            pagPlaying.loadAnim("pag/pag_voicemoji_msg_playing.pag") {
                iftvVoiceMojiLogo.invisible()
                pagPlaying.apply {
                    visible()
                    setColor(R.color.color_foreground_highlight_default.asColor())
                    setLoop(true)
                    playAnim()
                }
            }
        }
    }

    override fun stopVoiceAnim() {
        binding.apply {
            if (pagPlaying.isPlaying()) {
                pagPlaying.stopAnim()
                pagPlaying.gone()
            }
            iftvVoiceMojiLogo.visible()
            ivVoiceMoji.scaleXYAnim(150L, scale, 1f).start()
            vVoiceButton.background = ColorDrawable(R.color.color_background_3_default.asColor())
            iftvVoiceMojiLogo.setColorFilter(R.color.color_text_white_secondary.asColor())
            voiceMojiName.setTextColor(R.color.color_text_white_secondary.asColor())
        }
    }
}