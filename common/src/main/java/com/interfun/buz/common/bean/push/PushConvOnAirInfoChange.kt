package com.interfun.buz.common.bean.push

import com.buz.idl.onair.bean.OnAirPreview
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.utils.fromJson
import kotlinx.coroutines.launch
import org.json.JSONObject

class PushConvOnAirInfoChange: PushDataBase() {
    private val TAG = "PushGroupOnAirInfoChange"
    override fun handlePushEvent(op: Int, sendTimestamp: Long, data: JSONObject?) {
        if (null == data) return
        logInfo(TAG,"handlePushEvent:op = $op data = $data ")
        val convType = data.optInt("conversationType")
        val convId = data.optLong("conversationTargetId")
        val previewJson = data.optJSONObject("onAirPreview")?:return
        val preview = previewJson.toString().fromJson<OnAirPreview>()?:return
        userLifecycleScope?.launch{
            // OnAirStatusManager.updateConvChannelInfo(convId, convType, preview)
            //todo: 补充更新逻辑
        }
    }
}