package com.interfun.buz.core.widget_liveplace.view

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.compose.animation.*
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asPainter
import com.interfun.buz.compose.ktx.rememberMutableBoolean
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_liveplace.R
import com.interfun.buz.core.widget_liveplace.ui.LivePlaceTag
import kotlinx.coroutines.delay

/**
 * <AUTHOR>
 * @date 2025/1/2
 * @desc
 */
class LivePlaceTagView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null,
) : FrameLayout(context, attrs) {

    private val showContentState = mutableStateOf(false)
    private val showTextState = mutableStateOf(false)
    private val alphaState = mutableFloatStateOf(1f)
    private val topicState = mutableStateOf("")

    init {
        addView(ComposeView(context).apply {
            setContent {
                LivePlaceTagOnChat(
                    isShowContent = { showContentState.value },
                    isShowText = { showTextState.value },
                    alphaValue = { alphaState.value },
                    topic = { topicState.value }
                )
            }
        })
    }

    fun showLivePlaceTag(show: Boolean, topic: String) {
        showContentState.value = show
        topicState.value = topic
    }

    fun showText(show: Boolean) {
        showTextState.value = show
    }

    override fun setAlpha(alpha: Float) {
        alphaState.value = alpha
    }
}

@Composable
private fun LivePlaceTagOnChat(
    isShowContent: () -> Boolean,
    isShowText: () -> Boolean,
    alphaValue: () -> Float,
    topic: () -> String,
) {
    AnimatedVisibility(
        visible = isShowContent(),
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
        ) {
            Image(
                painter = R.drawable.liveplace_bg_ellipse.asPainter(),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize(108f / 116f)
                    .align(Alignment.Center)
                    .graphicsLayer {
                        alpha = alphaValue()
                        //矫正一下 drawable 会有一点偏移
                        translationX = -1f
                        translationY = -1f
                    }
            )
            LivePlaceTagText(
                isShowText = isShowText,
                alphaValue = alphaValue,
                topic.invoke(),
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

@Composable
fun LivePlaceTagText(
    isShowText: () -> Boolean,
    alphaValue: () -> Float,
    topic: String,
    modifier: Modifier = Modifier
) {
    var showText by rememberMutableBoolean()
    LaunchedEffect(isShowText()) {
        if (isShowText()) {
            delay(300)
            showText = true
        } else {
            showText = false
        }
    }
    BoxWithConstraints(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxWidth(106f / 116f)
            .height(24.dp)
    ) {
        val parentWidth = maxWidth
        val widthState = animateDpAsState(
            targetValue = if (showText) parentWidth else 42.dp,
            animationSpec = tween(if (showText) 350 else 200)
        )
        Box(
            modifier = Modifier
                .width(widthState.value)
                .fillMaxHeight()
                .background(
                    color = R.color.color_background_highlight_1_default.asColor(),
                    shape = RoundedCornerShape(30.dp)
                )
        )
        AnimatedVisibility(
            visible = showText,
            enter = fadeIn() + scaleIn(initialScale = 0.75f, animationSpec = tween(350)),
            exit = fadeOut() + scaleOut()
        ) {
            val focusRequester = remember { FocusRequester() }

            Box (modifier = Modifier.size(13.dp)
                .focusRequester(focusRequester)
                .focusable() ){  }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 8.dp)
                    .basicMarquee(iterations = Int. MAX_VALUE, repeatDelayMillis = 500, velocity = 25.dp,),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    // 宽度是最大宽度先-16（左右间距 8）
                    // 然后再+1，目的是让很短的文字也能实现跑马灯（让Text宽度超出Box的宽度）
                    modifier = Modifier.widthIn(min = parentWidth - 16.dp + 1.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = topic,
                        style = TextStyles.labelSmall(),
                        color = R.color.color_text_black_primary.asColor(),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        AnimatedVisibility(
            visible = !showText,
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            LivePlaceTag(
                isLive = true,
                showTurnOnAnim = false
            )
        }
        Box(
            modifier = Modifier
                .width(widthState.value)
                .fillMaxHeight()
                .graphicsLayer {
                    alpha = (1f - alphaValue()).coerceIn(0f, 1f)
                }
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        color = R.color.neutral_black.asColor(),
                        shape = RoundedCornerShape(12.dp)
                    )
            )
        }
    }
}