package com.interfun.buz.common.manager.voicecall

import android.Manifest
import com.buz.idl.realtimecall.request.RequestHangUpRealTimeCall
import com.buz.idl.realtimecall.service.BuzNetRealTimeCallServiceClient
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.launchMain
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.CallConflictState.BEING_INVITED
import com.interfun.buz.common.bean.voicecall.CallConflictState.NO_CAMERA_PERMISSION
import com.interfun.buz.common.bean.voicecall.CallConflictState.NO_CONFLICT
import com.interfun.buz.common.bean.voicecall.CallConflictState.NO_RECORD_PERMISSION
import com.interfun.buz.common.bean.voicecall.CallConflictState.ON_AIR
import com.interfun.buz.common.bean.voicecall.CallConflictState.ON_CALL
import com.interfun.buz.common.bean.voicecall.CallEndType
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.CameraStatus
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.MicStatus
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.chat.VoiceConflictType
import com.interfun.buz.common.manager.chat.VoiceConflictTypeManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle.DESTROY
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.onair.standard.IGlobalOnAirController
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import org.json.JSONObject

object VoiceCallPortal {

    private val TAG = "VoiceCallPortal"
    val currentChannelId: Long?
        get() = currentRoomValue?.roomChannelId

    private val _currentRoom = MutableStateFlow<VoiceCallRoom?>(null)
    val currentRoom: StateFlow<VoiceCallRoom?> = _currentRoom
    val currentRoomValue: VoiceCallRoom? get() = _currentRoom.value

    private val _roomLifeCycle = MutableStateFlow<Pair<RoomLifecycle, Long?>>(DESTROY to null)
    val roomLifecycle: StateFlow<Pair<RoomLifecycle, Long?>> = _roomLifeCycle
    val voiceCallScope = MainScope()
    private val _rejectResult = MutableSharedFlow<Pair<Int, Long>>() //Pair<rCode, channelId>
    val rejectResult: SharedFlow<Pair<Int, Long>> = _rejectResult

    val isPipModeShowingFlow: StateFlow<Boolean>
        get() = currentRoomValue?.isPipModeShowingFlow ?: MutableStateFlow(false)

    // 是否所有成员都关闭了摄像头
    @OptIn(ExperimentalCoroutinesApi::class)
    val isAllMemberCloseCamera: Flow<Boolean> = currentRoom
        .filterNotNull()
        .flatMapLatest { it.members }
        .map { members -> members.all { it.cameraStatus == CameraStatus.CLOSE } }
        .flowOn(Dispatchers.Default)


    private val client by lazy {
        BuzNetRealTimeCallServiceClient().withConfig()
    }

    fun startRealTimeCall(
        userList: List<CallRoomUser>,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        groupId: Long?,
        traceId: String,
        voiceCallRoomSignal: IVoiceCallRoomSignal?
    ): CallConflictState {
        logInfo(
            TAG,
            "startRealTimeCall:userList =${userList.map { "${it.userId}-${it.rtcUserId}-${it.cameraStatus}" }}, channelType = $channelType, callType = $callType, groupId = $groupId, traceId = $traceId",
            logLine = LogLine.START_RTCAll
        )
        val minimizeTargetId = when (channelType) {
            ChannelType.TYPE_VOICE_CALL_1V1 -> {
                val target = userList.find { it.userId != UserSessionManager.uid }
                target?.userId ?: 0L
            }

            ChannelType.TYPE_VOICE_CALL_GROUP -> {
                groupId!!
            }

            else -> 0L
        }

        val checkCallAndPermission = checkPermissionAndCallState(minimizeTargetId, callType)
        logInfo(
            TAG,
            "startRealTimeCall: checkCallAndPermission=$checkCallAndPermission",
            logLine = LogLine.START_RTCAll
        )
        if (checkCallAndPermission != NO_CONFLICT) {
            return checkCallAndPermission
        }

        val checkInviteState = checkBeingInviteState(userList, channelType)
        logInfo(
            TAG,
            "startRealTimeCall: checkInviteState=$checkInviteState",
            logLine = LogLine.START_RTCAll
        )
        if (checkInviteState != NO_CONFLICT) {
            return checkInviteState
        }

        routerServices<ChatService>().value?.stopVoiceMessagePlayback()
        //保证线程安全
        voiceCallScope.launch(Dispatchers.Main, CoroutineStart.UNDISPATCHED) {
            // 防止快速点击创建无意义的房间
            val isInOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
            if (!isOnRealTimeCall() && !isInOnAir) {
                ChannelPendStatusManager.changeCallWaitingStatus(
                    isWaiting = true, isCallConnected = false
                )
                createRoom(minimizeTargetId, channelType, callType, voiceCallRoomSignal)
                currentRoomValue?.startRealTimeCall(
                    userList, channelType, callType, groupId, traceId
                )
            }
        }
        return NO_CONFLICT
    }

    fun joinRealTimeCall(
        channelId: Long,
        targetId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        source: Int,
        cameraStatus: @CameraStatus Int? = null,
        micStatus: @MicStatus Int? = null,
        voiceCallRoomSignal: IVoiceCallRoomSignal?
    ): CallConflictState {
        logInfo(TAG, "joinRealTimeCall:channelId = $channelId, source = $source")
        val result = checkPermissionAndCallState(targetId, callType)
        if (result != NO_CONFLICT) return result

        routerServices<ChatService>().value?.stopVoiceMessagePlayback()

        //保证线程安全
        voiceCallScope.launch(Dispatchers.Main) {
            // 防止快速点击时，创建多个房间
            val isInVoiceCall = isOnRealTimeCallEnhance()
            val isInOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() == true

            if (!isInVoiceCall && !isInOnAir) {
                logInfo(TAG, "joinRealTimeCall:real create room ")
                createRoom(targetId, channelType, callType, voiceCallRoomSignal)
                currentRoomValue?.joinRealTimeCall(
                    cid = channelId,
                    channelType = channelType,
                    source = source,
                    callType = callType,
                    cameraStatus = cameraStatus,
                    micStatus = micStatus
                )
            }
        }
        return result
    }

    fun hangUp() {
        logInfo(TAG, "hangUp:")
        currentRoomValue?.hangUp()
    }

    fun reject(channelId: Long) {
        voiceCallScope.launchIO {
            logInfo(TAG, "reject:channelId = $channelId")
            _rejectResult.emit(0 to channelId)

            val request = RequestHangUpRealTimeCall(channelId, 2)
            var responseCode = -1
            /**
             * 大佬说要强化感知退房请求
             * 如果弱网或者无网需要重复请求 代码不优雅但是可以解决问题。
             * 优雅解决方案：给我时间
             */
            try {
                //30s是因为最长被呼叫时间是30s
                withTimeout(30 * 1000) {
                    while (responseCode != 0 && responseCode != CallEndType.EXC_FINISH && isActive) {
                        val response = client.hangUpRealTimeCall(request)
                        responseCode = response.code
                        logInfo(TAG, "hangUpRTC: code = ${response.code}")
                        //融合一下断网？
                        delay(1000)
                    }
                }
            } catch (e: Exception) {
                logError(TAG, e)
            }
        }
    }

    fun handlePush(op: Int, sendTimestamp: Long, data: JSONObject?) {
        logInfo(TAG, "handlePush:op = $op, sendTimestamp = $sendTimestamp, data = $data")
        currentRoomValue?.onReceivePush(op, sendTimestamp, data)
    }

    fun isOnRealTimeCall(): Boolean {
        return currentRoomValue.isNotNull()
    }

    fun isOnRealTimeCallEnhance(): Boolean {
        val currentRoomValueBackup = currentRoomValue
        return currentRoomValueBackup != null && !currentRoomValueBackup.viewModel.callHangup
    }


    /**
     * check environment before start  call
     *  1. judge permission
     *  2. judge is on buz Calling or online
     */
    private fun checkPermissionAndCallState(
        targetId: Long, callType: @CallType Int
    ): CallConflictState {
        if (!isPermissionGranted(Manifest.permission.RECORD_AUDIO)) {
            logInfo(TAG, "checkBeforeCall: No record permission ",logLine = LogLine.START_RTCAll)
            return NO_RECORD_PERMISSION
        }

        if (CallType.isVideoCall(callType) && !isPermissionGranted(Manifest.permission.CAMERA)) {
            logInfo(TAG, "checkBeforeCall: No camera permission ",logLine = LogLine.START_RTCAll)
            return NO_CAMERA_PERMISSION
        }

        if (isOnRealTimeCallEnhance()) {
            return ON_CALL
        }
        //处于ON AIR房间
        if (routerServices<IGlobalOnAirController>().value?.isInOnAir() == true) {
            return ON_AIR
        }

        return NO_CONFLICT
    }

    private fun checkBeingInviteState(
        users: List<CallRoomUser>, channelType: @ChannelType Int
    ): CallConflictState {
        val isBeingInvite = ChannelPendStatusManager.isPendingFlow.value.first
        if (channelType != ChannelType.TYPE_VOICE_CALL_1V1) {
            return if (isBeingInvite) {
                BEING_INVITED
            } else {
                NO_CONFLICT
            }
        }

        val userId = users.first().userId
        val inviter = ChannelPendStatusManager.statusFlow.value.second?.targetId
        if (!isBeingInvite || inviter == userId) {
            return NO_CONFLICT
        } else {
            return BEING_INVITED
        }
    }

    private fun checkCurrentBuzState(): Boolean {
        return currentRoomValue == null
    }

    private suspend fun createRoom(
        targetId: Long, channelType: @ChannelType Int, callType: @CallType Int, roomSignal: IVoiceCallRoomSignal?
    ) {
        _currentRoom.emit(VoiceCallRoom(targetId, channelType, callType, roomSignal))
        VoiceConflictTypeManager.addConflictType(VoiceConflictType.VCOnCallingType)
        logInfo(TAG, "VoiceCallPortal#createRoom: ${_currentRoom.value}", logLine = LogLine.START_RTCAll)
        currentRoomValue!!.getLifeCycle()
            .observeLifecycleForever(object : RoomLifecycleStateObserver {
                override fun onStateChange(state: RoomLifecycle, room: VoiceCallRoom) {
                    logInfo(TAG, "onStateChange:state = $state, channelId = ${room.roomChannelId}", logLine = LogLine.RTC_CALL)
                    voiceCallScope.launchMain {
                        _roomLifeCycle.emit(state to room.roomChannelId)
                    }
                    if (state == DESTROY) {
                        voiceCallScope.launchMain {
                            logInfo(TAG, "onStateChange: _currentRoom set null", logLine = LogLine.RTC_CALL)
                            _currentRoom.value?.destroy()
                            _currentRoom.emit(null)
                        }
                        VoiceConflictTypeManager.removeConflictType(VoiceConflictType.VCOnCallingType)
                        logInfo(TAG, "onStateChange: currentRoom = $currentRoomValue", logLine = LogLine.RTC_CALL)
                    }
                }
            })

    }
}