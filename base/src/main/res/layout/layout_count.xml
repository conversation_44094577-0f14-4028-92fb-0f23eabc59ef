<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="10dp"
        android:textColor="#000"
        android:textSize="15sp"
        tools:text="描述" />

    <TextView
        android:id="@+id/tvDecreaseMore"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="10dp"
        android:background="#1A0B0C0D"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="－"
        android:textColor="#000"
        android:textSize="22sp"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvDecrease"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center_vertical"
        android:background="#1A0B0C0D"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="－"
        android:textColor="#000"
        android:textSize="12sp"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/etAmount"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:background="@color/black_03"
        android:gravity="center"
        android:includeFontPadding="false"
        android:inputType="number"
        android:minWidth="40dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:text="0"
        android:textColor="#000"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvIncrease"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center_vertical"
        android:background="#1A0B0C0D"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="＋"
        android:textColor="#000"
        android:textSize="12sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvIncreaseMore"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="10dp"
        android:layout_weight="1"
        android:background="#1A0B0C0D"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="＋"
        android:textColor="#000"
        android:textSize="22sp"
        android:textStyle="bold"
        android:visibility="gone" />
</LinearLayout>