package com.interfun.buz.common.manager.af.handler

import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.af.AFRouteEvent
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.af.Handler
import org.json.JSONObject

/**
 * share to invite生成的oneLink对应的deep_link_sub1 处理者
 */
object ShareToInviteHandler:Handler {
    private val TAG = "ShareToInviteHandler"

    override fun handleOnAppOpenAttributionDeepLink(extraData: JSONObject) {
        log(TAG,"handleOnAppOpenAttributionOldVersion: ")
        val action = extraData.optString("action", "")
        if (action.isNullOrEmpty()) {
            log(TAG, "action is null or empty:$action")
            return
        }

        val userId = JSONObject(action).optJSONObject("extraData")?.optLong("userId", 0L)
        if (userId == UserSessionManager.uid) return

        AFRouteEvent.post(action)
    }

    override fun handleOnConversionDataSuccess(extraData: JSONObject) {
        log(TAG,"handleOnConversionDataSuccessOldVersion: ")
        val action = extraData.optString("action", "")
        if (action.isNullOrEmpty()) {
            log(TAG, "action is null or empty:$action")
            return
        }

        val actionJson = JSONObject(action)
        val params = HashMap<String,Any>()
        params[RouterParamKey.UserProfile.KEY_AFTER_REGISTER] = true
        AFRouteEvent.post(actionJson.toString(), params,isFirstLaunch = true)
    }
}