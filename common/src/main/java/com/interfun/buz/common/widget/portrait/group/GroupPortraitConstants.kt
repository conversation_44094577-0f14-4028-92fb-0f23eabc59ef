package com.interfun.buz.common.widget.portrait.group

import android.graphics.PointF
import android.graphics.PorterDuff.Mode.DST_OUT
import android.graphics.PorterDuff.Mode.DST_OVER
import android.graphics.PorterDuff.Mode.SRC_OVER
import android.graphics.PorterDuffXfermode
import coil.transform.CircleCropTransformation

/**
 * <AUTHOR>
 * @date 2023/6/27
 * @desc
 */
object GroupPortraitConstants {

    val circleCropTransformation = CircleCropTransformation()
    val modeSrcOver = PorterDuffXfermode(SRC_OVER)
    val modeDstOut = PorterDuffXfermode(DST_OUT)
    val modeDstOver = PorterDuffXfermode(DST_OVER)

    private val pointMember1 = arrayOf(
        PointF(0f, 0f)
    )

    private val pointMember2 = arrayOf(
        PointF(-33f / 200f, 0f),
        PointF(33f / 200f, 0f)
    )

    private val pointMember3 = arrayOf(
        PointF(0f, -37f / 200f),
        PointF(33f / 200f, 24f / 200f),
        PointF(-33f / 200f, 24f / 200f)
    )

    private val pointMember4 = arrayOf(
        PointF(-33f / 200f, -33f / 200f),
        PointF(33f / 200f, -33f / 200f),
        PointF(33f / 200f, 33f / 200f),
        PointF(-33f / 200f, 33f / 200f)
    )

    private val pointMember5 = arrayOf(
        PointF(0f, -47f / 200f),
        PointF(45f / 200f, -14f / 200f),
        PointF(28f / 200f, 38f / 200f),
        PointF(-28f / 200f, 38f / 200f),
        PointF(-45f / 200f, -14f / 200f)
    )

    private val pointMember6 = arrayOf(
        PointF(0f, -51f / 200f),
        PointF(44f / 200f, -25f / 200f),
        PointF(44f / 200f, 25f / 200f),
        PointF(0f, 51f / 200f),
        PointF(-44f / 200f, 25f / 200f),
        PointF(-44f / 200f, -25f / 200f)
    )

    fun getPoints(size: Int): Array<PointF> {
        return when (size) {
            1 -> pointMember1
            2 -> pointMember2
            3 -> pointMember3
            4 -> pointMember4
            5 -> pointMember5
            6 -> pointMember6
            else -> pointMember1
        }
    }
}