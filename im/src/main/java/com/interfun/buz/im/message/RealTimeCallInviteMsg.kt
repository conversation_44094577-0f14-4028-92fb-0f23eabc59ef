package com.interfun.buz.im.message

import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.secondsToTime
import com.interfun.buz.common.R
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.CallState.End
import com.interfun.buz.im.message.CallState.End.toCallState
import com.interfun.buz.im.message.CallState.Miss
import com.interfun.buz.im.message.CallState.NoAnswer
import com.interfun.buz.im.message.CallState.Start
import com.lizhi.im5.sdk.message.model.IM5MsgContent
import com.lizhi.im5.sdk.message.model.MessageTag
import org.json.JSONException
import org.json.JSONObject


/**
 * Author: ChenYouSheng
 * Date: 2025/3/10
 * Email: <EMAIL>
 * Desc: 实时语音/视频通话邀请卡片
 */
@MessageTag(
    type = IMType.TYPE_REAL_TIME_CALL, flag = MessageTag.COUNTANDPERSIST
)
class RealTimeCallInviteMsg : IM5MsgContent {

    var channelId: Long = 0 //	频道id
    var channelType: Int = 0 // 频道类型
    var callType: @CallType Int = 0   // 通话类型，1=语音，2=视频
    val title: String get() = generateTitle()
    val desc: String get() = generateDesc()

    var duration: Int = 0  //通话时长，单位秒
    var status: CallState = CallState.Unknown  //通话状态：1=开始、2=结束、3=无人应答，4miss call

    fun generateTitle(): String {
        return when (status) {
            Start -> if (callType == CallType.TYPE_VIDEO) R.string.rtc_videocall.asString() else R.string.rtc_voicecall.asString()
            End,NoAnswer -> if (callType == CallType.TYPE_VIDEO) R.string.rtc_videocall_end.asString() else R.string.rtc_voicecall_end.asString()
            Miss -> if (callType == CallType.TYPE_VIDEO) R.string.rtc_missed_videocall.asString() else R.string.rtc_missed_voicecall.asString()
            else -> ""
        }
    }

    fun generateDesc(): String {
        return when (status) {
            Start -> R.string.rtc_join.asString()
            End -> this.duration.secondsToTime(format = "%02d:%02d")
            NoAnswer -> R.string.rtc_no_people_answer.asString()
            Miss -> R.string.rtc_call_again.asString()
            else -> ""
        }
    }

    override fun encode(): String {
        try {
            val newJson = JSONObject()
            newJson.put("channelId", channelId)
            newJson.put("channelType", channelType)
            newJson.put("callType", callType)
            newJson.put("duration", duration)
            newJson.put("status", status.value)
            return newJson.toString()
        } catch (t: Throwable) {
            t.printStackTrace()
            return ""
        }
    }

    override fun decode(contentStr: String?): Boolean {
        try {
            if (!contentStr.isNullOrEmpty()) {
                val jsonObject = JSONObject(contentStr)
                channelId = jsonObject.optLong("channelId", 0)
                channelType = jsonObject.optInt("channelType", 0)
                callType = jsonObject.optInt("callType", 0)
                duration = jsonObject.optInt("duration", 0)
                status = jsonObject.optInt("status", -1).toCallState()
                return true
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return false
    }


    override fun getDigest(): String {
        return ""
    }

    override fun setExtra(extra: String?) {
    }

    override fun getExtra(): String {
        return ""
    }


}

//通话状态：1=开始、2=结束、3=无人应答，4miss call
sealed class CallState(val value: Int) {
    data object Start : CallState(1)
    data object End : CallState(2)
    data object NoAnswer : CallState(3)
    data object Miss : CallState(4)
    data object Unknown : CallState(-1)

    fun Int.toCallState(): CallState {
        return when (this) {
            1 -> Start
            2 -> End
            3 -> NoAnswer
            4 -> Miss
            else -> Unknown
        }
    }
}


