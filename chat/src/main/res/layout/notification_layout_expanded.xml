<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rlRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="4dp"
    tools:layout_margin="20dp">

    <FrameLayout
        android:id="@+id/flInfo"
        android:layout_width="match_parent"
        android:layout_height="56dp">

        <FrameLayout
            android:id="@+id/flPortrait"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            tools:padding="6dp">

            <ImageView
                android:id="@+id/ivPortraitBg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:src="@drawable/chat_notification_bg" />

            <ImageView
                android:id="@+id/ivPortraitCurrent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical" />

            <TextView
                android:id="@+id/tvVoiceMoji"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:textSize="30sp"
                style="@style/main_describe"
                android:gravity="center"
                android:maxLines="1"
                android:text="@string/ic_add"
                android:textColor="@color/white"/>

        </FrameLayout>

        <ImageView
            android:id="@+id/ivSpeaking"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/chat_notification_speaking"
            android:visibility="gone"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="56dp"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical|start"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvGroupName"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="1dp"
                android:layout_weight="1.1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_40"
                android:textSize="12sp"
                android:textStyle="bold"
                android:visibility="visible"
                tools:text="群名TIYA lalaland" />

            <TextView
                android:id="@+id/tvUserName"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1.2"
                android:gravity="bottom"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_80"
                android:textSize="15sp"
                android:textStyle="bold"
                tools:text="Lucas" />

            <ImageView
                android:id="@+id/ivSpacing"
                android:layout_width="50dp"
                android:layout_height="2dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvSpeaking"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:text="@string/notification_speaking"
                android:textColor="@color/notification_60"
                android:textSize="12sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvVoiceMojiView"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:includeFontPadding="false"
                android:singleLine="true"
                tools:text="[Voicemoji: 😍]"
                android:textColor="@color/notification_60"
                android:textSize="11sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvSendTime"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_30"
                android:textSize="12sp"
                tools:text="现在Just now" />

        </LinearLayout>
    </FrameLayout>

    <ImageView
        android:id="@+id/ivLine"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="60dp"
        android:background="@color/notification_06" />

    <TextView
        android:id="@+id/tvMoreChats"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ivLine"
        android:layout_marginTop="12dp"
        android:includeFontPadding="false"
        android:text="@string/notification_more_chats"
        android:textColor="@color/notification_60"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/tvNoChatsTips"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvMoreChats"
        android:layout_marginTop="10dp"
        android:includeFontPadding="false"
        android:text="@string/notification_no_chats_tips"
        android:textColor="@color/notification_30"
        android:textSize="12sp"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/flInfo1"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_below="@+id/tvMoreChats"
        android:layout_marginTop="6dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/ivPortraitRecent1"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/chat_notification_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="78dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvGroupName1"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="1dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_40"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="TIYA lalalandTIYA lalalandTIYA lalalandTIYA lalalandTIYA lalaland"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvUserName1"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_80"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="LucasLucasLucasLucaLucasLucasLucasLuca" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvSendTime1"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:includeFontPadding="false"
            android:textColor="@color/notification_30"
            android:textSize="12sp"
            tools:text="11 min ago" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/flInfo2"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_below="@+id/flInfo1"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/ivPortraitRecent2"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/chat_notification_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="78dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvGroupName2"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="1dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_40"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="TIYA lalalandTIYA lalalandTIYA lalalandTIYA lalalandTIYA lalaland"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvUserName2"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_80"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="LucasLucasLucasLucaLucasLucasLucasLuca" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvSendTime2"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:includeFontPadding="false"
            android:textColor="@color/notification_30"
            android:textSize="12sp"
            tools:text="11 min ago" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/flInfo3"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_below="@+id/flInfo2"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/ivPortraitRecent3"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/chat_notification_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="78dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvGroupName3"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="1dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_40"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="TIYA lalalandTIYA lalalandTIYA lalalandTIYA lalalandTIYA lalaland"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvUserName3"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="@color/notification_80"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="LucasLucasLucasLucaLucasLucasLucasLuca" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvSendTime3"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:includeFontPadding="false"
            android:textColor="@color/notification_30"
            android:textSize="12sp"
            tools:text="11 min ago" />

    </FrameLayout>

</RelativeLayout>