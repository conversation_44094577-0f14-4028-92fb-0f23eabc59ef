package com.interfun.buz.media.player

import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.interfun.buz.media.bean.BuzMediaItem
import com.interfun.buz.media.player.view.PlayerConfig

interface IMediaPlayerBuilder {

    /**
     * 设置播放器的渲染容器，必传
     */
    fun renderParent(parent: ViewGroup): BuzMediaPlayer.Builder

    /**
     * 设置LifecycleOwner，必传
     */
    fun lifecycleOwner(lifecycleOwner: LifecycleOwner): BuzMediaPlayer.Builder

    /**
     * 设置播放器名字，必传，每个播放器的名字都是唯一的
     */
    fun playerName(name: String): BuzMediaPlayer.Builder

    /**
     * 设置数据提供者，支持分页加载
     */
    fun dataProvider(dataProvider: DataProvider): BuzMediaPlayer.Builder


    /**
     * 设置播放配置
     */
    fun playerConfig(config: PlayerConfig): BuzMediaPlayer.Builder

    /**
     * 当前传入来的数据
     * @param mediaList 列表数据
     * @param selectedItem 当前选中的item
     */
    fun mediaList(
        mediaList: List<BuzMediaItem>,
        selectedItem: BuzMediaItem? = null
    ): BuzMediaPlayer.Builder

    /**
     * 位置回调
     */
    fun positionCallback(positionCallback: PositionChangeCallback): BuzMediaPlayer.Builder

    /**
     * 数据变更回调
     */
    fun dataChangeCallback(dataCallback: DataChangeCallback): BuzMediaPlayer.Builder

    /**
     * 滚动监听
     */
    fun onScrollListener(scrollListener: RecyclerView.OnScrollListener): BuzMediaPlayer.Builder

    /**
     * 开始构建
     */
    fun build(): BuzMediaPlayer

}


/**
 * 数据提供者
 */
interface DataProvider {
    /**
     * 分页加载之前的数据
     */
    suspend fun loadMorePreviousItems(firstItem: BuzMediaItem): List<BuzMediaItem>

    /**
     * 分页加载之后的数据
     */
    suspend fun loadMoreNextItems(lastItem: BuzMediaItem): List<BuzMediaItem>
}

/**
 * 当前选中的位置回调
 */
interface PositionChangeCallback {

    /**
     * @param byDataChange:true表示数据变化触发的
     */
    fun onPositionChange(position: Int, item: BuzMediaItem, byDataChange: Boolean)
}

interface DataChangeCallback {
    fun onChange(newList: List<BuzMediaItem>, selectedItem: BuzMediaItem)
}
