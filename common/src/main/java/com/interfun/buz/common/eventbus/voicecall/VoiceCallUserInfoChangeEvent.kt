package com.interfun.buz.common.eventbus.voicecall

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc voice call user info changed event, need to refresh the user list
 * @Author:<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * @Date: 2022/8/31
 */
class VoiceCallUserInfoChangeEvent(val channelId: Long): BaseEvent() {

    companion object{
        fun post(channelId: Long){
            LiveEventBus.get(VoiceCallUserInfoChangeEvent::class.java).post(VoiceCallUserInfoChangeEvent(channelId))
        }
    }
}