package com.interfun.buz.chat.voicemoji.ktx

import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategoryType
import com.interfun.buz.im.message.ImVoiceEmojiCategoryType

fun ImVoiceEmojiCategoryType.convertToVoiceEmojiCategoryType(): VoiceEmojiCategoryType {
    return when (this) {
        ImVoiceEmojiCategoryType.Unknown -> VoiceEmojiCategoryType.Unknown
        ImVoiceEmojiCategoryType.Default -> VoiceEmojiCategoryType.Default
        ImVoiceEmojiCategoryType.Creative -> VoiceEmojiCategoryType.Creative
        ImVoiceEmojiCategoryType.BlindBox -> VoiceEmojiCategoryType.BlindBox
    }
}