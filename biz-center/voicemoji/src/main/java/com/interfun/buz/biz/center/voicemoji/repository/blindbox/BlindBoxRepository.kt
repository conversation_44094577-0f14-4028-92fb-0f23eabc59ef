package com.interfun.buz.biz.center.voicemoji.repository.blindbox

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import com.buz.idl.voicemoji.response.ResponseGetBlindBoxResource
import com.buz.idl.voicemoji.response.ResponseGetBlindBoxes
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.biz.center.voicemoji.datasource.blindbox.LocalBlindBoxDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.blindbox.RemoteBlindBoxDataSource
import com.interfun.buz.biz.center.voicemoji.model.blindbox.BlindBoxTipsStatus
import com.interfun.buz.biz.center.voicemoji.model.blindbox.RequestBlindType
import com.interfun.buz.biz.center.voicemoji.model.blindbox.VoiceEmojiBlindData
import com.interfun.buz.common.bean.CancelableResp
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.manager.userLifecycleScope
import com.lizhi.component.basetool.env.AppEnvironment
import com.lizhi.component.basetool.env.Environments
import com.lizhi.itnet.lthrift.service.ITResponse
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.math.max

data class RefreshRequest(
    val requestType: RequestBlindType,
    val ack: CompletableDeferred<CancelableResp<ResponseGetBlindBoxes>>
)

/**
 * Author: ChenYouSheng
 * Date: 2024/12/16
 * Email: <EMAIL>
 * Desc: 盲盒表情数据仓库
 */
object BlindBoxRepository {

    @SuppressLint("StaticFieldLeak")
    private val localDS = LocalBlindBoxDataSource()
    private val remoteDS = RemoteBlindBoxDataSource()

    private val refreshChannel = Channel<RefreshRequest>()

    const val TAG = "BlindBoxRepository"

    init {
        GlobalScope.launch {
            startRefreshJob()
        }
    }

    private suspend fun startRefreshJob() {
        coroutineScope {
            var lastRequestJob: Job? = null
            var lastRequest: RefreshRequest? = null
            while (isActive) {
                val request = refreshChannel.receive()
                val requestType = request.requestType
                logInfo(TAG, "refreshChannel.receive type=${requestType}")

                if (lastRequest?.requestType == RequestBlindType.Init) {
                    lastRequestJob?.join()
                } else {
                    // 只有当上一个请求类型是 normal 时，才取消之前的请求
                    lastRequestJob?.cancelAndJoin()
                }
                val job = async {
                    val resp = doRequest(requestType)
                    logInfo(TAG, "doRequest type=${requestType}")

                    // save to local
                    if (resp is Resp.Success) {
                        updateLocalBlindBoxData(requestType, resp.data)
                    }

                    logInfo(TAG, "doRequest complete type=${requestType}")
                    // 通知等待处返回结果
                    request.ack.complete(resp)

                    // 定时刷新
                    if (resp is Resp.Success) {
                        handleDelayRefreshTask(resp.data)
                    }
                }

                job.invokeOnCompletion { exception ->
                    logInfo(TAG, "invokeOnCompletion type=${requestType}")
                    if (!request.ack.isCompleted) {
                        val cancelReason = exception?.message ?: "cancel by exception"
                        request.ack.complete(CancelableResp.Cancel(cancelReason))
                    }
                }
                lastRequestJob = job
                lastRequest = request
            }
        }


    }

    /**
     * 同步盲盒列表
     */
    suspend fun syncBlindBoxList(requestType: RequestBlindType = RequestBlindType.Normal): CancelableResp<ResponseGetBlindBoxes> {
        logInfo(TAG, "syncBlindBoxList")
        cancelDelayRefresh()
        val ack = CompletableDeferred<CancelableResp<ResponseGetBlindBoxes>>()
        val request = RefreshRequest(requestType, ack)
        refreshChannel.send(request)
        return ack.await()  //等待 ack.complete(resp)
    }


    private suspend fun doRequest(requestType: RequestBlindType): CancelableResp<ResponseGetBlindBoxes> {
        try {
            val extra = localDS.getBlindBoxList().first()?.extra
            return remoteDS.getBlindBoxList(requestType = requestType, extra ?: "")
        } catch (e: Exception) {
            logError(TAG, "Request failed", e)
            // 返回一个失败的响应，避免协程直接失败
            return Resp.Error(-1, "Request failed due to an exception")
        }
    }

    /**
     * 获取盲盒列表
     */
    fun getBlindBoxList(): Flow<VoiceEmojiBlindData?> {
        return localDS.getBlindBoxList().map { it?.blindBox }.flowOn(Dispatchers.IO)
    }


    /**
     * 获取盲盒资源文件
     */
    fun getBlindBoxResources(): Flow<Resp<ResponseGetBlindBoxResource>> =
        flow { emit(remoteDS.getBlindBoxResources()) }.flowOn(Dispatchers.Default)


    /**
     * 开盲盒
     */
    suspend fun openBlindBox(srvOpenIndex: Int): ITResponse<ResponseGetBlindBoxes> {
        return remoteDS.openBlindBox(srvOpenIndex)
    }

    /**
     * 更新本地缓存
     */
    suspend fun updateLocalBlindBoxData(
        requestType: RequestBlindType = RequestBlindType.Normal, resp: ResponseGetBlindBoxes
    ) {
        localDS.updateBlindBoxList(requestType, resp)
    }

    /**
     * 是否初始化成功了
     */
    fun hasInitSuccess(): Flow<Boolean> {
        return localDS.isInit()
    }


    /**
     * 是否有新盲盒待领取
     */
    fun hasNewBlindBox(): Flow<Boolean> {
        return localDS.getBlindBoxList().map { it?.blindBox?.status?.hasNewBlindBox() ?: false }
    }

    /**
     * 是否可以展示盲盒介绍弹窗
     */
    fun canShowBlindBoxDialog(): Flow<Boolean> {
        return localDS.getBlindBoxList()
            .map { it?.blindBox?.status != BlindBoxTipsStatus.CannotShowBlindBoxDialog }
    }


    // 处理定时刷新
    private fun handleDelayRefreshTask(blindBoxData: ResponseGetBlindBoxes?) {
        val delayRefreshMills = blindBoxData?.delayRefreshMills
        logInfo(TAG, "handleDelayRefreshTask invoke delayRefreshMills=${delayRefreshMills}")
        if (null != delayRefreshMills && delayRefreshMills > 0L) {
            val delayTime = calcTimes(delayRefreshMills)
            startDelayRefresh(delayTime)
        } else {
            cancelDelayRefresh()
        }
    }


    private val fetchDataHandler = Handler(Looper.getMainLooper())

    private val fetchDataRunnable = Runnable {
        userLifecycleScope?.launch {
            logInfo(TAG, "fetchDataRunnable invoke")
            syncBlindBoxList()
        }
    }

    private fun startDelayRefresh(delayTime: Long) {
        logInfo(TAG, "startDelayRefresh delayTime=${delayTime}")
        fetchDataHandler.removeCallbacks(fetchDataRunnable)
        fetchDataHandler.postDelayed(fetchDataRunnable, delayTime)
    }

    private fun cancelDelayRefresh() {
        logInfo(TAG, "cancelDelayRefresh")
        fetchDataHandler.removeCallbacks(fetchDataRunnable)
    }

    /**
     * PRODUCT env at least 60s refresh
     */
    private fun calcTimes(delayRefreshMills: Long): Long {
        return if (Environments.getEnv(appContext) == AppEnvironment.PRODUCT) max(
            delayRefreshMills, 60 * 1000L
        ) else delayRefreshMills
    }
}