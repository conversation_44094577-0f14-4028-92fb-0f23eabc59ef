package com.interfun.buz.startup.task.main

import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.isDebug
import com.interfun.buz.base.ktx.isReleaseLogBuildType
import com.interfun.buz.common.constants.APP_ID
import com.interfun.buz.common.constants.appLocationModel
import com.interfun.buz.common.constants.channelId
import com.interfun.buz.common.constants.deviceId
import com.interfun.buz.startup.task.Task
import com.interfun.buz.common.utils.StartupCostTrace
import com.interfun.buz.startup.BuildConfig
import com.lizhi.component.basetool.env.AppEnvironment
import com.lizhi.component.basetool.env.Environments
import com.lizhi.component.itnet.base.ITNet
import com.lizhi.component.itnet.dispatch.strategy.mushroom.mushRoom
import com.lizhi.component.itnet.myip.ip
import com.yibasan.lizhifm.lzlogan.Logz

/**
 * @Desc: itnet网络初始化Task
 * @Author: <EMAIL>
 * @Date: 2022/6/8
 */
class ITNetInitTask(private val isBuildWithJenkins: Boolean) : Task(TASK_TAG) {

    companion object {
        const val TASK_TAG = "ITNetInitTask"
        const val MY_IP_ADDRESS = "https://myip.lizhifm.com/myip"
        const val PRODUCT_UPDATE_KEY_URL = "https://mushroom.buz-app.com/protocol/sec/update_key"
        const val PRE_UPDATE_KEY_URL = "http://mushroom.buz-app.com/protocol/sec/update_key"
        const val TOWER_UPDATE_KEY_URL = "http://mushroom.yfxn.lizhi.fm/protocol/sec/update_key"
        const val MUSHROOM_KEY = "7e5cf88eeb1bc9e9d152b7667e168d82" //跟签名有关，对应正式包的签名
        const val DEBUG_MUSHROOM_KEY = "fd6f5cd0b06de91a55005cb1ab4c2b19" //跟签名有关，非签名包
    }

    override fun run() {
        val startTime = System.currentTimeMillis()
        // 配置公共参数
        ITNet.CommonProperty.apply {
            deviceID = deviceId
            channelID = channelId
        }

        ITNet.get(APP_ID.toString()).apply {
            this.ip().getIp(MY_IP_ADDRESS) {
                Logz.tag(TASK_TAG).i("MyIpCallback ipaddress = ${it?.ip}")
                appLocationModel = it
            }
            val startTime = System.currentTimeMillis()
            mushRoom(
                url = if (AppEnvironment.TOWER == Environments.getEnv(appContext)) {
                    TOWER_UPDATE_KEY_URL
                } else if (AppEnvironment.PRE == Environments.getEnv(appContext)) {
                    PRE_UPDATE_KEY_URL
                } else {
                    PRODUCT_UPDATE_KEY_URL
                },
                //MUSHROOM_KEY 跟包的签名有关，如果需要本地编译releaseLog包（使用debug签名），则需要注意使用DEBUG_MUSHROOM_KEY
                signKey = if(isDebug || (isReleaseLogBuildType && !isBuildWithJenkins)) DEBUG_MUSHROOM_KEY else MUSHROOM_KEY
            )
            Logz.tag("TestTime").d("time = ${System.currentTimeMillis() - startTime}")
        }
        StartupCostTrace.itNetInitCostTime = System.currentTimeMillis() - startTime
    }

}