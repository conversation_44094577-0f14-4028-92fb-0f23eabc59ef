package com.interfun.buz.chat.wt.entity

class WTPayloadTypes(val types: List<WTPayloadType>)
/**
 * <AUTHOR>
 * @date 2022/11/1
 * @desc
 */
enum class WTPayloadType {
    //User
    UpdateUserOnlineStatus,
    UpdateUserRelation,
    UpdateBtnAddLoadingStatus,

    //Group
    UpdateGroupMemberStatus,
    UpdateAddressedUser,
    UpdateWaitingAi,

    //Common
    UpdateInfo,
    UpdateUnreadCount,
    UpdateConversation, //todo remove, no need in new home wt rv
    UpdatePlayingStatus,
    UpdateSpeakingEnable,
    UpdateSpeakingUnable,
    UpdateMuteStatus,
    NOTHING,
    UpdateAnimation,

    //ROBOT
    UpdateTopic,

    // RealTimeCall
    UpdateRealTimeCallUserList,

    // LivePlace status
    UpdateLivePlaceStatus
}