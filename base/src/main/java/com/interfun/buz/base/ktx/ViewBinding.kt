@file:Suppress("unused")

package com.interfun.buz.base.ktx

import android.app.Dialog
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.drakeet.multitype.ItemViewDelegate
import com.google.android.material.navigation.NavigationView
import com.google.android.material.tabs.TabLayout
import com.interfun.buz.base.R
import com.interfun.buz.base.coroutine.CloseableCoroutineScope
import com.interfun.buz.base.ktx.Method.BIND
import kotlinx.coroutines.*
import kotlin.LazyThreadSafetyMode.NONE
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

/**
 * ViewBinding封装 引用自：https://juejin.cn/post/6906153878312452103#comment
 * 使用文档：https://dylancaicoding.github.io/ViewBindingKTX/#/zh/extensions
 */

/**--------------- Activity ---------------*/
inline fun <reified VB : ViewBinding> ComponentActivity.binding(setContentView: Boolean = true) = lazy(
    NONE
) {
    inflateBinding<VB>(layoutInflater).also { binding ->
        if (setContentView) setContentView(binding.root)
        if (binding is ViewDataBinding) binding.lifecycleOwner = this
    }
}


/**--------------- Fragment ---------------*/
enum class Method { BIND, INFLATE }

inline fun <reified VB : ViewBinding> Fragment.binding() =
    FragmentBindingProperty(VB::class.java)

inline fun <reified VB : ViewBinding> Fragment.binding(method: Method) =
    if (method == BIND) FragmentBindingProperty(VB::class.java) else FragmentInflateBindingProperty(VB::class.java)

class FragmentBindingProperty<VB : ViewBinding>(private val clazz: Class<VB>) :
    ReadOnlyProperty<Fragment, VB> {

    override fun getValue(thisRef: Fragment, property: KProperty<*>): VB =
        requireNotNull(thisRef.view) { "The constructor missing layout id or the property of ${property.name} has been destroyed." }
            .getBinding(clazz).also { binding ->
                if (binding is ViewDataBinding) binding.lifecycleOwner = thisRef.viewLifecycleOwner
            }
}

class FragmentInflateBindingProperty<VB : ViewBinding>(private val clazz: Class<VB>) :
    ReadOnlyProperty<Fragment, VB> {
    private var binding: VB? = null
    private val handler by lazy { Handler(Looper.getMainLooper()) }

    override fun getValue(thisRef: Fragment, property: KProperty<*>): VB {
        if (binding == null) {
            try {
                @Suppress("UNCHECKED_CAST")
                binding = (clazz.getMethod("inflate", LayoutInflater::class.java).invoke(null, thisRef.layoutInflater) as VB)
                    .also { binding -> if (binding is ViewDataBinding) binding.lifecycleOwner = thisRef.viewLifecycleOwner }
            } catch (e: IllegalStateException) {
                throw IllegalStateException("The property of ${property.name} has been destroyed.")
            }
            thisRef.viewLifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
                override fun onDestroy(owner: LifecycleOwner) {
                    handler.post { binding = null }
                }
            })
        }
        return binding!!
    }
}


/**--------------- Dialog ---------------*/
inline fun <reified VB : ViewBinding> Dialog.binding() = lazy(NONE) {
    inflateBinding<VB>(layoutInflater).also { setContentView(it.root) }
}


/**--------------- View ---------------*/
inline fun <reified VB : ViewBinding> View.getBinding() = getBinding(VB::class.java)

@Suppress("UNCHECKED_CAST")
fun <VB : ViewBinding> View.getBinding(clazz: Class<VB>) =
    getTag(R.id.tag_view_binding) as? VB ?: (clazz.getMethod("bind", View::class.java)
        .invoke(null, this) as VB)
        .also { setTag(R.id.tag_view_binding, it) }


/**--------------- ViewGroup ---------------*/
inline fun <reified VB : ViewBinding> ViewGroup.inflate() =
    inflateBinding<VB>(LayoutInflater.from(context), this, true)

inline fun <reified VB : ViewBinding> ViewGroup.binding(attachToParent: Boolean = false) = lazy(NONE) {
    inflateBinding<VB>(LayoutInflater.from(context), if (attachToParent) this else null, attachToParent)
}


/**--------------- ViewHolder ---------------*/
inline fun <reified VB : ViewBinding> RecyclerView.ViewHolder.withBinding(block: VB.(RecyclerView.ViewHolder) -> Unit) = apply {
    block(getBinding(), this@withBinding)
}

fun <VB : ViewBinding> BindingViewHolder<VB>.withBinding(block: VB.(BindingViewHolder<VB>) -> Unit) = apply {
    block(binding, this@withBinding)
}

inline fun <reified VB : ViewBinding> RecyclerView.ViewHolder.getBinding() = itemView.getBinding<VB>()

inline fun <reified VB : ViewBinding> BindingViewHolder(parent: ViewGroup) =
    BindingViewHolder(inflateBinding<VB>(parent))

fun BindingViewHolder<*>.runOnMain(scope: CoroutineScope?, block: suspend CoroutineScope.() -> Unit) {
    scope?.launch(Dispatchers.Main) {
        block()
    }
}

open class BindingViewHolder<VB : ViewBinding>(val binding: VB) : RecyclerView.ViewHolder(binding.root),
    LifecycleOwner,DefaultLifecycleObserver {
    override var lifecycle: LifecycleRegistry = LifecycleRegistry(this).apply {
        addObserver(this@BindingViewHolder)
    }
    private var holderScope: CoroutineScope? = null
    private var isRecycled = false
    companion object {
        private const val TAG = "BindingViewHolder"
    }

    constructor(parent: ViewGroup, inflate: (LayoutInflater, ViewGroup, Boolean) -> VB) :
            this(inflate(LayoutInflater.from(parent.context), parent, false)) {
    }

    init {
        initLifecycle()
    }

    private fun initLifecycle() {
        moveState("initLifecycle", Lifecycle.State.INITIALIZED)
    }
    /**
     * scope:用于 lifecycle 的协程管理，可在 Delegate 开启协程，自动取消协程
     * 注意：holderScope 会在 onViewRecycled 中被置空
     * 并在 onBindViewHolder 重新初始化，
     */
    fun getHolderScope(): CoroutineScope?{
        return holderScope
    }
    /**
     * 以下场景会调用initHolder
     * 1. onCreateViewHolder
     */
    fun initHolder(business: String) {
        moveState("onCreateViewHolder-${business}", Lifecycle.State.CREATED)
    }
    /**
     * 以下场景会调用bindHolder
     * 1. onBindViewHolder
     */
    fun bindHolder(business: String) {
        this.binding.root.setTag(R.id.tag_viewholder,this)
        val isRecycled = isRecycled
        this.isRecycled = false
        if (isRecycled) {
            moveState("onBindViewHolder-${business}", Lifecycle.State.CREATED
            )
        }
        moveState("onBindViewHolder-${business}", Lifecycle.State.RESUMED)

    }

    /**
     * 以下两种场景会调用 unbindHolder
     * 1. 在 onViewRecycled 中调用
     * 2. 在 RecyclerView detach 的时候调用
     */
    fun unbindHolder(business: String) {
        this.binding.root.setTag(R.id.tag_viewholder,null)
        isRecycled = true
        moveState("onViewRecycled-${business}", Lifecycle.State.DESTROYED)
    }

    /**
     * 以下两种场景会调用 onCreate
     * 1. onCreateViewHolder
     * 2. onBindViewHolder(在复用的场景下)
     */
    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        holderScope?.cancel()
        holderScope = CloseableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    }

    /**
     * 以下两种场景会调用
     * 1. 在 onViewRecycled 中调用
     * 2. 在 RecyclerView detach 的时候调用
     */
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        Log.i(TAG, "BindingViewHolder onDestroy position:${bindingAdapterPosition}")
        holderScope?.cancel()
        holderScope = null
    }

    /**
     * business: 用于区分不同的业务，方便打印log，📌问题
     */
    private fun moveState(business: String, state: Lifecycle.State) {
        if (lifecycle?.currentState != state) {
            /*Log.i(TAG, "moveState $business position:${bindingAdapterPosition}" +
                        " currentState:${lifecycle?.currentState} target:${state} hashcode:${this.hashCode()}")*/
            lifecycle?.currentState = state
            when (state) {
                Lifecycle.State.CREATED->lifecycle?.addObserver(this)//由于 ViewHolder 复用的机制，所以需要在每次复用的时候重新添加观察者
                Lifecycle.State.DESTROYED->lifecycle?.removeObserver(this)
                else ->{}
            }
        }
    }
}

inline fun <VB : ViewBinding> BindingViewHolder<VB>.onClick(view: View, crossinline action: (binding: VB, pos: Int) -> Unit) =
    apply { view.click(isSharingIntervals = true) { action(binding, absoluteAdapterPosition) } }

inline fun <T, VB : ViewBinding> BindingViewHolder<VB>.onClick(
    delegate: ItemViewDelegate<T, BindingViewHolder<VB>>,
    view: View,
    clickIntervals: Long = 500L,
    isSharingIntervals: Boolean = false,
    vibrate: Boolean = false,
    crossinline action: (binding: VB, item: T, pos: Int) -> Unit
) =
    apply {
        view.click(
            clickIntervals = clickIntervals,
            isSharingIntervals = isSharingIntervals,
            vibrate = vibrate
        ) {
            if (absoluteAdapterPosition in 0 until delegate.adapterItems.size) {
                action(binding, delegate.getItem(absoluteAdapterPosition), absoluteAdapterPosition)
            }
        }
    }

inline fun <VB : ViewBinding> BindingViewHolder<VB>.onItemClick(crossinline action: (binding: VB, pos: Int) -> Unit) =
    onClick(itemView, action)

inline fun <T, VB : ViewBinding> BindingViewHolder<VB>.onItemClick(
    delegate: ItemViewDelegate<T, BindingViewHolder<VB>>,
    clickIntervals: Long = 500L,
    isSharingIntervals: Boolean = false,
    vibrate: Boolean = false,
    crossinline action: (binding: VB, item: T, pos: Int) -> Unit
) {
    onClick(delegate, itemView, clickIntervals, isSharingIntervals, vibrate, action)
}

inline fun <VB : ViewBinding> BindingViewHolder<VB>.onLongClick(view: View, crossinline action: (binding: VB, pos: Int) -> Unit) =
    apply { view.longClick { action(binding, absoluteAdapterPosition) } }

inline fun <T, VB : ViewBinding> BindingViewHolder<VB>.onLongClick(delegate: ItemViewDelegate<T, BindingViewHolder<VB>>, view: View,
    crossinline action: (binding: VB, item:T, pos: Int) -> Unit) =
    apply {
        view.longClick {
            if (absoluteAdapterPosition in 0 until delegate.adapterItems.size){
                action(binding, delegate.getItem(absoluteAdapterPosition), absoluteAdapterPosition)
            }
        }
    }

inline fun <VB : ViewBinding> BindingViewHolder<VB>.onItemLongClick(crossinline action: (binding: VB, pos: Int) -> Unit) =
    onLongClick(itemView, action)

inline fun <T, VB : ViewBinding> BindingViewHolder<VB>.onItemLongClick(delegate: ItemViewDelegate<T, BindingViewHolder<VB>>,
    crossinline action: (binding: VB, item:T, pos: Int) -> Unit) =
    onLongClick(delegate, itemView, action)


/**--------------- NavigationView ---------------*/
inline fun <reified VB : ViewBinding> NavigationView.updateHeaderView(index: Int = 0, block: VB.() -> Unit) =
    getHeaderView(index)?.getBinding<VB>()?.run(block)


/**--------------- TabLayout ---------------*/
inline fun <reified VB : ViewBinding> TabLayout.Tab.setCustomView(block: VB.() -> Unit) {
    requireNotNull(parent) { "Tab not attached to a TabLayout" }
    inflateBinding<VB>(LayoutInflater.from(parent!!.context)).apply(block).let { binding ->
        customView = binding.root
        customView?.tag = binding
    }
}

inline fun <reified VB : ViewBinding> TabLayout.updateCustomTab(index: Int, block: VB.() -> Unit) =
    getTabAt(index)?.customView?.getBinding<VB>()?.also(block)

inline fun <reified VB : ViewBinding> TabLayout.doOnCustomTabSelected(
    crossinline onTabUnselected: VB.(TabLayout.Tab) -> Unit = {},
    crossinline onTabReselected: VB.(TabLayout.Tab) -> Unit = {},
    crossinline onTabSelected: VB.(TabLayout.Tab) -> Unit = {},
) =
    addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            tab.customView?.getBinding<VB>()?.onTabSelected(tab)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            tab.customView?.getBinding<VB>()?.onTabUnselected(tab)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {
            tab.customView?.getBinding<VB>()?.onTabReselected(tab)
        }
    })


/**--------------- Base ---------------*/
inline fun <reified VB : ViewBinding> inflateBinding(layoutInflater: LayoutInflater) =
    VB::class.java.getMethod("inflate", LayoutInflater::class.java).invoke(null, layoutInflater) as VB

inline fun <reified VB : ViewBinding> inflateBinding(parent: ViewGroup) =
    inflateBinding<VB>(LayoutInflater.from(parent.context), parent, false)

inline fun <reified VB : ViewBinding> inflateBinding(
    layoutInflater: LayoutInflater, parent: ViewGroup?, attachToParent: Boolean
) =
    VB::class.java.getMethod("inflate", LayoutInflater::class.java, ViewGroup::class.java, Boolean::class.java)
        .invoke(null, layoutInflater, parent, attachToParent) as VB

inline fun <reified T : View> ViewBinding.findViewById(id: Int): T {
    return root.findViewById(id)
}

/**
 * 获取BindingViewHolder的LifecycleOwner
 */
fun ViewBinding.getHolderLifecycleOwner(): LifecycleOwner? {
    return root.getTag(R.id.tag_viewholder) as? LifecycleOwner
}

/**
 * 根据 ViewBinding 获取 BindingViewHolder
 */
fun ViewBinding.getHolder(): BindingViewHolder<*>? {
    return root.getTag(R.id.tag_viewholder) as? BindingViewHolder<*>
}
