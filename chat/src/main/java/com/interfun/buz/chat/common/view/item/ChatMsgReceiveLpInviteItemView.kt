package com.interfun.buz.chat.common.view.item

import android.widget.TextView
import androidx.compose.ui.platform.ViewCompositionStrategy
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.chat.common.entity.ChatMsgReceiveLpInviteMsgItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.databinding.ChatItemRecvLpInviteBinding
import com.interfun.buz.common.widget.view.PortraitImageView
import com.interfun.buz.im.message.BuzLivePlaceShareMessage


class ChatMsgReceiveLpInviteItemView(
    itemCallback: ChatItemCallback
) : BaseLivePlaceInviteItemView<ChatMsgReceiveLpInviteMsgItemBean, ChatItemRecvLpInviteBinding>(
    itemCallback
) {

    override fun onViewHolderCreated(holder: BindingViewHolder<ChatItemRecvLpInviteBinding>) {
        super.onViewHolderCreated(holder)
        holder.binding.infoContainer.setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnDetachedFromWindow)

    }

    override fun onBindViewHolder(
        holder: BindingViewHolder<ChatItemRecvLpInviteBinding>,
        item: ChatMsgReceiveLpInviteMsgItemBean
    ) {
        super.onBindViewHolder(holder, item)
        holder.binding.infoContainer.setContent {
            val buzLivePlaceShareMessage = item.msg.content as BuzLivePlaceShareMessage
            ShowInviteCard(holder, buzLivePlaceShareMessage, {
                itemCallback.clickJoinLpInvite(item.msg)
            })
        }
    }


    override fun getGroupMemberNameTextView(binding: ChatItemRecvLpInviteBinding): TextView? {
        return binding.tvGroupMemberName
    }

    override fun getAnchorView(binding: ChatItemRecvLpInviteBinding) = binding.infoContainer

    override fun getPortraitImageView(binding: ChatItemRecvLpInviteBinding): PortraitImageView? {
        return binding.ivPortrait
    }

}