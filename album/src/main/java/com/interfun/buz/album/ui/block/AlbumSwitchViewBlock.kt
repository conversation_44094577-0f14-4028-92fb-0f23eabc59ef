package com.interfun.buz.album.ui.block

import android.animation.ValueAnimator
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.fragment.app.Fragment
import com.interfun.buz.album.bean.MediaFolder
import com.interfun.buz.album.databinding.AlbumPhotoListLayoutBinding
import com.interfun.buz.album.ui.viewmodel.AlbumMediaDataViewModel
import com.interfun.buz.album.ui.widget.AlbumListPopWindow
import com.interfun.buz.album.ui.widget.AlbumSwitchView
import com.interfun.buz.album.utils.AlbumTracker
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingBlock


/**
 * 相册文件夹加载显示&切换逻辑
 * <AUTHOR>
 * @date 2024/3/28
 */
class AlbumSwitchViewBlock(
    private val fragment: Fragment,
    binding: AlbumPhotoListLayoutBinding
) : BaseBindingBlock<AlbumPhotoListLayoutBinding>(binding) {

    companion object{
        private const val TAG = "AlbumSwitchViewBlock"
    }

    private var mAlbumListPopWindow: AlbumListPopWindow? = null
    private var mAlbumList: MutableList<MediaFolder> = mutableListOf()
    private val albumViewModel by fragment.fragmentViewModels<AlbumMediaDataViewModel>()

    override fun onCreate() {
        super.onCreate()
        albumViewModel.selectedMediaFolder.value = null
    }

    override fun initView() {
        super.initView()

        mAlbumListPopWindow = AlbumListPopWindow(context)

        mAlbumListPopWindow?.setAlbumChooseListener {
            AlbumTracker.onClickAlbumItemView(it.folderName)
            //标记目前选择的相册
            albumViewModel.selectedMediaFolder.value = it
            it.isSelected = true
            //选择相册回调
            albumViewModel.chooseAlbum(context, it)
            //更新标题
            binding.asvSwitch.setFolderName(it.folderName)
        }

        binding.asvSwitch.setOnPanelClickListener(object : AlbumSwitchView.OnPanelClickListener {
            override fun onPanelStatus(isExpand: Boolean) {
                //更新展开/折叠状态
                if (isExpand) {
                    showAlbumSwitchWindow()
                } else {
                    dismissAlbumSwitchWindow()
                }
            }
        })
    }

    override fun initData() {
        super.initData()
        fragment.activity?.let {
            albumViewModel.obtainAllAlbumList(it)
        }
        albumViewModel.mediaFolder.observe(fragment.viewLifecycleOwner) {
            logInfo(TAG, "mediaFolder observe folder size = ${it.size}")
            //相册文件夹数据监听
            mAlbumList.clear()
            if (it.isEmpty()) {
                return@observe
            }
            mAlbumList.addAll(it)
            //默认选中第一个相册
            val selected = albumViewModel.selectedMediaFolder.value ?: mAlbumList.first()
            mAlbumListPopWindow?.initSelectedAlbumFolderPosition(mAlbumList.indexOf(selected))

            mAlbumList.findSafety { folder ->
                folder.bucketId == selected.bucketId
            }?.isSelected = true
            albumViewModel.chooseAlbum(context, selected)
            binding.asvSwitch.setFolderName(selected.folderName)

            // Set max width for the album switch
            val switchLayout = binding.asvSwitch.layoutParams as LayoutParams
            switchLayout.matchConstraintMaxWidth = fragment.requireContext.screenWidth - binding.tvCancel.width * 2
            binding.asvSwitch.layoutParams = switchLayout
        }
    }

    private fun showAlbumSwitchWindow() {
        if (mAlbumList.isEmpty()) {
            albumViewModel.obtainAllAlbumList(fragment.requireContext())
            dismissAlbumSwitchWindow()
        } else {
            AlbumTracker.onAlbumPanelExpand()
            bindDataAndShow()
        }
    }

    private fun bindDataAndShow() {
        mAlbumListPopWindow?.setOnDismissListener {
            changeViewPanelBg(1f)
            binding.viewPanelMask.invisible()
            binding.asvSwitch.close()
        }
        mAlbumListPopWindow?.bindAlbumFolder(mAlbumList)
        mAlbumListPopWindow?.showAsDropDown(binding.llTitle)

        changeViewPanelBg(0f)
        binding.viewPanelMask.visible()
    }

    private fun dismissAlbumSwitchWindow() {
        mAlbumListPopWindow?.dismiss()
        binding.viewPanelMask.invisible()
    }

    private fun changeViewPanelBg(value: Float) {
        val animator = ValueAnimator.ofFloat(value, 1 - value)
        animator.apply {
            addUpdateListener { animator ->
                val fraction = animator.animatedValue as Float
                binding.viewPanelMask.alpha = fraction
            }
            setDuration(150L)
        }.start()
    }
}