<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    tools:background="@color/black_90"
    tools:layout_width="124dp"
    tools:layout_height="90dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceVoiceMojiText"
        android:layout_width="90dp"
        android:layout_height="90dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.interfun.buz.chat.voicemoji.view.widget.VoiceEmojiTextView
        android:id="@+id/voiceMojiText"
        android:layout_width="90dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/ic_add"
        android:textColor="@color/text_white_important"
        app:autoSizeTextType="uniform"
        app:layout_constraintTop_toTopOf="@+id/spaceVoiceMojiText"
        app:layout_constraintBottom_toBottomOf="@+id/spaceVoiceMojiText"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="@+id/spaceVoiceMojiText"
        app:layout_constraintStart_toStartOf="@+id/spaceVoiceMojiText" />

    <androidx.legacy.widget.Space
        android:id="@+id/startMidCircle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="@id/middleCircle"
        app:layout_constraintTop_toTopOf="@id/middleCircle" />

    <com.interfun.buz.base.widget.round.RoundView
        android:id="@+id/middleCircle"
        android:layout_width="8dp"
        android:layout_height="0dp"
        android:background="@drawable/chat_circle_overlay_grey_26"
        app:layout_constraintStart_toEndOf="@id/voiceMojiText"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintBottom_toBottomOf="@id/bigCircle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bigCircle"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:background="@drawable/chat_circle_overlay_grey_26"
        app:layout_constraintTop_toTopOf="@id/voiceMojiText"
        app:layout_constraintStart_toStartOf="@id/startMidCircle">


        <ImageView
            android:id="@+id/iftvVoiceMojiLogo"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/chat_ic_ve_unread"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <org.libpag.PAGView
            android:id="@+id/pagPlaying"
            android:layout_width="16dp"
            android:layout_height="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="end"
            app:constraint_referenced_ids="pagPlaying,iftvVoiceMojiLogo" />

        <!--   Do not use sp, otherwise the large font is hard to look at.    -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/voiceMojiName"
            style="@style/main_describe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14dp"
            android:textColor="@color/white_80"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/barrier"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="sdsd" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>