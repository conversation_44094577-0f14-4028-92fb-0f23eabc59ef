package com.interfun.buz.core.widget_liveplace.utils

import android.content.Context
import com.interfun.buz.base.ktx.asDimension
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.utils.ScreenUtil.getNavigationHeight
import com.interfun.buz.base.utils.ScreenUtil.getScreenHeightReal
import com.interfun.buz.base.utils.ScreenUtil.getStatusBarHeight
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.manager.realtimecall.MinimizeViewModel
import com.interfun.buz.common.service.HomeService
import com.interfun.buz.core.widget_liveplace.R

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @desc
 * 当切换到 LivePlace 模式时，整个弹窗从全屏展开变为半屏展开，半屏的高度受三个高度影响，从上到下为：
 * 1. 顶部背景层的打光图片，高度为状态栏高度 + 标题栏高度 + 首页列表高度 + 额外10dp
 * 2. 屏幕底部漏出头像区域的高度为20dp，上面的内容即是 LivePlace 区域的高度
 */
object LivePlaceEntranceHeightHelper {

    private val bottomMargin = 20.dp
    private val homeRvHeight
        get() = routerServices<HomeService>().value?.getHomeRVHeight() ?: 240.dp

    fun getLightImageHeight(context: Context): Int {
        val topHeight = if (MinimizeViewModel.isMinimizing) 46.dp else 0
        val titleBarHeight = R.dimen.title_bar_height.asDimension().toInt()
        return topHeight + getStatusBarHeight(context) + titleBarHeight + homeRvHeight + 10.dp
    }

    // 底部 20dp 间距用于漏出部分头像
    fun getEntranceFullHeight(context: Context) =
        (getProfileFullHeight(context) - getLightImageHeight(context) - bottomMargin)
            .coerceAtMost(450.dp)

    fun getLiveProfileHeightRatio(context: Context): Float {
        // 弹窗顶部有这些高度的透明的间距
        val topMargin = if (MinimizeViewModel.isMinimizing) {
            getStatusBarHeight(context) + 46.dp
        } else {
            getStatusBarHeight(context)
        }
        val contentHeight = topMargin + getEntranceFullHeight(context) + bottomMargin
        return contentHeight / getProfileFullHeight(context).toFloat()
    }

    private fun getProfileFullHeight(context: Context) =
        getScreenHeightReal(context) - getNavigationHeight(context)
}