package com.interfun.buz.common.di

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import com.interfun.buz.component.hilt.GlobalQualifier
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
internal object CommonModule {

    @GlobalQualifier
    @Singleton
    @Provides
    fun provideGlobalDispatcher(): CoroutineDispatcher {
        return Dispatchers.Default
    }

    @GlobalQualifier
    @Singleton
    @Provides
    fun provideGlobalScope(@GlobalQualifier dispatcher: CoroutineDispatcher): CoroutineScope {
        return CoroutineScope(SupervisorJob() + dispatcher)
    }

    @GlobalQualifier
    @Provides
    fun provideGlobalLivecycle(): Lifecycle {
        return ProcessLifecycleOwner.get().lifecycle
    }

}