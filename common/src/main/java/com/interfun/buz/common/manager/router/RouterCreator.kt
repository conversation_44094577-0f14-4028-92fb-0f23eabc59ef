package com.interfun.buz.common.manager.router

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.NEW_FEATURE_NOTIFICATION_H5
import com.interfun.buz.common.constants.RouterParamValues
import com.interfun.buz.common.manager.router.converter.*
import org.json.JSONObject

object RouterCreator {
    private val TAG = "RouterCreator"

    /**
     * @param type [RouterParamValues.AI.TYPE_SOURCE_LANGUAGE],[RouterParamValues.AI.TYPE_SOURCE_LANGUAGE]
     * @param source [RouterParamValues.AI.TRANSLATION_SOURCE_PROFILE_SETTING],[RouterParamValues.AI.TRANSLATION_SOURCE_CENTERED_MSG]
     */
    fun createSettingTranslatorLanguageRouter(robotId: Long, type: Int, source: Int): JSONObject {
        val converter = RouterMapping.getConverter(TranslatorLangSettingRouterConverter::class.java)
        val args = TranslatorLangSettingArgs(robotId, type, source)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    fun createPrivateChatRouter(
        fromId: String, targetId: String,
        serMsgId: String? = null,
        reactionOpUserId: String? = null,
        msgId: Long? = null,
        openVoiceFilter: Boolean = false
    ): JSONObject {
        val converter = RouterMapping.getConverter(PrivateChatRouterConverter::class.java)
        val args = PrivateChatRouterArgs(fromId, targetId, serMsgId, reactionOpUserId, msgId, openVoiceFilter)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    fun createGroupChatRouter(
        groupId: Long,
        serMsgId: String? = null,
        reactionOpUserId: String? = null,
        msgId: Long? = null
    ): JSONObject {
        val converter = RouterMapping.getConverter(GroupChatRouterConverter::class.java)
        val args = GroupChatRouterArgs(groupId, serMsgId, reactionOpUserId, msgId)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    /**
     * create channel 1v1 router
     * @param sourceFromAnswer 来源 用于埋点+判断是否点击call_style通知栏的answer, 1:来自call style的 answerIntent
     */
    fun createChannelPrivateChatRouter(
        channelId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        callerUserId: Long,
        jumpType: Int,
        sourceFromAnswer: Int = 0,
        jumFrom: @JumpVoiceCallPageFrom Int
    ): JSONObject {
        logInfo(TAG, "createChannelPrivateChatRouter: channelType = $channelType")
        val converter = RouterMapping.getConverter(VoiceCallPrivateChatRouterConverter::class.java)
        val args = ChannelPrivateChatRouterArgs(
            channelId,
            callerUserId,
            jumpType,
            sourceFromAnswer,
            jumFrom,
            channelType,
            callType
        )
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    /**
     * create voice call group router
     * @param sourceFromAnswer 来源 用于埋点+判断是否点击call_style通知栏的answer 1:来自call style的 answerIntent
     */
    fun createChannelGroupChatRouter(
        channelId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        callerUserId: Long,
        groupId: Long,
        jumpType: Int,
        sourceFromAnswer: Int = 0,
        jumFrom: @JumpVoiceCallPageFrom Int
    ): JSONObject {
        logInfo(TAG, "createChannelGroupChatRouter: channelType = $channelType")
        val converter = RouterMapping.getConverter(VoiceCallGroupChatRouterConverter::class.java)
        val args = ChannelGroupChatRouterArgs(
            channelId,
            callerUserId,
            groupId,
            jumpType,
            sourceFromAnswer,
            jumFrom,
            channelType,
            callType
        )
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }


    /**
     * @param type 1:private 2:group
     */
    fun createHomeListRouter(
        targetId: String?,
        type: Int = 1,
        clickNotificationSpot: Int = 0,
        router: String? = null
    ): JSONObject {
        val converter = RouterMapping.getConverter(HomeListRouterConverter::class.java)
        val args = HomeListRouterArgs(type, targetId, clickNotificationSpot,router)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    fun createHomeNewFeatureRouter(): JSONObject {
        val converter = RouterMapping.getConverter(FeatureNotificationConverter::class.java)
        val args = FeatureNotificationRouterArgs(NEW_FEATURE_NOTIFICATION_H5)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    fun createGroupInfoRouter(groupId: String, source: Int, inviterId: String? = null): JSONObject {
        val converter = RouterMapping.getConverter(GroupInfoConverter::class.java)
        val args = GroupInfoRouterArgs(groupId, source, inviterId)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    fun createUserProfileRouter(userId: Long, source: Int): JSONObject {
        val converter = RouterMapping.getConverter(UserProfileRouterConverter::class.java)
        val args = UserProfileRouterArgs(userId, source)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }

    fun createPhoneInputRouter(page: LoginNotificationPageType): JSONObject {
        val converter = RouterMapping.getConverter(LoginNotificationConverter::class.java)
        val args = LoginNotificationRouterArgs(page)
        val extraData = converter.convertToExtraData(args)
        return RouterManager.create(converter.scheme, extraData)
    }
//
//    fun createOnAirPreviewRouter(previewParam: OnAirPreviewParam): JSONObject {
//        val converter = RouterMapping.getConverter(OnAirPreviewConverter::class.java)
//        val extraData = converter.convertToExtraData(previewParam)
//        return RouterManager.create(converter.scheme, extraData)
//    }

}