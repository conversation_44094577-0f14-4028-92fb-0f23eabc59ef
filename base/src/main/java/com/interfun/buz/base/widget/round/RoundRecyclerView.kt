package com.interfun.buz.base.widget.round

import android.content.Context
import android.graphics.Canvas
import android.graphics.Shader
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView

/**
 * <AUTHOR>
 *
 * @date 2022/6/23
 *
 * @desc
 */
open class RoundRecyclerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr), IRoundLayout {

    private val roundHelper = RoundLayout(this, attrs)

    var isIntercept = false

    override fun dispatchTouchEvent(ev: MotionEvent?): Bo<PERSON>an {
        return if (isIntercept) {
            false
        } else {
            super.dispatchTouchEvent(ev)
        }
    }

    override fun draw(canvas: Canvas) {
        roundHelper.drawBefore(canvas)
        super.draw(canvas)
        roundHelper.drawAfter(canvas)
    }

    fun setRadius(radius: Float?) {
        setRadius(radius, radius, radius, radius)
    }

    override fun setBorder(color: Int, gradient: Shader?, width: Float) {
        roundHelper.strokeWidth = width
        roundHelper.strokeColor = color
        gradient?.let{ roundHelper.strokeGradient = gradient }
        invalidate()
    }

    override fun setRadius(tlR: Float?, trR: Float?, brR: Float?, blR: Float?) {
        roundHelper.setRadius(tlR, trR, brR, blR)
        invalidate()
    }
}