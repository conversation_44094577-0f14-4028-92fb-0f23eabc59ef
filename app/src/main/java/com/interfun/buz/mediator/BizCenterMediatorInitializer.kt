package com.interfun.buz.mediator

import android.content.Context
import com.interfun.buz.biz.center.voicefilter.di.VoiceFilterBizCenterMediator
import com.interfun.buz.im.IMMediator
import com.interfun.buz.social.SocialBizCenterMediator
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class BizCenterMediatorInitializer @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val socialBizCenterMediator: SocialBizCenterMediator,
    private val voiceFilterBizCenterMediator: VoiceFilterBizCenterMediator,
    private val imMediator: IMMediator,
) {
    fun init() {
        socialBizCenterMediator.init(appContext)
        voiceFilterBizCenterMediator.initRepository(appContext)
        imMediator.init()
    }
}