package com.interfun.buz.common.widget.view

import android.content.Context
import android.content.res.TypedArray
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.interfun.buz.common.R

/**
 * Author: ChenYouSheng
 * Date: 2023/11/15
 * Email: <EMAIL>
 * Desc: 与[LinearGradientIconFontTextView]的区别是LinearGradientIconFontTextView受iconfont/buz.ttf的影响，布局style的设置无效
 */
class LinearGradientTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
) : AppCompatTextView(context, attrs) {

    init {
        val typedArray: TypedArray =
            context.obtainStyledAttributes(attrs, R.styleable.LinearGradientTextView)
        val colorStart =
            typedArray.getColor(R.styleable.LinearGradientTextView_colorStart, Color.WHITE)
        val colorMiddle =
            typedArray.getColor(R.styleable.LinearGradientTextView_colorMiddle, Color.WHITE)
        val colorEnd = typedArray.getColor(R.styleable.LinearGradientTextView_colorEnd, Color.WHITE)
        typedArray.recycle()

        val textWidth = paint.measureText(text.toString())
        val shader: Shader = LinearGradient(
            0f, 0f, textWidth, 0f, intArrayOf(colorStart, colorMiddle, colorEnd),
            null, Shader.TileMode.CLAMP
        )
        paint.shader = shader
    }
}
