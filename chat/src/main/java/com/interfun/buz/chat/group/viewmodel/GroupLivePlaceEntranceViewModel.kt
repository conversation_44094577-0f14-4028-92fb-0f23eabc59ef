package com.interfun.buz.chat.group.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.push.PushOP
import com.interfun.buz.common.database.entity.ExistPlaceType
import com.interfun.buz.common.database.entity.LivePlaceBaseInfo
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.core.widget_liveplace.R
import com.interfun.buz.core.widget_liveplace.state.LivePlaceEntranceUIState
import com.interfun.buz.core.widget_liveplace.state.toEntranceUIState
import com.interfun.buz.core.widget_liveplace.state.toEntranceUIStateSync
import com.interfun.buz.im.signal.SignalManagerPresenter
import com.interfun.buz.liveplace.manager.LivePlaceCacheHelper
import com.interfun.buz.liveplace.repository.LivePlaceBaseInfoRepositoryImpl
import com.interfun.buz.onair.bean.isLivePlaceChannelInfo
import com.interfun.buz.social.repo.GroupRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.update
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @desc
 */
@HiltViewModel
class GroupLivePlaceEntranceViewModel @Inject constructor(
    private val infoRepo: LivePlaceBaseInfoRepositoryImpl,
    private val groupRepository: GroupRepository,
) : ViewModel() {

    companion object {
        const val TAG = "GroupLivePlaceEntranceViewModel"
    }

    private val needUpdateFlow = MutableStateFlow(0)

    fun isInGroupFlow(groupId: Long) = groupRepository.getGroupExtraFlow(groupId).map { it.isInGroup }

    fun livePlaceUIState(groupId: Long) = channelFlow {
        launch {
            // 接收信令更新预览信息变更
            SignalManagerPresenter.obtainInstance().obtainSignalingFlow(filter = { signalData ->
                signalData.topic == PushOP.PushLivePlacePreviewInfoChange.op
            }).collectLatest {
                logInfo(TAG, "PushLivePlacePreviewChange")
                ChannelStatusManager.getConvChannelPreview(groupId, 2)
            }
        }
        launch {
            // 接收信令更新空间信息变更
            SignalManagerPresenter.obtainInstance().obtainSignalingFlow(filter = { signalData ->
                signalData.topic == PushOP.PUSH_LIVE_PLACE_BASIC_UPDATE.op
            }).collectLatest {
                logInfo(TAG, "PushLivePlaceInfoChange")
                requestLivePlace(groupId)
            }
        }
        launch {
            groupRepository.getGroupExtraFlow(groupId).map { it.isInGroup }
                .distinctUntilChanged()
                .collect {
                    needUpdateFlow.update { it + 1 }
                }
        }
        combine(
            infoRepo.livePlaceForGroupFlow(groupId),
            infoRepo.getLivePlaceExistInfoFlow(groupId, PlaceType.GROUP),
            needUpdateFlow
        ) { livePlaceInfo, existInfo, updateCount ->
            livePlaceInfo to (existInfo?.existPlaceType ?: ExistPlaceType.PENDING)
        }.collectLatest { pair ->
            val (livePlaceInfo, existType) = pair
            logInfo(
                TAG, "groupId: $groupId, " +
                    "livePlaceInfo is null: ${livePlaceInfo.isNull()}, existType: $existType"
            )
            val uiState = buildUIState(groupId, livePlaceInfo, existType)
            send(uiState)
            if (uiState is LivePlaceEntranceUIState.Created) {
                ChannelStatusManager.convChannelInfoChangeList
                    .filter { it.map { changeInfo -> changeInfo.convTargetId }.contains(groupId) }
                    .collectLatest {
                        send(buildUIState(groupId, livePlaceInfo, existType))
                    }
            }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000L),
        initialValue = LivePlaceCacheHelper
            .getExistsInfoFromMem(groupId)
            .toEntranceUIState(groupId, PlaceType.GROUP)
    )

    fun requestLivePlace(groupId: Long) = launchIO {
        infoRepo.requestLivePlaceForGroup(groupId)
    }

    fun checkAndGetLivePrePreview(groupId: Long) = launchIO {
        ChannelStatusManager.getConvChannelPreview(groupId, 2)
    }

    private suspend fun buildUIState(
        targetId: Long,
        livePlaceInfo: LivePlaceBaseInfo?,
        existType: ExistPlaceType
    ): LivePlaceEntranceUIState {
        val uiState = existType.toEntranceUIStateSync(targetId, PlaceType.GROUP)
        if (livePlaceInfo == null) {
            return uiState
        }
        if (uiState is LivePlaceEntranceUIState.NotEnable) {
            return uiState
        }
        val livePlacePreview = ChannelStatusManager.getConvChannelInfo(targetId)?.takeIf {
            it.isLivePlaceChannelInfo()
        }
        val isLive = livePlacePreview?.channelStatus == 2
        return LivePlaceEntranceUIState.Created(
            channelId = livePlacePreview?.channelId,
            topic = livePlaceInfo.topic,
            name = GroupInfoCacheManager.getGroupInfoBeanByIdSync(targetId)?.groupName,
            bgImgUrl = livePlaceInfo.bgImgUrl,
            isCustomizeBg = livePlaceInfo.isCustomizeBg,
            isLive = isLive,
            memberNum = livePlacePreview?.channelInfo?.memberCount ?: 0,
            topMemberPortraits = livePlacePreview?.channelInfo?.topMembers,
            btnText = if (isLive) {
                R.string.join_live_place.asString()
            } else {
                R.string.live_place_enter.asString()
            }
        )
    }
}