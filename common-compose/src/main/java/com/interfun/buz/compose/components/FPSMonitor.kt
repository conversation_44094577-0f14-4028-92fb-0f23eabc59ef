package com.interfun.buz.compose.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.util.fastRoundToInt

/**
 * <AUTHOR>
 * @date 2024/3/5
 * @desc
 */
@Composable
fun FPSMonitor(
    modifier: Modifier = Modifier,
    updateInterval: Int = 5 // 每5帧更新一次FPS
) {
    var fps by remember { mutableIntStateOf(0) }
    val frameTimestamps = remember { mutableListOf<Long>() }
    val textColor by animateColorAsState(
        targetValue = if (fps > 57) Color.Green else Color.Red, label = ""
    )

    LaunchedEffect(Unit) {
        while (true) {
            withFrameMillis { frameTime ->
                frameTimestamps.add(frameTime)
                // 仅保留最新的updateInterval + 1个时间戳
                if (frameTimestamps.size > updateInterval + 1) {
                    frameTimestamps.removeAt(0)
                }
                // 每updateInterval帧更新一次FPS
                if (frameTimestamps.size == updateInterval + 1) {
                    val duration = frameTimestamps.last() - frameTimestamps.first()
                    fps = (updateInterval * 1000 / duration.toFloat()).fastRoundToInt()
                }
            }
        }
    }

    Text(
        text = "FPS: $fps",
        modifier = modifier,
        color = textColor
    )
}
