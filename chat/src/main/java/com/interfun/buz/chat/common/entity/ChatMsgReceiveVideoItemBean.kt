package com.interfun.buz.chat.common.entity

import com.interfun.buz.media.player.manager.MediaDownloadManager
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.yibasan.lizhifm.lzlogan.Logz

/**
 * Author: ChenYouSheng
 * Date: 2023/6/6
 * Email: chenyo<PERSON><PERSON>@lizhi.fm
 * Desc: 图片接收消息
 */
class ChatMsgReceiveVideoItemBean(
    override var msg: IMessage,
    override val msgType: ChatMsgType = ChatMsgType.Video,
    private var remainingBytes: Long = 0L

) : ChatMsgReceiveMediaItemBean() {
    init {
        remainingBytes = if (msg.content is IM5VideoMessage) {
            val remoteUrl = (msg.content as IM5VideoMessage).remoteUrl
            if (remoteUrl.isNullOrEmpty()) {
                0L
            }else{
                val downloadSize =
                    MediaDownloadManager.getVideoDownloadSize(remoteUrl)
                Logz.tag("ChatMsgReceiveVideoItemBean")
                    .d("remoteUrl:$remoteUrl downloadSize:$downloadSize totalBytes:${(msg.content as IM5VideoMessage).totalBytes} leftSize:${(msg.content as IM5VideoMessage).totalBytes - downloadSize}")
                (msg.content as IM5VideoMessage).totalBytes - downloadSize
            }
        } else 0L
    }

    override fun getRemainingBytes(): Long {
        return remainingBytes
    }

    override fun setRemainingBytes(bytes: Long) {
        remainingBytes = bytes
    }
}