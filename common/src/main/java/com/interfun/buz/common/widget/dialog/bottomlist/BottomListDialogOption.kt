package com.interfun.buz.common.widget.dialog.bottomlist

import android.os.Parcelable
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.common.R
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 * @date 2023/6/29
 * @desc
 */
@Parcelize
data class BottomListDialogOption(
    val iconRes: Int? = null,
    val imageRes: String? = null,
    val title: String,
    val optionType: DialogOptionType = DialogOptionType.DefaultOption,
    val backgroundType: BackgroundType = BackgroundType.Default,
    val autoDismiss: Boolean = true,  // 点击item后是否自动关闭dialog
    val onOptionClick: (() -> Unit)? = null,
    val iconSize: Float? = null,
) : Parcelable {

    fun getTextColor(): Int {
        return when (optionType) {
            DialogOptionType.HighlightOption -> R.color.basic_primary.asColor()
            DialogOptionType.WarningOption -> R.color.secondary_error.asColor()
            DialogOptionType.CaptionOption -> R.color.text_white_secondary.asColor()
            else -> R.color.text_white_important.asColor()
        }
    }
}

enum class DialogOptionType {
    DefaultOption,
    HighlightOption,
    WarningOption,
    CaptionOption,
    LineSeparatorOption
}

enum class BackgroundType{
    Default,
    OverlayGrey20,
    OverlayWhite4
}