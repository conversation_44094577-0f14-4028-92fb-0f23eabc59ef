package com.interfun.buz.chat.common.entity

import com.interfun.buz.im.message.UpdateGroupNameNotifyCommand
import com.lizhi.im5.sdk.message.IMessage

/**
 * Author: ChenYouSheng
 * Date: 2023/5/10
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * Desc: 修改群昵称
 */
class ChatMsgReceiveUpdateGroupNameNotifyItemBean(
    override var msg: IMessage, override val msgType: ChatMsgType = ChatMsgType.Command, val command: UpdateGroupNameNotifyCommand
) : BaseChatMsgItemBean()