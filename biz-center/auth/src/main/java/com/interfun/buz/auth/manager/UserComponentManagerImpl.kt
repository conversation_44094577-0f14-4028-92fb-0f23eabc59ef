package com.interfun.buz.auth.manager

import com.interfun.buz.auth.repository.UserAuthRepository
import com.interfun.buz.component.hilt.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
internal class UserComponentManagerImpl @Inject constructor(
    private val userAuthRepository: UserAuthRepository,
    private val userComponentBuilder: UserComponentBuilder,
    @GlobalQualifier private val scope: CoroutineScope
) : UserComponentManager {

    private val userComponentHolder = userAuthRepository.userAuthFlow.map {
        createComponentHolder(it)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        createComponentHolder(userAuthRepository.userAuthFlow.value)
    )

    private val userComponentFlow = userComponentHolder.map { it.component }
        .stateIn(scope, SharingStarted.Eagerly, userComponentHolder.value.component)

    override fun getUserComponentFlow(): StateFlow<UserComponent> {
        return userComponentFlow
    }

    private fun createComponentHolder(auth: UserAuth): ComponentHolder {
        val component =
            userComponentBuilder.setUserAuth(auth)
                .setUserId(auth.userId)
                .setScope(auth.userScope)
                .build()
        return ComponentHolder(component, auth)
    }

    private class ComponentHolder(
        val component: UserComponent,
        val userAuth: UserAuth
    ) {
        //为了userComponentHolder flow 默认值与变更时生成多次component，需要重写equals和hashCode方法
        override fun equals(other: Any?): Boolean {
            return other is ComponentHolder && other.userAuth == this.userAuth
        }

        override fun hashCode(): Int {
            return userAuth.hashCode()
        }
    }

}