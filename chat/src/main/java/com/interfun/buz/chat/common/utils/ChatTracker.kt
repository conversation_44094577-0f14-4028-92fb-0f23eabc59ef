package com.interfun.buz.chat.common.utils

import com.interfun.buz.base.ktx.getIfTrueOrFalse
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.toSafeLong
import com.interfun.buz.chat.ai.invite.viewmodel.BotJoinOrExitGroupResult
import com.interfun.buz.chat.common.entity.BottomPanelContentType
import com.interfun.buz.chat.common.entity.BottomPanelContentType.MORE_PANEL
import com.interfun.buz.chat.common.entity.BottomPanelContentType.VOICE_RECORD_PANEL
import com.interfun.buz.chat.common.entity.ChatMsgType
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.login.LoginMainABTestManager
import com.interfun.buz.common.tracker.trackerString
import com.interfun.buz.common.tracker.trackerStringAI
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.utils.language.LanguageManager.getSystemLanguage
import com.interfun.buz.common.utils.logAFAndFireBaseEvent
import com.interfun.buz.domain.chat.entity.FileSendFrom
import com.interfun.buz.domain.social.helper.ShareType.*
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.getConversationId
import com.interfun.buz.im.ktx.isGroup
import com.interfun.buz.im.ktx.targetIsRobotOnPrivate
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object ChatTracker {

    /** APP页面浏览-他人聊天页 */
    fun onChatPrivatePageView(
        isRegister: Boolean,
        targetId: Long,
        serMsgId: String? = null,
    ) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2022091406")
            put(TrackConstant.KEY_TITLE, "他人聊天页")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (targetId.isRobot()) "1" else "0")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            val content = isRegister.getIfTrueOrFalse("registered", "unregister")
            put(TrackConstant.KEY_PAGE_CONTENT, content)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, serMsgId ?: "")
            put(TrackConstant.KEY_LOG_TIME, System.currentTimeMillis().toString())
        }
    }

    /** APP页面浏览-群聊天页 */
    fun onChatGroupPageView(serMsgId: String? = null) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2022091407")
            put(TrackConstant.KEY_TITLE, "群聊天页")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, serMsgId ?: "")
            put(TrackConstant.KEY_LOG_TIME, System.currentTimeMillis().toString())
        }
    }


    /** APP元素点击-APP内-好友上线弹窗 */
    fun onFriendOnlinePopupClick() {
        val pageBusinessType = if (CommonMMKV.isQuietModeEnable) "quiet" else "available"
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023072003")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "好友上线")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
        }
    }

    /** APP元素点击-群聊天页-退出群聊 */
    fun onChatLeaveGroupClick() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2022091425")
            put(TrackConstant.KEY_TITLE, "群聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "退出群聊")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }

    /** APP元素点击-群聊天页-添加群成员 */
    fun onChatAddGroupMemberClick() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2022091426")
            put(TrackConstant.KEY_TITLE, "群聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "添加群成员")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }

    /** APP元素点击-群聊天页-编辑群资料 */
    fun onChatEditGroupInfoClick() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2022091427")
            put(TrackConstant.KEY_TITLE, "群聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "编辑群资料")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }

    /** APP元素点击-对讲机首页，点击中间单个群时上报 */
    fun onHomeGroupClick() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2022091428")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "单个群聊")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    /** APP元素点击-应用内消息弹窗 */
    fun onChatPopupMessageClick(
        pageBusinessType: String,
        pageBusinessId: String,
        elementBusinessType: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023060903")
            put(TrackConstant.KEY_TITLE, "顶部")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, pageBusinessId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
        }
    }

    fun onCreateChannelPageViewScreen(business_num: Int) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2022091404")
            put(TrackConstant.KEY_TITLE, "创群选人页")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_BUSINESS_NUM, business_num)
        }
    }

    /**
     * 点击事件-对讲机首页，点击中间单个好友时上报
     */
    fun postContactFriendClickEvent(
        element_business_content: String,
        element_business_type: String? = null
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2022091403")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "单个好友")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, element_business_content)
            if (element_business_type != null) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, element_business_type)
            }
        }
    }

    fun postGetImageFailResult(reason: String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023062001")
            put(TrackConstant.KEY_RESULT_TYPE, "send_photo_error")
            put(TrackConstant.KEY_PAGE_TYPE, "photo_error")
            put(TrackConstant.KEY_IS_SUCCESS, "fail")
            put(TrackConstant.KEY_FAIL_REASON, reason)
        }
    }


    fun postCreateGroupResult(
        groupId: Long?,
        business_num: Int,
        is_success: String,
        fail_reason: String? = null,
        source: Int
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2022091402")
            put(TrackConstant.KEY_RESULT_TYPE, "create_group")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId?.toString() ?: "0")
            put(TrackConstant.KEY_BUSINESS_NUM, business_num)
            put(TrackConstant.KEY_IS_SUCCESS, is_success)
            if (fail_reason != null) {
                put(TrackConstant.KEY_FAIL_REASON, fail_reason)
            }
            put(TrackConstant.KEY_SOURCE, "$source")
        }

        val afEvent = "af_group"
        val firebaseEvent = "fb_group"
        logAFAndFireBaseEvent(afEvent, firebaseEvent, is_success == "success")
    }


    fun postWTInviteResult(
        businessType: String,
        businessId: String,
        business_num: Int,
        is_success: String,
        fail_reason: String? = null
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2022102804")
            put(TrackConstant.KEY_RESULT_TYPE, "send_walkie_talkie_invite")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, businessId)
            put(TrackConstant.KEY_BUSINESS_NUM, business_num)
            put(TrackConstant.KEY_IS_SUCCESS, is_success)
            if (fail_reason != null) {
                put(TrackConstant.KEY_FAIL_REASON, fail_reason)
            }
        }
    }

    fun onShareGroupLinkClick(
        exclusiveId: String,
        title: String,
        elementContent: String,
        pageType: String,
        businessNum: Int,
        pageBusinessId: String? = null
    ) {
        CommonTracker.postClickEvent(
            exclusive_id = exclusiveId,
            title = title,
            elementContent = elementContent,
            pageType = pageType,
            business_num = businessNum,
            page_business_id = pageBusinessId
        )
    }

    fun onGroupInfoDialogView(
        isNewUser: Boolean,
        groupId: String,
        isInGroup: Boolean,
        fromChatHistory: Boolean,
        fromChatHeader: Boolean,
        content: String,
        isLive:Boolean
    ) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2023051701")
            put(TrackConstant.KEY_TITLE, "群资料页弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isNewUser) "1" else "2")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
            put(TrackConstant.KEY_PAGE_STATUS, if (isInGroup) "2" else "1")
            if (fromChatHistory) {
                put(TrackConstant.KEY_SOURCE, "chat_history_name")
            } else if (fromChatHeader) {
                put(TrackConstant.KEY_SOURCE, "liveplace_sofa")
            }
            put(TrackConstant.KEY_PAGE_CONTENT, content)
            put(TrackConstant.KEY_CONTENT_NAME, if (isLive) "onlive" else "not_onlive")
        }
    }

    fun onShareGroupQRCodeClick() {
        CommonTracker.postClickEvent(
            exclusive_id = "AC2023051704",
            title = "群资料页",
            elementContent = "二维码",
            pageType = "chat"
        )
    }

    fun onChaHomeMinePortraitClick() {
        CommonTracker.postClickEvent("AC2022091401", "首页", "个人主页按钮", "home")
    }

    fun onChaHomeContactClick() {
        CommonTracker.postClickEvent("AC2022091402", "首页", "通讯录页按钮", "home")
    }

    fun onChatHomePageView() {
        val content_name = if(LoginMainABTestManager.isShowNewHomePagePlanB) "new_UI" else "old_UI"
        CommonTracker.onPageViewScreen(
            "AVS2022091401",
            "首页",
            "home",
            isReportImmediately = true
        ) {
            put(TrackConstant.KEY_CONTENT_NAME, content_name)
        }
    }

    fun onCampaignEntryClick() {
        CommonTracker.postClickEvent("AC2023120701", "首页", "活动入口", "home")
    }

    fun onClickAddFriendInHomePage(userId: Long) {
        CommonTracker.postClickEvent(
            "AC2022123008",
            "首页",
            "添加好友",
            "chat",
            element_business_id = userId.toString(),
        )
        logAFAndFirebaseInviteEvent()
    }

    fun onUserSendMsgResult(isSuccess: Boolean, resultCode: String? = null) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023062901")
            put(TrackConstant.KEY_RESULT_TYPE, "research_msg")
            put(TrackConstant.KEY_PAGE_TYPE, "user_talk")
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            if (resultCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, resultCode)
            }
        }
    }

    /** APP弹窗浏览-删除历史消息二次确认弹窗 */
    fun onClearMessageDialogView(businessId: String, businessType: String) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2023021402")
            put(TrackConstant.KEY_TITLE, "删除历史消息弹窗")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, businessId)
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }

    /** APP元素点击-删除历史消息二次确认弹窗-确认按钮 */
    fun onSureClearMessageClick(businessID: String, businessType: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023021402")
            put(TrackConstant.KEY_TITLE, "删除历史消息弹窗")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "确认删除")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, businessID)
        }
    }

    private fun logAFAndFirebaseInviteEvent() {
        val afEvent = "af_invite"
        val firebaseEvent = "fb_invite"
        logAFAndFireBaseEvent(afEvent, firebaseEvent, true)
    }

    /**
     * 引导用户添加机器人弹窗曝光时上报
     */
    fun onRobotGuidanceDialogExposed() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2023041801")
            put(TrackConstant.KEY_TITLE, "添加机器人引导弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "robot")
        }
    }

    /**
     * 好友上线popup曝光
     */
    fun onFriendOnlinePopup() {
        val pageBusinessType = if (CommonMMKV.isQuietModeEnable) "quiet" else "available"
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2023072001")
            put(TrackConstant.KEY_TITLE, "好友上线弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "friend_online")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
        }
    }

    fun onExposeAutoPlayTip(){
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024102101")
            put(TrackConstant.KEY_TITLE, "home_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "autoplay_guidance")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    fun onExposePressRecordTip(){
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024102102")
            put(TrackConstant.KEY_TITLE, "home_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "send_voice_tooltip")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    /**
     * 消息弹窗曝光
     */
    fun onPopupMessageExposed(pageBusinessId: String) {
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2023060901")
            put(TrackConstant.KEY_TITLE, "顶部")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, pageBusinessId)
        }
    }

    fun onToastUserBanned(msgFromId: String) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024070901")
            put(TrackConstant.KEY_TITLE, "private")
            put(TrackConstant.KEY_PAGE_TYPE, msgFromId)
        }
    }

    fun onReportMsgClick(msg:IMessage,chatMsgType: ChatMsgType) {
        GlobalScope.launchIO {
            val pageBusinessType = when{
                msg.targetIsRobotOnPrivate() -> "robot"
                msg.isGroup -> "group"
                else -> "private"
            }

            val elementBusinessType = when(chatMsgType){
                ChatMsgType.WT,
                ChatMsgType.VoiceText,
                ChatMsgType.Voice -> "report_voice_message"
                ChatMsgType.Text -> "report_word_message"
                ChatMsgType.Image -> "report_photo_message"
                ChatMsgType.Video -> "report_video_message"
                ChatMsgType.File -> "report_File_message"
                else -> ""
            }

            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024070901")
                put(TrackConstant.KEY_TITLE, "chat")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "long_press_report_message")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
                put(TrackConstant.KEY_PAGE_BUSINESS_ID,msg.getConversationId())
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,elementBusinessType)
            }
        }
    }

    fun onReportAsrClick(isPrivate: Boolean, conversationId: String, traceId: String) {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024082704")
                put(TrackConstant.KEY_TITLE, "chat_history")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "report_transcribe")
                put(TrackConstant.KEY_PAGE_TYPE, "chat")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if(isPrivate) "private" else "group")
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, conversationId)
                val systemLanguage = getSystemLanguage()
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, systemLanguage)
                val appLangCode = LanguageManager.getLocaleLanguageCode()
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, appLangCode)
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, traceId)
            }
    }

    fun onReportMsgSendResult(msg:IMessage,chatMsgType: ChatMsgType,code:Int) {
        GlobalScope.launchIO {
            val pageBusinessType = when{
                msg.targetIsRobotOnPrivate() -> "robot"
                msg.isGroup -> "group"
                else -> "private"
            }

            val elementBusinessType = when(chatMsgType){
                ChatMsgType.WT,
                ChatMsgType.VoiceText,
                ChatMsgType.Voice -> "report_voice_message"
                ChatMsgType.Text -> "report_word_message"
                ChatMsgType.Image -> "report_photo_message"
                ChatMsgType.Video -> "report_video_message"
                else -> ""
            }

            BuzTracker.onResult {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024070901")
                put(TrackConstant.KEY_RESULT_TYPE,"report_message_result")
                put(TrackConstant.KEY_PAGE_TYPE, "chat")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
                put(TrackConstant.KEY_PAGE_BUSINESS_ID,msg.getConversationId())
                put(TrackConstant.KEY_CONTENT_ID,msg.msgTraceId)
                put(TrackConstant.KEY_IS_SUCCESS,if (code.isSuccess) "success" else "fail")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,elementBusinessType)
                if (msg.isGroup){
                    put(TrackConstant.KEY_BUSINESS_TYPE,msg.fromId)
                }
                if (!code.isSuccess){
                    put(TrackConstant.KEY_FAIL_REASON,code)
                }
            }
        }
    }

    /**
     * 添加机器人引导弹窗，点击【try it out】或【no，thanks】时上报
     */
    fun onClickItemOnRobotGuidance(clickTry: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023041802")
            put(TrackConstant.KEY_TITLE, "添加机器人引导弹窗")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "去试试")
            put(TrackConstant.KEY_PAGE_TYPE, "robot")
            val elementBusinessType = if (clickTry) "open" else "close"
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
        }
    }

    fun onClickLeaveMsgEntry(targetId: Long, isGroup: Boolean, isRobot: Boolean = false) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023060901")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "聊天")
            val type = if (isGroup) "group" else "private"
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, type)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
        }
    }

    fun onClickLeaveMsgTakePhoto(targetId: Long, isGroup: Boolean, isRobot: Boolean = false) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023060902")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "拍照")
            val type = if (isGroup) "group" else "private"
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, type)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_PAGE_STATUS, if (isRobot) "1" else "0")
        }
    }

    /**
     * 点击跳转到第一条未读消息的按钮时上报
     */
    fun onClickJumpToUnread(convId: String, convType: IM5ConversationType) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023060904")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "unread")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId)
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, convType.trackerString)
        }
    }

    /**
     * 跳转到第一条未读消息的页面曝光时上报
     */
    fun onJumpToUnreadShow(convId: String, convType: IM5ConversationType, robot: Boolean) {
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2023060902")
            put(TrackConstant.KEY_TITLE, "未读消息")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId)
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, convType.trackerStringAI(robot))
        }
    }

    /**
     * 群聊/私聊开始播放历史对讲机语音，获取结果时上报(播放就打，不用打失败的)
     */
    fun onPlayLeaveVoice(
        convId: String,
        hasPlayedBefore: Boolean,
        convType: IM5ConversationType,
        isRobot: Boolean
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023060904")
            put(TrackConstant.KEY_RESULT_TYPE, "play_old_voice")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId)
            put(TrackConstant.KEY_CONTENT_ID, if (hasPlayedBefore) "1" else "0")
            put(TrackConstant.KEY_IS_SUCCESS, "success")
            put(TrackConstant.KEY_FAIL_REASON, "")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, convType.trackerStringAI(isRobot))
        }
    }

    /**
     * 在聊天页，点击【文本输入框】时上报
     */
    fun onClickTextInputBox() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023070605")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "文本输入框")
        }
    }

    /**
     * 在聊天页，点击【发送】按钮时上报
     */
    fun onClickSendText(isRobot: Boolean, userId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023070606")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "发送文字")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isRobot) "1" else "0")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$userId")
        }
    }

    /**
     * 在文字弹窗页，点击【copy text】按钮时上报
     */
    fun onClickCopyMessageText(isRobot: Boolean, userId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023070607")
            put(TrackConstant.KEY_TITLE, "文字弹窗页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "复制")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isRobot) "1" else "0")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$userId")
        }
    }

    /**
     * 在聊天页/首页点击【+】后，再点【album】按钮时上报
     */
    fun onClickSendFromAlbum() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023070609")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "album")
        }
    }

    /**
     * 在聊天页点击【+】后，再点【take photo】按钮时上报
     */
    fun onClickSendFromTakePhoto(isRobot: Boolean, userId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023070608")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "take photo")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isRobot) "1" else "0")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$userId")
        }
    }

    /**
     * 指定serMsgId的Message加载曝光
     */
    fun onSerMsgIdLoadResult(
        convType: IM5ConversationType,
        serMsgId: String,
        loadedOnFirstPage: Boolean,
        isSuccess: Boolean
    ) {
        //logDebug("LZTrack", "RB2023110901 # serMsgId: $serMsgId, convType = $convType, loadedOnFirstPage = $loadedOnFirstPage")
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023110901")
            put(TrackConstant.KEY_RESULT_TYPE, "message_load_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (convType == IM5ConversationType.PRIVATE) "private" else "group"
            )
            put(TrackConstant.KEY_SOURCE, serMsgId)
            put(TrackConstant.KEY_LOG_TIME, System.currentTimeMillis().toString())
            put(TrackConstant.KEY_BUSINESS_NUM, if (loadedOnFirstPage) 1 else 0)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
        }
    }

    fun onClickVoiceEmojiInHistory(unicodeEmoji: String, id: String, isBlindBox: Boolean = false) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC20231120604")
            put(TrackConstant.KEY_TITLE, "他人聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "重播voicemoji")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, unicodeEmoji)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isBlindBox) "Y" else "N")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, id)
        }
    }

    fun onClickAddBotToGroupEvent(botUserId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023111302")
            put(TrackConstant.KEY_TITLE, "邀请机器人加群弹窗")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "加群add按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "add_AI_to_group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$botUserId")
        }
    }

    fun onInviteBot2GroupDialogExposure(totalGroupCount: Int) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2023111301")
            put(TrackConstant.KEY_TITLE, "邀请机器人加群弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "add_AI_to_group")
            put(TrackConstant.KEY_BUSINESS_NUM, totalGroupCount)
        }
    }

    fun onVoiceFilterPreviewDialogExposure(
        isPrivate: Boolean,
        targetId: String,
        voiceFilterId: String,
        campaignId: Long? = null
    ) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024112101")
            put(TrackConstant.KEY_TITLE, "voice_filter_preview")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_PAGE_STATUS, voiceFilterId)
            campaignId?.let{
                put(TrackConstant.KEY_CONTENT_NAME, it.toString())
            }
        }
    }

    fun onAddBotToGroupResult(botUserId: Long, source: String, result: BotJoinOrExitGroupResult?) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023111301")
            put(TrackConstant.KEY_RESULT_TYPE, "add_AI_to_group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$botUserId")
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_IS_SUCCESS, if (result?.success == true) "success" else "fail")
            if (result?.success == false) {
                put(TrackConstant.KEY_FAIL_REASON, "${result.rCode}")
            }
        }
    }

    fun onGetBotMemberResult(groupId: Long, businessNum: Int, pageStatus: String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023111303")
            put(TrackConstant.KEY_RESULT_TYPE, "group_have_AI")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$groupId")
            put(TrackConstant.KEY_PAGE_STATUS, pageStatus)
            put(TrackConstant.KEY_BUSINESS_NUM, businessNum)
        }
    }

    fun onRemoveBotFromGroupResult(isSuccess: Boolean,botUserIds: List<Long>) {
        for (botUserId in botUserIds) {
            BuzTracker.onResult {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023111302")
                put(TrackConstant.KEY_RESULT_TYPE, "remove_AI_from_group")
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$botUserId")
                put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            }
        }
    }

    fun onClickAddressBotInHome(botId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023111303")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "首页@机器人按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$botId")
        }
    }

    fun onClickSkipToHaveUnreadMsgConversation(){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024010801")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "未读消息提示")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    fun onRemoveConversationVoiceMsgClick(elementBusinessContent: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024010802")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "关闭自动播放语音")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, elementBusinessContent)
        }
    }

    fun onSkipToHaveUnreadMsgConversationViewExposure() {
       BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024010801")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "未读消息提示")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    fun onClickAddressBotInChatList(botId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023111304")
            put(TrackConstant.KEY_TITLE, "@机器人列表")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "选择机器人")
            put(TrackConstant.KEY_PAGE_TYPE, "chat_robot_list")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$botId")
        }
    }

    fun onAddressBotListDialogExposed() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2023111302")
            put(TrackConstant.KEY_TITLE, "@机器人列表")
            put(TrackConstant.KEY_PAGE_TYPE, "chat_robot_list")
        }
    }

    fun onRemoveAiFromGroupSucceed(botId: Long) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023111302")
            put(TrackConstant.KEY_RESULT_TYPE, "remove_AI_from_group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$botId")
            put(TrackConstant.KEY_IS_SUCCESS, "success")
        }
    }

    fun onVoiceEmojiPlayResult(
        appStatus: String,
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023120601")
            put(TrackConstant.KEY_RESULT_TYPE, "voicemoji_play_reslut")
            /**
             * 枚举值:
             * 1:前台对准（首页当前聊天对象)
             * 2:前台不对准（首页非当前聊天对象)
             * 3:APP压后台
             */
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, appStatus)
//            /**
//             * 播放结果:
//             * 1:成功
//             * 0:失败
//             */
//            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) 1 else 0)
        }
    }

    fun onClickReadMore() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023122501")
            put(TrackConstant.KEY_TITLE, "猜谜机器人弹窗")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "Read_more")
        }
    }
    fun onClickGotIt() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023122502")
            put(TrackConstant.KEY_TITLE, "猜谜机器人弹窗")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "Got_it")
        }
    }

    fun onClickAddressBotInChatListHorizontal(userId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"AC2023122503")
            put(TrackConstant.KEY_TITLE,"聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT,"@机器人")
            put(TrackConstant.KEY_PAGE_TYPE,"group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,"$userId")
        }
    }

    fun onClickAddBotToGroup(groupMemberCount: Int) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"AC2023122504")
            put(TrackConstant.KEY_TITLE,"群资料页")
            put(TrackConstant.KEY_ELEMENT_CONTENT,"加AI进群")
            put(TrackConstant.KEY_PAGE_TYPE,"chat")
            put(TrackConstant.KEY_BUSINESS_NUM,groupMemberCount)
        }
    }

    fun onClickAddBotsToGroup(groupId: Long,botUserIds: List<Long>) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023122505")
            put(TrackConstant.KEY_TITLE, "群资料页加AI列表")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "add按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "add_AI_to_group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$groupId")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,
                botUserIds.joinToString(prefix = "[", postfix = "]", separator = ","))
        }
    }

    fun onPuzzleDialogExpose() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2023122501")
            put(TrackConstant.KEY_TITLE, "猜谜机器人引导弹窗")
        }
    }

    fun onPuzzleGameGuideTipExpose(businessContent: String) {
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "2023122501")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "机器人操作引导")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, businessContent)
        }
    }

    fun onTranslationFeedbackResult(botUserId: String,isLike: Boolean, msgId: String?, replyMsgId: String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024022602")
            put(TrackConstant.KEY_MENU, botUserId)
            put(TrackConstant.KEY_RESULT_TYPE,"robot_feedback_result_valuate")
            put(TrackConstant.KEY_SOURCE, if (isLike) "like" else "dislike")
            put(TrackConstant.KEY_CONTENT_ID, msgId ?: "")
            put(TrackConstant.KEY_CONTENT_NAME, replyMsgId)
        }
    }

    fun onClickSwitchLanguageInHome(targetId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024022601")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "switch")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
        }
    }

    fun onClickAC2024032101(
        convType: IM5ConversationType,
        targetId: Long,
        msgType: ChatMsgType?,
        hasVoiceFilter: Boolean
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032101")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "long_press_msg")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (convType == IM5ConversationType.PRIVATE) "private" else "group"
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
            val elementBusinessType = when (msgType) {
                ChatMsgType.Text -> "word_message"
                ChatMsgType.Image -> "photo_message"
                ChatMsgType.Video -> "video_message"
                ChatMsgType.LOCATION -> "location_message"
                ChatMsgType.WT,
                ChatMsgType.Voice,
                ChatMsgType.VoiceText -> "voice_message"

                ChatMsgType.VoiceEmoji -> "voicemoji_message"
                ChatMsgType.VoiceGif -> "VG_message"
                ChatMsgType.File -> "File_message"
                else -> "other_message"
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID,
                if (hasVoiceFilter) "voice_filter" else "not_voice_filter"
            )
        }
    }

    fun onClickAC2024032102(
        veId: Long,
        fromPanel: Boolean,
        isLongClick: Boolean,
        position: Int? = null
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032102")
            put(TrackConstant.KEY_TITLE, "QR_voicemoji弹窗")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voicemoji")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (fromPanel) "2" else "1")
            position?.let {
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$it")
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isLongClick) "long" else "short")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, "$veId")
        }
    }

    fun onClickAC2024032103() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032103")
            put(TrackConstant.KEY_TITLE, "QR_voicemoji弹窗")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "plus")
        }
    }

    fun onClickAC2024032104(count: Int) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032104")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "QR_to_msg")
            put(TrackConstant.KEY_BUSINESS_NUM, count)
        }
    }

    fun onClickAC2024032105(veId: Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032105")
            put(TrackConstant.KEY_TITLE, "QR_reaction_info")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "remove")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, "$veId")
        }
    }

    fun onViewVS2024032101(convType: IM5ConversationType, targetId: String) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024032101")
            put(TrackConstant.KEY_TITLE, "QR_voicemoji弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (convType == IM5ConversationType.PRIVATE) "private" else "group"
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
        }
    }

    fun onViewVS2024032102(pair: Pair<IM5ConversationType, String>?, veId:Long) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024032102")
            put(TrackConstant.KEY_TITLE, "timeout_warning弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            if (pair != null) {
                put(
                    TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                    if (pair.first == IM5ConversationType.PRIVATE) "private" else "group"
                )
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, pair.second)
            }
            put(TrackConstant.KEY_PAGE_CONTENT, "$veId")
        }
    }

    fun onViewVS2024032103() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024032103")
            put(TrackConstant.KEY_TITLE, "expanded_voicemoji弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }

    fun onViewVS2024032104() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024032104")
            put(TrackConstant.KEY_TITLE, "QR_reaction_info弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }

    fun onResultRB2024032101(convType: IM5ConversationType, targetId: String, serMsgId: String, veId: Long) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032101")
            put(TrackConstant.KEY_RESULT_TYPE, "cancel_reacted_voicemoji")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (convType == IM5ConversationType.PRIVATE) "private" else "group"
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_CONTENT_ID, serMsgId)
            put(TrackConstant.KEY_MENU, "$veId")
        }
    }

    fun onResultRB2024032102(convType: IM5ConversationType, targetId: String, serMsgId: String, veId: Long) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032102")
            put(TrackConstant.KEY_RESULT_TYPE, "change_reacted_voicemoji")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (convType == IM5ConversationType.PRIVATE) "private" else "group"
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_CONTENT_ID, serMsgId)
            put(TrackConstant.KEY_MENU, "$veId")
        }
    }

    fun onViewVS2024051001() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024051001")
            put(TrackConstant.KEY_TITLE, "online_notificiation_popup")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }

    fun onClickAC2024051001() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024051001")
            put(TrackConstant.KEY_TITLE, "online_notificiation_popup")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "friend_online_notification")
        }
    }


    fun postOpenAlbumInChatHistoryClick(isGroup: Boolean, hasAlbumPermission: Boolean){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032502")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "chat_send_media")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (isGroup) "group" else "private"
            )
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (hasAlbumPermission) "yes" else "no"
            )
        }
    }

    fun onAlbumDialogExpose(isFullScreen: Boolean, source: String) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024032501")
            put(TrackConstant.KEY_TITLE, "照片选择弹窗")
            put(TrackConstant.KEY_PAGE_TYPE, "照片选择页")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (isFullScreen) "full_screen" else "half_screen")
            put(TrackConstant.KEY_SOURCE, source)
        }
    }

    /**
     * 点击Video tab/右滑切换成拍摄视频时上报
     */
    fun onClickSwitchToVideoMode() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032506")
            put(TrackConstant.KEY_TITLE, "拍摄页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "switch_to_video")
        }
    }

    /**
     * 短按（拍照）或长按（拍摄）相机按钮事件
     */
    fun onShortOrLongPressCameraButton(isLongPress: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032507")
            put(TrackConstant.KEY_TITLE, "拍摄页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "shooting_media")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isLongPress) "video" else "image")
        }
    }

    /**
     * 点击转换按钮，或双击屏幕转换摄像头上报事件
     */
    fun onClickSwitchCamera(isFront: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032508")
            put(TrackConstant.KEY_TITLE, "拍摄页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "switching_camera")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isFront) "front" else "back")
        }
    }

    /**
     * 点击相机闪光灯事件，上报开关
     */
    fun onClickSwitchFlashLight(isOn: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032509")
            put(TrackConstant.KEY_TITLE, "拍摄页面")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "flash_light")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isOn) "on" else "off")
        }
    }

    /**
     * 拍摄页面曝光时上报，【source】指定页面来源
     */
    fun onCameraPageExpose(source: String) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024032501")
            put(TrackConstant.KEY_TITLE, "拍摄页面")
            put(TrackConstant.KEY_PAGE_TYPE, "shooting_page")
            put(TrackConstant.KEY_SOURCE, source)
        }
    }



    /**
     * 点击聊天页的某一个媒体时上报
     */
    fun onClickChatMediaPreview(isVideo: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032514")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "view_media")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isVideo) "video" else "image")
        }
    }

    /**
     * 点击历史聊天页的媒体的右上角三点时上报
     */
    fun onClickChatMediaPreviewViewMore() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032515")
            put(TrackConstant.KEY_TITLE, "媒体查看页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "view_more")
        }
    }

    /**
     * 点击下载历史聊天页的媒体时上报
     */
    fun onClickChatMediaPreviewDownload(isVideo: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032516")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "download_image")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isVideo) "video" else "image")
        }
    }

    /**
     * 点击分享历史聊天页的媒体时上报
     */
    fun onClickChatMediaPreviewShare(isVideo: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032517")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "share_image")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isVideo) "video" else "image")
        }
    }

    /**
     * 上报拍摄结果
     */
    fun postShootingMediaResult(type: String, videoLength: Long?, isSuccess: Boolean, isBack: Boolean, failReason: String?) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032503")
            put(TrackConstant.KEY_RESULT_TYPE, "shooting_media_result")
            put(TrackConstant.KEY_PAGE_TYPE, "拍摄页")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, type)
            videoLength?.let { put(TrackConstant.KEY_BUSINESS_NUM, it) }
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isBack) "back" else "front")
            put(TrackConstant.KEY_FAIL_REASON, failReason.toString())
        }
    }

    /**
     * 上报视频压缩结果
     */
    fun postCompressMediaResult(videoLength: Long, compressDuration: Long, isSuccess: Boolean, failReason: String?) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032504")
            put(TrackConstant.KEY_RESULT_TYPE, "compress_media_result")
            put(TrackConstant.KEY_PAGE_TYPE, "聊天页")
            put(TrackConstant.KEY_CONTENT_ID, videoLength.toString())
            put(TrackConstant.KEY_BUSINESS_NUM, compressDuration)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_FAIL_REASON, failReason.toString())
        }
    }

    /**
     * 长按消息显示菜单
     */
    fun onLongPressViewExposed(targetId: Long, convType: IM5ConversationType, msgType: ChatMsgType) {
        GlobalScope.launch(Dispatchers.IO) {
            BuzTracker.onElementExposure {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024062501")
                put(TrackConstant.KEY_TITLE, "chat")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "long_press_message_expose")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
                val elementBusinessType = when (msgType) {
                    ChatMsgType.Text -> "long_press_word_message"
                    ChatMsgType.Image -> "long_press_photo_message"
                    ChatMsgType.Video -> "long_press_video_message"
                    ChatMsgType.LOCATION -> "long_press_location_message"
                    ChatMsgType.WT,
                    ChatMsgType.Voice,
                    ChatMsgType.VoiceText -> "long_press_voice_message"
                    ChatMsgType.VoiceEmoji -> "long_press_voicemoji_message"
                    else -> "unknown"
                }
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
            }
        }
    }

    private fun getPageBusinessType(targetId: Long, convType: IM5ConversationType): String {
        return when (convType) {
            IM5ConversationType.PRIVATE -> if (targetId.isRobot()) "robot" else "private"
            else -> "group"
        }
    }

    /**
     * 长按消息显示菜单
     */
    fun onForwardTargetPage(targetListSize: Int) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024062501")
            put(TrackConstant.KEY_TITLE, "forward_target_page")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_CONTENT, targetListSize.toString())

        }
    }


    /**
     * 长按消息菜单：点击转发/复制
     */
    fun onClickAC2024062501(targetId: Long, convType: IM5ConversationType, msgType: ChatMsgType, businessContent: String) {
        GlobalScope.launch(Dispatchers.IO) {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062501")
                put(TrackConstant.KEY_TITLE, "chat")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "long_press_message")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, businessContent)
                val elementBusinessType = when (msgType) {
                    ChatMsgType.Text -> "word_message"
                    ChatMsgType.Image -> "photo_message"
                    ChatMsgType.Video -> "video_message"
                    ChatMsgType.LOCATION -> "location_message"
                    ChatMsgType.WT,
                    ChatMsgType.Voice,
                    ChatMsgType.VoiceText -> "voice_message"
                    ChatMsgType.VoiceEmoji -> "voicemoji_message"
                    ChatMsgType.VoiceGif -> "VG_message"
                    ChatMsgType.File -> "File_message"
                    else -> "unknown"
                }
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
            }
        }
    }

    /**
     * 长按消息菜单：点击删除
     */
    fun onClickAC2024062502(targetId: Long, convType: IM5ConversationType, msgType: ChatMsgType) {
        GlobalScope.launch(Dispatchers.IO) {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062502")
                put(TrackConstant.KEY_TITLE, "chat")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "long_press_delete_message")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
                val elementBusinessType = when (msgType) {
                    ChatMsgType.Text -> "delete_word_message"
                    ChatMsgType.Image -> "delete_photo_message"
                    ChatMsgType.Video -> "delete_video_message"
                    ChatMsgType.LOCATION -> "delete_location_message"
                    ChatMsgType.WT,
                    ChatMsgType.Voice,
                    ChatMsgType.VoiceText -> "delete_voice_message"
                    ChatMsgType.VoiceEmoji -> "delete_voicemoji_message"
                    ChatMsgType.VoiceGif -> "delete_VG_message"
                    ChatMsgType.File -> "delete_File_message"
                    else -> "unknown"
                }
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
            }
        }
    }

    /**
     * 转发列表页，点击顶部按钮发送
     */
    fun onClickAC2024062503(targetId: String?, convType: IM5ConversationType, targetListSize: Int) {
        GlobalScope.launch(Dispatchers.IO) {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062503")
                put(TrackConstant.KEY_TITLE, "chat")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "forward_send_button")
                if (targetId.isNullOrEmpty()) { // 为了双端统一（IOS从聊天预览页转发时，这两个字段传空）
                    put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, "")
                    put(TrackConstant.KEY_PAGE_BUSINESS_ID, "")
                } else {
                    put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId.toSafeLong(), convType))
                    put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
                }

                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, targetListSize.toString())            }
        }
    }

    /**
     * 长按消息菜单：点击delete for me/ delete for everyone
     */
    fun onClickAC2024062504(targetId: String, convType: IM5ConversationType, businessContent: String) {
        GlobalScope.launch(Dispatchers.IO) {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062504")
                put(TrackConstant.KEY_TITLE, "chat")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "delete_for_selection")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId.toSafeLong(), convType))
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, businessContent)
            }
        }
    }

    /**
     * 删除消息结果打点
     */
    fun onResultRB2024062502(targetId: String, convType: IM5ConversationType, msgType: Int, traceId: String, contentName:String, isSuccess: Boolean, resultCode: String? = null) {
        GlobalScope.launch(Dispatchers.IO) {
            BuzTracker.onResult {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024062502")
                put(TrackConstant.KEY_RESULT_TYPE, "delete_message_result")
                put(TrackConstant.KEY_PAGE_TYPE, "chat")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId.toSafeLong(), convType))
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)

                val elementBusinessType = when (msgType) {
                    IMType.TYPE_TEXT_MSG -> "delete_word_message_result"
                    IMType.TYPE_IMAGE_MSG -> "delete_photo_message_result"
                    IMType.TYPE_VIDEO_MSG -> "delete_video_message_result"
                    IMType.TYPE_LOCATION_MSG -> "delete_location_message_result"
                    IMType.TYPE_VOICE_EMOJI -> "delete_voicemoji_message_result"
                    IMType.TYPE_VOICE_MSG,
                    IMType.TYPE_VOICE_TEXT,
                    IMType.TYPE_VOICE_TEXT_NEW,
                    IMType.TYPE_WALKIE_TALKIE_VOICE -> "delete_voice_message_result"
                    else -> "unknown"
                }
                put(TrackConstant.KEY_BUSINESS_TYPE, elementBusinessType)
                put(TrackConstant.KEY_CONTENT_ID, traceId)
                put(TrackConstant.KEY_CONTENT_NAME, contentName)
                if (isSuccess) {
                    put(TrackConstant.KEY_IS_SUCCESS, "success")
                } else {
                    put(TrackConstant.KEY_IS_SUCCESS, "fail")
                    resultCode?.let {
                        put(TrackConstant.KEY_FAIL_REASON, resultCode)
                    }
                }
            }
        }
    }

    /**
     * 聊天预览页，点击转发打点
     */
    fun onClickAC2024062511() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062511")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "long_press_media")
        }
    }

    /**
     * Report the event when a hyperlink message is sent
     */
    fun postHyperlinkMsgOnSendResult(
        convType: IM5ConversationType,
        source: String,
        targetId: String,
        traceId: String?,
        url: String?,
        isSuccess: Boolean,
        failReason: String? = null
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024072602")
            put(TrackConstant.KEY_RESULT_TYPE, "resolving_link_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId.toLong(), convType))
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_SOURCE, source)
            traceId?.let {
                put(TrackConstant.KEY_CONTENT_ID, it)
            }
            url?.let {
                put(TrackConstant.KEY_BUSINESS_TYPE, url)
            }
            if (isSuccess) {
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            } else {
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
                failReason?.let {
                    put(TrackConstant.KEY_FAIL_REASON, failReason)
                }
            }
        }
    }

    /**
     * When user click the try it button in buz official account chat history
     */
    fun onClickTryItOfficialAccount(traceId: String?, businessNum: Int?) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024072604")
            put(TrackConstant.KEY_TITLE, "official_account_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "try_it_button")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            businessNum?.let {
                put(TrackConstant.KEY_BUSINESS_NUM, businessNum)
            }
            traceId?.let {
                put(TrackConstant.KEY_CONTENT_NAME, it)
            }
        }
    }

    /**
     * Report the duration of staying in the official account page when the page is closed
     */
    fun onChatOfficialAccountPageView(id: String, blocked: Boolean, serMsgId: String? = null, startTime: Long = 0L, endTime: Long = 0L) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024072601")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, "official_account")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, id)
            put(TrackConstant.KEY_PAGE_STATUS, if (blocked) "being_blocked" else "not_being_blocked")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, serMsgId ?: "")
            val duration = endTime - startTime
            put(TrackConstant.KEY_BUSINESS_NUM, duration)
            put(TrackConstant.KEY_LOG_TIME, System.currentTimeMillis().toString())
        }
    }

    /**
     * When user click the hyperlink button in the official account chat history page
     */
    fun onClickHyperlinkOfficialAccount(traceId: String?, url: String, businessType: Int?) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024072605")
            put(TrackConstant.KEY_TITLE, "official_account_chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "hyperlink_button")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, url)
            businessType?.let {
                put(TrackConstant.KEY_BUSINESS_NUM, it)
            }
            traceId?.let {
                put(TrackConstant.KEY_CONTENT_NAME, it)
            }
        }
    }

    /**
     * 右滑/长按消息触发引用
     */
    fun onClickACAC2024112901(
        conversationType: IM5ConversationType?,
        businessId: String,
        source: String,
        msgType: ChatMsgType?
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112901")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "reply_message")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            val businessType = if (conversationType == IM5ConversationType.GROUP) {
                "group"
            } else {
                "private"
            }
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, businessId)
            put(TrackConstant.KEY_SOURCE, source)
            val elementBusinessType = when (msgType) {
                ChatMsgType.Text -> "reply_word_message"
                ChatMsgType.Image -> "reply_photo_message"
                ChatMsgType.Video -> "reply_video_message"
                ChatMsgType.LOCATION -> "reply_location_message"
                ChatMsgType.WT,
                ChatMsgType.Voice,
                ChatMsgType.VoiceText -> "reply_voice_message"
                ChatMsgType.VoiceEmoji -> "reply_voicemoji_message"
                ChatMsgType.VoiceGif -> "reply_VG_message"
                else -> "unknown"
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
        }
    }

    /**
     * 点击一键到底
     */
    fun onClickAC2024112902(
        conversationType: IM5ConversationType?,
        businessId: String,
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112902")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "jump_ahead_latest_message")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            val businessType = if (conversationType == IM5ConversationType.GROUP) {
                "group"
            } else {
                "private"
            }
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, businessId)
        }
    }

    /**
     * 历史聊天页一键到底
     */
    fun onExposedEE2024112901(
        conversationType: IM5ConversationType?,
        businessId: String,
    ) {
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024112901")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "jump_ahead_latest_message_exposure")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            val businessType = if (conversationType == IM5ConversationType.GROUP) {
                "group"
            } else {
                "private"
            }
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, businessId)
        }
    }

    fun onPageViewAVS2024121301(afLinkHash: String?, totalCount: Int) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024121301")
            put(TrackConstant.KEY_TITLE, "homepage")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            afLinkHash?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, afLinkHash)
            }
            put(TrackConstant.KEY_BUSINESS_NUM, totalCount)
        }
    }


    fun onVFGuidanceDialogExpose() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2025010901")
            put(TrackConstant.KEY_TITLE, "voice_filter_intro")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    fun onVFGuidanceDialogClick(clickArea:String){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010901")
            put(TrackConstant.KEY_TITLE, "voice_filter_intro")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_intro_action")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, clickArea)
        }
    }

    fun onGifSearchResult(isSuccess: Boolean, searchContent: String?, errorCode: Int?) {
        if (searchContent.isNullOrEmpty().not()) {
            BuzTracker.onResult {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025011701")
                put(TrackConstant.KEY_RESULT_TYPE, "VG_search_page_result")
                put(TrackConstant.KEY_PAGE_TYPE, "VG_search")
                put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, searchContent!!)
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,"search")
                if (null != errorCode && isSuccess.not()) {
                    put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                }
            }
        }
    }

    /**
     * When user click the button of the Ramadan's message
     */
    fun onClickButtonInRamadanMessage(isGroup: Boolean, convId: String, action: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025021302")
            put(TrackConstant.KEY_TITLE, "chat_history_iman_buddy")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "chat_history_iman_buddy_action")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, action)
        }
    }

    /**
     * Type @ in input text and shows the group mention panel
     */
    fun onInsertAtCharacterToShowMentionPanel(groupId: String, numOfGroupMember: Int) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025031401")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "group_chat_mention")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
            put(TrackConstant.KEY_BUSINESS_NUM, numOfGroupMember)
        }
    }

    /**
     * Click item in the mention panel to mention the user
     */
    fun onClickItemInPanelToMention(isRobot: Boolean, selectedUserId: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025031402")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "group_chat_mention_avatar")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isRobot) "robot" else "group_member")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, selectedUserId)
        }
    }

    /**
     * Long clicking portrait in chat list to mention the user
     */
    fun onLongClickPortraitToMention(isRobot: Boolean, selectedUserId: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025031403")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "group_chat_mention_avatar_longpress")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isRobot) "robot" else "group_member")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, selectedUserId)
        }
    }

    fun onVS2025041601() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2025041601")
            put(TrackConstant.KEY_TITLE, "group_live_place_create_confirmation")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
        }
    }

    fun onAC2025041601(isPrivate: Boolean, targetId: Long, isLive: Boolean, isCreated: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041601")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_entrance")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
            put(TrackConstant.KEY_PAGE_STATUS, if (isLive) "onlive" else "not_onlive")
            put(TrackConstant.KEY_CONTENT_NAME, if (isCreated) "created" else "not_created")
        }
    }

    fun onAC2025041602(isCancel: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041602")
            put(TrackConstant.KEY_TITLE, "group_live_place_create_confirmation")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "group_live_place_create_confirmation_action")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (isCancel) "cancel" else "start")
        }
    }

    fun onAC2025041603(
        isPrivate: Boolean,
        targetId: Long,
        isLive: Boolean,
        isCreated: Boolean
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041603")
            put(TrackConstant.KEY_TITLE, "chat_profile_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "profile_page_live_place_entrance")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
            put(TrackConstant.KEY_PAGE_STATUS, if (isLive) "onlive" else "not_onlive")
            put(TrackConstant.KEY_CONTENT_NAME, if (isCreated) "created" else "not_created")
        }
    }

    /**
     * action const of:
     * share
     * add
     * rename
     * retry
     */
    fun clickAC2025041704(action:String, groupId: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041704")
            put(TrackConstant.KEY_TITLE, "group_chat_cold_start")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "group_chat_cold_start_action")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, action)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
        }
    }

    fun onClickAC2025051502(
        convType: IM5ConversationType,
        targetId: String,
        expand: Boolean,
        hasVoiceFilter: Boolean,
        msgType: ChatMsgType
    ) {
        val isPrivate = convType == IM5ConversationType.PRIVATE
        val elementBusinessType = when (msgType) {
            ChatMsgType.Text -> "word_message"
            ChatMsgType.Image -> "photo_message"
            ChatMsgType.Video -> "video_message"
            ChatMsgType.LOCATION -> "location_message"
            ChatMsgType.WT,
            ChatMsgType.Voice,
            ChatMsgType.VoiceText -> "voice_message"
            ChatMsgType.VoiceEmoji -> "voicemoji_message"
            ChatMsgType.VoiceGif -> "VG_message"
            else -> "other_message"
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025051502")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "long_press_msg_more")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (expand) "expand" else "collapse")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, if(hasVoiceFilter) "voice_filter" else "not_voice_filter")
        }
    }

    /**
     * Click more or voice button in chat history page
     */
    fun onClickMoreOrVoiceButton(
        isPrivate: Boolean,
        targetId: String,
        type: BottomPanelContentType,
        isInVoiceFilter: Boolean?
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025051501")
            put(TrackConstant.KEY_TITLE, "chat_history")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "chat_history_button")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (type == MORE_PANEL) "plus" else if (type == VOICE_RECORD_PANEL) "microphone" else "")
            isInVoiceFilter?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, if (isInVoiceFilter) "in_voice_filter" else "not_in_voice_filter")
            }
        }
    }

    /**
     * Click to open system file picker
     */
    fun onClickFileOption(
        isPrivate: Boolean,
        targetId: String,
        sendFrom: FileSendFrom
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025061601")
            put(TrackConstant.KEY_TITLE, "homepage")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "send_file_entrance")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(
                TrackConstant.KEY_SOURCE,
                if (sendFrom == FileSendFrom.HOME) "homepage" else if (sendFrom == FileSendFrom.CHAT) "chat_history" else ""
            )
        }
    }

    /**
     * Click file item view to view file
     */
    fun onClickToOpenFile(
        isPrivate: Boolean,
        targetId: String,
        clickFromHomePage: Boolean,
        extension: String,
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025061602")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "check_file_message")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_SOURCE, if (clickFromHomePage) "homepage" else "chat_history")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, extension)
        }
    }

    /**
     * when user click export voice
     */
    fun onClickExportVoice(
        isPrivate: Boolean,
        targetId: String,
        voiceFilterId: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025061902")
            put(TrackConstant.KEY_TITLE, "voice_filter")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_export")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, voiceFilterId)
        }
    }

    /**
     * when user click 'X' / 'retry' in export voice progress
     */
    fun onClickExportAction(
        isPrivate: Boolean,
        targetId: String,
        voiceFilterId: String,
        exportAction: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025061903")
            put(TrackConstant.KEY_TITLE, "voice_filter")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_export_action")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, voiceFilterId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, exportAction)
        }
    }


}