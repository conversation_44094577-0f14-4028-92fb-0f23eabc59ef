package com.interfun.buz.common.anim

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.transition.TransitionValues
import android.transition.Visibility
import android.view.View
import android.view.ViewGroup
import androidx.core.animation.doOnEnd

class VisibilitySlideTransition(
    private val startTransitionX: Float = 0f,
    private val endTransitionX: Float = 0f,
    private val startTransitionY: Float = 0f,
    private val endTransitionY: Float = 0f,
) : Visibility() {

    override fun onAppear(
        sceneRoot: ViewGroup?,
        view: View,
        startValues: TransitionValues?,
        endValues: TransitionValues?,
    ): Animator {
        val animatorSet = AnimatorSet()
        val scaleXAnim =
            ObjectAnimator.ofFloat(view, "translationX", endTransitionX, startTransitionX)
        val scaleYAnim =
            ObjectAnimator.ofFloat(view, "translationY", endTransitionY, startTransitionY)
        animatorSet.playTogether(scaleXAnim, scaleYAnim)
        animatorSet.doOnEnd {
            view.translationX = startTransitionX
            view.translationY = startTransitionY
        }
        return animatorSet
    }

    override fun onDisappear(
        sceneRoot: ViewGroup,
        view: View,
        startValues: TransitionValues?,
        endValues: TransitionValues?,
    ): Animator {
        val animatorSet = AnimatorSet()
        val scaleXAnim =
            ObjectAnimator.ofFloat(view, "translationX", startTransitionX, endTransitionX)
        val scaleYAnim =
            ObjectAnimator.ofFloat(view, "translationY", startTransitionY, endTransitionY)
        animatorSet.playTogether(scaleXAnim, scaleYAnim)
        animatorSet.doOnEnd {
            view.translationX = endTransitionX
            view.translationY = endTransitionY
        }
        return animatorSet
    }
}