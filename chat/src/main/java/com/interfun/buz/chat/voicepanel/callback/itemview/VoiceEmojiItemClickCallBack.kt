package com.interfun.buz.chat.voicepanel.callback.itemview

import coil.size.Size
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.chat.voicepanel.view.itemview.holder.ItemViewType

interface VoiceEmojiItemClickCallBack {
    fun onVoiceEmojiClick(
        voiceEmojiEntity: VoiceEmojiEntity,
        location: IntArray,
        size: Size,
        textSize: Float
    )

    fun onVoiceEmojiLongClick(
        voiceEmojiEntity: VoiceEmojiEntity,
        collectId: Long? = null,
        location: IntArray,
        size: Size,
        voiceEmojiSize: Size,
        textSize: Float,
        viewType: ItemViewType
    )

    fun getCurrentSelectedId(): Long? = null

    fun onVoiceEmojiCollectionRemoveClick(
        collectId: Long,
        objectId: String,
        collectionVeType: CollectionType.VoiceEmoji
    ) {

    }
}