package com.interfun.buz.chat.common.view.activity

import android.Manifest
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.BitmapFactory
import android.graphics.drawable.ShapeDrawable
import android.media.ExifInterface
import android.media.ExifInterface.ORIENTATION_TRANSVERSE
import android.os.Bundle
import android.util.Log
import android.util.Range
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import android.view.OrientationEventListener
import android.view.ScaleGestureDetector
import android.view.Surface
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.OptIn
import androidx.camera.core.AspectRatio
import androidx.camera.core.Camera
import androidx.camera.core.CameraControl
import androidx.camera.core.CameraSelector
import androidx.camera.core.DisplayOrientedMeteringPointFactory
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.FocusMeteringAction.FLAG_AF
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCapture.Metadata
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.MeteringPoint
import androidx.camera.core.MirrorMode.MIRROR_MODE_ON_FRONT_ONLY
import androidx.camera.core.Preview
import androidx.camera.core.impl.MutableOptionsBundle
import androidx.camera.core.impl.OptionsBundle
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.ExperimentalPersistentRecording
import androidx.camera.video.FallbackStrategy
import androidx.camera.video.FileOutputOptions
import androidx.camera.video.Quality
import androidx.camera.video.QualitySelector
import androidx.camera.video.Recorder
import androidx.camera.video.VideoCapture
import androidx.camera.video.VideoRecordEvent
import androidx.camera.video.impl.VideoCaptureConfig
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.content.ContextCompat
import androidx.core.content.PermissionChecker
import androidx.core.view.postDelayed
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.album.bean.MediaItem
import com.interfun.buz.album.event.CloseAlbumEvent
import com.interfun.buz.album.event.PreviewSendMediaSuccessEvent
import com.interfun.buz.album.ui.activity.MediaPreviewActivity
import com.interfun.buz.album.ui.fragment.BaseAlbumDialogFragment
import com.interfun.buz.album.utils.AlbumTracker
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.bind
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.collectIn
import com.interfun.buz.base.ktx.context
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.getImageAbsolutePath
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.inVisibleIf
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.base.ktx.invisible
import com.interfun.buz.base.ktx.isAlarmRinging
import com.interfun.buz.base.ktx.isMemoryFullForRecording
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.isReceivedCall
import com.interfun.buz.base.ktx.isScreenOff
import com.interfun.buz.base.ktx.isStatusBarVisible
import com.interfun.buz.base.ktx.launchMain
import com.interfun.buz.base.ktx.layoutMargin
import com.interfun.buz.base.ktx.layoutMarginBottom
import com.interfun.buz.base.ktx.lifecycleOwner
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.screenHeightReal
import com.interfun.buz.base.ktx.screenWidth
import com.interfun.buz.base.ktx.statusBarHeight
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.base.ktx.withIOContext
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.base.utils.ScreenUtil
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.base.widget.round.shape.RoundedCornerMaskShape
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.manager.VideoRecordStateManager
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.common.utils.TakePhotoTrackUtils
import com.interfun.buz.chat.common.view.block.TakePhotoMediaFocusBlock
import com.interfun.buz.chat.databinding.ChatActivityTakePhotoBinding
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.PATH_CHAT_ACTIVITY_TAKE_PHOTO_SEND
import com.interfun.buz.common.constants.PhotoSelectSource
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.album.UpdateAlbumListDataEvent
import com.interfun.buz.common.manager.UserManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.chat.CallPendStatus.ANSWER
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.service.HomeService
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.media.CameraButton
import com.interfun.buz.common.widget.media.CameraMode
import com.interfun.buz.media.bean.MediaType
import com.interfun.buz.media.video.compressor.VideoParameters
import com.lizhi.component.basetool.common.AppStateWatcher
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.lzlogan.Logz
import dagger.hilt.android.AndroidEntryPoint
import java.io.File
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlin.math.abs


/**
 * 拍照并且发送图片消息Activity
 * <note>
 *     1.根据产品确认，文件保存以时间戳命名
 * <note/>
 */
@SuppressLint("InvalidWakeLockTag")
@Route(path = PATH_CHAT_ACTIVITY_TAKE_PHOTO_SEND)
@AndroidEntryPoint
class TakePhotoSendActivity : BaseBindingActivity<ChatActivityTakePhotoBinding>() {

    companion object{
        const val TAG = "TakePhotoActivity"
        const val VIDEO_RECORD_MAX_TIME = (59.5 * 1000).toInt()
        const val EXTRA_TARGET_ID = "extra_target_id"
        const val EXTRA_CONV_TYPE = "extra_conv_type"
        const val EXTRA_SOURCE = "extra_source"
        const val EXTRA_PORTRAIT = "extra_portrait"
        const val EXTRA_TITLE = "extra_title"
        const val EXTRA_REFERENCE_MSG_ID = "EXTRA_REFERNCE_MSG_ID"
    }

    private lateinit var cameraExecutor: ExecutorService
    private var imageCapture: ImageCapture? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    private var targetId: String? = null
    private var referenceId: Long? = null
    private var convType: Int = IM5ConversationType.NONE.value
    private var source: String = ""
    private var portrait: List<String?> = emptyList()
    private var title: String = ""
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var cameraControl: CameraControl? = null
    private var flashOn: Boolean = false
    private var userLeaveByBackPress = false
    private var hasTouchUpStopRecordVideo = false // 是否已经松手停止录制
    private var gotoPreviewTask:(()->Unit)? = null

    class CameraLifecycle : LifecycleOwner {
        val lifecycleRegistry = LifecycleRegistry.createUnsafe(this)
        override val lifecycle: Lifecycle
            get() = lifecycleRegistry
    }

    val cameraLifecycle = CameraLifecycle()
    private var startSendPic = false
    private var startPickPic = false
    private var takePhotoState: TakePhotoState = TakePhotoState.IDLE

//    private var photoPicker =
//        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
//            uri?.let { handleChooseImage(it) }
//        }
//
//    private var pickPictureLauncher = pickContentLauncher { uri ->
//        uri?.let { handleChooseImage(it) }
//    }

    private var startForResult: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        logInfo(TAG,"get start activity result: ${result.resultCode}")
        setResult(result.resultCode)
        if (result.resultCode == Activity.RESULT_OK) {
            if (!isFinishing) {
                finish()
            }
        }
    }

    private val orientationEventListener by lazy {
        object : OrientationEventListener(this) {
            override fun onOrientationChanged(orientation: Int) {
                if (orientation == ORIENTATION_UNKNOWN) {
                    return
                }
                val rotation = when (orientation) {
                    in 45..134 -> Surface.ROTATION_270
                    in 135..224 -> Surface.ROTATION_180
                    in 225..314 -> Surface.ROTATION_90
                    else -> Surface.ROTATION_0
                }
                imageCapture?.targetRotation = rotation
                videoCapture?.targetRotation = rotation
            }

        }
    }

    private val gestureListener = object: SimpleOnGestureListener() {
        var handleCameraMode = true

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            val x = e.x
            val y = e.y
            focus(x, y, false)
            return true
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            if (canSwitchCamera()) {
                switchCamera()
            } else {
                logInfo(TAG, "onDoubleTap, the takePhotoState is ${takePhotoState}")
            }
            return true
        }

        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            log(TAG, "onFling: velocityX=$velocityX")
            if (handleCameraMode && !isRecordingVideo() && abs(velocityX) > binding.pvPhoto.width / 3) {
                binding.tlCameraMode.selectTab(if (velocityX > 0) CameraMode.VIDEO else CameraMode.PHOTO)
            }
            handleCameraMode = true
            return true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        overridePendingTransition(R.anim.anim_take_photo_enter, 0)
        TakePhotoMediaFocusBlock().bind(this)

        isStatusBarVisible = false

        /*photoPicker = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
            uri?.let { handleChooseImage(it) }
        }
        pickPictureLauncher = pickContentLauncher { uri ->
            uri?.let { handleChooseImage(it) }
        }
        startForResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                finish()
            }
        }*/
        lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onCreate(owner: LifecycleOwner) {
                cameraLifecycle.lifecycleRegistry.currentState = Lifecycle.State.CREATED
            }

            override fun onStart(owner: LifecycleOwner) {
                if (startPickPic || startSendPic) {
                    return
                }
                log(TAG, "cameraLifecycle onStart")
                cameraLifecycle.lifecycleRegistry.currentState = Lifecycle.State.STARTED
                ChatTracker.onCameraPageExpose(source)
            }

            override fun onResume(owner: LifecycleOwner) {
                if (startPickPic || startSendPic) {
                    return
                }
                log(
                    TAG,
                    "cameraLifecycle onResume,startPickPic$startPickPic,startSendPic:$startSendPic"
                )
                cameraLifecycle.lifecycleRegistry.currentState = Lifecycle.State.RESUMED
            }

            override fun onPause(owner: LifecycleOwner) {
                cameraLifecycle.lifecycleRegistry.currentState = Lifecycle.State.STARTED
            }

            override fun onStop(owner: LifecycleOwner) {
                cameraLifecycle.lifecycleRegistry.currentState = Lifecycle.State.CREATED
            }

            override fun onDestroy(owner: LifecycleOwner) {
                cameraLifecycle.lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
            }
        })

        AppStateWatcher.addBackgroundWatcher {
            if (VideoRecordStateManager.isRecording) {
                binding.roundTakePhotoOutSide.stopRecordingVideo()
            }
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                userLeaveByBackPress = true
                if (isRecordingVideo()){
                    binding.roundTakePhotoOutSide.stopRecordingVideo()
                }
                finish()
                overridePendingTransition(0, R.anim.anim_take_photo_exit)
            }
        })

        BusUtil.observe<PreviewSendMediaSuccessEvent>(lifecycleOwner) {
            setResult(Activity.RESULT_OK)
            finishActivitySafe() // 在ActivityResult处理会会有显示中间页问题
        }

        BusUtil.observe<CloseAlbumEvent>(lifecycleOwner){
            finishActivitySafe()
        }


        ChannelPendStatusManager.isPendingFlow.collectIn(this){
            if (it.second != ANSWER) return@collectIn
            if (isRecordingVideo()){
                binding.roundTakePhotoOutSide.stopRecordingVideo()
            }
        }

        UserManager.isUserLogin.collectIn(this){
            if (it) return@collectIn
            if (isRecordingVideo()){
                VideoRecordStateManager.stopRecording()
            }
        }

        VideoRecordStateManager.isRecording_.collectIn(this){
            logInfo(TAG,"onCreate: onRecordingStateChange: $it ")
            binding.root.keepScreenOn = it
        }
    }

    private fun finishActivitySafe() {
        if (isRecordingVideo()){
            binding.roundTakePhotoOutSide.stopRecordingVideo()
        }
        if (!isFinishing) {
            finish()
        }
    }

    private fun correctCameraLifecycle() {
        if (cameraLifecycle.lifecycle.currentState != lifecycle.currentState) {
            cameraLifecycle.lifecycleRegistry.currentState = lifecycle.currentState
        }
    }

    override fun initData() {
        cameraExecutor = Executors.newSingleThreadExecutor()
        targetId = intent.getStringExtra(EXTRA_TARGET_ID)
        val tmpReferenceId = intent.getLongExtra(EXTRA_REFERENCE_MSG_ID,-1)
        if (tmpReferenceId>0) {
            referenceId= tmpReferenceId
        }
        convType = intent.getIntExtra(EXTRA_CONV_TYPE, IM5ConversationType.NONE.value)
        source = intent.getStringExtra(EXTRA_SOURCE) ?: ""
        portrait = intent.getStringArrayListExtra(EXTRA_PORTRAIT) ?: emptyList()
        title = intent.getStringExtra(EXTRA_TITLE) ?: ""
        log(TAG, "initData: convType = $convType")
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()
        layoutPreview()
        startCamera()
        updateFlashState()
        binding.tvTakePhotoDesc.postDelayed(3000) {
            fadeTextAnim()
        }
        binding.roundTakePhotoOutSide.updateState(CameraMode.PHOTO)
        //set preview rounded corner mask
        binding.vPhotoMask.background = ShapeDrawable(
            RoundedCornerMaskShape(
                20.dp,
                resources.getColor(com.interfun.buz.common.R.color.black)
            )
        )

        binding.tlCameraMode.setOnTabSelectedListener {
            if (it == CameraMode.VIDEO) {
                ChatTracker.onClickSwitchToVideoMode()
            }
            updateCameraMode(it)
        }

        binding.iftvFlashLight.setOnClickListener {
            if (CommonMMKV.lastUserUseBackCamera) {
                changeFlashMode()
                ChatTracker.onClickSwitchFlashLight(flashOn)
            }
        }

        binding.iftvClose.click {
            finishActivitySafe()
            overridePendingTransition(0, R.anim.anim_take_photo_exit)

        }
        binding.roundAlbumEntry.click {
            startPickPic = true
            targetId?.let {
                routerServices<HomeService>().value?.openHomeAlbumDialog(
                    fragmentManager = supportFragmentManager, targetId = it,
                    convType = convType,
                    source = PhotoSelectSource.Camera.value
                ) {
                    if (this is BaseAlbumDialogFragment) {
                        setOnSendMediaCallback { finish() }
                        ChatTracker.onAlbumDialogExpose(true, "shooting")
                    }
                }
            }
            ChatTracker.onClickSendFromAlbum()
            AlbumTracker.onMediaPreviewPageExpose(type = "0", source = "1")
        }
//        binding.roundTakePhotoOutSide.click {
//            log(TAG,"initView: take photo")
//            takePhoto()
//        }
        binding.iftvSwitchCamera.click {
            //禁止在录制过程切换摄像机
            if (canSwitchCamera() ) {
                switchCamera()
            } else {
                logInfo(TAG, "the takePhotoState is ${takePhotoState}")
            }
        }

        val gestureDetector = GestureDetector(context, gestureListener)

        val scaleGestureDetector = ScaleGestureDetector(
            context,
            object : ScaleGestureDetector.SimpleOnScaleGestureListener() {

                override fun onScale(detector: ScaleGestureDetector): Boolean {
                    log(
                        TAG,
                        "onScale: curSpan=${detector.currentSpan}, preSpan=${detector.previousSpan}"
                    )
                    zoom((detector.currentSpan - detector.previousSpan) / binding.pvPhoto.width)
                    return true
                }
            })
        binding.pvPhoto.setOnTouchListener { _, event ->
            if (event.pointerCount > 1){
                gestureListener.handleCameraMode = false
            }
            gestureDetector.onTouchEvent(event)
            scaleGestureDetector.onTouchEvent(event)
            return@setOnTouchListener true
        }

        binding.roundTakePhotoOutSide.setOnCameraButtonCallback(object :
            CameraButton.OnCameraButtonCallback {
            override fun takePhoto() {
                log(TAG, "OnCameraButtonCallback.takePhoto")
                <EMAIL>()
            }

            override fun startRecordingVideo(longPress: Boolean) {
                log(TAG, "OnCameraButtonCallback.startRecordingVideo")
                hasTouchUpStopRecordVideo = false
                <EMAIL>(longPress)
            }

            override fun stopRecordingVideo() {
                log(TAG, "OnCameraButtonCallback.stopRecordingVideo")
                hasTouchUpStopRecordVideo = true
                VideoRecordStateManager.stopRecording()
            }

            override fun longPressedAndScroll(scrolledY: Float, scrollTotalHeight: Int) {
                log(TAG, "OnCameraButtonCallback.longPressedAndScroll")
                // 2是产品要求的效果
                zoom((scrolledY / scrollTotalHeight)*2)
            }

            override fun focusOnLongPressRecording(x: Float, y: Float) {
                log(TAG, "OnCameraButtonCallback.focusOnLongPressRecording")
                focus(x, y, false)
            }
        })

    }

    private fun updateFlashState(){
        binding.iftvFlashLight.setTextColor(R.color.text_white_important.asColor())
        binding.iftvFlashLight.visibleIf(
            binding.tlCameraMode.cameraMode
                == CameraMode.PHOTO && CommonMMKV.lastUserUseBackCamera
        )
        val flashText =
            if (flashOn) R.string.ic_camera_flash
            else R.string.ic_camera_flash_off

        if (flashOn)
            binding.iftvFlashLight.setTextColor(R.color.basic_primary.asColor())
        binding.iftvFlashLight.setText(flashText)
    }

    private fun getIM5ConversationType():IM5ConversationType{
        return when (convType){
            IM5ConversationType.PRIVATE.value -> IM5ConversationType.PRIVATE
            IM5ConversationType.GROUP.value -> IM5ConversationType.GROUP
            else -> IM5ConversationType.PRIVATE
        }
    }

    /**
     * 根据16:9的屏幕高宽比,适配不同屏幕下的拍照预览的显示区域
     */
    private fun layoutPreview() {
        val statusBarHeight = statusBarHeight
        val screenWidth = screenWidth
        val screenHeight = screenHeightReal - ScreenUtil.getNavigationHeight()
        val previewHeight = screenWidth * 16 / 9
        if (screenHeight - previewHeight <= statusBarHeight) {
            //极小屏，pvPhoto需要全屏展示
            logDebug(TAG, "Very small screen, preview View needs to be displayed in full screen")
            binding.pvPhoto.layoutMargin(0, -statusBarHeight, 0, 0)
        } else if (screenHeight - previewHeight < statusBarHeight + 60.dp) {
            //previewHeight 高度不够，需要按照屏幕居中显示previewHeight的高度，
            logDebug(TAG, "The preview view is not show enough and needs to be displayed in the upper and lower positions according to the center of the screen")
            val vMargin = (screenHeight - previewHeight - statusBarHeight - 60.dp) / 2
            binding.pvPhoto.layoutMargin(0,  vMargin, 0, vMargin + 60.dp)
        } else {
            //previewHeight 高度够
            logDebug(TAG, "The preview view is show enough")
            binding.pvPhoto.layoutMargin(
                0,
                0,
                0,
                screenHeight - previewHeight - statusBarHeight
            )
            binding.tlCameraMode.layoutMarginBottom(screenHeight - previewHeight - statusBarHeight - 60.dp)
        }
    }

    private fun updateCameraMode(mode: CameraMode) {
        updateFlashState()
        binding.roundTakePhotoOutSide.updateState(mode)
    }

    @SuppressLint("RestrictedApi")
    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            cameraProvider = cameraProviderFuture.get()
            bindCameraToLifecycle {
                getDefaultCameraSelector()
            }
        }, ContextCompat.getMainExecutor(this))
    }

    @SuppressLint("RestrictedApi")
    private fun bindCameraToLifecycle(getCameraSelectorBlock: () -> CameraSelector?) {
        val quality = QualitySelector.from(
            Quality.HD,
            FallbackStrategy.higherQualityThan(
                Quality.HD
            )
        )
        val preview = Preview.Builder().setTargetAspectRatio(AspectRatio.RATIO_16_9).build()
        preview.setSurfaceProvider(binding.pvPhoto.surfaceProvider)
        if (imageCapture == null) {
            imageCapture = ImageCapture.Builder()
                .setTargetAspectRatio(AspectRatio.RATIO_16_9)
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .build()
        }

        if (videoCapture == null) {
            val recorder = Recorder.Builder()
                .setQualitySelector(quality)
                .setTargetVideoEncodingBitRate(3000 * 1024)
                .build()
            val optionsBundle = MutableOptionsBundle.create()
            optionsBundle.insertOption(VideoCaptureConfig.OPTION_VIDEO_OUTPUT, recorder)
            val videoCaptureConfig = VideoCaptureConfig<Recorder>(OptionsBundle.from(optionsBundle))
            videoCapture = VideoCapture.Builder.fromConfig(videoCaptureConfig)
                .setTargetFrameRate(Range.create(24, 30))
                .setMirrorMode(MIRROR_MODE_ON_FRONT_ONLY)
                .build()
        }

        val cameraSelector = getCameraSelectorBlock()

        if (cameraSelector.isNull()) {
            logError(TAG, "startCamera: cameraSelector is null")
            toast(R.string.common_save_qrcode_fail_tip.asString())
            return
        }

        try {
            cameraProvider?.unbindAll()
            camera = cameraProvider?.bindToLifecycle(
                cameraLifecycle,
                cameraSelector!!,
                preview,
                imageCapture,
                videoCapture
            )
            cameraControl = camera?.cameraControl
        } catch (exc: Exception) {
            TakePhotoTrackUtils.errReport(exc,"4")
            logError(TAG, "Use case binding failed", exc)
        }
    }

    private fun getDefaultCameraSelector(): CameraSelector? {
        val cameraProvider = ProcessCameraProvider.getInstance(this).get()
        return if (CommonMMKV.lastUserUseBackCamera && cameraProvider.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA)) {
            CameraSelector.DEFAULT_BACK_CAMERA
        } else if (!CommonMMKV.lastUserUseBackCamera && cameraProvider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)) {
            CameraSelector.DEFAULT_FRONT_CAMERA
        } else if (cameraProvider.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA)) {
            CommonMMKV.lastUserUseBackCamera = true
            CameraSelector.DEFAULT_BACK_CAMERA
        } else if (cameraProvider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)) {
            CommonMMKV.lastUserUseBackCamera = false
            CameraSelector.DEFAULT_FRONT_CAMERA
        } else {
            null
        }
    }

    @SuppressLint("RestrictedApi")
    private fun switchCamera() {
        ChatTracker.onClickSwitchCamera(CommonMMKV.lastUserUseBackCamera)
        if (cameraProvider?.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA) == true
            && cameraProvider?.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA) == true
        ) {
            bindCameraToLifecycle {
                val cameraSelector = when {
                    CommonMMKV.lastUserUseBackCamera && cameraProvider?.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA) == true -> {
                        CommonMMKV.lastUserUseBackCamera = false
                        CameraSelector.DEFAULT_FRONT_CAMERA
                    }

                    CommonMMKV.lastUserUseBackCamera && cameraProvider?.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA) == true -> {
                        CameraSelector.DEFAULT_BACK_CAMERA
                    }

                    !CommonMMKV.lastUserUseBackCamera && cameraProvider?.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA) == true -> {
                        CommonMMKV.lastUserUseBackCamera = true
                        CameraSelector.DEFAULT_BACK_CAMERA
                    }

                    !CommonMMKV.lastUserUseBackCamera && cameraProvider?.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA) == true -> {
                        CameraSelector.DEFAULT_FRONT_CAMERA
                    }

                    else -> {
                        CameraSelector.DEFAULT_BACK_CAMERA
                    }
                }
                changeFlashMode(flashOn)
                cameraSelector
            }
        }
    }

    private fun getOutputDirectory(folderName: String = "Buz"): File {
        val mediaDir = externalCacheDir?.let {
            File(it, folderName).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists())
            mediaDir else File(filesDir, folderName).apply { mkdirs() }
    }

    private fun takePhoto() {
        ChatTracker.onShortOrLongPressCameraButton(isLongPress = false)
        val imageCapture = imageCapture ?: return
        VibratorUtil.vibrator()
        if (isMemoryFullForRecording) {
            notEnoughStorageDialog()
            return
        }
        binding.roundTakePhotoOutSide.isEnabled = false
        val photoFile = File(getOutputDirectory(), System.currentTimeMillis().toString() + ".jpg")
        val metaData = Metadata()
        metaData.isReversedHorizontal = !CommonMMKV.lastUserUseBackCamera
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).setMetadata(metaData).build()
        takePhotoState = TakePhotoState.START
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exc: ImageCaptureException) {
                    takePhotoState = TakePhotoState.ERROR
                    runOnUiThread { binding.roundTakePhotoOutSide.isEnabled = true }
                    if (!isDestroyed && !isFinishing) {
                        toast(R.string.common_save_qrcode_fail_tip.asString())
                    }
                    Log.e(TAG, "Photo capture failed: ${exc.message}", exc)
                    TakePhotoTrackUtils.errReport(exc,"1")
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    takePhotoState = TakePhotoState.SUCCESS
                    runOnUiThread { binding.roundTakePhotoOutSide.isEnabled = true }
                    if (output.savedUri.isNull()) {
                        toast(R.string.common_save_qrcode_fail_tip.asString())
                        logError(TAG, "onImageSaved: output.savedUri is null")
                        return
                    }

                    val path = output.savedUri!!.getImageAbsolutePath(application) ?: ""
                    if (userLeaveByBackPress || !UserSessionManager.hasSession()){
                        deleteFileOnUserLeave(path)
                        return
                    }

                    val options = BitmapFactory.Options()
                    options.inJustDecodeBounds = true
                    val bitmap = BitmapFactory.decodeFile(output.savedUri!!.path, options)
                    var width = options.outWidth
                    var height = options.outHeight
                    val imageOrientation = getImageOrientation(path)
                    if (imageOrientation == ExifInterface.ORIENTATION_ROTATE_90
                        || imageOrientation == ExifInterface.ORIENTATION_ROTATE_270
                        || imageOrientation == ORIENTATION_TRANSVERSE){
                        width = options.outHeight
                        height = options.outWidth
                    }

                    goToPreview(mediaUri = path, thumbnailUrl = path, width, height, MediaType.Image)
                }
            }
        )
    }


    fun deleteFileOnUserLeave(path:String?){
        if (path.isNullOrEmpty()) return
        val file = File(path)
        if (file.exists()){
            file.delete()
        }
    }

    /**
     * get the image direction of rotation used to get the right width and height
     */
    private fun getImageOrientation(imageLocalPath: String): Int{
        return try {
            val orientation = ExifInterface(imageLocalPath).getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )
            logInfo(TAG, "getImageOrientation orientation = $orientation")
            orientation
        } catch (e: Exception) {
            logError(TAG,e)
            TakePhotoTrackUtils.errReport(e,"2")
            ExifInterface.ORIENTATION_NORMAL
        }
    }

    @OptIn(ExperimentalPersistentRecording::class)
    @SuppressLint("RestrictedApi")
    private fun startRecordVideo(longPress: Boolean) {
        if (longPress) ChatTracker.onShortOrLongPressCameraButton(true)
        VibratorUtil.vibrator()
        val curRecording = VideoRecordStateManager.recording
        val videoCapture = this.videoCapture ?: return
        // Stop the current recording session.
        if (curRecording != null) {
            curRecording.stop()
            VideoRecordStateManager.recording = null
            return
        }
        updateFlashState()
        binding.roundAlbumEntry.invisible()
        binding.tlCameraMode.invisible()
        binding.iftvClose.invisible()
        binding.roundTakePhotoOutSide.setMaxProgress(VIDEO_RECORD_MAX_TIME)
        // create and start a new recording session
        val videoFile = File(getOutputDirectory(), System.currentTimeMillis().toString() + ".mp4")
        val fileOutputOptions = FileOutputOptions.Builder(videoFile).build()
//        val rotation = windowManager.defaultDisplay.rotation
        log(TAG, "videoCapture.targetRotation=${videoCapture.targetRotation}")
        VideoRecordStateManager.recording = videoCapture.output
            .prepareRecording(this, fileOutputOptions)
            .apply {
                if (PermissionChecker.checkSelfPermission(
                        this@TakePhotoSendActivity,
                        Manifest.permission.RECORD_AUDIO
                    ) == PermissionChecker.PERMISSION_GRANTED
                ) {
                    withAudioEnabled()
                }
            }
            .asPersistentRecording()
            .start(ContextCompat.getMainExecutor(this)) { recordEvent ->
                VideoRecordStateManager.recordingState = recordEvent
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        Logz.tag(TAG).d("Video Record: Start, $hasTouchUpStopRecordVideo")
                        if (!hasTouchUpStopRecordVideo) { // 长按快速松开，start回调
                            // 有可能会滞后导致不能VideoRecordStateManager.stopRecording()
                            VideoRecordStateManager.isRecording_.value = true
                            binding.iftvSwitchCamera.invisible()
                            binding.tvRecordingTime.visible()
                            binding.tvRecordingTime.tag = System.currentTimeMillis()
                            updateRecordingTimer()
                        } else {
                            binding.iftvSwitchCamera.visible()
                            VideoRecordStateManager.stopRecording()
                        }
                    }

                    is VideoRecordEvent.Finalize -> {
                        lifecycleScope.launchMain {
                            Logz.tag(TAG).d("Video Record: Finalize")
                            (binding.tvRecordingTime.getTag(R.id.tvRecordingTime) as? Runnable)?.let {
                                binding.tvRecordingTime.removeCallbacks(it)
                            }

                            binding.tvRecordingTime.tag = null
                            binding.tvRecordingTime.setTag(R.id.tvRecordingTime, null)
                            binding.tvRecordingTime.invisible()
                            binding.roundAlbumEntry.visible()
                            binding.tlCameraMode.visible()
                            binding.iftvClose.visible()
                            binding.iftvSwitchCamera.visible()
                            binding.roundTakePhotoOutSide.setDefaultButton()
                            updateFlashState()

                            if (userLeaveByBackPress || !UserSessionManager.hasSession()){
                                deleteFileOnUserLeave(recordEvent.outputResults.outputUri.path)
                                return@launchMain
                            }

                            if (!recordEvent.hasError()) {
                                val msg = "Video capture succeeded: " +
                                        "${recordEvent.outputResults.outputUri.path}"
                                log(TAG, msg)
                                val videoParameters: VideoParameters? = withIOContext {
                                        VideoParameters.detachVideoModel(recordEvent.outputResults.outputUri.path)
                                    }
                                if (videoParameters == null) {
                                    toast(R.string.common_save_qrcode_fail_tip.asString())
                                    return@launchMain
                                }
                                if (videoParameters.duration < 1000) {
                                    toast(R.string.video_too_short.asString())
                                    File(recordEvent.outputResults.outputUri.path).delete()
                                    VideoRecordStateManager.recording = null
                                    return@launchMain
                                }
                                recordEvent.outputResults.outputUri.path?.let {
                                    val isRotated = videoParameters.rotation == 90 || videoParameters.rotation == 270
                                    goToPreview(it, it,
                                        if (isRotated) videoParameters.height else videoParameters.width,
                                        if (isRotated) videoParameters.width else videoParameters.height,
                                        MediaType.Video, videoParameters.duration)
                                }
                            } else {
                                VideoRecordStateManager.recording?.stop()
                                VideoRecordStateManager.recording?.close()
                                VideoRecordStateManager.recording = null
                                logError(
                                    TAG, "Video capture ends with error: " +
                                            "${recordEvent.error}"
                                )
                                if (recordEvent.error != VideoRecordEvent.Finalize.ERROR_NO_VALID_DATA) {
                                    toast(R.string.common_save_qrcode_fail_tip.asString())
                                }else{
                                    toast(R.string.video_too_short.asString())
                                }
                            }
                            ChatTracker.postShootingMediaResult(
                                type = "video",
                                videoLength = videoFile.length(),
                                isSuccess = !recordEvent.hasError(),
                                isBack = CommonMMKV.lastUserUseBackCamera,
                                failReason = recordEvent.error.takeUnless {
                                    recordEvent.error == VideoRecordEvent.Finalize.ERROR_NONE
                                }.toString()
                            )
                        }

                    }
                }
            }
    }


    private fun updateRecordingTimer(){
        val recordingRunnable = object : Runnable {
            override fun run() {
                if (isMemoryFullForRecording || isScreenOff || isReceivedCall || isAlarmRinging) {
                    if (isMemoryFullForRecording) { notEnoughStorageDialog() }
                    binding.roundTakePhotoOutSide.stopRecordingVideo()
                    return
                }
                if (VideoRecordStateManager.isRecording) {
                    val tag = binding.tvRecordingTime.tag
                    if (tag == null || tag !is Long) return
                    val startTime: Long = tag
                    val recordTime =
                        System.currentTimeMillis() - startTime
                    if (recordTime <= VIDEO_RECORD_MAX_TIME) {
                        binding.tvRecordingTime.text =
                            String.format(
                                "%02d:%02d",
                                recordTime / 1000 / 60,
                                (recordTime / 1000) % 60
                            )
                        binding.roundTakePhotoOutSide.setProgress(recordTime.toInt())
                        binding.tvRecordingTime.postDelayed(this, 50)
                    } else {
                        binding.tvRecordingTime.text =
                            R.string.common_complete.asString()
                        binding.roundTakePhotoOutSide.setProgress(
                            VIDEO_RECORD_MAX_TIME
                        )
                        binding.roundTakePhotoOutSide.stopRecordingVideo()
                    }
                }
            }
        }
        binding.tvRecordingTime.setTag(R.id.tvRecordingTime, recordingRunnable)
        recordingRunnable.run()
    }

    private fun notEnoughStorageDialog(){
        CommonAlertDialog(
            context = context,
            title = com.interfun.buz.common.R.string.storage_issue_unable_to_record.asString(),
            positiveText = com.interfun.buz.common.R.string.ok.asString(),
            positiveCallback = {
                it.dismiss()
                finish()
            }
        ).show()
    }


    override fun onStart() {
        super.onStart()
        startSendPic = false
        startPickPic = false
        orientationEventListener.enable()
    }

    override fun onStop() {
        super.onStop()
        orientationEventListener.disable()
        binding.root.keepScreenOn = false
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraProvider?.unbindAll()
        binding.root.keepScreenOn = false
    }

    /**
     * 聚焦
     * @param auto 聚焦模式
     */
    @SuppressLint("RestrictedApi")
    private fun focus(x: Float, y: Float, auto: Boolean) {
        cameraControl?.cancelFocusAndMetering()
        renderFocusView(x, y, true)
        val createPoint: MeteringPoint = if (auto) {

            val meteringPointFactory = DisplayOrientedMeteringPointFactory(
                binding.pvPhoto.display,
                camera?.cameraInfo!!,
                binding.pvPhoto.width.toFloat(),
                binding.pvPhoto.height.toFloat()
            )
            meteringPointFactory.createPoint(x, y)
        } else {
            val meteringPointFactory = binding.pvPhoto.meteringPointFactory
            meteringPointFactory.createPoint(x, y)
        }

        val build = FocusMeteringAction.Builder(createPoint, FLAG_AF)
            .setAutoCancelDuration(3, TimeUnit.SECONDS)
            .build()

        val future = cameraControl?.startFocusAndMetering(build)

        future?.addListener({
            try {

                if (future.get().isFocusSuccessful) {
                    //聚焦成功
                    log(TAG, "聚焦成功")
                } else {
                    //聚焦失败
                    log(TAG, "聚焦失败")
                }
            } catch (e: Exception) {
                TakePhotoTrackUtils.errReport(e,"3")
                Log.e(TAG, "异常" + e.message)
            }
            renderFocusView(x, y, false)
        }, ContextCompat.getMainExecutor(this))

    }

    private fun renderFocusView(x: Float, y: Float, visible: Boolean) {
        binding.vCameraFocus.translationX = x - binding.vCameraFocus.width / 2
        binding.vCameraFocus.translationY = y - binding.vCameraFocus.height / 2
        binding.vCameraFocus.inVisibleIf(!visible)
    }

    /**
     * 缩放
     */
    private fun zoom(zoomCoefficient: Float) {
        val zoomState = camera?.cameraInfo?.zoomState
        val zoomRatio: Float? = zoomState?.value?.zoomRatio        //当前值
        val maxZoomRatio: Float? = zoomState?.value?.maxZoomRatio//缩放最大值
        val minZoomRatio: Float? = zoomState?.value?.minZoomRatio //缩放最小值
        val curZoomRatio =
            zoomRatio?.plus(zoomCoefficient * (maxZoomRatio?.minus(minZoomRatio!!)!!))!!
        if (curZoomRatio < maxZoomRatio!! && curZoomRatio > minZoomRatio!!) {
            cameraControl?.setZoomRatio(curZoomRatio)
        }
    }

    private fun changeFlashMode(flashOn: Boolean = !this.flashOn) {
        this.flashOn = flashOn
        //闪光灯模式
        val flashMode = if (!CommonMMKV.lastUserUseBackCamera) {
            ImageCapture.FLASH_MODE_OFF
        } else if (flashOn) {
            ImageCapture.FLASH_MODE_ON
        } else {
            ImageCapture.FLASH_MODE_OFF
        }
        updateFlashState()
        imageCapture?.flashMode = flashMode
    }

//    private fun tryPauseOrResumeRecordingVideo(pause: Boolean = false): Boolean {
//        if (VideoRecordStateManager.recordingState.isNotNull()) {
//            when (VideoRecordStateManager.recordingState) {
//                is VideoRecordEvent.Start, is VideoRecordEvent.Resume -> {
//                    if (pause) {
//                        VideoRecordStateManager.pauseRecording()
//                        return true
//                    }
//                }
//
//                is VideoRecordEvent.Pause -> {
//                    if (!pause) {
//                        VideoRecordStateManager.resumeRecording()
//                        return true
//                    }
//                }
//            }
//        }
//        return false
//    }

    private fun isRecordingVideo() : Boolean {
        return VideoRecordStateManager.recording != null
    }

    private fun goToPreview(mediaUri: String, thumbnailUrl: String, width: Int, height: Int, mediaType: MediaType, duration: Long = 0) {
        logInfo(TAG,"goToPreview:mediaUri = $mediaUri, thumbnailUrl = $thumbnailUrl, width = $width, height = $height, mediaType = $mediaType, duration = $duration")
        gotoPreviewTask = {
            logInfo(TAG,"goToPreview: real invoke")
            UpdateAlbumListDataEvent.postDelay(1000)
            if (mediaType == MediaType.Image) {
                ChatTracker.postShootingMediaResult(
                    type = "image",
                    videoLength = null,
                    isSuccess = takePhotoState == TakePhotoState.SUCCESS,
                    isBack = CommonMMKV.lastUserUseBackCamera,
                    failReason = takePhotoState.takeIf {
                        takePhotoState == TakePhotoState.ERROR
                    }.toString()
                )
            }
            val data = MediaItem(
                mediaUri = mediaUri,
                width = width,
                height = height,
                duration = duration,
                type  = mediaType
            )

            val intent = Intent(context, MediaPreviewActivity::class.java).apply {
                putExtra(RouterParamKey.Album.KEY_SOURCE, RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE)
                putExtra(RouterParamKey.Album.KEY_PREVIEW_DATA, data)
                putExtra(RouterParamKey.Album.KEY_TARGET_ID, targetId)
                putExtra(RouterParamKey.Album.KEY_CONV_TYPE, convType)
                putExtra(RouterParamKey.Album.KEY_REFERENCE_MSG_ID, referenceId)
            }
            startForResult.launch(intent)
            gotoPreviewTask = null
        }

        logInfo(TAG,"goToPreview: ${lifecycle.currentState}")
        if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)){
            gotoPreviewTask?.invoke()
        }
    }

    override fun onResume() {
        super.onResume()
        logInfo(TAG,"onResume: $gotoPreviewTask")
        gotoPreviewTask?.invoke()
    }

    fun canSwitchCamera(): Boolean {
        return takePhotoState != TakePhotoState.START&& !VideoRecordStateManager.isRecording_.value
    }

    private fun fadeTextAnim() {
        val fadeAnim = ValueAnimator.ofFloat(binding.tvTakePhotoDesc.alpha, 0f)
        fadeAnim.apply {
            addUpdateListener {
                val animatedValue = it.animatedValue as Float
                binding.tvTakePhotoDesc.alpha = animatedValue
            }
            doOnStart { binding.tvTakePhotoDesc.visible() }
            setDuration(300)
            doOnEnd { binding.tvTakePhotoDesc.gone() }
        }.start()
    }
}

public enum class TakePhotoState {
    START, SUCCESS, ERROR, IDLE,
}

