package com.interfun.buz.im.ktx

import android.Manifest
import com.interfun.buz.base.ktx.isPermissionGranted
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import java.io.File

fun IM5VideoMessage.videoUrl(): String? {
    val isLocalOrgExist = orgVideoPath?.let {
        val file = File(it)
        file.exists() && file.canRead()
    } ?: false

    val isLocalPathExist = localPath?.let {
        File(it).exists()
    } ?: false
    return when {
        // orgVideoPath 源视频本地路径（未压缩的，发送方set）
        isLocalOrgExist -> orgVideoPath
        // localPath 视频本地路径（压缩后的）
        isLocalPathExist -> localPath
        // remoteUrl 视频远端路径
        else -> remoteUrl
    }
}

fun IM5VideoMessage.localVideoUrl(): String? {
    val isLocalOrgExist = orgVideoPath?.let {
        val file = File(it)
        file.exists() && file.canRead()
    } ?: false

    val isLocalPathExist = localPath?.let {
        File(it).exists()
    } ?: false
    return when {
        // orgVideoPath 源视频本地路径（未压缩的，发送方set）
        isLocalOrgExist -> orgVideoPath
        // localPath 视频本地路径（压缩后的）
        isLocalPathExist -> localPath
        else -> null
    }
}

fun IM5VideoMessage.coverThumbUrl(): String? {
    return when {
        coverLocalPath.isNullOrEmpty().not() -> {
            val file = File(coverLocalPath)
            if (file.exists()) {
                file.absolutePath
            } else {
                coverThumbPath ?: coverRemoteUrl
            }
        }

        else -> coverThumbPath ?: coverRemoteUrl
    }
}