package com.interfun.buz.startup

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.splashscreen.SplashScreen
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.core.splashscreen.SplashScreenViewProvider
import androidx.core.view.doOnPreDraw
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.basis.BasisActivity
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.constants.KEY_ROUTER
import com.interfun.buz.common.constants.PATH_STARTUP_ACTIVITY_ENTRY
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.utils.StartupCostTrace
import com.interfun.buz.common.utils.StartupTrace
import com.interfun.buz.startup.launch.LaunchUriIntentDispatcher
import com.interfun.buz.startup.track.StartupTrack
import dagger.hilt.android.AndroidEntryPoint

/**
 * @Desc: 入口分发的启动Activity
 * @Author: <EMAIL>
 * @Date: 2022/6/8
 */
@Route(path = PATH_STARTUP_ACTIVITY_ENTRY)
@AndroidEntryPoint
open class EntryPointActivity : BasisActivity() {

    private val isAppInBackgroundBeforeThisActivity = isAppInForeground

    companion object {

        fun getGatewayIntentByRouter(context: Context, router: String?): Intent {
            return Intent(context, GatewayActivity::class.java).apply {
                putExtra(RouterParamKey.Startup.KEY_ROUTER, router)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        StartupCostTrace.recordCreateActivity()
        log("EntryPointActivity", "onCreate")
        installSplashScreen()/*.apply {
            setKeepOnScreenCondition { true }
        }*/
        StartupTrack.reportAppOpenLoginPageStep(1)
        super.onCreate(savedInstanceState)
        window?.decorView?.doOnPreDraw {
            StartupTrace.onFirstFrameShow()
        }
        StartupTrack.reportAppOpenLoginPageStep(2)
        LaunchStartupManager.startupActivity(this@EntryPointActivity,isAppInBackgroundBeforeThisActivity)
        LaunchUriIntentDispatcher.dispatchIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        log("EntryPointActivity", "onNewIntent")
    }

    override fun onResume() {
        super.onResume()
    }

    override fun initPreOnCreate() {

    }

//    override fun setSystemBarImmersive() {
//
//    }

}