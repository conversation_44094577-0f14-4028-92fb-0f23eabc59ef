package com.interfun.buz.voicecall.common.view.widget

import android.animation.Animator
import android.content.Context
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isGone
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.assertutil.buzAssert
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.goneIf
import com.interfun.buz.base.ktx.isVisible
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.removeFromParent
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CameraStatus
import com.interfun.buz.common.ktx.alphaAnim
import com.interfun.buz.common.ktx.animatorPlayRepeatedly
import com.interfun.buz.common.ktx.animatorPlayWithFraction
import com.interfun.buz.common.ktx.fractionChangeAnim
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle.CONNECTED
import com.interfun.buz.common.manager.voicecall.RoomVCResManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.onair.helper.SeatBgPaletteHelper
import com.interfun.buz.onair.standard.SeatRtcVolumeInfo
import com.interfun.buz.voicecall.common.utils.VCTracker
import com.interfun.buz.voicecall.databinding.VoicecallUserViewBinding
import com.yibasan.lizhifm.audio.LiveTextureView
import com.yibasan.lizhifm.audio.MediaContentType
import com.yibasan.lizhifm.liveinteractive.CameraStopReason
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

open class RealTimeCallUserCard @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : CardView(context, attrs) {

    var isInitialised = false
    private var iconEnabled = false
    private var canShowSpeakingAnim = false
    private var canShowSpeakingBorder = false
    private var cameraEventHandlerAdded = false
    private var isUserMutedFlow = MutableStateFlow<Boolean?>(null)
    private val isUserMuted get() = isUserMutedFlow.value
    private var isUserPoorNetwork: Boolean? = null
    private var speakingAnimator: Animator? = null
    private val binding: VoicecallUserViewBinding by lazy {
        VoicecallUserViewBinding.inflate(LayoutInflater.from(context), this)
    }

    companion object {
        const val TAG = "RealTimeCallUserCard"
    }

    private var videoTextureView: LiveTextureView? = null



    fun cameraCaptureStartedReact() {
        logInfo(TAG, "onCameraCaptureStarted(${videoTextureView?.viewID})")
        videoTextureView?.textureView?.visible()
    }

    fun cameraDeviceChangedReact() {
        logInfo(TAG, "onCameraDeviceChanged(${videoTextureView?.viewID})")

    }

    fun cameraCaptureStoppedReact(reason: CameraStopReason?){
        logInfo(
            TAG,
            "onCameraCaptureStopped(${videoTextureView?.viewID}) reason=${reason}",
            logLine = LogLine.RTC_CALL
        )
        videoTextureView?.textureView?.gone()
        VCTracker.cameraStopInVc(reason)
    }



    fun initCard(
        user: CallRoomUser,
        applyTextureView: Boolean = true,
        cameraManager: RoomVCResManager) {
        logInfo(TAG, "initCard: user=${user.userName}")
        if (isInitialised) return
        isInitialised = true

        with(user) {
            setUserInfo(this)
            if (applyTextureView) setVideoTextureView(cameraManager)
        }
    }
    var roomVCResManager: RoomVCResManager? = null

    fun bindingTextureView(from: String, roomVCResManager: RoomVCResManager) {
        this.roomVCResManager=roomVCResManager
        val videoTextureViewBackup = videoTextureView
        if (videoTextureViewBackup != null) {
            roomVCResManager.recycleLiveTextureView(videoTextureViewBackup)
            videoTextureView = null;
        }
        videoTextureView = roomVCResManager.createTextureView(context, from).apply {
            logInfo(TAG, "createTextureView($viewID - $from)")
            textureView.id = View.generateViewId()
            textureView.layoutParams = ConstraintLayout.LayoutParams(0, 0).apply {
                topToTop = binding.clContent.id
                bottomToBottom = binding.clContent.id
                startToStart = binding.clContent.id
                endToEnd = binding.clContent.id
            }

            textureView.removeFromParent()
            (binding.clContent as ViewGroup).addView(textureView)
        }
    }

    /* -------------------- Voice Call Texture View -------------------- */
    fun setUserInfo(user: CallRoomUser) {
        logInfo(TAG, "setUserInfo: user=${user.userName}")
        setUsername(user.contactFirstName.takeIf { it.isNotEmpty() } ?: user.userName)
        if (user.portrait.isNullOrEmpty()) {
            findViewTreeLifecycleOwner()?.lifecycleScope?.launch {
                val portrait =
                    UserRelationCacheManager.getUserRelationInfoByUidSync(user.userId)?.portrait
                setPortrait(portrait)
            }
        } else {
            setPortrait(user.portrait)
        }
    }

    fun showUserName(playAnimation: Boolean = false) {
        if (playAnimation) {
            binding.tvUserName.apply {
                visible()
                alphaAnim(250L, alpha, 1f).start()
            }
            binding.vShadowBackground.apply {
                visible()
                alphaAnim(250L, alpha, 1f).start()
            }
        } else {
            binding.tvUserName.visible()
            binding.vShadowBackground.visible()
        }
    }

    fun hideUserName(playAnimation: Boolean = false) {
        if (playAnimation) {
            binding.tvUserName.apply {
                alphaAnim(250L, alpha, 0f) {
                    gone()
                }.start()
            }
            if (binding.iftvMuted.isGone && binding.iftvNetwork.isGone) {
                binding.vShadowBackground.apply {
                    alphaAnim(250L, alpha, 0f) {
                        gone()
                    }.start()
                }
            }
        } else {
            binding.tvUserName.gone()
            binding.vShadowBackground.goneIf(binding.iftvMuted.isGone && binding.iftvNetwork.isGone)
        }

    }

    private fun setPortrait(url: String?) {
        binding.userPortraitIV.setPortrait(url)
        initAnimSpeakingColor(url)
    }

    private fun setUsername(username: String) {
        binding.tvUserName.text = username
    }

    /* -------------------- Status Icon -------------------- */
    fun updateMuteStatus(isMute: Boolean) {
        if (isUserMuted == isMute) return
        isUserMutedFlow.tryEmit(isMute)

        updateIconVisibility(binding.iftvMuted, isMute)
        if (canShowSpeakingAnim && isUserMuted == false && binding.userPortraitIV.alpha == 1f) {
            binding.speakingAnim.visible()
            speakingAnimator?.start()
        } else {
            binding.speakingAnim.gone()
            binding.speakingBorder.gone()
            speakingAnimator?.cancel()
        }
    }

    fun updateNetworkStatus(isPoorNetwork: Boolean) {
        if (isUserPoorNetwork == isPoorNetwork) return
        logDebug(TAG, "updateNetworkStatus: isPoorNetwork=$isPoorNetwork")
        isUserPoorNetwork = isPoorNetwork
        updateIconVisibility(binding.iftvNetwork, isPoorNetwork)
    }

    private fun updateIconVisibility(view: View, shouldShow: Boolean) {
        view.visibleIf(shouldShow && iconEnabled)
        binding.vShadowBackground.visibleIf(
            binding.tvUserName.isVisible() ||
                    binding.iftvMuted.isVisible() ||
                    binding.iftvNetwork.isVisible()
        )
    }

    fun setIconEnabled(enable: Boolean) {
        this.iconEnabled = enable
        binding.iftvMuted.visibleIf(enable && isUserMuted.getBooleanDefault())
        binding.iftvNetwork.visibleIf(enable && isUserPoorNetwork.getBooleanDefault())
        binding.vShadowBackground.visibleIf(
            binding.tvUserName.isVisible() ||
                    binding.iftvMuted.isVisible() ||
                    binding.iftvNetwork.isVisible()
        )
    }

    fun setCanShowSpeakingAnim(canShowSpeakingAnim: Boolean) {
        this.canShowSpeakingAnim = canShowSpeakingAnim
        if (canShowSpeakingAnim && isUserMuted == false && binding.userPortraitIV.alpha == 1f) {
            binding.speakingAnim.visible()
            speakingAnimator?.start()
        } else {
            binding.speakingAnim.gone()
            speakingAnimator?.cancel()
        }
    }

    fun setCanShowSpeakingBorder(canShowSpeakingBorder: Boolean) {
        this.canShowSpeakingBorder = canShowSpeakingBorder
    }

    /* -------------------- Video Call Texture View -------------------- */
    private fun setVideoTextureView(cameraManager: RoomVCResManager) {
        if (videoTextureView == null) {
            bindingTextureView("setVideoTextureView", cameraManager)
        }
    }

    private fun isConnected(): Boolean {
        return VoiceCallPortal.currentRoom.value?.getLifeCycle()?.state?.isAtLeast(CONNECTED) == true
    }

    fun startCameraCaptureNew(isFront: Boolean, from: String, cameraManager: RoomVCResManager) {
        this.roomVCResManager=cameraManager
        binding.vTextureViewBackground.visible()
        if (videoTextureView == null) {
            bindingTextureView(from, cameraManager)
        }
        logInfo(
            TAG,
            "startCameraCapture(viewID=${videoTextureView?.viewID}): isFront=$isFront, from=$from",
            logLine = LogLine.MY_RTC_CAMERA_STATE
        )
        videoTextureView?.let {videoTextureView->
            cameraManager.updateCameraView(videoTextureView)
            cameraManager.startCameraCapture(context,videoTextureView,isFront,from=from)
            videoTextureView.textureView.visible()
        }
    }

    fun stopCameraCapture(from: String = "", roomVCResManager: RoomVCResManager) {
        logInfo(
            TAG, "stopCameraCapture(viewID=${videoTextureView?.viewID}), from=$from",
            logLine = LogLine.MY_RTC_CAMERA_STATE
        )
        videoTextureView?.textureView?.gone()
        binding.vTextureViewBackground.gone()
        roomVCResManager.stopCameraCapture(from = from)
        recycleLiveTextureView()
    }

    fun switchCameraAnim(playAnimation: Boolean = false) {
        logInfo(TAG, "switchCamera(${videoTextureView?.viewID}): ")
        if (playAnimation) {
            animatorPlayWithFraction(
                duration = 200L,
                values = floatArrayOf(0f, 1f, 0f),
                updateListener = { fraction ->
                    scaleX = fractionChangeAnim(fraction, 1f, 0f)
                }
            ).start()
        }
    }

    fun showTextureViewIf(show: Boolean) {
        logInfo(TAG, "showTextureViewIf(${videoTextureView?.viewID}): show=$show")
        videoTextureView?.textureView?.visibleIf(show)
        if (!show) {
            videoTextureView?.clearView()
        }
    }

    fun bindRemoteVideoView(user: CallRoomUser, cameraManager: RoomVCResManager) {
        logInfo(TAG, "bindRemoteVideoView(${videoTextureView?.viewID}): user=${user.userName}")
        val channelUserId = user.rtcUserId.toLong()
        if (videoTextureView == null) {
            bindingTextureView("bindRemoteVideoView",cameraManager)
        }
        videoTextureView?.let {
            cameraManager.bindRemoteVideoView(channelUserId,
                MediaContentType.camera,
                it)
            showTextureViewIf(CameraStatus.isOpen(user.cameraStatus))
        }
    }

//    private fun unbindRemoteVideoView() {
//        logInfo(TAG, "unbindRemoteVideoView(${videoTextureView?.viewID})")
//        videoTextureView?.let {
//            it.textureView.gone()
//            DoreRTCEnginManager.unbindRemoteVideoView(it)
////            tempReleaseCamera()
//            recycleLiveTextureView()
//        }
//    }

//    private fun tempReleaseCamera() {
//        if (videoTextureView == null) return
//        logInfo(TAG, "tempReleaseCamera(${videoTextureView?.viewID})")
//        videoTextureView?.apply {
//            textureView.removeFromParent()
//            release()
//        }
//        videoTextureView = null
//    }

    fun releaseCamera(roomVCResManager: RoomVCResManager?) {
        logInfo(TAG, "releaseCamera(${videoTextureView?.viewID})")
        isInitialised = false
        cameraEventHandlerAdded = false
        recycleLiveTextureView()
    }

    fun recycleLiveTextureView() {
        logInfo(TAG, "RealTimeCallUserCard recycleLiveTextureView START: viewID=${videoTextureView?.viewID}, " +
                "roomVCResManager=${roomVCResManager != null}, timestamp=${System.currentTimeMillis()}")
        val videoTextureViewBackup = videoTextureView
        if (videoTextureViewBackup != null) {
            this.roomVCResManager?.recycleLiveTextureView(videoTextureViewBackup)
            buzAssert({this.roomVCResManager!=null},{"roomVCResManager is null"})
            logInfo(TAG, "RealTimeCallUserCard recycleLiveTextureView SUCCESS: viewID=${videoTextureViewBackup.viewID}")
        } else {
            logInfo(TAG, "RealTimeCallUserCard recycleLiveTextureView: videoTextureView is null")
        }
        videoTextureView = null
    }



    /* -------------------- Speaking Anim -------------------- */
    private var scaleAnim: Animator? = null

    private fun initAnimSpeakingColor(userPortrait: String?) {
        val speakAniJob = findViewTreeLifecycleOwner()?.lifecycleScope?.launch {
            speakingAnimator?.cancel()
            speakingAnimator = animatorPlayRepeatedly(
                duration = 5000L,
                interpolator = LinearInterpolator(),
                updateListener = { fraction ->
                    combineView(
                        binding.lowLayer,
                        binding.topLayer
                    ).rotation = fractionChangeAnim(fraction, 0f, 360f)
                }
            )

            if (userPortrait != null) {
                val seatColor = SeatBgPaletteHelper.obtainColorFromRelation(userPortrait)
                setSpeakingViewColorFilter(seatColor)
            }
        }
        speakAniJob?.invokeOnCompletion {
            speakingAnimator?.cancel()
        }
    }

    fun bindVolumeFlow(
        scope: CoroutineScope,
        member: CallRoomUser,
        seatRtcVolumeInfoListFlow: Flow<List<SeatRtcVolumeInfo>>
    ) {
        // Unwrap the proxy
        val volumeFlow = seatRtcVolumeInfoListFlow
            .flatMapConcat { list -> list.asFlow() } // Flatten the list directly
            .filter { it.rtcId == member.rtcUserId.toLong() }
            .map { it.volume }
            .distinctUntilChanged()

        scope.launch {
            volumeFlow.collect { volume ->
                if (isConnected().not() || isUserMuted != false) return@collect
                logDebug(
                    TAG,
                    "bindVolumeFlow(${member.userName}): volume=${volume} (${volume > 0f})"
                )
                if (canShowSpeakingAnim && binding.userPortraitIV.alpha == 1f) {
                    updateSpeakingViewScale(volume / 180f)
                }
            }
        }

//        val speakingBorderVisibleFlow = seatRtcVolumeInfoListFlow
//            .flatMapConcat { list -> list.asFlow() } // Flatten the list directly
//            .filter { it.rtcId == member.rtcUserId.toLong() }
//            .map { it.volume }
//            .foldInSizeWithTimeout(3,1000L)
//            .map { list -> list.find { it > 0 }.isNotNull() }
//            .distinctUntilChanged()
//
//        scope.launch {
//            combine(speakingBorderVisibleFlow, isUserMutedFlow) { visible, muted ->
//                visible && muted == false
//            }.collect { visible ->
//                logDebug(TAG, "speakingBorderVisibleFlow: $visible")
//                if (canShowSpeakingBorder) {
//                    binding.speakingBorder.visibleIf(visible)
//                }
//            }
//        }
    }

    private fun setSpeakingViewColorFilter(seatColor: Int) {
        binding.lowLayer.colorFilter = createColorFilter(seatColor)
        binding.topLayer.colorFilter = createColorFilter(seatColor)
    }

    private fun createColorFilter(color: Int): ColorMatrixColorFilter {
        val red = (color shr 16 and 0xFF) / 255f
        val green = (color shr 8 and 0xFF) / 255f
        val blue = (color and 0xFF) / 255f

        val colorMatrix = ColorMatrix(
            floatArrayOf(
                0f, 0f, 0f, 0f, red * 255f,
                0f, 0f, 0f, 0f, green * 255f,
                0f, 0f, 0f, 0f, blue * 255f,
                0f, 0f, 0f, 1f, 0f
            )
        )
        return ColorMatrixColorFilter(colorMatrix)
    }

    private fun updateSpeakingViewScale(volume: Float) {
        scaleAnim?.cancel()

        val topScale = if (volume == 0f) (100..102).random() / 100f else (volume * 10 + 105) / 100f
        val lowScale = if (volume == 0f) (102..105).random() / 100f else (volume * 10 + 110) / 100f
        scaleAnim = animatorPlayWithFraction(
            duration = 200,
            interpolator = LinearInterpolator(),
            updateListener = { fraction ->
                binding.apply {
                    topLayer.scaleX = fractionChangeAnim(fraction, topLayer.scaleX, topScale)
                    topLayer.scaleY = fractionChangeAnim(fraction, topLayer.scaleY, topScale)
                    lowLayer.scaleX = fractionChangeAnim(fraction, lowLayer.scaleX, lowScale)
                    lowLayer.scaleY = fractionChangeAnim(fraction, lowLayer.scaleY, lowScale)
                }
            }
        )
        scaleAnim?.start()
    }

    override fun onDetachedFromWindow() {
        logInfo(TAG, "RealTimeCallUserCard onDetachedFromWindow: viewID=${videoTextureView?.viewID}, " +
                "timestamp=${System.currentTimeMillis()}")
        releaseCamera(roomVCResManager)
        speakingAnimator?.cancel()
        super.onDetachedFromWindow()
    }
}

