<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.luck.picture.lib.widget.RecyclerPreloadView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@+id/bottom_nar_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tv_current_data_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:alpha="0"
        android:background="@color/ps_color_99_black"
        android:gravity="center_vertical"
        android:paddingStart="10dp"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:textColor="@color/ps_color_white"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar" />

    <com.luck.picture.lib.widget.TitleBar
        android:id="@+id/title_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="48dp" />

    <com.luck.picture.lib.widget.BottomNavBar
        android:id="@+id/bottom_nar_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:layout_height="45dp" />


    <com.luck.picture.lib.widget.CompleteSelectView
        android:id="@+id/ps_complete_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:enabled="false"
        android:layout_marginEnd="15dp"
        android:background="@drawable/ps_transparent_space"
        app:layout_constraintBottom_toBottomOf="@id/bottom_nar_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/bottom_nar_bar" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tv_data_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="3dp"
        android:text="@string/ps_empty"
        android:textColor="@color/ps_color_aab2bd"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>