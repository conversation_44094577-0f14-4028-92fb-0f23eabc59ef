package com.interfun.buz.base.widget.view

import android.content.Context
import android.graphics.Camera
import android.graphics.Canvas
import android.graphics.Matrix
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.interfun.buz.base.R

/**
 * 点击时透明度变为0.6
 */
open class PressableTextView @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defaultStyle: Int = 0
) : AppCompatTextView(context, attr, defaultStyle) {

    var usePressEffect = true
    var autoRTL = false

    init {
        val pressableTextView = context.obtainStyledAttributes(attr, R.styleable.PressableTextView)
        usePressEffect =
            pressableTextView.getBoolean(R.styleable.PressableTextView_pressEffect, true)
        autoRTL = pressableTextView.getBoolean(R.styleable.PressableTextView_autoRTL, true)
        pressableTextView.recycle()
    }

    override fun draw(canvas: Canvas) {
        val saveCount = canvas.save()

        if (autoRTL && layoutDirection == LAYOUT_DIRECTION_RTL) {
            val camera = Camera()
            val matrix = Matrix()
            camera.getMatrix(matrix)
            matrix.preScale(-1f, 1f, width / 2f, height / 2f)
            canvas.concat(matrix)
        }

        // Draw the view content here
        super.draw(canvas)
//
//        if (autoRTL){
//            // Restore the canvas to its previous state
//        }
        canvas.restoreToCount(saveCount)
    }

    override fun setPressed(pressed: Boolean) {
        super.setPressed(pressed)
        if (usePressEffect) {
            alpha = if (pressed) {
                0.6f
            } else {
                1f
            }
        }
    }
}