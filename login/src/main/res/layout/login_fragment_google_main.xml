<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black_100"
    tools:context=".view.activity.LoginActivity">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieUserPortraitBackground"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-30dp"
        android:layout_marginEnd="-30dp"
        app:layout_constraintBottom_toTopOf="@id/clContainer"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="spread" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="70dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/lottieUserPortraitBackground"
        app:layout_constraintVertical_bias="0.62"
        app:layout_constraintVertical_chainStyle="packed">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clPrimaryGoogle"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginHorizontal="38dp"
            android:layout_marginBottom="19dp"
            android:background="@drawable/common_r28_overlay_white"
            app:layout_constraintBottom_toTopOf="@id/tvTextOtherLoginWith">

            <com.interfun.buz.common.widget.view.loading.CircleLoadingView
                android:id="@+id/loadingGoogleLogin"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="10dp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/ivLoginGoogle"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_centerVertical="true"
                android:padding="2dp"
                android:src="@drawable/googleg_standard_color_18"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tvLoginGoogle"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvLoginGoogle"
                style="@style/button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6.5dp"
                android:text="@string/continue_with_google"
                android:textColor="@color/text_black_main"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivLoginGoogle"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvTextOtherLoginWith"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="19dp"
            android:text="@string/other_login_way"
            android:textColor="@color/text_white_main"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/tvPhoneLogin"/>

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/tvPhoneLogin"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginBottom="20dp"
            android:layout_marginEnd="10dp"
            android:background="@drawable/common_oval_transparent_stroke_1"
            android:gravity="center"
            android:text="@string/ic_tel"
            android:textColor="@color/text_white_main"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:textSize="@dimen/general_font_size_24"
            app:layout_constraintBottom_toTopOf="@id/tvAgreement"
            app:layout_constraintEnd_toStartOf="@id/tvEmailLogin"
            app:layout_constraintStart_toStartOf="parent" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/tvEmailLogin"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/common_oval_transparent_stroke_1"
            android:gravity="center"
            android:text="@string/ic_email_solid"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:textColor="@color/text_white_main"
            android:textSize="@dimen/general_font_size_24"
            app:layout_constraintBottom_toBottomOf="@id/tvPhoneLogin"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvPhoneLogin" />

        <TextView
            android:id="@+id/tvAgreement"
            style="@style/regular"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:gravity="center"
            android:text="@string/login_user_agreement_and_policy"
            android:textAlignment="center"
            android:textColor="@color/text_white_disable"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>