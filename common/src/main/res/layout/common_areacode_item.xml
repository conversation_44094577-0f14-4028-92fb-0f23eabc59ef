<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/overlay_grey_10">

    <TextView
        android:id="@+id/tvAreaTitle"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="22dp"
        android:layout_marginBottom="22dp"
        android:textColor="@color/text_white_main"
        android:textSize="16sp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textDirection="locale"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvAreaCode"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/united_state" />

    <TextView
        android:id="@+id/tvAreaCode"
        style="@style/main_body"
        android:layout_width="wrap_content"
        android:textDirection="locale"
        android:layout_height="wrap_content"
        android:layout_marginTop="22dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="22dp"
        android:textColor="@color/text_white_main"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="+1" />

</androidx.constraintlayout.widget.ConstraintLayout>