package com.interfun.buz.common.utils

import android.text.SpannableString
import android.text.style.URLSpan
import android.text.util.Linkify
import coil.Coil
import coil.request.ImageRequest
import coil.request.SuccessResult
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.chat.LinkMetadataInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.HttpUrl.Companion.toHttpUrl
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import org.jsoup.Jsoup

object HyperlinkUtil {
    const val TAG = "HyperlinkUtil"

    /**
     * Returns first URL from list of URLs in text
     */
    fun extractFirstURL(text: String?): String? {
        if (text.isNullOrEmpty()) return null
        val spanBuilder = SpannableString(text)
        Linkify.addLinks(spanBuilder, Linkify.WEB_URLS)
        val urlSpans = spanBuilder.getSpans(0, spanBuilder.length, URLSpan::class.java)
        if (urlSpans.isEmpty()) return null
        logDebug(TAG, "first url==>${urlSpans.first().url}")
        return urlSpans.first().url
    }

    fun hasLink(text: String): Boolean {
        return extractFirstURL(text).isNullOrEmpty().not()
    }

    /**
     * Method to extract metadata such as: web icon, site name, cover image/thumbnail, title & description from the web url
     * @param url URL of the web page. MUST start with http:// or https://
     * @return LinkMetadataInfo with the extracted metadata
     */
    suspend fun getMetadata(url: String?): LinkMetadataInfo? = withContext(Dispatchers.IO) {
        logInfo(TAG, "getMetadata from: $url")
        if (url == null) return@withContext null
        if (url.startsWith("http://").not() && url.startsWith("https://").not()) return@withContext null
        runCatching {
            val doc = Jsoup.connect(url)
                .timeout(6000)
                .referrer("http://www.google.com")
                .userAgent("Chrome")
                .get()

            // Site Name
            val ogSiteNameElement =
                doc.select("meta[property=og:site_name]").firstOrNull() // <meta> og:ste_name
            val appNameElement = doc.select("meta[property=al:android:app_name]")
                .firstOrNull() // <meta> al:android:app_name

            // Title
            val titleElement = doc.select("title").firstOrNull() // <title>
            val ogTitleElement =
                doc.select("meta[property=og:title]").firstOrNull() // <meta> og:title

            // Description
            val ogDescriptionElement =
                doc.select("meta[property=og:description]")
                    .firstOrNull() // <meta> og:description
            val descriptionElement =
                doc.select("meta[name=description]").firstOrNull() // <meta> description

            // Cover Image/Thumbnail
            val ogImageElement =
                doc.select("meta[property=og:image]").firstOrNull() // <meta> og:image

            // Logo/Icon
            val iconElement = doc.select("link[rel=icon]").firstOrNull() // <link> icon
            val shortcutIconElement =
                doc.select("link[rel=shortcut icon]").firstOrNull() // <link> shortcut icon
            val dataDefaultIconElement =
                doc.select("link[rel=data-default-icon]")
                    .firstOrNull() // <link> data-default-icon

            val siteName =
                ogSiteNameElement?.attr("content") ?: appNameElement?.attr("content") ?: ""
            val title =
                ogTitleElement?.attr("content") ?: titleElement?.text() ?: ""
            val desc =
                ogDescriptionElement?.attr("content")
                    ?: descriptionElement?.attr("content") ?: ""
            val image =
                ogImageElement?.attr("content")?.toHttpUrlOrNull()
            val icon =
                (iconElement?.attr("href")
                    ?: shortcutIconElement?.attr("href")
                    ?: dataDefaultIconElement?.attr("href"))?.toHttpUrlOrNull()

            logInfo(
                TAG,
                "url: $url\n" +
                        "site_name: $siteName\n" +
                        "title: $title\n" +
                        "desc: $desc\n" +
                        "image: $image\n" +
                        "icon: $icon"
            )
            LinkMetadataInfo(
                linkUrl = url,
                iconPath =
                    if (icon.isNull() || loadImageRequestIsSuccess(icon.toString()).not()) null
                    else icon.toString(),
                siteName = siteName,
                imagePath =
                    if (image.isNull() || loadImageRequestIsSuccess(image.toString()).not()) null
                    else image.toString(),
                title = title,
                desc = desc
            )
        }.getOrNull()
    }

    private suspend fun loadImageRequestIsSuccess(url: String): Boolean {
        val imageLoader = Coil.imageLoader(appContext)
        val request = ImageRequest.Builder(appContext)
            .data(url)
            .build()

        return when(imageLoader.execute(request)) {
            is SuccessResult -> true
            else -> false
        }
    }
}
