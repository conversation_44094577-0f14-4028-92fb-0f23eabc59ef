package com.interfun.buz.chat.common.ktx

import androidx.exifinterface.media.ExifInterface
import com.interfun.buz.common.ktx.getStringDefault
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.ktx.coverThumbUrl
import com.interfun.buz.im.ktx.imageUrl
import com.interfun.buz.im.ktx.videoUrl
import com.interfun.buz.im.message.BuzImageMessage
import com.interfun.buz.im.message.MediaTextMsg
import com.interfun.buz.media.bean.BuzMediaItem
import com.interfun.buz.media.bean.MediaType
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.model.IM5ImageMessage
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Author: ChenYouSheng
 * Date: 2024/7/29
 * Email: <EMAIL>
 * Desc:
 */
fun IMessage.asBuzMediaItem(): BuzMediaItem? {
    val message = this
    return when (content) {
        is IM5ImageMessage -> {
            val imageMessage = content as IM5ImageMessage
            val url = imageMessage.imageUrl()
            if (url.isNullOrEmpty()) {
                return null
            }
            val msgContent = content as BuzImageMessage
            val isLocalPathExist = msgContent.localPath?.let {
                File(it).exists()
            } ?: false
            BuzMediaItem(
                mediaId = msgId,
                mediaUri = if (isLocalPathExist) msgContent.localPath ?: "" else msgContent.remoteUrl ?: "",
                remoteUrl = msgContent.remoteUrl ?: "",
                thumbnailUrl = msgContent.thumbUrl ?: "",
                width = msgContent.imageWidth,
                height = msgContent.imageHeight,
                combineAesIv = aesComponent?.combineAesKeyAndIv(),
                type = MediaType.Image,
                orientation = msgContent.orientation,
                imMessage = message
            )
        }

        is IM5VideoMessage -> {
            val videoMessage = content as IM5VideoMessage
            val url = videoMessage.videoUrl()
            if (url.isNullOrEmpty()) {
                return null
            }
            BuzMediaItem(
                mediaId = msgId,
                mediaUri = videoMessage.videoUrl().getStringDefault(),
                remoteUrl = videoMessage.remoteUrl ?: "",
                thumbnailUrl = videoMessage.coverThumbUrl().getStringDefault(),
                hdCoverUrl = videoMessage.coverRemoteUrl ?: "",
                width = videoMessage.videoWidth,
                height = videoMessage.videoHeight,
                duration = videoMessage.duration,
                type = MediaType.Video,
                imMessage = message
            )
        }

        is MediaTextMsg -> {
            val msgContent = content as MediaTextMsg
            msgContent.mediaItem?.run {
                if (getSourceCoverUrl().isEmpty() || !(isImage() || isVideo())) {
                    return null
                }
                BuzMediaItem(
                    mediaId = msgId,
                    mediaUri = remoteUrl,
                    remoteUrl = remoteUrl,
                    thumbnailUrl = thumbnailUrl,
                    width = width,
                    height = height,
                    combineAesIv = aesComponent?.combineAesKeyAndIv(),
                    type = if (type == 1) MediaType.Image else MediaType.Video,
                    orientation = ExifInterface.ORIENTATION_NORMAL,
                    imMessage = message
                )
            }
        }

        else -> null
    }
}

suspend fun IMessage.contentExtra(): IMMessageContentExtra = withContext(Dispatchers.IO) {
    IMMessageContentExtra.parseFromJson(content.extra)
}
