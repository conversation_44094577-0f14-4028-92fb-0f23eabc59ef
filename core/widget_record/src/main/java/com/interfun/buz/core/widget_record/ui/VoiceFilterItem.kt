package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ContentTransform
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalViewConfiguration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.interfun.buz.base.ktx.calculateValue
import com.interfun.buz.base.ktx.isEmpty
import com.interfun.buz.base.ktx.isUrl
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.lottie.VoicePlayLottie
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.modifier.detectTapGesturesWithOutConsume
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig

/**
 * <AUTHOR>
 * @date 2024/12/4
 * @desc
 */
private const val recordBgPaddingMin = 6f / 60f
private const val recordBgPaddingMax = 8f / 140f

@Composable
fun VoiceFilterItem(
    isValidFilter: Boolean,
    isShowAIBg: Boolean,
    filterName: String,
    filterIcon: String,
    mainColor: Color,
    micColor: Color,
    showBadge: Boolean,
    fraction: Float = 1f,
    itemWidthMax: Dp = 116.dp,
    itemWidthMin: Dp = 60.dp,
    onClickItem: () -> Unit = {},
    onClickName: () -> Unit = {},
    isVoiceFilterPreviewing: () -> Boolean,
    isListScrollingLambda: () -> Boolean,
    modifier: Modifier = Modifier,
) {
    // SideEffect {
    //     RecomposeCountHelper.increment("VoiceFilterItem-$filterName")
    // }

    val itemWidth = calculateDp(start = itemWidthMin, end = itemWidthMax, progress = fraction)
    val itemAlpha = calculateValue(start = 0.5f, end = 1f, progress = fraction)

    val paddingFraction = recordBgPaddingMin + (recordBgPaddingMax - recordBgPaddingMin) * fraction
    val touchSlop = LocalViewConfiguration.current.touchSlop
    val config = LocalRecordUIConfig.current

    Column(
        modifier = modifier
            .width(itemWidth)
            .fillMaxHeight()
            .graphicsLayer {
                alpha = itemAlpha
            },
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .padding(bottom = 6.dp)
        ) {
            VoiceFilterName(
                isValidFilter = isValidFilter,
                filterName = filterName,
                mainColor = mainColor,
                showBadge = showBadge,
                fraction = fraction,
                modifier = Modifier
                    .align(alignment = Alignment.BottomCenter)
                    .pointerInput(Unit) {
                        detectTapGesturesWithOutConsume(touchSlop) {
                            onClickName.invoke()
                        }
                    },
                isListScrollingLambda = isListScrollingLambda,
                isVoiceFilterPreviewing = isVoiceFilterPreviewing
            )
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(itemWidthMax)
                .pointerInput(Unit) {
                    detectTapGesturesWithOutConsume(touchSlop) {
                        onClickItem.invoke()
                    }
                }
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = itemWidth * paddingFraction)
                    .aspectRatio(1f)
                    .align(Alignment.Center)
            ) {
                if (isValidFilter) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(CircleShape)
                            .background(color = mainColor)
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(CircleShape)
                            .background(color = config.voiceFilterNameBackgroundColor.asColor())
                            .graphicsLayer {
                                alpha = 1f - fraction
                            }
                    )
                    AnimatedContent(
                        modifier = Modifier
                            .fillMaxSize()
                            .graphicsLayer {
                                alpha = fraction
                            },
                        targetState = isShowAIBg,
                        transitionSpec = { ContentTransform(fadeIn(), fadeOut()) }
                    ) {
                        if (it) {
                            Image(
                                modifier = Modifier.fillMaxSize(),
                                painter = R.drawable.bg_record_btn_ai.asPainter(),
                                contentDescription = null
                            )
                        } else {
                            Image(
                                modifier = Modifier.fillMaxSize(),
                                painter = R.drawable.bg_record_btn_default.asPainter(),
                                contentDescription = null
                            )
                        }
                    }
                }
                IconFontText(
                    iconRes = R.string.ic_mic_open,
                    iconColor = micColor,
                    iconSize = 32.dp,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .graphicsLayer {
                            alpha = if (filterIcon.isEmpty()) {
                                1f
                            } else {
                                fraction
                            }
                        }
                )
                val alphaIcon = 1f - fraction
                if (isValidFilter) {
                    VoiceFilterIcon(
                        iconOrUrl = filterIcon,
                        iconSize = 24.dp,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .graphicsLayer {
                                alpha = alphaIcon
                            }
                    )
                } else {
                    IconFontText(
                        iconText = filterIcon,
                        iconSize = 24.dp,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .graphicsLayer {
                                alpha = alphaIcon
                            }
                    )
                }
            }
        }
        WeightSpace(1f)
    }
}

@Composable
fun VoiceFilterIcon(
    iconOrUrl: String,
    iconSize: Dp,
    modifier: Modifier = Modifier,
) {
    if (iconOrUrl.isEmpty()) return
    if (iconOrUrl.isUrl()) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(iconOrUrl)
                .size(iconSize.dpToPx().toInt())
                .build(),
            contentDescription = "VoiceFilterIcon",
            modifier = modifier.size(iconSize)
        )
    } else {
        Text(
            text = iconOrUrl,
            maxLines = 1,
            fontSize = iconSize.dpToSp(),
            modifier = modifier
        )
    }
}

@Composable
private fun VoiceFilterName(
    isValidFilter: Boolean,
    filterName: String,
    mainColor: Color,
    showBadge: Boolean,
    fraction: Float = 1f,
    modifier: Modifier = Modifier,
    isListScrollingLambda: () -> Boolean,
    isVoiceFilterPreviewing: () -> Boolean,
) {
    val canShowBadge = showBadge && (fraction < 1f || isListScrollingLambda())
    val currentTitleColor = lerp(
        start = mainColor,
        stop = R.color.color_text_white_tertiary.asColor(),
        fraction = 1f - fraction
    )
    val config = LocalRecordUIConfig.current
    Box(modifier = modifier.wrapContentSize()) {
        Box(
            modifier = Modifier
                .wrapContentSize()
                .clip(RoundedCornerShape(20.dp))
                .background(
                    color = config.voiceFilterNameBackgroundColor
                        .asColor()
                        .copy(alpha = fraction)
                )
                .padding(start = 8.dp * fraction, end = 8.dp * fraction)
        ) {
            if (isValidFilter) {
                VoicePlayLottie(
                    isPlaying = isVoiceFilterPreviewing(),
                    color = mainColor,
                    modifier = Modifier
                        .size(14.dp)
                        .align(Alignment.CenterStart)
                        .graphicsLayer { alpha = fraction }
                )
            }
            val startPadding = if (isValidFilter) 19.dp else 0.dp
            Text(
                text = filterName,
                color = currentTitleColor,
                style = TextStyles.labelMedium(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(
                        start = startPadding * fraction,
                        end = 0.dp,
                        top = 3.dp,
                        bottom = 3.dp
                    )
                    .graphicsLayer {
                        alpha = if (isValidFilter) 1f else fraction
                    }
            )
        }
        if (canShowBadge) {
            val offsetX = 4.dp + (-4).dp * (1 - fraction)
            val config = LocalRecordUIConfig.current
            Box(
                modifier = Modifier
                    .size(6.dp + 8.dp * fraction)
                    .align(Alignment.TopEnd)
                    .offset(x = offsetX, y = (-4).dp * fraction)
                    .border(
                        width = 4.dp * fraction,
                        color = config.bgColor.asColor(),
                        shape = CircleShape
                    )
                    .padding(4.dp * fraction)
                    .background(
                        color = R.color.color_foreground_consequential_default.asColor(),
                        shape = CircleShape
                    )
            )
        }
    }
}

@Composable
@Preview(heightDp = 220)
fun PreviewVoiceFilterItem() {
    LazyRow(
        contentPadding = PaddingValues(all = 10.dp),
        horizontalArrangement = Arrangement.spacedBy(5.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val list = (0..2).toList()
        items(items = list) {
            VoiceFilterItem(
                isValidFilter = true,
                isShowAIBg = false,
                filterName = "FilterName",
                filterIcon = String(Character.toChars(0x1F300)),
                mainColor = Color.Cyan,
                micColor = Color.Blue,
                showBadge = true,
                fraction = 1f - it / (list.size - 1).toFloat(),
                isListScrollingLambda = { true },
                isVoiceFilterPreviewing = { true },
            )
        }
    }
}

@Composable
@Preview
fun PreviewVoiceFilterName() {
    LazyColumn(
        contentPadding = PaddingValues(all = 10.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(5.dp),
    ) {
        val list = (0..9).toList()
        items(items = list) {
            VoiceFilterName(
                isValidFilter = true,
                filterName = "FilterName",
                mainColor = Color.Cyan,
                showBadge = true,
                fraction = 1f - it / (list.size - 1).toFloat(),
                isListScrollingLambda = { true },
                isVoiceFilterPreviewing = { true },
            )
        }
    }
}