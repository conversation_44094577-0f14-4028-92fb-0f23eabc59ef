package com.interfun.buz.common.widget.behavior

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.MotionEventCompat
import androidx.core.view.ViewCompat
import androidx.customview.widget.ViewDragHelper
import androidx.customview.widget.ViewDragHelper.Callback
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.R
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * @Desc 参考了 SwipeDismissBehavior 的思路实现页面上划退出
 * @Author:<EMAIL>
 * @Date: 2024/4/11
 */
class SwipeUpDismissBehavior<V:View>(context: Context, attrs: AttributeSet): CoordinatorLayout.Behavior<V>(context, attrs) {

    companion object{
        const val TAG = "SwipeUpDismissBehavior"

        fun <V : View> from(view: V): SwipeUpDismissBehavior<V> {
            val params = view.layoutParams
            require(params is CoordinatorLayout.LayoutParams) { "The view is not a child of CoordinatorLayout" }
            val behavior = params.behavior
            require(behavior is SwipeUpDismissBehavior<*>) { "The view is not associated with SwipeUpDismissBehavior" }
            return behavior as SwipeUpDismissBehavior<V>
        }
    }

    private var dragView: View? = null
    private var viewDragHelper: ViewDragHelper? = null
    var listener: OnDismissListener? = null
    private var interceptingEvents = false
    private val dragViewTag = R.string.common_tag_drag_view.asString()

    private var dragDismissThreshold = 0.5f
    private val mTouchSlop = ViewConfiguration.get(context).scaledTouchSlop
    private val autoFinishedVelocityLimit = 2000f
    private var downX = 0f
    private var downY = 0f
    override fun onLayoutChild(parent: CoordinatorLayout, child: V, layoutDirection: Int): Boolean {
        dragView = findDragView(child)
        return super.onLayoutChild(parent, child, layoutDirection)
    }

    private fun findDragView(view: View?): View? {
        if (view?.tag == dragViewTag) {
            return view
        }
        if (view is ViewGroup) {
            val group = view
            var i = 0
            val count = group.childCount
            while (i < count) {
                val scrollingChild = findDragView(group.getChildAt(i))
                if (scrollingChild != null) {
                    return scrollingChild
                }
                i++
            }
        }
        return null
    }

    private val dragCallback: Callback = object : Callback() {
        private val INVALID_POINTER_ID = -1
        private var originalCapturedViewBottom = 0
        private var activePointerId = INVALID_POINTER_ID

        override fun tryCaptureView(child: View, pointerId: Int): Boolean {
            // Only capture if we don't already have an active pointer id
            /*((activePointerId == INVALID_POINTER_ID || activePointerId == pointerId))*/
            return child == dragView
        }

        override fun onViewCaptured(capturedChild: View, activePointerId: Int) {
            this.activePointerId = activePointerId
            originalCapturedViewBottom = capturedChild.bottom
            // The view has been captured, and thus a drag is about to start so stop any parents
            // intercepting
            val parent = capturedChild.parent
            parent?.requestDisallowInterceptTouchEvent(true)
        }

        override fun onViewDragStateChanged(state: Int) {

        }

        override fun onViewReleased(child: View, xvel: Float, yvel: Float) {
            //log(TAG, "onViewReleased xvel = $xvel, yvel = $yvel")
            // Reset the active pointer ID
            activePointerId = INVALID_POINTER_ID
            val targetTop: Int
            var dismiss = false
            if (shouldDismiss(child, yvel)) {
                targetTop = -originalCapturedViewBottom
                dismiss = true
            } else {
                // Else, reset back to the original height
                targetTop = 0
            }
            //log(TAG, "onViewReleased targetTop = $targetTop, finalLeft = ${child.left}")
            if (viewDragHelper?.settleCapturedViewAt(child.left, targetTop) == true) {
                ViewCompat.postOnAnimation(child, SettleRunnable(child, dismiss))
            }
        }

        private fun shouldDismiss(child: View, yvel: Float): Boolean {
            val distance = child.bottom - originalCapturedViewBottom
            val thresholdDistance = Math.round(child.height * dragDismissThreshold)
            val swipeBackFactor = abs(distance.toDouble()) >= thresholdDistance
            return backJudgeBySpeed(yvel) || swipeBackFactor
        }

        private fun backJudgeBySpeed(yvel: Float): Boolean{
            return yvel < -autoFinishedVelocityLimit
        }

        override fun getViewHorizontalDragRange(child: View): Int {
            return 0
        }

        override fun getViewVerticalDragRange(child: View): Int {
            return child.height
        }

        override fun clampViewPositionHorizontal(child: View, left: Int, dx: Int): Int {
            //log(TAG, "clampViewPositionHorizontal left = $left, dx = $dx")
            return child.left
        }

        override fun clampViewPositionVertical(child: View, top: Int, dy: Int): Int {
            //log(TAG, "clampViewPositionVertical top = $top, dy = $dy")
            val min = -originalCapturedViewBottom
            val max = originalCapturedViewBottom - child.height
            return clamp(min, top, max)
        }

        override fun onViewPositionChanged(child: View, left: Int, top: Int, dx: Int, dy: Int) {
            //log(TAG, "onViewPositionChanged left = $left, top = $top, dx = $dx, dy = $dy")
        }
    }

    override fun onInterceptTouchEvent(
        parent: CoordinatorLayout, child: V, event: MotionEvent
    ): Boolean {
        if (dragView != null){
            when (MotionEventCompat.getActionMasked(event)) {
                MotionEvent.ACTION_DOWN -> {
                    downX = event.rawX
                    downY = event.rawY
                }
                MotionEvent.ACTION_MOVE -> if (parent.isPointInChildBounds(child, event.x.toInt(), event.y.toInt())) {
                    val distanceX: Float = abs(event.rawX - downX)
                    val distanceY: Float = abs(event.rawY - downY)
                    if (distanceX > mTouchSlop && distanceX > distanceY) {
                        return super.onInterceptTouchEvent(parent, child, event)
                    }
                }
            }
            ensureViewDragHelper(parent)
            return viewDragHelper!!.shouldInterceptTouchEvent(event)
        }
        return false
    }

    override fun onTouchEvent(parent: CoordinatorLayout, child: V, event: MotionEvent): Boolean {
        if (dragView != null && viewDragHelper != null) {
            viewDragHelper?.processTouchEvent(event)
            return true
        }
        return false
    }

    private fun ensureViewDragHelper(parent: ViewGroup) {
        if (viewDragHelper == null) {
            viewDragHelper = ViewDragHelper.create(parent, dragCallback)
        }
    }

    fun clamp(min: Float, value: Float, max: Float): Float {
        return min(max(min.toDouble(), value.toDouble()), max.toDouble()).toFloat()
    }

    fun clamp(min: Int, value: Int, max: Int): Int {
        return min(max(min.toDouble(), value.toDouble()), max.toDouble()).toInt()
    }

    /** The fraction that `value` is between `startValue` and `endValue`.  */
    fun fraction(startValue: Float, endValue: Float, value: Float): Float {
        return (value - startValue) / (endValue - startValue)
    }

    inner class SettleRunnable internal constructor(
        private val view: View,
        private val dismiss: Boolean
    ) :
        Runnable {
        override fun run() {
            if (viewDragHelper != null && viewDragHelper?.continueSettling(true) == true) {
                ViewCompat.postOnAnimation(view, this)
            } else {
                if (dismiss) {
                    listener?.onDismiss(view)
                }
            }
        }
    }

    interface OnDismissListener {
        /** Called when `view` has been dismissed via swiping.  */
        fun onDismiss(view: View)
    }
}