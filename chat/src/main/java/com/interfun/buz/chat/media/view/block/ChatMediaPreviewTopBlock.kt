package com.interfun.buz.chat.media.view.block

import android.Manifest.permission
import android.os.Build
import android.os.Bundle
import android.view.Gravity
import android.widget.Toast
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.ChatMsgType
import com.interfun.buz.chat.common.entity.ChatMsgType.Image
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.databinding.ChatMediaFragmentPreviewListBinding
import com.interfun.buz.chat.forward.ForwardData
import com.interfun.buz.chat.forward.fragment.ChatTargetListFragment
import com.interfun.buz.chat.forward.viewmodel.ChatTargetItem
import com.interfun.buz.chat.media.view.fragment.ChatMediaPreviewListFragment
import com.interfun.buz.chat.media.viewmodel.ChatMediaPreviewListViewModel
import com.interfun.buz.chat.media.viewmodel.DownloadState
import com.interfun.buz.chat.media.viewmodel.MediaDownloadViewModel
import com.interfun.buz.chat.privy.viewmodel.PrivateChatMsgViewModelNew
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.eventbus.album.UpdateAlbumListDataEvent
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.IBuzSharedService
import com.interfun.buz.common.widget.dialog.RoundBottomListDialog
import com.interfun.buz.common.widget.dialog.bottomlist.BackgroundType
import com.interfun.buz.common.widget.dialog.bottomlist.BottomListDialogOption
import com.interfun.buz.im.ktx.canForwardMsg
import com.interfun.buz.im.ktx.isReceive
import com.interfun.buz.im.ktx.isTextMessage
import com.interfun.buz.media.bean.MediaType
import com.interfun.buz.media.bean.MediaUpdatePayload
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.text.isEmpty

class ChatMediaPreviewTopBlock(
    val fragment: ChatMediaPreviewListFragment, binding: ChatMediaFragmentPreviewListBinding
) : BaseBindingBlock<ChatMediaFragmentPreviewListBinding>(binding) {

    private val mediaDownloadViewModel by fragment.viewModels<MediaDownloadViewModel>()
    private val previewListViewModel by fragment.viewModels<ChatMediaPreviewListViewModel>()
    private val privateChatMsgViewModelNew by fragment.fragmentViewModels<PrivateChatMsgViewModelNew>()

    private val dataFormat by lazy { SimpleDateFormat("M/dd/yyyy HH:mm", Locale.getDefault()) }
    private val buzMediaPlayer by lazy {
        fragment.obtainRegisterInterface2(IBuzMediaPlayerProvider::class.java)?.getBuzMediaPlayer()
    }

    private var roundBottomListDialog: RoundBottomListDialog? = null

    companion object {
        const val BOTTOM_DIALOG_TAG = "RoundBottomListDialog"
        const val TAG = "ChatMediaPreviewTopBlock"
    }

    override fun initData() {
        super.initData()

        binding.iftBack.click {
            fragment.onBackPressed()
        }
        handleMoreOption()
        observerDownloadState()
        observerItemChange()
        observeReportResult()
        onRestoreForwardTargetListCallBack()
    }

    private fun observerItemChange() {
        previewListViewModel.delayItem.collectIn(fragment) { selectItem ->
            if (selectItem == null) {
                return@collectIn
            }
            val message = selectItem.item.getMessage() ?: return@collectIn
            val dataStr = dataFormat.format(message.createTime)
            binding.tvMediaTime.text = dataStr
            binding.tvFromWhere.text =
                if (message.userInfo?.userId?.isMe() == true) {
                    appStringContext.getString(R.string.from_you)
                } else {
                    val userInfo = UserRelationCacheManager.getUserRelationFromMem(message.userInfo?.userId?.toLong().getLongDefault(-1))
                    val nickname = userInfo?.getContactFirstName() ?: message.userInfo?.nickName
                    appStringContext.getString(R.string.chat_media_from, nickname)
                }
        }

        previewListViewModel.notifyItemChangeFlow.collectIn(fragment) { (position, payload) ->
            buzMediaPlayer?.mAdapter?.notifyItemChanged(position, payload)
        }
        previewListViewModel.recallMsgListFlow.collectIn(fragment) { recallMsgList ->
            val convType = if (fragment.isPrivate) IM5ConversationType.PRIVATE else IM5ConversationType.GROUP

            val mediaItem = previewListViewModel.getCurrentMediaItem()
            val hasRecallCurrentItem = recallMsgList.findSafety {
                it.msgId == mediaItem?.mediaId  && convType == it.conversationType} != null
            if (hasRecallCurrentItem) {
                cancelSystemShare()
                hideMoreOptionDialog()
                toast(R.string.message_recalled)
            }
        }

    }

    private fun observeReportResult(){
        previewListViewModel.reportMsgResult.collectLatestIn(fragment.viewLifecycleOwner){
            if (it.isSuccess){
                toastSolidCorrect(R.string.report_sent)
            } else if (!it.codeRequestSuccess){
                toast(R.string.network_error.asString())
            }else{
                toast(R.string.operation_failed.asString())
            }
        }
    }

    private fun handleMoreOption() {
        binding.iftMore.click {
            ChatTracker.onClickChatMediaPreviewViewMore()
            showMoreOptionDialog()
        }
        buzMediaPlayer?.setOnViewLongClickListener {
            VibratorUtil.vibrator()
            showMoreOptionDialog()
        }
    }

    private fun showMoreOptionDialog() {
        if (fragment.isAdded.not() || fragment.isVisible.not()) return
        buzMediaPlayer?.getViewPager()?.isUserInputEnabled = false
        val isOfficial = routerServices<ChatService>().value?.isCurrentItemOfficial()
        roundBottomListDialog?.dismiss()
        val optionList =  arrayListOf<BottomListDialogOption>().apply {
            add(BottomListDialogOption(
                iconRes = R.string.ic_download,
                title = R.string.chat_media_save_to_photos.asString(),
                backgroundType = BackgroundType.OverlayGrey20
            ))
            val mediaItem = previewListViewModel.getCurrentMediaItem()
            if (mediaItem?.imMessage?.canForwardMsg() == true && isOfficial?.not() == true) {
                add(BottomListDialogOption(
                    iconRes = R.string.ic_forward_stroke, title = R.string.forward.asString(),
                    backgroundType = BackgroundType.OverlayGrey20
                ))
            }
            add(BottomListDialogOption(
                iconRes = R.string.ic_share, title = R.string.chat_media_share.asString(),
                backgroundType = BackgroundType.OverlayGrey20
            ))
            if (mediaItem?.imMessage?.isReceive == true && isOfficial?.not() == true) {
                add(BottomListDialogOption(
                    iconRes = R.string.ic_report_msg, title = R.string.report.asString(),
                    backgroundType = BackgroundType.OverlayGrey20
                ))
            }
        }
         RoundBottomListDialog.newInstance().let {
           roundBottomListDialog = it
            it.setData(
                dataList = optionList,
                onClickCallback = { position, option ->
                    when(option.title) {
                        R.string.chat_media_save_to_photos.asString() -> {
                            downloadMediaItem()
                        }
                        R.string.forward.asString() -> {
                            onForwoard()
                        }
                        R.string.chat_media_share.asString() -> {
                            startSystemShare()
                        }
                        R.string.report.asString() -> {
                            reportMedia()
                        }
                        else -> {}
                    }
                },
                onDismissCallback = {
                    buzMediaPlayer?.getViewPager()?.isUserInputEnabled = true
                }
            ).show(fragment.childFragmentManager, BOTTOM_DIALOG_TAG)
        }
    }

    private fun hideMoreOptionDialog() {
        roundBottomListDialog?.dismiss()
        roundBottomListDialog = null
    }

    private fun observerDownloadState() {
        mediaDownloadViewModel.downloadStateShareFlow.collect(fragment) {
            logDebug(
                TAG,
                "downloadStateShareFlow DownloadState==>${it.state}，progress=${it.progress}"
            )
            when (it.state) {
                DownloadState.PREPARE -> {
                    it.mediaItem.isDownloading = true
                    previewListViewModel.notifyMediaItemChange(
                        it.mediaItem,
                        MediaUpdatePayload.StartDownload
                    )
                }

                DownloadState.DOWNLOADING -> {
                }

                DownloadState.SUCCESS -> {
                    it.mediaItem.isDownloading = false
                    previewListViewModel.notifyMediaItemChange(
                        it.mediaItem,
                        MediaUpdatePayload.FinishDownload
                    )
                    toastSolidCorrect(R.string.chat_media_saved_msg.asString(), IconToastStyle.ICON_TOP_TEXT_BOTTOM)
                    UpdateAlbumListDataEvent.post()
                }

                DownloadState.ERROR -> {
                    it.mediaItem.isDownloading = false
                    previewListViewModel.notifyMediaItemChange(
                        it.mediaItem,
                        MediaUpdatePayload.FinishDownload
                    )
                    R.string.media_download_failed.toast()
                }
            }
        }
    }

    var shareJob: Job?=null

    /**
     * 取消正在执行的分享
     */
    fun cancelSystemShare(){
        if (shareJob?.isActive==true) {
            shareJob?.cancel()
          val activity  = fragment.requireActivity() as BaseActivity
            activity.hideDataLoading()
        }
        shareJob=null
    }
    private fun startSystemShare() {
        val mediaItem = previewListViewModel.getCurrentMediaItem() ?: return
        var mediaUri = mediaItem.remoteUrl.getStringDefault()
        if (mediaUri.isBlank()) {
            mediaUri =mediaItem.mediaUri
        }
        if (mediaUri.isEmpty()) {
            R.string.share_fail.asString().toast()
            return
        }
        val mimeType = if (mediaItem.type==MediaType.Video) "video/*" else "image/*"
        val activity = fragment.requireActivity() as BaseActivity
        if (mediaItem.isMediaUriLocalFile) {
            shareJob =  activity.lifecycleScope.launch {
                activity.showDataLoading(cancelableFlag = false)
               val ret = routerServices<IBuzSharedService>().value?.shareMedia(
                    activity,
                    mediaItem.mediaUri,
                    mimeType
                )
                //todo 根据ret错误类型提示
                activity.hideDataLoading()
            }
        } else {
            shareJob =  activity.lifecycleScope.launch {
                activity.showDataLoading(cancelableFlag = false)
                if (mediaItem.type==MediaType.Video){
                    routerServices<IBuzSharedService>().value?.shareVideoFromUrl(
                        activity,
                        mediaItem.mediaUri
                    )
                }else{
                    routerServices<IBuzSharedService>().value?.shareImageFromUrl(
                        activity,
                        mediaItem.mediaUri
                    )
                }
//                val ret = routerServices<IBuzSharedService>().value?.shareMedia(
//                    activity,
//                    mediaItem.mediaUri,
//                    mimeType
//                )
                //todo 根据ret错误类型提示
                activity.hideDataLoading()
            }
        }
        ChatTracker.onClickChatMediaPreviewShare(isVideo = mediaItem.type == MediaType.Video)
    }

    private fun reportMedia(){
        val mediaItem = previewListViewModel.getCurrentMediaItem() ?: return
        val msg = mediaItem.getMessage()?:return
        val msgType = when (mediaItem.type){
            MediaType.Video -> ChatMsgType.Video
            MediaType.Image -> Image
            else ->Image
        }
        previewListViewModel.reportMsg(msg,msgType)
    }

    private fun downloadMediaItem() {
        val mediaItem = previewListViewModel.getCurrentMediaItem() ?: return
        ChatTracker.onClickChatMediaPreviewDownload(isVideo = mediaItem.type == MediaType.Video)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mediaDownloadViewModel.downloadMedia(mediaItem)
        } else {
            if (isPermissionGranted(permission.WRITE_EXTERNAL_STORAGE)) {
                mediaDownloadViewModel.downloadMedia(mediaItem)
                return
            }
            val writeStoragePermissionInterceptor = fragment.getWriteStorageInterceptor()
            writeStoragePermissionInterceptor.setOnPermissionGrantedListener {
                mediaDownloadViewModel.downloadMedia(mediaItem)
            }
            writeStoragePermissionInterceptor.setOnPermissionDenyListener {
                toast(R.string.media_save_error)
            }

            writeStoragePermissionInterceptor.requestPermission()
        }
    }

    private fun onForwoard() {
        val imMessage = previewListViewModel.getCurrentMediaItem()?.imMessage ?: return
        ChatTargetListFragment.newInstance(ChatTargetListFragment.SOURCE_CHAT_PREVIEW_FORWARD)
            .setSendCallBack { list ->
                logDebug("the need forward list size: ${list.size}")
                onForwardMsg(imMessage, list)
            }
            .setExtraData(Bundle().apply {
                putLong(ForwardData.KEY_MSG_ID, imMessage.msgId)
                putString(ForwardData.KEY_TARGET_ID, imMessage.targetId)
                putString(ForwardData.KEY_IM_CONVERSATION_TYPE, imMessage.conversationType.name)
            })
//                    .enableMultiSelect(false) // 不支持多选,默认单选
            .setDismissImmediately(true) // 点击发送后立即关闭界面
            .setCheckAiState(!imMessage.isTextMessage) // 非文体消息转发时需要根据配置判断是否转发给AI
            .showDialog(fragment.childFragmentManager)
        ChatTracker.onClickAC2024062511()
    }

    private fun onForwardMsg(msg: IMessage, targetList: List<ChatTargetItem>) {
        privateChatMsgViewModelNew.forwardMessage(
            fragment.viewLifecycleOwner,
            msg,
            targetList,
        )
    }

    /**
     * 转发目标列表dialog被销毁恢复后，重新设置回调
     */
    private fun onRestoreForwardTargetListCallBack() {
        ChatTargetListFragment.onRestoreForwardTargetListCallBack(fragment) { msg, list ->
            onForwardMsg(msg, list)
        }
    }
}