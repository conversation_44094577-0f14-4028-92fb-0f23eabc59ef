package com.interfun.buz.download.utils

import android.content.Context
import android.os.Environment
import java.io.File

/**
 * Author: ChenYouSheng
 * Date: 2025/6/19
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
object FileUtils {

    fun getFilesDir(context: Context, dir: String, external: Boolean = true): File {
        return if (external && Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
            File(context.getExternalFilesDir(null), dir)
        } else {
            File(context.filesDir, dir)
        }.also {
            if (it.exists().not()) {
                it.mkdirs()
            }
        }
    }
}