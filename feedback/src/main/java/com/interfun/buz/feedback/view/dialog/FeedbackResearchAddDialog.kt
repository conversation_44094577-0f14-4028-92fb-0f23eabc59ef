package com.interfun.buz.feedback.view.dialog

import android.content.DialogInterface
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.eventbus.user.AddBuzAccountEvent
import com.interfun.buz.common.ktx.getHighLightWord
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.feedback.R
import com.interfun.buz.feedback.databinding.FeedbackDialogResearchAddBinding
import com.interfun.buz.feedback.utils.FeedbackTracker
import com.interfun.buz.feedback.viewmodel.FeedbackResearchViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FeedbackResearchAddDialog : BaseBottomSheetDialogFragment() {

    override val TAG = "FeedbackResearchAddDialog"
    private val viewModel by viewModels<FeedbackResearchViewModel>()
    private val noText = R.string.ic_correct_empty.asString()
    private val yesText = R.string.ic_correct_solid.asString()
    private val btnYesBg = R.drawable.common_r8_basic_primary.asDrawable()
    private val btnNoBg = R.drawable.common_r8_al30_basic_primary.asDrawable()
    init {
        canceledOnTouchOutside = false
    }

    companion object {
        fun newInstance(): FeedbackResearchAddDialog = FeedbackResearchAddDialog()
    }

    private val binding: FeedbackDialogResearchAddBinding by lazy {
        FeedbackDialogResearchAddBinding.inflate(layoutInflater)
    }

    override fun onCreateView(): View {
        return binding.root
    }

    override fun initView(view: View?) {
        binding.btnAccept.background = btnNoBg
        val content = getHighLightWord(
            R.string.feedback_research_limit_adult_tip.asString(),
            R.string.feedback_buz_research_account.asString(),
            R.color.text_white_main
        )
        binding.tvContent.text = content

        val title = R.string.common_clap.asString() + " " + R.string.feedback_introduce_buz_research_team.asString()
        binding.tvTitle.text = title
    }

    override fun initListener(view: View?) {
        combineView(binding.tvDismiss,binding.vDismissClick).click {
            if (viewModel.loadingFlow.value) return@click
            viewModel.reportInterviewInviteResult(false)
            dismiss()
        }

        binding.vConfirmAgeClick.click {
            if (viewModel.loadingFlow.value) return@click
            val text = if (isUseOlderThan18()) noText else yesText
            val btnBg = if (isUseOlderThan18()) btnNoBg else btnYesBg
            val textColor =
                if (isUseOlderThan18()) R.color.text_white_main.asColor() else R.color.basic_primary.asColor()
            binding.iftvYes.text = text
            binding.iftvYes.setTextColor(textColor)
            binding.btnAccept.background = btnBg
        }

        binding.btnAccept.click {
            if (!isUseOlderThan18() || viewModel.loadingFlow.value) return@click
            viewModel.reportInterviewInviteResult(true)
            FeedbackTracker.onUserAcceptResearchClick()
        }

        viewModel.loadingFlow.collectIn(this) {
            if (it) {
                binding.btnAccept.showLoading()
            } else {
                binding.btnAccept.hideLoading()
            }
        }

        viewModel.addResearchMemberResult.collectIn(this){
            if (it == null){ return@collectIn }
            AddBuzAccountEvent(it).post()
            FeedbackResearchFinishDialog.newInstance().showDialog(activity)
            dismiss()
        }
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        logInfo(TAG,"onCancel: ")
        viewModel.reportInterviewInviteResult(false)
    }

    private fun isUseOlderThan18(): Boolean {
        return binding.iftvYes.text == yesText
    }
}