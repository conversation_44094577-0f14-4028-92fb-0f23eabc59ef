package com.interfun.buz.chat.wt.service

import android.app.Notification
import android.widget.RemoteViews
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean

enum class NotificationInfoType {
    GROUP,
    USER
}

interface NotificationBaseInfo {
    val type: NotificationInfoType
    val lastConvTime: Long?
    val targetId: Long
    val voiceMoji:String?
    val isGroup get() = type == NotificationInfoType.GROUP
    val isUser get() = type == NotificationInfoType.USER
    var isSpeaking: Boolean
    fun dispatch(
        group: OneParamCallback<NotificationGroupInfo>? = null,
        user: OneParamCallback<NotificationUserInfo>? = null
    ) {
        when (type) {
            NotificationInfoType.USER -> user?.invoke(this as NotificationUserInfo)
            NotificationInfoType.GROUP -> group?.invoke(this as NotificationGroupInfo)
        }
    }

    fun <T> transForm(
        group: OneParamOneReturnCallback<NotificationGroupInfo, T>,
        user: OneParamOneReturnCallback<NotificationUserInfo, T>
    ): T {
        return when (type) {
            NotificationInfoType.USER -> user.invoke(this as NotificationUserInfo)
            NotificationInfoType.GROUP -> group.invoke(this as NotificationGroupInfo)
        }
    }
}

class NotificationUserInfo(
    val user: UserRelationInfo,
    override val voiceMoji:String?,
    override val lastConvTime: Long?) :
    NotificationBaseInfo {
    override val type = NotificationInfoType.USER
    override val targetId: Long = user.userId
    override var isSpeaking = false

    override fun toString(): String {
        return "isGroup:${isGroup},targetId:${targetId}"
    }
}

class NotificationGroupInfo(
    val groupInfo: GroupInfoBean,
    val lastMsgUser: UserRelationInfo?,
    override val voiceMoji:String?,
    override val lastConvTime: Long?
) : NotificationBaseInfo {
    override val targetId: Long = groupInfo.groupId
    override val type = NotificationInfoType.GROUP
    override var isSpeaking = false
    override fun toString(): String {
        return "isGroup:${isGroup},targetId:${targetId}"
    }
}

class ForegroundNotificationArgs(
    val recentChats: List<NotificationBaseInfo>?
) {
    override fun toString(): String {
        return "recentChats:${recentChats?.joinToString()}"
    }
}

class NotificationWidget(
    val notification: Notification,
    val collapsedView: RemoteViews,
    val expandedView: RemoteViews
)

interface BitmapKey {
    val targetId: Long
}

data class UserBitmapKey(override val targetId: Long, val url: String) : BitmapKey
data class GroupBitmapKey(override val targetId: Long, val groupInfo: GroupInfoBean) : BitmapKey