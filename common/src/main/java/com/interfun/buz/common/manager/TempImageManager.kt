package com.interfun.buz.common.manager

import android.graphics.Bitmap
import com.interfun.buz.base.ktx.application
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import java.io.File
import java.io.FileOutputStream

object TempImageManager {
    private val TAG = "TempImageManager"
    val TEMP_IMAGE_SAVE_PATH = "image"
    val TEMP_IMAGE_CROP_PATH = "crop"

    fun saveBitmapInCache(bitmap: Bitmap, folder: String = TEMP_IMAGE_SAVE_PATH): String? {
        val dir = File(application.cacheDir, folder)
        if (dir.exists().not()) dir.mkdirs()
        val imageFile = File(dir, "${System.currentTimeMillis()}.jpg")
        val outputStream = FileOutputStream(imageFile)
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
        try {
            outputStream.flush()
        } catch (e: Exception) {
            e.printStackTrace()
            logError(TAG, "createVideoThumb: Exception")
            return null
        } finally {
            outputStream.close()
        }
        logInfo(TAG, "createVideoThumb: ${imageFile.absolutePath}")

        return imageFile.path
    }

    fun deleteFile(path: String) {
        val file = File(path)
        if (file.exists()) {
            val result = file.delete()
            logInfo(TAG, "deleteFile: $result")
        }
    }

    /**
     * 应该这某个具体的时间，把文件夹清除
     */
    fun deleteAll(folder: String = TEMP_IMAGE_SAVE_PATH) {
        val dir = File(application.cacheDir, folder)
        dir.walkBottomUp().forEach {
            it.delete()
        }
    }

    fun getTempImageTotalSize(folder: String = TEMP_IMAGE_SAVE_PATH): Long {
        val dir = File(application.cacheDir, folder)
        return dir.walkTopDown().filter {
            it.isFile
        }.map {
            it.length()
        }.sum()
    }

}