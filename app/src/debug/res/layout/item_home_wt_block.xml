<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="28dp"
    android:background="@drawable/common_secondary_background"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent">

    <com.interfun.buz.demo.homepage.widget.HomeWTListRecyclerView
        android:id="@+id/rvWtList"
        android:layout_width="0dp"
        android:layout_height="186dp"
        android:clipChildren="false"
        android:orientation="horizontal"
        android:overScrollMode="never"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_wt_user_demo" />

    <com.interfun.buz.demo.homepage.widget.HomeWTMessageRecyclerView
        android:id="@+id/rvMessageBlock"
        android:layout_width="0dp"
        android:layout_height="126dp"
        android:layout_marginTop="16dp"
        android:clipChildren="false"
        android:orientation="horizontal"
        android:overScrollMode="never"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/rvWtList"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_wt_message_demo"/>


</androidx.constraintlayout.widget.ConstraintLayout>