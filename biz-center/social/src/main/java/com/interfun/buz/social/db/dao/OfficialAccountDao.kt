package com.interfun.buz.social.db.dao

import androidx.room.*
import com.interfun.buz.social.db.entity.BuzOfficialAccountExtra
import com.interfun.buz.social.db.entity.MyOfficialAccount

@Dao
interface OfficialAccountDao {

    @Query("select * from buz_official_account_extra where userId = :userId")
    fun queryOfficialExtra(userId: Long): BuzOfficialAccountExtra?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun updateExtra(extra: BuzOfficialAccountExtra)

    @Query("select userId from my_official_account order by `index`")
    fun getAllMyOfficialAccountIds(): List<Long>

    @Query("delete from my_official_account")
    fun deleteAllMyOfficialAccount()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertMyOfficialAccount(myOfficialAccount: List<MyOfficialAccount>)

    @Transaction
    fun replaceAllMyOfficialAccountIds(ids: List<Long>) {
        deleteAllMyOfficialAccount()
        insertMyOfficialAccount(ids.mapIndexed { index, userId ->
            MyOfficialAccount(
                userId,
                index
            )
        })
    }
}