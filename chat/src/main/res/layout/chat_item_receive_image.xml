<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:paddingVertical="@dimen/chat_item_between_same_padding_top"
    tools:background="@color/overlay_grey_10"
    tools:paddingHorizontal="@dimen/chat_list_horizontal_padding">

    <TextView
        style="@style/caption"
        android:id="@+id/tvGroupMemberName"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:paddingHorizontal="6dp"
        android:gravity="center_vertical"
        android:background="@drawable/common_r10_overlay_white_6"
        android:textColor="@color/text_white_secondary"
        android:visibility="gone"
        android:maxLines="1"
        android:maxWidth="200dp"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="@id/flImage"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/replyView"
        tools:layout_marginTop="@dimen/chat_item_between_different_padding_top"
        tools:visibility="visible"
        tools:text="Anna" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/flImage"
        app:layout_constraintEnd_toEndOf="@+id/flImage"
        app:layout_constraintStart_toStartOf="@+id/flImage"
        app:layout_constraintTop_toTopOf="@+id/flImage" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="@dimen/chat_item_portrait_size"
        android:layout_height="@dimen/chat_item_portrait_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/flImage"
        app:layout_constraintStart_toStartOf="parent"
        tools:src="@drawable/common_user_default_portrait_round"
        tools:visibility="visible" />

    <com.interfun.buz.chat.common.view.widget.ReplyItemView
        android:layout_width="wrap_content"
        android:id="@+id/replyView"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/chat_dimen_reply_height_at_history_Bottom"
        android:paddingTop="@dimen/chat_dimen_reply_height_at_history_top"
        app:layout_constraintWidth_min="@dimen/chat_dimen_mim_width_reply"
        android:layout_marginEnd="@dimen/chat_item_receive_margin_end_with_portrait"
        app:layout_constrainedWidth="true"
        android:background="@color/color_background_1_default"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/flImage"
        app:layout_constraintTop_toBottomOf="@id/tvGroupMemberName"
        app:round_top_left_radius="@dimen/chat_item_bg_radius"
        app:layout_goneMarginTop="0dp"
        android:layout_marginTop="@dimen/chat_item_between_same_padding_top"
        app:round_top_right_radius="@dimen/chat_item_bg_radius"
        app:layout_constraintHorizontal_bias="0"
        app:round_bottom_right_radius="@dimen/chat_item_bg_radius"/>

    <com.interfun.buz.base.widget.round.RoundFrameLayout
        android:id="@+id/flImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/chat_item_receive_margin_start_with_portrait"
        android:layout_marginEnd="@dimen/chat_item_receive_margin_end_with_portrait"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/replyView"
        app:layout_goneMarginStart="0dp"
        android:layout_marginTop="@dimen/chat_dimen_reply_height_at_history_diff"
        app:layout_goneMarginTop="0dp"
        app:round_bottom_end_radius="@dimen/chat_item_bg_radius"
        app:round_bottom_start_radius="@dimen/chat_item_bg_radius_min"
        app:round_top_end_radius="@dimen/chat_item_bg_radius"
        app:round_top_start_radius="@dimen/chat_item_bg_radius_min"
        tools:layout_height="120dp"
        tools:layout_width="160dp">

        <ImageView
            android:id="@+id/ivImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="centerCrop"
            tools:layout_height="120dp"
            tools:layout_width="160dp"
            tools:src="@drawable/chat_item_receive_image_default_bg" />

    </com.interfun.buz.base.widget.round.RoundFrameLayout>

    <View
        android:id="@+id/vBottomBg"
        android:layout_width="0dp"
        android:layout_height="22dp"
        app:layout_constraintTop_toTopOf="@id/spaceContent"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="@id/spaceContent"
        android:layout_marginStart="10dp"
        app:layout_constraintEnd_toEndOf="@id/tvLeftSize"
        android:layout_marginEnd="-6dp"
        android:background="@drawable/common_rect_overlay_mask_raidus_22"
        />

    <View
        android:id="@+id/glStateStart"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toStartOf="@id/vBottomBg"
        android:layout_marginStart="6dp"
        android:visibility="invisible"/>

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvDownload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ic_download"
        android:textColor="@color/text_white_important"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@id/glStateStart"
        app:layout_constraintTop_toTopOf="@id/vBottomBg"
        app:layout_constraintBottom_toBottomOf="@id/vBottomBg"
       />


    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/cpiDownloadProgress"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:progress="0"
        android:indeterminate="true"
        app:indicatorSize="14dp"
        app:trackColor="@color/transparent"
        app:indicatorColor="@color/text_white_important"
        app:trackThickness="2dp"
        app:circularflow_defaultRadius="2dp"
        app:layout_constraintStart_toStartOf="@id/glStateStart"
        app:layout_constraintTop_toTopOf="@id/vBottomBg"
        app:layout_constraintBottom_toBottomOf="@id/vBottomBg"
        tools:visibility="invisible"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/iconBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="iftvDownload,cpiDownloadProgress"
        />

    <TextView
        android:id="@+id/tvLeftSize"
        style="@style/text_body_small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="24M"
        android:textColor="@color/color_text_white_important"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/iconBarrier"
        android:layout_marginStart="2dp"
        app:layout_constraintTop_toTopOf="@id/vBottomBg"
        app:layout_constraintBottom_toBottomOf="@id/vBottomBg"
        android:layout_marginEnd="10dp"/>



</com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout>