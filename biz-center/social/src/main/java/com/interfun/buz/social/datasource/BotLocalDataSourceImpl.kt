package com.interfun.buz.social.datasource

import androidx.collection.LruCache
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.interfun.buz.base.coroutine.withReentrantLock
import com.interfun.buz.base.ktx.and
import com.interfun.buz.base.ktx.awaitInScopeIO
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.social.db.entity.BotExtra.BotStatus
import com.interfun.buz.common.utils.fromJson
import com.interfun.buz.common.utils.gsonInstance
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.db.dao.BotDao
import com.interfun.buz.social.db.entity.*
import com.interfun.buz.social.di.SocialUserQualifier
import com.interfun.buz.social.storage.SocialDataStoreKey
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withContext
import javax.inject.Inject

internal class BotLocalDataSourceImpl @Inject constructor(
    @UserQualifier private val botDao: BotDao,
    @UserQualifier private val userScope: CoroutineScope,
    @SocialUserQualifier private val userDataStore: DataStore<Preferences>,
) : BotLocalDataSource {

    private val botInfoLruCache = LruCache<Long, BotExtra>(3000)
    private val botInfoUpdateFlow = MutableSharedFlow<Set<Long>>()
    private val botSettingLruCache = LruCache<Long, BotWholeSettingEntity>(3000)
    private val botSettingUpdateFlow = MutableSharedFlow<Long>()

    private val botInfoMutex = Mutex()
    private val botSettingMutex = Mutex()

    companion object {
        private const val TAG = "BotLocalDataSource"
    }

    override suspend fun getBotExtra(botUserId: Long): BotExtra? {
        return botInfoLruCache[botUserId] ?: queryBotInfoFromDb(botUserId)
    }

    @Deprecated("use getBotExtra instead")
    override fun getBotExtraFromMemory(botUserId: Long): BotExtra? {
        return botInfoLruCache[botUserId]
    }

    override fun loadAllBotExtraFromCache() {
        userScope.launch {
            queryAllBotFromDB()
        }
    }

    override fun loadAllBotSettingsFromCache() {
        userScope.launch {
            queryAllBotSettingsFromDb()
        }
    }

    override suspend fun getBotExtra(botUserIds: List<Long>): List<BotExtra?> {
        if (botUserIds.isEmpty()) {
            return emptyList()
        }
        if (botUserIds.size == 1) {
            return listOf(getBotExtra(botUserIds[0]))
        }
        //尽量复用缓存，没有缓存的在批量从数据库查，提升性能
        val memoryCacheList = arrayOfNulls<BotExtra>(botUserIds.size)
        //这个map的作用是为了保证返回的数据跟查询的用户顺序对应
        var notCachedMap: MutableMap<Long, Int>? = null
        botUserIds.forEachIndexed { index, id ->
            val user = botInfoLruCache[id]
            memoryCacheList[index] = user
            if (user == null) {
                if (notCachedMap == null) {
                    notCachedMap = HashMap()
                }
                notCachedMap!!.put(id, index)
            }
        }
        val map = notCachedMap
        if (map != null) {
            val notCachedList = map.keys.toList()
            queryBotInfoFromDb(notCachedList)?.forEach {
                val index = map[it.botUserId]
                memoryCacheList[index!!] = it
            }
        }
        return memoryCacheList.toList()
    }

    override fun getBotExtraFlow(botUserId: Long): Flow<BotExtra?> =
        botInfoUpdateFlow.onSubscription { emit(hashSetOf(botUserId)) }
            .filter { it.contains(botUserId) }
            .mapLatest { getBotExtra(botUserId) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.Default)

    override fun getBotExtraFlow(botUserIds: List<Long>): Flow<Map<Long, BotExtra>> =
        channelFlow {
            val botMap = HashMap<Long, BotExtra>(botUserIds.size, 1f)
            val userIdSet = botUserIds.toSet()
            val updateFlow = botInfoUpdateFlow.mapNotNull {
                val intersectionSet = it and userIdSet
                intersectionSet.ifEmpty { null }
            }.shareIn(this, SharingStarted.WhileSubscribed(5000))
            updateFlow.onSubscription {
                emit(userIdSet)
            }.collect { updatedSet ->
                val userIdList = updatedSet.toList()
                val botList = getBotExtra(userIdList)
                botList.forEach {
                    if (it != null) {
                        botMap[it.botUserId] = it
                    }
                }
                send(HashMap(botMap))
            }
        }

    override suspend fun updateBotExtra(botInfo: BotExtra): BotExtra {
        return awaitInScopeIO(userScope) {
            botInfoMutex.withReentrantLock {
                botDao.insertBotInfo(botInfo)
                botInfoLruCache.put(botInfo.botUserId, botInfo)
                botInfo
            }.also {
                botInfoUpdateFlow.emit(hashSetOf(botInfo.botUserId))
            }
        }
    }

    override suspend fun updateBotExtraList(botInfoList: List<BotExtra>): List<BotExtra> {
        return awaitInScopeIO(userScope) {
            botInfoMutex.withReentrantLock {
                botDao.insertBotInfoList(botInfoList)
                botInfoList.forEach {
                    botInfoLruCache.put(it.botUserId, it)
                }
                botInfoList
            }.also {
                botInfoUpdateFlow.emit(botInfoList.mapTo(hashSetOf()) { it.botUserId })
            }
        }
    }

    override suspend fun updateBotStatus(botUserId: Long, botStatus: BotStatus) {
        return awaitInScopeIO(userScope) {
            botInfoMutex.withReentrantLock {
                botDao.insertOrUpdateBotStatus(botUserId, botStatus)
                botInfoLruCache.get(botUserId)?.let { botInfo ->
                    botInfoLruCache.put(botUserId, botInfo.copy(status = botStatus))
                }
            }
            botInfoUpdateFlow.emit(hashSetOf(botUserId))
        }
    }

    override fun getMarketBotListFlow(): Flow<List<Long>?> {
        return userDataStore.data.map { preferences ->
            val dataStr = preferences[SocialDataStoreKey.aiMarketUserListKey]
            dataStr?.fromJson<List<Long>>()
        }.flowOn(Dispatchers.Default)
    }

    override fun getHasNewBotInMarketFlow(): Flow<Boolean> {
        return userDataStore.data.map { preferences ->
            preferences[SocialDataStoreKey.hasNewBotInMarket] ?: false
        }
    }

    override suspend fun clearNewBotInMarketFlag() {
        userDataStore.edit { preferences ->
            preferences[SocialDataStoreKey.hasNewBotInMarket] = false
        }
    }

    override suspend fun updateMarketBotList(botUserIdList: List<Long>) =
        withContext(Dispatchers.Default) {
            val oldMarketBotIdList = getMarketBotListFlow().first()?.toHashSet()
            val hasNewBot = if (oldMarketBotIdList != null) {
                botUserIdList.any { !oldMarketBotIdList.contains(it) }
            } else {
                true
            }
            userDataStore.edit { preferences ->
                try {
                    if (hasNewBot) {
                        preferences[SocialDataStoreKey.hasNewBotInMarket] = true
                    }
                    preferences[SocialDataStoreKey.aiMarketUserListKey] =
                        gsonInstance.toJson(botUserIdList)
                } catch (t: Throwable) {
                    logError(TAG, t, "encode aiMarketUserListKey error in updateMarketBotList")
                }
            }
            Unit
        }

    override suspend fun getBotSettings(botUserId: Long): BotWholeSettingEntity? {
        return botSettingLruCache[botUserId] ?: queryBotSettingsFromDb(botUserId)
    }

    @Deprecated("use getBotSettings instead")
    override fun getBotSettingsFromMemory(botUserId: Long): BotWholeSettingEntity? {
        return botSettingLruCache[botUserId]
    }

    override fun getBotSettingsFlow(botUserId: Long): Flow<BotWholeSettingEntity?> =
        botSettingUpdateFlow.onSubscription { emit(botUserId) }
            .filter { it == botUserId }
            .mapLatest { getBotSettings(botUserId) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.Default)

    override suspend fun updateBotSettings(
        botUserId: Long,
        languageCode: String?,
        voiceStyleId: Long?,
        sourceLanguage: String?,
        targetLanguage: String?,
    ): BotWholeSettingEntity? {
        return awaitInScopeIO(userScope) {
            val settings = ArrayList<BotSingleSettingEntity>()
            if (languageCode != null) {
                settings.add(
                    BotSingleSettingEntity(
                        botUserId,
                        BotSettingKeyMap.languageCode.first,
                        languageCode
                    )
                )
            }
            if (voiceStyleId != null) {
                settings.add(
                    BotSingleSettingEntity(
                        botUserId,
                        BotSettingKeyMap.voiceStyleId.first,
                        voiceStyleId.toString()
                    )
                )
            }
            if (sourceLanguage != null) {
                settings.add(
                    BotSingleSettingEntity(
                        botUserId,
                        BotSettingKeyMap.sourceLanguage.first,
                        sourceLanguage
                    )
                )
            }
            if (targetLanguage != null) {
                settings.add(
                    BotSingleSettingEntity(
                        botUserId,
                        BotSettingKeyMap.targetLanguage.first,
                        targetLanguage
                    )
                )
            }
            botDao.insertBotSingleSettings(settings)
            //里面刷新内存
            val newSetting = queryBotSettingsFromDb(botUserId)
            botSettingUpdateFlow.emit(botUserId)
            if (sourceLanguage != null && sourceLanguage != TranslatorLanguageItem.AUTO_LANGUAGE_CODE) {
                updateNewlyUsedTranslatorLanguage(botUserId, sourceLanguage)
            }
            if (targetLanguage != null && targetLanguage != TranslatorLanguageItem.AUTO_LANGUAGE_CODE) {
                updateNewlyUsedTranslatorLanguage(botUserId, targetLanguage)
            }
            newSetting
        }
    }

    override suspend fun switchSourceAndTargetLanguage(botUserId: Long): BotWholeSettingEntity? {
        return awaitInScopeIO(userScope) {
            botDao.switchSourceAndTargetLanguage(botUserId)
            val newSetting = queryBotSettingsFromDb(botUserId)
            botSettingUpdateFlow.emit(botUserId)
            newSetting
        }
    }

    override fun getRecentlyUsedTranslatorLanguage(botUserId: Long): Flow<List<String>?> {
        return userDataStore.data.map { preferences ->
            val dataStr =
                preferences[SocialDataStoreKey.recentlyUsedTranslatorLanguageKey(botUserId)]
            val storedList = dataStr?.fromJson<List<String>>()
            if (storedList.isNullOrEmpty()) {
                val aiSettings = getBotSettings(botUserId)
                val languageBean = getBotExtra(botUserId)?.getTranslatorLanguageOrDefault(
                    aiSettings?.sourceLanguage,
                    aiSettings?.targetLanguage
                )
                if (languageBean == null) {
                    return@map storedList
                }
                val defaultList = ArrayList<String>(2)
                if (languageBean.sourceLanguage.code != TranslatorLanguageItem.AUTO_LANGUAGE_CODE) {
                    defaultList.add(languageBean.sourceLanguage.code)
                }
                if (languageBean.targetLanguage.code != TranslatorLanguageItem.AUTO_LANGUAGE_CODE) {
                    defaultList.add(languageBean.targetLanguage.code)
                }
                return@map defaultList
            }
            return@map storedList
        }.flowOn(Dispatchers.Default)
    }

    private suspend fun updateNewlyUsedTranslatorLanguage(botUserId: Long, languageCode: String) {
        userDataStore.edit { preferences ->
            val oldLanguageList =
                try {
                    preferences[SocialDataStoreKey.recentlyUsedTranslatorLanguageKey(botUserId)]?.fromJson<List<String>>()
                } catch (t: Throwable) {
                    logError(
                        TAG,
                        t,
                        "decode recentlyUsedTranslatorLanguageKey error in updateNewlyUsedTranslatorLanguage"
                    )
                    null
                }
            if (oldLanguageList?.toHashSet()?.contains(languageCode) == true) {
                //去重
                return@edit
            }
            val botExtra = getBotExtra(botUserId)
            val serverLanguageSet = hashSetOf<String>()
            botExtra?.options?.translateTargetLanguage?.forEach {
                serverLanguageSet.add(it.languageCode)
            }
            botExtra?.options?.translateSourceLanguage?.forEach {
                serverLanguageSet.add(it.languageCode)
            }
            val newList = ArrayList<String>()
            newList.add(languageCode)
            if (oldLanguageList != null) {
                var addedSize = 0
                for (language in oldLanguageList) {
                    if (addedSize >= 3) {
                        //最多4个，所以从旧的里面最多取3个
                        break
                    }
                    //serverLanguageSet.isEmpty()是为了怕本地没数据的情况，不会存储
                    if (serverLanguageSet.isEmpty() || serverLanguageSet.contains(language)) {
                        //如果是服务器支持的语言，才添加
                        newList.add(language)
                        addedSize++
                    }
                }
            }
            preferences[SocialDataStoreKey.recentlyUsedTranslatorLanguageKey(botUserId)] =
                gsonInstance.toJson(newList)
        }
    }

    override suspend fun deleteBotInfo(botUserId: Long) {
        awaitInScopeIO(userScope) {
            botInfoMutex.withReentrantLock {
                botDao.deleteBotInfo(botUserId)
                botInfoLruCache.remove(botUserId)
            }
            botInfoUpdateFlow.emit(hashSetOf(botUserId))
            botSettingMutex.withReentrantLock {
                botDao.deleteBotSingleSettings(botUserId)
                botSettingLruCache.remove(botUserId)
            }
            botSettingUpdateFlow.emit(botUserId)
        }
    }

    private suspend fun queryBotInfoFromDb(botUserIds: List<Long>): List<BotExtra>? {
        return awaitInScopeIO(userScope) {
            botInfoMutex.withReentrantLock {
                botDao.queryBotInfo(botUserIds)?.apply {
                    this.forEach { botInfoLruCache.put(it.botUserId, it) }
                }
            }
        }
    }

    private suspend fun queryAllBotFromDB() {
        awaitInScopeIO(userScope) {
            botInfoMutex.withReentrantLock {
                val botInfoList = botDao.queryAllBotInfo()
                botInfoList?.forEach { botInfoLruCache.put(it.botUserId, it) }
            }
        }
    }

    private suspend fun queryBotInfoFromDb(botUserId: Long): BotExtra? {
        return awaitInScopeIO(userScope) {
            botInfoMutex.withReentrantLock {
                botDao.queryBotInfo(botUserId)?.apply {
                    botInfoLruCache.put(botUserId, this)
                }
            }
        }
    }

    private suspend fun queryAllBotSettingsFromDb() {
        awaitInScopeIO(userScope) {
            botSettingMutex.withReentrantLock {
                val botSettingsList = botDao.queryAllBotSingleSettings()
                val settingMap = hashMapOf<Long, MutableList<BotSingleSettingEntity>>()
                botSettingsList?.forEach {
                    val list = settingMap[it.botUserId]
                    if (list == null) {
                        settingMap[it.botUserId] = mutableListOf(it)
                    } else {
                        list.add(it)
                    }
                }
                settingMap.forEach { (userId, settings) ->
                    val result = if (settings.isNotEmpty()) {
                        BotWholeSettingEntity.build(userId, settings)
                    } else {
                        null
                    }
                    result?.let {
                        botSettingLruCache.put(userId, it)
                    }
                }
            }
        }
    }

    private suspend fun queryBotSettingsFromDb(botUserId: Long): BotWholeSettingEntity? {
        return awaitInScopeIO(userScope) {
            botSettingMutex.withReentrantLock {
                val singleSettings = botDao.queryBotSingleSettings(botUserId)
                val result = if (singleSettings.isNotEmpty()) {
                    BotWholeSettingEntity.build(botUserId, singleSettings)
                } else {
                    null
                }
                result?.let {
                    botSettingLruCache.put(botUserId, it)
                }
                result
            }
        }
    }
} 