<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/black_100"
    android:id="@+id/clRoot"
    android:paddingTop="20dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/viewTalking"
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:src="@drawable/chat_shape_talking"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />


    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/portraitImageView"
        android:layout_width="130dp"
        android:layout_height="130dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewTalking"
        app:layout_constraintEnd_toEndOf="@+id/viewTalking"
        app:layout_constraintStart_toStartOf="@+id/viewTalking"
        app:layout_constraintTop_toTopOf="@+id/viewTalking"
        />

    <TextView
        android:id="@+id/tvRejected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/reject"
        style="@style/main_body"
        app:layout_constraintBottom_toBottomOf="@+id/portraitImageView"
        app:layout_constraintEnd_toEndOf="@+id/portraitImageView"
        app:layout_constraintStart_toStartOf="@+id/portraitImageView"
        app:layout_constraintTop_toTopOf="@+id/portraitImageView"
        />

    <ImageView
        android:id="@+id/ivCloseMic"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_member_mic_off"
        app:layout_constraintBottom_toBottomOf="@+id/portraitImageView"
        app:layout_constraintEnd_toEndOf="@+id/portraitImageView"
        />

    <TextView
        style="@style/body"
        android:id="@+id/tvNickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constrainedWidth="true"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/white_60"
        app:layout_constraintEnd_toEndOf="@+id/portraitImageView"
        app:layout_constraintStart_toStartOf="@+id/portraitImageView"
        app:layout_constraintTop_toBottomOf="@+id/portraitImageView"
        tools:text="XXXXXXXXXXXXXXXXXXXXXXXX"
        />


    <org.libpag.PAGView
        android:id="@+id/pagWaitingView"
        android:layout_width="36dp"
        android:layout_height="8dp"
        app:layout_constraintTop_toTopOf="@id/portraitImageView"
        app:layout_constraintBottom_toBottomOf="@id/portraitImageView"
        app:layout_constraintStart_toStartOf="@id/portraitImageView"
        app:layout_constraintEnd_toEndOf="@id/portraitImageView"
        />

</androidx.constraintlayout.widget.ConstraintLayout>