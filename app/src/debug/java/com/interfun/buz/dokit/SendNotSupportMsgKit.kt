package com.interfun.buz.dokit

import android.app.Activity
import com.interfun.buz.R
import com.interfun.buz.chat.wt.entity.isGroup
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.im.IMAgent
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IM5Message
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class ShowNotSupportMsgKit : BaseBusinessKit(R.string.kit_send_not_support_msg) {

    override fun handleOnClick(activity: Activity) {
        GlobalScope.launch {
            val currentItem = WTStatusManager.currentSelectedItem
            if (currentItem.isGroup) {
                val msgContent = NotSupportMsg()
                val msg = IM5Message.obtain(
                    currentItem?.targetId!!.toString(),
                    IM5ConversationType.GROUP,
                    msgContent
                )
                IMAgent.sendMessageSync(msg, null)
            } else {
                val msgContent = NotSupportMsg()
                val msg = IM5Message.obtain(
                    currentItem?.targetId!!.toString(),
                    IM5ConversationType.PRIVATE,
                    msgContent
                )
                IMAgent.sendMessageSync(msg, null)
            }
        }
    }
}