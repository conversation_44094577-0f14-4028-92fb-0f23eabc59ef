package com.interfun.buz.album.ui.block

import android.Manifest
import android.app.Activity
import android.content.Context.TELEPHONY_SERVICE
import android.os.Build
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import androidx.fragment.app.viewModels
import androidx.media3.common.Player
import com.interfun.buz.album.R
import com.interfun.buz.album.R.string
import com.interfun.buz.album.bean.*
import com.interfun.buz.album.databinding.AlbumPreviewLayoutBinding
import com.interfun.buz.album.event.PreviewSendMediaSuccessEvent
import com.interfun.buz.album.event.RefreshAlbumCompleteEvent
import com.interfun.buz.album.event.SelectPhotoEvent
import com.interfun.buz.album.manager.AlbumManager
import com.interfun.buz.album.ui.fragment.MediaPreviewFragment
import com.interfun.buz.album.ui.viewmodel.MediaPositionChangeInfo
import com.interfun.buz.album.ui.viewmodel.PreviewMediaDataViewModel
import com.interfun.buz.album.utils.AlbumTracker
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.album.event.SelectedAlbumItemsRemoveEvent
import com.interfun.buz.album.ui.viewmodel.PreviewMediaViewModel
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.eventbus.album.StartHomeSendingMediaAnimationEvent
import com.interfun.buz.common.ktx.toastSolidCorrect
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.media.bean.BuzMediaItem
import com.interfun.buz.media.bean.MediaType
import com.interfun.buz.media.constants.ALBUM_LIST_PREVIEW_PLAYER
import com.interfun.buz.media.player.BuzMediaPlayer
import com.interfun.buz.media.player.DataChangeCallback
import com.interfun.buz.media.player.DataProvider
import com.interfun.buz.media.player.PositionChangeCallback
import com.interfun.buz.media.player.manager.PagePlayerManager
import com.interfun.buz.media.player.view.OnActiveIntercept
import com.interfun.buz.media.player.view.PlayerConfig
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.collections.immutable.toPersistentList
import okhttp3.internal.toLongOrDefault

interface IBuzMediaPlayerProvider {
    fun getBuzMediaPlayer(): BuzMediaPlayer?
}

/**
 * 媒体大图预览
 * <AUTHOR>
 * @date 2024/4/10
 */
class PreviewListBlock(
    private val fragment: MediaPreviewFragment,
    binding: AlbumPreviewLayoutBinding,
    private val targetId: String?,
    private val convType: Int?,
    private val source: Int,
    private val mediaItem: BuzMediaItem?,
    private val position: Int = 0,
    private val bucketId: Long? = null,
    private val albumFolderMediaCount: Int? = null,
    private val referenceMsgId: Long?
) : BaseBindingBlock<AlbumPreviewLayoutBinding>(binding), IBuzMediaPlayerProvider {

    companion object {
        const val TAG = "PreviewListBlock"
    }

    private val autoDismissTopAndBottomAnim by lazy {
        fragment.obtainRegisterInterface2(DismissTopAndBottomCallback::class.java)
    }

    private val previewViewModel by fragment.activityViewModels<PreviewMediaDataViewModel>()
    private val previewViewModelNew by fragment.viewModels<PreviewMediaViewModel>()

    private var mediaList: MutableList<BuzMediaItem> = mutableListOf()

    private var buzMediaPlayer: BuzMediaPlayer? = null
    private var canLoadMoreNextItemsState = true;
    private var hasClickSendBtn = false

    private val playerConfig = PlayerConfig().apply {
        // Actions taken when center play icon is clicked
        onClickCenterPlayIcon = {
            Logz.tag(TAG).d("Center play icon is clicked")
            autoDismissTopAndBottomAnim?.startHideOnClickIfShow()
        }

        onActiveIntercept = object : OnActiveIntercept {
            override fun onIntercept(item: BuzMediaItem?): Boolean {
                val isInVoiceCall = VoiceCallPortal.isOnRealTimeCall()
                val isInOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
                if (isInVoiceCall || isInOnAir) {
                    string.calling_unable_to_play_vid.toast()
                    return true
                }

                if (isInOnAir) {
                    toast(R.string.exit_current_call_and_try.asString())
                    return true
                }
                return false
            }
        }
    }

    private val dataCallback = object : DataChangeCallback {
        override fun onChange(newList: List<BuzMediaItem>, selectedItem: BuzMediaItem) {
            logInfo(TAG, "DataChangeCallback ${selectedItem.mediaId}")
            currentItem = selectedItem
            currentPosition = newList.indexOf(selectedItem)
        }
    }

    private val positionCallback = object : PositionChangeCallback {
        override fun onPositionChange(
            position: Int,
            item: BuzMediaItem,
            byUser: Boolean
        ) {
            //中部大图预览 当前选中item回调
            logInfo(TAG, "onPositionChange = $position, and currentPosition = $currentPosition")
            if (currentPosition == position) {
                return
            }
            currentItem = item
            currentPosition = position

            updateSelectStatus()

            (item as? MediaItem)?.let {
                //带动底部list变动
                previewViewModel.currentSelectedItemShareFlow.emitInScope(
                    fragment.viewLifecycleScope,
                    MediaPositionChangeInfo(position, it)
                )
            }
        }
    }

    private val dataProvider = object : DataProvider {
        override suspend fun loadMorePreviousItems(firstItem: BuzMediaItem): List<BuzMediaItem> {
            return listOf()
        }

        override suspend fun loadMoreNextItems(lastItem: BuzMediaItem): List<BuzMediaItem> {
            logInfo(TAG, "loadMoreNextItems, source = $source, currentPosition = $currentPosition")
            return if (canLoadMoreNextItemsState && source == RouterParamKey.Album.ALBUM_SOURCE_ALBUM_ALL_TYPE) {
                previewViewModel.loadPageMediaData(
                    fragment.requireContext(),
                    currentPosition,
                    bucketId ?: 0,
                    albumFolderMediaCount ?: 0
                )
            } else {
                listOf()
            }
        }
    }

    private var currentItem: BuzMediaItem? = null
    private var currentPosition: Int = 0
    private var mediaContent: String = "" // 【AVS2024032502】页面类务型，上传目前已选择的媒体类型
    private var previewSource: Int = 0 //【AVS2024032502】页面来源枚举值

    override fun onCreate() {
        super.onCreate()
        registerPhoneCallListener()
        fragment.registerInterfaceBridge(IBuzMediaPlayerProvider::class.java, this)
    }

    override fun onDestroy() {
        super.onDestroy()
        hasClickSendBtn = false
        fragment.unregisterInterfaceBridge(IBuzMediaPlayerProvider::class.java)
        unregisterPhoneCallListener()
    }

    override fun initView() {
        super.initView()

        //预览时点击右上角选择media
        if (mediaItem.isNotNull()) {
            binding.flSelectTouchArea.invisible()
        }

        // Choose button in preview mode (not in album list)
        binding.flSelectTouchArea.click(clickIntervals = 10) {
            if (getCurrentItemChooseState()) {
                changeChooseState()
            } else {
                if (AlbumManager.instance.exceedSelectedSize()) {
                    R.string.select_media_reach_limit.toast()
                } else {
                    changeChooseState()
                }
            }
            autoDismissTopAndBottomAnim?.cancelAutoDismiss()
        }

        binding.iftvHD.click {
            val currentHDSelected = AlbumManager.instance.isHDSelected()
            AlbumTracker.onClickHdButton(currentHDSelected.not())
            //If the current picture is not selected, you need to select it
            if (currentHDSelected.not() && currentPosition < mediaList.size) {
                val item = mediaList[currentPosition] as? MediaItem
                item?.let {
                    val curPhotoSelectedList =
                        AlbumManager.instance.getCurrentSelectMediaList().filter { media ->
                            media.type == MediaType.Image
                        }
                    item.hasChoose = curPhotoSelectedList.containsMediaItemByMediaId(it)
                    if (it.hasChoose.not() && curPhotoSelectedList.isEmpty() && !AlbumManager.instance.exceedSelectedSize()) {
                        logDebug(
                            TAG,
                            "click HD button, the current photos is not selected, you need to select it"
                        )
                        AlbumManager.instance.addSelectMediaList(it.apply {
                            hasChoose = true
                        })
                        SelectPhotoEvent.post(it, it.index)
                        updateSelectStatus()
                        previewViewModel.itemStatusChangeShareFlow.emitInScope(
                            fragment.viewLifecycleScope,
                            MediaPositionChangeInfo(currentPosition, it)
                        )
                        if (AlbumManager.instance.exceedSelectedSize()) {
                            R.string.select_media_reach_limit.toast()
                        }
                    }
                }

                toastSolidCorrect(R.string.hd_is_on.asString())
            }
            AlbumManager.instance.setHDSelect(currentHDSelected.not())
            autoDismissTopAndBottomAnim?.showAndDelayHide()
        }

        binding.btnSend.click {
            if (hasClickSendBtn) { // 防止重复点击发送
                return@click
            }
            hasClickSendBtn = true
            val selectedMedia: ArrayList<MediaItem> = arrayListOf()
            var addVideoItemBitmap: MediaItem? = null
            when (source) {
                RouterParamKey.Album.ALBUM_SOURCE_ALBUM_ALL_TYPE,
                RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE -> {
                    val selectMediaList = AlbumManager.instance.getCurrentSelectMediaList()
                    if (selectMediaList.isNotEmpty()) {
                        selectedMedia.addAll(selectMediaList)
                    } else if (currentPosition < mediaList.size) {
                        val currentPreviewMediaItem = mediaList[currentPosition]
                        logInfo(
                            TAG,
                            "currentPreviewMediaItem is null : ${currentPreviewMediaItem.isNull()}"
                        )
                        currentPreviewMediaItem.let {
                            selectedMedia.add(it as MediaItem)
                            addVideoItemBitmap = it
                        }
                    }
                }

                RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE -> {
                    logInfo(TAG, "initView: source = $source")
                    selectedMedia.add(mediaItem as MediaItem)
                    addVideoItemBitmap = mediaItem
                }
            }
            //检查是否有视频时长大于30分钟的
            val filterHugeVideo = selectedMedia.find {
                it.isDurationMoreThan(30 * 60 * 1000L)
            }
            if (filterHugeVideo != null) {
                R.string.video_too_large.asString().toast()
                hasClickSendBtn = false
                return@click
            }
            if (addVideoItemBitmap?.type == MediaType.Video) {
                // 拍照点击发送与预览时没有选中图片点击发送，保存视频封面
                setMediaItemVideoCover(addVideoItemBitmap!!, true) {
                    sendMessage(selectedMedia = selectedMedia)
                }
            } else {
                sendMessage(selectedMedia = selectedMedia)
            }
            fragment.activity?.apply {
                val newIntent = intent.apply {
                    putParcelableArrayListExtra(
                        RouterParamKey.Album.KEY_SEND_MEDIA_CALLBACK,
                        selectedMedia
                    )
                }
                setResult(Activity.RESULT_OK, newIntent)
                PreviewSendMediaSuccessEvent.post()
                fragment.finishActivity()
            }
            var imageCount = 0
            var videoCount = 0
            selectedMedia.forEach { if (it.isVideo) videoCount += 1 else imageCount += 1 }
        }
    }

    private fun sendMessage(selectedMedia: ArrayList<MediaItem>) {
        val selectedMediaBackup = selectedMedia.toPersistentList()
        val convType = IM5ConversationType.entries.find { it.value == convType }
        if (targetId.isNotNull() && convType.isNotNull()) {
            logInfo(TAG, "initView: sendMediaMessages")
            val eventTrackExtra = EventTrackExtra(source = RouterParamKey.Album.ALBUM_SEND_SOURCE_MEDIA_PREVIEW_FRAGMENT, contentName = source.toString())
            previewViewModelNew.sendMediaMessages(
                targetId = targetId!!.toLongOrDefault(0L),
                convType = convType!!,
                selectedMedia = selectedMediaBackup,
                replyId = referenceMsgId,
                isSendOriginImage = AlbumManager.instance.isHDSelected(),
                source = source,
                eventTrackExtra = eventTrackExtra
            )
            if (selectedMediaBackup.isNotEmpty()) {
                StartHomeSendingMediaAnimationEvent.post(
                    selectedMediaBackup.size > 1,
                    selectedMediaBackup.first(),
                    RouterParamKey.Album.ALBUM_SEND_SOURCE_MEDIA_PREVIEW_FRAGMENT
                )
            }
        }
    }

    private fun changeChooseState() {
        val item = mediaList.getOrNull(currentPosition) as? MediaItem
        item?.let {
            if (it.isDurationMoreThan(30 * 60 * 1000L)) {
                R.string.video_too_large.asString().toast()
                return@let
            }
            val currentItemHasChoose = it.hasChoose
            logInfo(TAG, "changeChooseState current item hasChoose = $currentItemHasChoose")
            //更新已选列表数据
            if (currentItemHasChoose) {
                AlbumManager.instance.removeSelectMediaList(it.apply {
                    hasChoose = false
                })
            } else {
                AlbumManager.instance.addSelectMediaList(it.apply {
                    hasChoose = true
                })
            }
            //post 通知相册列表页的选中状态的更新,深拷贝对象进行传递
            SelectPhotoEvent.post(it, it.index)
            //更新UI选中状态
            updateSelectStatus()
            setMediaItemVideoCover(it, item.hasChoose)
            previewViewModel.itemStatusChangeShareFlow.emitInScope(
                fragment.viewLifecycleScope,
                MediaPositionChangeInfo(currentPosition, it)
            )
            Logz.tag(TAG).d("itemStatusChangeShareFlow it=${it.mediaId}, position=$currentPosition")
            if (AlbumManager.instance.exceedSelectedSize()) {
                R.string.select_media_reach_limit.toast()
            }
        }
    }

    override fun initData() {
        super.initData()

        val albumPlayerName = generatePagePlayerName()
        //根据source决定mediaList来源
        //1: 相机拍照/录像预览，只展示一张
        //2: 已选媒体队列预览，只展示已选的，不需要支持分页加载
        //3: 当前相册队列全部预览，需要支持分页加载
        when (source) {
            RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE -> {
                mediaItem?.let {
                    mediaList.add(mediaItem)
                    mediaContent = if (mediaItem.type == MediaType.Video) "video" else "image"
                }
                previewSource = 4
                playerConfig.apply {
                    repeatMode = Player.REPEAT_MODE_ONE // auto-repeat when video ends
                    isShowPlayIconWhenInActive = false
                    isShowPlayIconWhenInit = false
                    forceRePlaySameVideo = true
                }
                PagePlayerManager.registerPlayTargetChangeCallback(albumPlayerName) { playTarget ->
                    playTarget?.onActive()
                }
            }

            RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE -> {
                mediaList.addAll(mutableListOf<BuzMediaItem>().apply {
                    addAll(AlbumManager.instance.getCurrentSelectMediaList())
                })
                recordPreviewMediaList()
                AlbumTracker.onClickPreviewButton(
                    type = mediaContent,
                    imageCount =
                    AlbumManager.instance.getCurrentSelectMediaList().size - AlbumManager.instance.getNumberOfVideoInSelectMediaList(),
                    videoCount =
                    AlbumManager.instance.getNumberOfVideoInSelectMediaList()
                )
                previewSource = 2
            }

            RouterParamKey.Album.ALBUM_SOURCE_ALBUM_ALL_TYPE -> {
                mediaList.addAll(AlbumManager.instance.getAllMediaItemList())
                recordPreviewMediaList()
                previewSource = 3
            }
        }

        AlbumTracker.onMediaPreviewPageExpose(
            type = mediaContent,
            source = previewSource.toString()
        )

        if (mediaList.lastOrNull() is MediaFooter) {
            //footer用于列表，预览去掉这个无效数据
            mediaList.removeLast()
        }
        buzMediaPlayer = BuzMediaPlayer.Builder()
            .mediaList(mediaList)
            .lifecycleOwner(fragment.viewLifecycleOwner)
            .playerName(albumPlayerName)
            .playerConfig(playerConfig)
            .also {
                if (canLoadMoreData()) { // 拍照、点击预览按钮进入预览页是不加载更多
                    it.dataProvider(this.dataProvider)
                }
            }
            .renderParent(binding.flContainer)
            .dataChangeCallback(dataCallback)
            .positionCallback(positionCallback)
            .build()

        // 除了预览录制视频，其他相册预览视频时都需要让用户能暂停视频
        if (source == RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE ||
            source == RouterParamKey.Album.ALBUM_SOURCE_ALBUM_ALL_TYPE
        ) {
            buzMediaPlayer?.setOnViewTapListener {
                buzMediaPlayer?.playOrPause()
            }
        }

        currentItem = mediaList.getOrNull(position)
        currentPosition = position
        updateSelectStatus()

        // 更新HD状态
        AlbumManager.instance.selectedHDStateFlow.collectIn(fragment.viewLifecycleOwner) { hdSelected ->
            binding.iftvHD.text = if (hdSelected) string.ic_hd_on.asString() else string.ic_hd_off.asString()
        }

        binding.flContainer.post {
            buzMediaPlayer?.scrollToPosition(position)
        }

        previewViewModel.bottomSelectedItemChangeShareFlow.collectIn(fragment) {
            //收到底部队列item点击事件，大图预览需要跟随滑动
            it?.let { item ->
                (currentItem as? MediaItem)?.let { mediaItem ->
                    // 每次点击底部缩略图，只能预览选中的图片(和底部栏图片一致)，并且定位
                    canLoadMoreNextItemsState = false
                    mediaList.clear()
                    mediaList.addAll(AlbumManager.instance.getCurrentSelectMediaList())
                    buzMediaPlayer?.setMediaList(mediaList, it.item)
                    buzMediaPlayer?.scrollToPosition(item.position)
                    updateSelectStatus()
                }
            }
        }

        if (source == RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE) {
            previewViewModel.itemStatusChangeShareFlow.collectIn(fragment) {
                it?.let {
                    logInfo(
                        TAG,
                        "Album preview select type, itemStatusChangeShareFlow Media position: ${it.position}, Media hasChoose: ${it.item.hasChoose}"
                    )
                }
            }
        }

        // 移除了选中被删除的内容，需要弹窗显示'发送未知错误'
        BusUtil.observe<SelectedAlbumItemsRemoveEvent>(fragment.viewLifecycleOwner) { event ->
            logInfo(
                TAG,
                "observe 'SelectedAlbumItemsRemoveEvent', fragment.isResumed = ${fragment.isResumed}"
            )
            if (mediaItem.isNotNull() || event.removeItemList.isEmpty()) {
                return@observe
            }
            mediaRemovedHandlingDialog()
        }

        BusUtil.observe<RefreshAlbumCompleteEvent>(fragment.viewLifecycleOwner) {
            when (source) {
                RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE -> {
                    mediaList.clear()
                    mediaList.addAll(AlbumManager.instance.getCurrentSelectMediaList())
                }

                RouterParamKey.Album.ALBUM_SOURCE_ALBUM_ALL_TYPE -> {
                    mediaList.clear()
                    mediaList.addAll(AlbumManager.instance.getAllMediaItemList())
                }
            }
            if (currentPosition < mediaList.size) {
                currentItem = mediaList[currentPosition]
            } else {
                mediaList[mediaList.size - 1]
                currentPosition = mediaList.size - 1
            }
            updateSelectStatus()
            buzMediaPlayer?.setMediaList(mediaList, currentItem)
        }

        if (source == RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE) {
            listenEndCallEvent()
        }

        ChannelPendStatusManager.statusFlow.collectIn(fragment.viewLifecycleOwner) {
            if (it.first == CallPendStatus.DECLINE || it.first == CallPendStatus.IDLE) {
                resumeMediaPlay()
            }
        }

        VoiceCallPortal.rejectResult.collectIn(fragment.viewLifecycleOwner) {
            val rCode = it.first
            if (rCode == 0 || rCode == 4004) {
                resumeMediaPlay()
            }
        }

    }

    private val phoneStateListener by lazy {
        object : PhoneStateListener() {
            override fun onCallStateChanged(state: Int, phoneNumber: String?) {
                super.onCallStateChanged(state, phoneNumber)
                logInfo(TAG, "onCallStateChanged: state = $state, phoneNumber = $phoneNumber")
                if (state == TelephonyManager.CALL_STATE_IDLE) {
                    mainThreadHandler.postDelayed({ resumeMediaPlay() }, 250)
                }
            }
        }
    }

    private fun registerPhoneCallListener() {
        if (checkPhoneStatePermission()) {
            val telephonyManager =
                fragment.activity?.getSystemService(TELEPHONY_SERVICE) as? TelephonyManager
            telephonyManager?.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE)
        }
    }

    private fun unregisterPhoneCallListener() {
        if (checkPhoneStatePermission()) {
            val telephonyManager =
                fragment.activity?.getSystemService(TELEPHONY_SERVICE) as? TelephonyManager
            telephonyManager?.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE)
        }
    }

    private fun checkPhoneStatePermission(): Boolean {
        return isPermissionGranted(android.Manifest.permission.READ_PHONE_STATE)
    }

    private fun listenEndCallEvent() {
        VoiceCallPortal.currentRoom.collectIn(fragment.viewLifecycleOwner) {
            if (it.isNull()) resumeMediaPlay()
        }
    }

    private fun resumeMediaPlay() {
        if (source == RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE && fragment.isDetached.not()) {
            buzMediaPlayer?.getPagePlayer()?.resumeMediaPlay()
        }
    }

    private fun canLoadMoreData(): Boolean {
        return source == RouterParamKey.Album.ALBUM_SOURCE_ALBUM_ALL_TYPE
    }

    private fun generatePagePlayerName(): String {
        // fix: 防止快速点击打开多次预览页面，导致上一个页面关闭的时候播放器release，导致返回到上一个预览页面的时候无法播放
        return ALBUM_LIST_PREVIEW_PLAYER + "_" + System.currentTimeMillis()
    }

    override fun onResume() {
        super.onResume()
        resumeMediaPlay()
    }

    /**
     * 更新预览右上角的选择态
     */
    private fun updateSelectStatus() {
        val item = mediaList.getOrNull(currentPosition) as? MediaItem
        item?.let {
            val currentSelectMediaList = AlbumManager.instance.getCurrentSelectMediaList()
            it.hasChoose = currentSelectMediaList.containsMediaItemByMediaId(it)
            if (it.hasChoose) {
                binding.icSelect.visible()
                binding.ivChoose.setImageResource(R.drawable.album_preview_select)
                binding.icSelect.text = R.string.ic_check.asString()
            } else {
                if (AlbumManager.instance.exceedSelectedSize()) {
                    binding.ivChoose.setImageResource(R.drawable.album_preview_select_disable)
                } else {
                    binding.ivChoose.setImageResource(R.drawable.album_preview_unselect)
                }
                binding.icSelect.gone()
            }
            //视频 和 相机拍照进入预览是不能选HD的
            if (it.type == MediaType.Video || source == RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE) {
                binding.iftvHD.gone()
            } else {
                binding.iftvHD.visible()
//                if (currentSelectMediaList.none { it.type == MediaType.Image } || AlbumManager.instance.isHDSelected()
//                        .not()) {
//                    updateHDSelectedStatus(false)
//                } else if (AlbumManager.instance.isHDSelected()) {
//                    updateHDSelectedStatus(true)
//                }
            }
        }
    }

    private fun mediaRemovedHandlingDialog() {
        CommonAlertDialog(
            context = context,
            title = string.media_went_wrong.asString(),
            positiveText = string.ok.asString(),
            positiveCallback = {
                it.dismiss()
                val selectMediaList = AlbumManager.instance.getCurrentSelectMediaList()
                val finishActivity = (source == RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE
                        && selectMediaList.isEmpty())
                        || buzMediaPlayer?.getMediaList().isNullOrEmpty()
                if (finishActivity) {
                    fragment.finishActivity()
                }
            }
        ).show()
    }

    /**
     * 跟随底部点击的media item，大图预览列表滚动
     */
    private fun scrollWithBottomInfo(item: MediaPositionChangeInfo, mediaItem: MediaItem) {
        //step1: 判断当前大图预览队列是否对应已选队列
        if (source == RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE) {
            //是: 直接跳转对应position
            buzMediaPlayer?.scrollToPosition(item.position)
        } else {
            //否： step2:检查底部点击的media，是否在当前相册
            if (item.item.bucketId == mediaItem.bucketId) {
                //是： 在同一个相册，直接滚动
                buzMediaPlayer?.scrollToPosition(item.item)
            } else {
                //否： 不在当前相册
                //TODO: kd 不在一个相册
            }
        }
    }

    private fun recordPreviewMediaList() {
        val selectedMediaListSize = AlbumManager.instance.getCurrentSelectMediaList().size
        val numberOfVideo = AlbumManager.instance.getNumberOfVideoInSelectMediaList()
        mediaContent = if (selectedMediaListSize == 0) {
            "0"
        } else if (numberOfVideo == 0) {
            "image"
        } else if (selectedMediaListSize == numberOfVideo) {
            "video"
        } else {
            "both"
        }
    }

    override fun getBuzMediaPlayer(): BuzMediaPlayer? {
        Logz.tag(TAG).d("Returning $buzMediaPlayer")
        return buzMediaPlayer
    }

    private fun getCurrentItemChooseState(): Boolean {
        val item = mediaList.getOrNull(currentPosition) as? MediaItem
        item?.let {
            val currentSelectMediaList = AlbumManager.instance.getCurrentSelectMediaList()
            it.hasChoose = currentSelectMediaList.containsMediaItemByMediaId(it)
            return it.hasChoose
        }
        return false
    }

    private fun setMediaItemVideoCover(
        item: MediaItem,
        setCoverState: Boolean,
        callback: (() -> Unit)? = null
    ) {
        if (item.type == MediaType.Video) {
            if (!setCoverState) {
                previewViewModel.removeVideoItemBitmapCache(item.mediaId, callback)
            } else if (!AlbumManager.instance.hasCacheVideoItemBitmap(item.mediaId)) {
                previewViewModel.addVideoItemBitmapCache(
                    item.mediaId,
                    buzMediaPlayer?.getCurrentVideoCoverBitmap(currentPosition),
                    callback
                )
            }
        } else {
            callback?.invoke()
        }
    }

}