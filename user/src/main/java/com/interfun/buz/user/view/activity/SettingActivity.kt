package com.interfun.buz.user.view.activity

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_SETTING
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.SettingFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2023/10/25
 * @desc Include：
 * [com.interfun.buz.user.view.fragment.SettingFragment]
 */
@Route(path = PATH_USER_ACTIVITY_SETTING)
@AndroidEntryPoint
class SettingActivity : SimpleContainerActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val tag = SettingFragment.TAG
        val savedFragment = supportFragmentManager.findFragmentByTag(tag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, SettingFragment(), tag)
                .commit()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}