<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background_2_default"
    app:round_top_left_radius="30dp"
    app:round_top_right_radius="30dp">

    <!-- 标题栏区域 (Space for padding or alignment at the top) -->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- ImageView positioned closer to the very top -->
    <ImageView
        android:id="@+id/ivHandle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/common_dialog_slider"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- TextView for "Share to" positioned below the ImageView -->

    <!-- RecyclerView replacing FrameLayout -->

    <TextView
        android:id="@+id/tvShareTo"
        style="@style/text_title_small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/live_place_share_to"
        android:textColor="@color/color_foreground_neutral_important_default"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.498"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivHandle"
        app:layout_constraintVertical_bias="0.5" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvShareOptions"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintVertical_bias="0.0" />

</com.interfun.buz.base.widget.round.RoundConstraintLayout>
