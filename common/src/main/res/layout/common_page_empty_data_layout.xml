<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    tools:background="@color/basic_vanta">

    <com.interfun.buz.base.widget.view.animContainer.AnimContainerView
        android:id="@+id/animEmptyDataView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:adjustViewBounds="true"
        android:visibility="visible"
        app:layout_constraintHeight_max="200dp"
        app:layout_constraintWidth_max="200dp"
        app:layout_constraintDimensionRatio="1.0"
        app:layout_constraintBottom_toTopOf="@id/tvEmptyDataTips"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvEmptyDataTips"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:gravity="center"
        android:textColor="@color/text_white_secondary"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/animEmptyDataView"
        app:layout_constraintBottom_toTopOf="@id/btnRetry"
        app:layout_constraintVertical_chainStyle="packed"/>

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnRetry"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginTop="20dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvEmptyDataTips"
        tools:text="@string/retry"
        app:type="primary_small" />

</merge>
