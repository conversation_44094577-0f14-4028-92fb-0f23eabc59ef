package com.interfun.buz.base.ktx.flowext

import com.interfun.buz.base.ktx.isDebug
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.*
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.selects.select
import java.util.concurrent.CancellationException

fun <T> Flow<T>.sample(periodMillis: ()->Long): Flow<T> {
    return scopedFlow { downstream ->
        val values = produce(capacity = Channel.CONFLATED) {
            collect { value -> send(value ?: Symbol("NULL")) }
        }
        var lastValue: Any? = null
        val ticker = fixedPeriodTicker(periodMillis)
        while (lastValue !== Symbol("DONE")) {
            select<Unit> {
                values.onReceiveCatching { result ->
                    result
                        .onSuccess { lastValue = it }
                        .onFailure {
                            it?.let { throw it }
                            ticker.cancel(ChildCancelledException())
                            lastValue = Symbol("DONE")
                        }
                }
                ticker.onReceive {
                    val value = lastValue ?: return@onReceive
                    lastValue = null // Consume the value
                    downstream.emit(Symbol("NULL").unbox(value))
                }
            }
        }
    }
}

private fun <R> scopedFlow(block: suspend CoroutineScope.(FlowCollector<R>) -> Unit): Flow<R> =
    flow {
        coroutineScope { block(this@flow) }
    }

private class ChildCancelledException : CancellationException("Child of the scoped flow was cancelled") {
    override fun fillInStackTrace(): Throwable {
        if (isDebug) return super.fillInStackTrace()
        // Prevent Android <= 6.0 bug, #1866
        stackTrace = emptyArray()
        return this
    }
}

private class Symbol(@JvmField val symbol: String) {
    override fun toString(): String = "<$symbol>"

    @Suppress("UNCHECKED_CAST", "NOTHING_TO_INLINE")
    inline fun <T> unbox(value: Any?): T = if (value === this) null as T else value as T
}

private fun CoroutineScope.fixedPeriodTicker(
    periodMillis: ()->Long,
): ReceiveChannel<Unit> {
    return produce(capacity = 0) {
        delay(periodMillis())
        while (true) {
            channel.send(Unit)
            delay(periodMillis())
        }
    }
}