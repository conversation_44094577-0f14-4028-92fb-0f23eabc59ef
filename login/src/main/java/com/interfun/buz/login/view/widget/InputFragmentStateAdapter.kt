package com.interfun.buz.login.view.widget

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter

interface IDataSource {
    fun getItemCount(): Int
    fun createFragment(position: Int): Fragment
}

class InputFragmentStateAdapter constructor(fragment: Fragment, val dataSource: IDataSource) :
    FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int {
        return dataSource.getItemCount()
    }

    override fun createFragment(position: Int): Fragment {
        return dataSource.createFragment(position)
    }
}