package com.interfun.buz.common.web.functions

import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.constants.CommonMMKV
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail

/**
 * <AUTHOR>
 * @date 2023/7/26
 * @desc
 */
class HideFeedbackDotFunction : JSFunction() {

    companion object {
        const val METHOD_NAME = "hideFeedbackDot"
    }

    override fun invoke(
        activity: BaseActivity,
        webView: LJavaScriptWebView,
        data: JSFunctionData
    ): JsCallbackDetail? {
        log(TAG, METHOD_NAME)
        CommonMMKV.isFeedbackEntryDotVisible = false
        CommonMMKV.isFeedbackLogDotVisible = false
        return getSuccessCallback(data)
    }
}