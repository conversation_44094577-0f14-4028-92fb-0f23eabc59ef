package com.interfun.buz.home.view.block

import android.annotation.SuppressLint
import android.view.Gravity
import androidx.fragment.app.viewModels
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.ABTestManager.isVFGuidancePlanB
import com.interfun.buz.common.widget.view.BuzToolTips
import com.interfun.buz.core.widget_record.entity.RecordAreaType
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.domain.record.ui.VoiceFilterIntroduceDialog
import com.interfun.buz.domain.record.viewmodel.RecordVoiceViewModel
import com.interfun.buz.domain.record.viewmodel.VoiceFilterViewModel
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import kotlinx.coroutines.flow.combine

class WTVoiceFilterToolTipsBlock(
    val fragment: ChatHomeFragmentNew,
    binding: ChatFragmentHomeNewBinding
) : BaseBindingBlock<ChatFragmentHomeNewBinding>(binding) {
    private val TAG = "WTVoiceFilterToolTipsBlock"
    private val voiceFilterViewModel by fragment.viewModels<VoiceFilterViewModel>()

    override fun initData() {
        super.initData()
        observeNeedShowGuide()
    }

    private fun observeNeedShowGuide(){
        voiceFilterViewModel.isVoiceFilterModeStateFlow.collectLatestIn(fragment.viewLifecycleOwner){
            if (it){
                if (fragment.atLeastResumed.not()) return@collectLatestIn
                VoiceFilterIntroduceDialog.tryToShowDialog(fragment.childFragmentManager)
                return@collectLatestIn
            }
        }
    }
}