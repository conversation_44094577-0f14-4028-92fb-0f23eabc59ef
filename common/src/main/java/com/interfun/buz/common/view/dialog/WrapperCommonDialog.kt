package com.interfun.buz.common.view.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.constants.PATH_COMMON_ALERT_DIALOG
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.common.widget.dialog.CommonAlertDialog

@Route(path = PATH_COMMON_ALERT_DIALOG)
class WrapperCommonDialog: BaseBottomSheetDialogFragment() {
    @Autowired(name = RouterParamKey.Alert.KEY_ALERT_TITLE)
    @JvmField
    var title: String? = null

    @Autowired(name = RouterParamKey.Alert.KEY_ALERT_CONTENT)
    @JvmField
    var content: String? = null

    @Autowired(name = RouterParamKey.Alert.KEY_ALERT_CONFIRM_TEXT)
    @JvmField
    var confirmText: String = "OK"


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ARouter.getInstance().inject(this)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        log(TAG,"onCreateDialog: currentLifeCycle = ${lifecycle.currentState} title = $title content = $content confirmText = $confirmText")
        return  CommonAlertDialog(
            context = activity!!,
            title = title,
            content,
            positiveText = confirmText,
            positiveCallback = {
                dismiss()
                <EMAIL>()
            })
    }

    override fun initView(view: View?) {

    }

    override fun initListener(view: View?) {}
}