@file:Suppress("unused")

package com.interfun.buz.base.ktx

import android.app.Activity
import android.app.Application
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.flow.map

fun Application.doOnActivityLifecycle(
    onActivityCreated: ((Activity, Bundle?) -> Unit)? = null,
    onActivityStarted: ((Activity) -> Unit)? = null,
    onActivityResumed: ((Activity) -> Unit)? = null,
    onActivityPaused: ((Activity) -> Unit)? = null,
    onActivityStopped: ((Activity) -> Unit)? = null,
    onActivitySaveInstanceState: ((Activity, Bundle?) -> Unit)? = null,
    onActivityDestroyed: ((Activity) -> Unit)? = null,
): Application.ActivityLifecycleCallbacks =
    object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            onActivityCreated?.invoke(activity, savedInstanceState)
        }

        override fun onActivityStarted(activity: Activity) {
            onActivityStarted?.invoke(activity)
        }

        override fun onActivityResumed(activity: Activity) {
            onActivityResumed?.invoke(activity)
        }

        override fun onActivityPaused(activity: Activity) {
            onActivityPaused?.invoke(activity)
        }

        override fun onActivityStopped(activity: Activity) {
            onActivityStopped?.invoke(activity)
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            onActivitySaveInstanceState?.invoke(activity, outState)
        }

        override fun onActivityDestroyed(activity: Activity) {
            onActivityDestroyed?.invoke(activity)
        }
    }.also {
        registerActivityLifecycleCallbacks(it)
    }

fun Fragment.doOnViewLifecycle(
    onCreateView: (() -> Unit)? = null,
    onStart: (() -> Unit)? = null,
    onResume: (() -> Unit)? = null,
    onPause: (() -> Unit)? = null,
    onStop: (() -> Unit)? = null,
    onDestroyView: (() -> Unit)? = null,
) =
    viewLifecycleOwner.doOnLifecycle(onCreateView, onStart, onResume, onPause, onStop, onDestroyView)

fun LifecycleOwner.doOnLifecycle(
    onCreate: (() -> Unit)? = null,
    onStart: (() -> Unit)? = null,
    onResume: (() -> Unit)? = null,
    onPause: (() -> Unit)? = null,
    onStop: (() -> Unit)? = null,
    onDestroy: (() -> Unit)? = null,
) =
    lifecycle.addObserver(object : DefaultLifecycleObserver {
        override fun onCreate(owner: LifecycleOwner) {
            super.onCreate(owner)
            onCreate?.invoke()
        }

        override fun onStart(owner: LifecycleOwner) {
            super.onStart(owner)
            onStart?.invoke()
        }

        override fun onResume(owner: LifecycleOwner) {
            super.onResume(owner)
            onResume?.invoke()
        }

        override fun onPause(owner: LifecycleOwner) {
            super.onPause(owner)
            onPause?.invoke()
        }

        override fun onStop(owner: LifecycleOwner) {
            super.onStop(owner)
            onStop?.invoke()
        }

        override fun onDestroy(owner: LifecycleOwner) {
            super.onDestroy(owner)
            onDestroy?.invoke()
        }
    })

val Fragment.viewLifecycleScope get() = viewLifecycleOwner.lifecycleScope

val Fragment.atLeastResumed get() = fragment.viewLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)
val Fragment.atLeastResumedFlow get() = fragment.viewLifecycleOwner.lifecycle.currentStateFlow.map {
    it.isAtLeast(Lifecycle.State.RESUMED)
}

/**
 * 注意：如果一个观察者同时实现了DefaultLifecycleObserver和LifecycleEventObserver，
 * DefaultLifecycleObserver要先于LifecycleEventObserver的onStateChanged调用。
 * 如果也添加了OnLifecycleEvent注解，那么注解会被忽略掉。请注意混合使用。
 */
