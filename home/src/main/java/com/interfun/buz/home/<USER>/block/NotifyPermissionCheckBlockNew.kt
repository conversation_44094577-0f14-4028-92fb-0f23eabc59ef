package com.interfun.buz.home.view.block

import android.Manifest
import android.content.Intent
import android.os.Build
import android.view.View
import android.widget.TextView
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.constants.ChatMMKV
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.NotificationUtil.REQUEST_CODE_NOTIFY
import com.interfun.buz.common.utils.PermissionHelper
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import com.lizhi.component.push.lzpushsdk.PushSdkManager
import com.yibasan.lizhifm.lzlogan.Logz

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2023/1/3
 */
class NotifyPermissionCheckBlockNew(
    val fragment: ChatHomeFragmentNew,
    binding: ChatFragmentHomeNewBinding
) : BaseBindingBlock<ChatFragmentHomeNewBinding>(binding) {

    val TAG="NotifyPermissionCheckBlock"
    private var notifySettingViewStub: View? = null
    private val permissionHelper = PermissionHelper(fragment)

    override fun initView() {
        val displayIntervalValid = System.currentTimeMillis() - ChatMMKV.lastNotifySettingShowTime > 24 * 60 * 60 * 1000
        val notifyEnable = NotificationUtil.isNotifyOpen()
        if (notifyEnable.not() && displayIntervalValid) {
            ChatMMKV.lastNotifySettingShowTime = System.currentTimeMillis()
            notifySettingViewStub = fragment.bindingCenter.notifySettingView.inflate()
            notifySettingViewStub?.findViewById<View>(R.id.iftvClose)?.apply {
                click {
                    notifySettingViewStub?.gone()
                }
            }

            val tvNotifySettingView = notifySettingViewStub?.findViewById<TextView>(R.id.tvNotifySetting)?.apply {
                click {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        if (fragment.activity.isNotNull()){
                            permissionHelper.request(
                                fragment.requireActivity(),
                                false,
                                Manifest.permission.POST_NOTIFICATIONS
                            ) { result ->
                                val item = result.resultMap[Manifest.permission.POST_NOTIFICATIONS]
                                if (item?.isGranted == true) {
                                    Logz.tag(TAG).i("NotifyPermissionCheckBlock frushToken ${UserSessionManager.uid.toString()}")
                                    PushSdkManager.instance.frushToken(UserSessionManager.uid.toString())
                                    onNotificationPermissionGranted()
                                } else if (item?.hasSysDialogShown != true) {
                                    NotificationUtil.requestsNotifyPermission(fragment)
                                }
                            }
                        }
                    } else {
                        NotificationUtil.requestsNotifyPermission(fragment)
                    }
                }
            }
            tvNotifySettingView?.text = buildSpannedString {
//                iconFontAlign{
//                    size(18.dp){
//                        typeface(FontUtil.fontIcon!!){
//                            append(R.string.ic_ring_solid.asString())
//                        }
//                    }
//                }
                append(R.string.chat_home_notify_permission_setting.asString())
                appendSpace(4.dp)
                color(R.color.basic_primary.asColor()){
                    append(R.string.settings.asString())
                }
                iconFontAlign(color = R.color.basic_primary.asColor()){
                    typeface(FontUtil.fontIcon!!){
                        append(R.string.ic_arrow_right_rtl.asString())
                    }
                }
            }
        }
    }

    private fun onNotificationPermissionGranted(){
        if (NotificationUtil.isNotifyOpen()){
            notifySettingViewStub?.gone()
            logInfo("NotifyPermissionCheckBlock","onNotificationPermissionGranted startForegroundService")
            WTStatusManager.startForegroundService()
        }
    }

    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?){
        if (requestCode == REQUEST_CODE_NOTIFY){
            onNotificationPermissionGranted()
        }
    }

}