package com.interfun.buz.component.hilt

import dagger.BindsInstance
import dagger.hilt.DefineComponent
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope

/**
 * 用户Component，绑定用户的生命周期。
 * Component 里面默认有 UserAuth、userId，CoroutineScope.只要是注入UserComponent的地方，都可以使用，例子参考UserModule
 */
@UserScope
@DefineComponent(parent = SingletonComponent::class)
interface UserComponent

@DefineComponent.Builder
interface UserComponentBuilder {

    @BindsInstance
    fun setUserId(userId: Long): UserComponentBuilder

    @BindsInstance
    fun setScope(scope: CoroutineScope): UserComponentBuilder

    @BindsInstance
    fun setUserAuth(userAuth: UserAuth): UserComponentBuilder

    fun build(): UserComponent
}