@file:Suppress("unused")

package com.interfun.buz.base.ktx

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.core.view.WindowCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import java.util.LinkedList
import kotlin.reflect.KClass

inline val Context.context: Context get() = this

inline val Activity.activity: Activity get() = this

inline val FragmentActivity.fragmentActivity: FragmentActivity get() = this

inline val ComponentActivity.lifecycleOwner: LifecycleOwner get() = this

var Activity.decorFitsSystemWindows: <PERSON>olean
    get() = noGetter()
    set(value) = WindowCompat.setDecorFitsSystemWindows(window, value)

inline val Activity.contentView: View?
    get() = (findViewById<View>(android.R.id.content) as ViewGroup).let {
        if (it.childCount > 0) it.getChildAt(0) else null
    }

val Context.activity: Activity?
    get() {
        var context: Context? = this
        while (context is ContextWrapper) {
            if (context is Activity) {
                return context
            }
            context = context.baseContext
        }
        return null
    }


internal val activityCache = LinkedList<Activity>()

val createdActivityCount get() =  activityCache.size

@Deprecated("请不要使用这个，这个方式是不靠谱的。请不要用这个来跳转页面，比如打开fragment可能会有一些异常问题，如hilt注入失败")
val topActivity: Activity? get() = activityCache.lastOrNull()

val activityList: List<Activity> get() = activityCache.toList()

val prevActivity: Activity? get() = activityCache.getOrNull(activityCache.size - 2)

val resumedActivity: Activity? get() = activityCache.findSafety { it is AppCompatActivity && it.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED) }

fun pickActivity(predicate: (Activity) -> Boolean): Activity? {
    return activityCache.find(predicate)
}

@Deprecated("请使用当前页面的activity或者context跳转")
fun startActivity(intent: Intent) = topActivity?.startActivity(intent)

@Deprecated("请使用当前页面的activity或者context跳转")
inline fun <reified T : Activity> startActivity(
    vararg pairs: Pair<String, Any?>,
    crossinline block: Intent.() -> Unit = {}
) =
    topActivity?.startActivity<T>(pairs = pairs, block = block)

inline fun <reified T : Activity> Context.startActivity(
    vararg pairs: Pair<String, Any?>,
    crossinline block: Intent.() -> Unit = {}
) =
    startActivity(intentOf<T>(*pairs).apply(block))

fun <T : Activity> finishActivity(clazz: KClass<T>): Boolean =
    activityCache.removeAll {
        if (it.javaClass == clazz) it.finish()
        it.javaClass == clazz
    }

fun finishAllActivities(): Boolean =
    activityCache.removeAll {
        it.finish()
        true
    }

fun finishAllActivitiesExceptNewest(): Boolean =
    topActivity.let { topActivity ->
        activityCache.removeAll {
            if (it != topActivity) it.finish()
            it != topActivity
        }
    }


fun Activity.finishWithResult(vararg pairs: Pair<String, *>) {
    setResult(Activity.RESULT_OK, Intent().putExtras(bundleOf(*pairs)))
    finish()
}

inline fun <reified T : Activity> finishActivity(): Boolean = finishActivity(T::class)

/** 用于Fragment的默认返回键事件 */
fun FragmentActivity.interceptOnBackPressed(lifecycleOwner: LifecycleOwner,handleOnBackPressed: (() -> Unit?)? = null) {
    onBackPressedDispatcher.addCallback(lifecycleOwner,
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleOnBackPressed?.invoke()
            }
        }
    )
}

fun FragmentActivity.pressBackTwiceToExitApp(
    delayMillis: Long = 2000,
    owner: LifecycleOwner = this,
    onFirstBackPressed: () -> Unit
) =
    onBackPressedDispatcher.addCallback(owner, object : OnBackPressedCallback(true) {
        private var lastBackTime: Long = 0

        override fun handleOnBackPressed() {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastBackTime > delayMillis) {
                onFirstBackPressed()
                lastBackTime = currentTime
            } else {
                finishAllActivities()
            }
        }
    })

fun Activity.turnScreenOnAndKeyguardOff() {
    window.addFlags(
        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                or WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
    )
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
        setShowWhenLocked(true)
        setTurnScreenOn(true)
    }

    //memory leak -> more detail: https://stackoverflow.com/questions/60477120/keyguardmanager-memory-leak
//    with(getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager) {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            requestDismissKeyguard(this@turnScreenOnAndKeyguardOff, null)
//
//        }
//    }
}

fun Activity.turnScreenOffAndKeyguardOn() {
    window.clearFlags(
        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                or WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
    )
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
        setShowWhenLocked(false)
        setTurnScreenOn(false)
    }
}

/**当前activity是否处于画中画模式*/
val Activity.isInPipMode: Boolean
    get() = if (isPiPSupported() && isPiPPermissionGranted()) {
        isInPictureInPictureMode
    } else false






