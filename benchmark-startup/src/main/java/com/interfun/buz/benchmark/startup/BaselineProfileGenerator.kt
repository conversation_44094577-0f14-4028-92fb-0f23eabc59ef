package com.interfun.buz.benchmark.startup

import androidx.benchmark.macro.junit4.BaselineProfileRule
import androidx.test.uiautomator.By
import androidx.test.uiautomator.Until
import org.junit.Rule
import org.junit.Test

class BaselineProfileGenerator {

    companion object{
        const val ID_PREFIX = "com.interfun.buz:id/"
    }

    @get:Rule
    val baselineProfileRule = BaselineProfileRule()

    @Test
    fun startup() = baselineProfileRule.collect(
        strictStability = true,
        packageName = "com.interfun.buz",
        profileBlock = {
            startActivityAndWait()
            device.wait(Until.hasObject(By.res("${ID_PREFIX}viewLoginWave")), 500)
            val loginEntry = device.findObject(By.res("${ID_PREFIX}viewLoginWave"))
            if (loginEntry != null) {
                loginEntry.click()
                device.wait(Until.hasObject(By.res("${ID_PREFIX}etAccount")), 500)
                device.findObject(By.res("${ID_PREFIX}etAccount"))?.text = Config.TEST_PHONE_NUM
                device.findObject(By.res("${ID_PREFIX}btnNext"))?.click()
                device.wait(Until.hasObject(By.res("${ID_PREFIX}etAccount")), 500)
                device.wait(Until.hasObject(By.res(createId("etCode"))), 3000)
                device.findObject(By.res(createId("etCode")))?.text = Config.TEST_VERIFICATION_CODE
                device.waitForIdle()
            }else {
                //home
                device.waitForIdle()
            }
            Thread.sleep(1000)
        }
    )

    fun createId(name: String): String {
        return "${ID_PREFIX}$name"
    }
}