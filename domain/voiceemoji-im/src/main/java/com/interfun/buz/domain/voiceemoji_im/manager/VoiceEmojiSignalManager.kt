package com.interfun.buz.domain.voiceemoji_im.manager

import com.interfun.buz.biz.center.voicemoji.repository.blindbox.BlindBoxRepository
import com.interfun.buz.common.bean.push.PushOP
import com.interfun.buz.im.signal.SignalManagerPresenter
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object VoiceEmojiSignalManager {
    init {
        GlobalScope.launch {
            SignalManagerPresenter.obtainInstance().obtainSignalingFlow {
                it.topic == PushOP.PushVoiceBlindStatusChange.op
            }.collect {
                BlindBoxRepository.syncBlindBoxList()
            }
        }
    }

    /**
     * 不要删除，临时处理，用于初始化该单例
     */
    fun addSignalObserver() {

    }
}