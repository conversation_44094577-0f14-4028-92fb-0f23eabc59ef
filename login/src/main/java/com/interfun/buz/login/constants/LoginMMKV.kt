package com.interfun.buz.login.constants

import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.MmkvUtil
import com.interfun.buz.login.viewmodel.pojo.SentCodeRet.SetCodeChannelDef
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * MMKV常量记录类，请保持规范：以模块名作为开头、避免重名
 * 使用方式：
 * 1. 通过by mmkv...的属性委托方式添加及使用，使用驼峰式命名
 * 2. 添加const val String作为Key，手动调用mmkv.encode\decode，规范为全大写+下划线
 */
object LoginMMKV : MMKVOwner {

    /**----------------- 常量区域 ------------------**/

    var isNewEditedInfoRegister : Boolean = false

    private const val LOGIN_KEY_REGISTER_DEVICE_BY_UID = "login_key_register_device_by_uid_"

    fun putNewRegisterDeviceFlag(newRegister: Boolean){
        GlobalScope.launch(Dispatchers.IO) {
            if (UserSessionManager.hasSession()){
                val key = LOGIN_KEY_REGISTER_DEVICE_BY_UID + UserSessionManager.uid
                MmkvUtil.putMmkvBoolean(key, newRegister)
            }
        }
    }

    fun getNewRegisterDeviceFlag(): Boolean {
        if (UserSessionManager.hasSession().not()) {
            return false
        }
        val key = LOGIN_KEY_REGISTER_DEVICE_BY_UID + UserSessionManager.uid
        return MmkvUtil.getMmkvBoolean(key, false)
    }

    /**--------------- 委托属性区域 -----------------**/

    //是否显示过注册期间的权限申请页面
    var hadShownSignUpPermission by mmkvBool(false)

    var limitResendCodePhone by mmkvString()
    var limitResendCodePhoneTime by mmkvLong()

    var limitResendCodeEmail by mmkvString()
    var limitResendCodeEmailTime by mmkvLong()

    var autoJumpVerify by mmkvString()

    /**
     * 存储上次发送验证码返回的channel信息。
     * 相关业务需求：在短信验证界面的重发文案中根据上次channel显示特定文案
     * [com.interfun.buz.login.view.fragment.ReceiveCodeFragment]
     * 参阅
     */
    @SetCodeChannelDef
    var beforeChannel by mmkvInt()
}