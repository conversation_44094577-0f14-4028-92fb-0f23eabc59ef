package com.interfun.buz.common.widget.view.network

import android.content.Context
import android.util.AttributeSet
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.widget.view.loading.BaseLoadingView
import org.libpag.PAGFile
import org.libpag.PAGView

class BuzLoadingView @JvmOverloads constructor
    (context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    PAGView(context, attrs, defStyleAttr) {

    private val pagFile: PAGFile = PAGFile.Load(context.assets, BaseLoadingView.FILE_NAME_PAGE)

    fun startLoading() {
        visible()
        composition = pagFile;
        setRepeatCount(0);
        play()

    }

    fun stopLoading() {
        stop()
        gone()
    }
}