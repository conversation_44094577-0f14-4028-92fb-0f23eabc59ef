package com.interfun.buz.notification.utils

import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.app.NotificationCompat
import androidx.core.app.Person
import androidx.core.graphics.drawable.IconCompat
import coil.Coil
import coil.request.ImageRequest
import coil.transform.CircleCropTransformation
import coil.transform.Transformation
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.getDisplayName
import com.interfun.buz.common.ktx.mergeWith
import com.interfun.buz.common.ktx.toAdaptiveBitmap
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.widget.portrait.PortraitUtil
import com.interfun.buz.feature.notification.R
import com.interfun.buz.push.model.FCMPushData
import com.interfun.buz.push.model.LivePlaceInviteExtra
import com.interfun.buz.push.model.PushGroupInfo
import com.interfun.buz.push.model.PushUserInfo

object LivePlaceNotifyUtils {

    private val SIZE_AVATAR = 48.dp

    suspend fun getIconAvatar(
        portraitUrl: String?,
        defaultIconRes: Int,
        allowHardware: Boolean = false,
        mergeDrawable: Boolean = false,
        vararg transformationsBelowO: Transformation
    ): Drawable? {
        if (portraitUrl.isNullOrEmpty()) {
            return defaultAvatar(defaultIconRes)
        }
        val resizeUrl = PortraitUtil.resizePortraitUrl(portraitUrl)
        return if (VERSION.SDK_INT < VERSION_CODES.O) {
            requestDrawable(
                resizeUrl, SIZE_AVATAR, SIZE_AVATAR, allowHardware, mergeDrawable, *transformationsBelowO
            )
        } else {
            requestDrawable(
                resizeUrl, SIZE_AVATAR, SIZE_AVATAR, allowHardware, mergeDrawable
            )
        } ?: defaultAvatar(defaultIconRes)
    }

    private suspend fun requestDrawable(
        url: String,
        width: Int,
        height: Int,
        allowHardware: Boolean = false,
        mergeDrawable: Boolean = false,
        vararg transformations: Transformation
    ): Drawable? {
        return Coil.imageLoader(appContext).execute(
            ImageRequest.Builder(appContext).data(url).size(width, height)
                .allowHardware(allowHardware).transformations(*transformations).build()
        ).drawable?.let {
            if (mergeDrawable){
                it.mergeWith(R.drawable.common_lp_live_state_in_notification_full.asDrawable()!!)
            }else{
                it
            }
        }
    }

    private suspend fun defaultAvatar(@DrawableRes icon: Int): Drawable? {
        val builder = ImageRequest.Builder(appContext).data(icon).allowHardware(false)
            .size(SIZE_AVATAR, SIZE_AVATAR)
        if (VERSION.SDK_INT < VERSION_CODES.O) {
            builder.transformations(CircleCropTransformation())
        }
        return Coil.imageLoader(appContext).execute(builder.build()).drawable as? BitmapDrawable
    }

    suspend fun createOpenLpChatStyle(fcmPushData: FCMPushData): NotificationCompat.MessagingStyle {
        val payload = fcmPushData.payload
        val groupInfo = payload.groupInfo
        val senderUserInfo = payload.senderUserInfo
        val pushExtra: LivePlaceInviteExtra? = payload.pushExtra
        val callUserName = payload.senderUserInfo?.userId?.let {
            UserRelationCacheManager.getUserRelationInfoFromCacheSync(it)?.getContactFirstName()?:payload.senderUserInfo?.name
        }
        val personName = if(pushExtra != null) {
            appStringContext.getString(R.string.live_place_opened, callUserName)
        }else{
            fcmPushData.title
        }
        val person = if (groupInfo != null) {
            createPersonGroup(groupInfo.portrait, personName, true)
        } else {
            createPersonUser(senderUserInfo?.portrait, personName, true)
        }
        val messagingStyle = NotificationCompat.MessagingStyle(person)
        val message = NotificationCompat.MessagingStyle.Message(
            if (pushExtra?.topic.isNullOrEmpty()) "" else appStringContext.getString(R.string.live_place_join_topic, "${pushExtra?.topic}"),
            System.currentTimeMillis(),
            person
        )
        if (groupInfo != null){
            messagingStyle.conversationTitle = groupInfo.name
        }
        messagingStyle.addMessage(message)
        return messagingStyle
    }

    private suspend fun createPersonGroup(
        portraitUrl: String? = null,
        personName: String,
        mergeDrawable: Boolean = false,
    ): Person {
        var icon: IconCompat? = null
        getIconAvatar(portraitUrl, R.drawable.common_pic_portrait_group_default, false, mergeDrawable, CircleCropTransformation())?.let {
            val adaptiveIcon =
                it.toAdaptiveBitmap(backgroundColor = com.interfun.buz.common.R.color.notification_avatar_bg.asColor())
            icon = IconCompat.createWithAdaptiveBitmap(adaptiveIcon)
        }
        return Person.Builder().setName(personName).setIcon(icon).build()
    }

    private suspend fun createPersonUser(
        portraitUrl: String? = null,
        personName: String,
        mergeDrawable: Boolean = false,
        @ColorInt iconBgColor: Int? = null
    ): Person {
        var icon: IconCompat? = null
        getIconAvatar(portraitUrl, R.drawable.common_pic_portrait_user_default, false, mergeDrawable, CircleCropTransformation())?.let {
            val adaptiveIcon =
                it.toAdaptiveBitmap(backgroundColor = iconBgColor)
            icon = IconCompat.createWithAdaptiveBitmap(adaptiveIcon)
        }
        return Person.Builder().setName(personName).setIcon(icon).build()
    }

    suspend fun createKonckMsgStyle(fcmPushData: FCMPushData): NotificationCompat.MessagingStyle{
        val payload = fcmPushData.payload
        val senderUserInfo = payload.senderUserInfo
        val pushExtra: LivePlaceInviteExtra? = payload.pushExtra
        val callUserName = payload.senderUserInfo?.userId?.let {
            UserRelationCacheManager.getUserRelationInfoFromCacheSync(it)?.getDisplayName()?:payload.senderUserInfo?.name
        }
        val personName = appStringContext.getString(R.string.live_place_want_chat, callUserName)
        val person = createPersonUser(senderUserInfo?.portrait, personName, false)
        val messagingStyle = NotificationCompat.MessagingStyle(person)
        val message = NotificationCompat.MessagingStyle.Message(
            R.string.live_place_lets_open.asString(),
            System.currentTimeMillis(),
            person
        )
        messagingStyle.conversationTitle = fcmPushData.title?:R.string.konck_konck_push_title.asString()
        messagingStyle.addMessage(message)
        return messagingStyle
    }

    suspend fun createInviteMsgStyle(senderUserInfo: PushUserInfo, groupInfo: PushGroupInfo?, topic: String?): NotificationCompat.MessagingStyle{
        val callUserName = senderUserInfo.userId.let {
            UserRelationCacheManager.getUserRelationInfoFromCacheSync(it)?.getContactFirstName()?: senderUserInfo.name
        }
        val personName = if (groupInfo != null){
            appStringContext.getString(R.string.live_place_xxx_invites_you, callUserName)
        }else{
            appStringContext.getString(R.string.live_place_invites_you)
        }
        val person = if (groupInfo != null) {
            createPersonGroup(groupInfo.portrait, personName, true)
        } else {
            createPersonUser(senderUserInfo.portrait, personName, true)
        }
        val messagingStyle = NotificationCompat.MessagingStyle(person)
        val message = NotificationCompat.MessagingStyle.Message(
            if (topic.isNullOrEmpty()) "" else appStringContext.getString(R.string.live_place_join_topic, "$topic"),
            System.currentTimeMillis(),
            person
        )
        if (groupInfo != null){
            messagingStyle.conversationTitle = groupInfo.name
        }else{
            messagingStyle.conversationTitle = senderUserInfo.name
        }
        messagingStyle.addMessage(message)
        return messagingStyle
    }
}