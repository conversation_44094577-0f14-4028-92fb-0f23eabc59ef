package com.interfun.buz.common.bean.voicecall

import android.content.Context
import android.media.AudioManager
import android.os.Parcelable
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.viewmodel.CreationExtras
import com.buz.idl.realtimecall.bean.CallUserInfo
import com.buz.idl.realtimecall.response.ResponseInviteRealTimeCall
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.audio.AudioManagerHelper
import com.interfun.buz.common.constants.RecordingConstant
import com.interfun.buz.common.eventbus.DismissOnCallMinimizeEvent
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.OnlineChatRingtoneManager
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.CallPendStatus.BEING_INVITED
import com.interfun.buz.common.manager.chat.CallPendStatus.DECLINE
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.voicecall.*
import com.interfun.buz.common.manager.voicecall.RoomLifecycle.*
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.InteractiveEventHandler
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.VoiceCallNotificationTracker
import com.interfun.buz.onair.standard.SeatRtcVolumeInfo
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.itnet.lthrift.service.ITResponse
import com.yibasan.lizhifm.audio.BaseRoleType
import com.yibasan.lizhifm.liveinteractive.LiveInteractiveConstant
import com.yibasan.lizhifm.liveinteractive.utils.LiveInteractiveSeatState
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.parcelize.Parcelize
import org.json.JSONObject
import kotlin.reflect.KClass

class VoiceCallRoom(
    val minimizeTargetId: Long, //最小化点击时跳转id
    val channelType: @ChannelType Int,
    val callType: @CallType Int,
    val voiceCallRoomSignal: IVoiceCallRoomSignal?
) : ViewModelStoreOwner, RoomLifecycleOwner, RoomManagerRegisterOwner, InteractiveEventHandler(),DefaultLifecycleObserver {
    private val TAG = "VoiceCallRoom"

    val scope = MainScope()
    override val viewModelStore = ViewModelStore()
    private val managerRegister = RoomManagerRegister()
    var roomLaunchMode: @RoomLaunchMode Int = RoomLaunchMode.CREATE
    val viewModel by lazy { ViewModelProvider(this,
      object:  ViewModelProvider.Factory {
          override fun <T : ViewModel> create(modelClass: KClass<T>, extras: CreationExtras): T {
             return VoiceCallViewModel(callType) as T
          }
      })[VoiceCallViewModel::class.java] }
    private val roomSeatManager = RoomSeatManager(this)
    val members: StateFlow<List<CallRoomUser>> get() = roomSeatManager.members
    private val roomAudioOutputManager = RoomAudioOutputManager(this)
    private val lifecycleManager = RoomLifecycleManager(this)
    private val roomLifecycleRegistry = RoomLifecycleRegistry(this)
    private val roomGuarder = RoomGuarder(this)
    private val roomTimeManager = RoomTimeManager(this)
    private val roomSensorManager = RoomSensorManager(this)
    private val roomPipModeManager = RoomPipModeManager(this)
    val targetStateChangeFlow get() = roomPipModeManager.targetStateChangeFlow
    val currentTarget get() = roomPipModeManager.currentTarget
    val callDuration = roomTimeManager.callDuration
    private val callDurationSecond get() = roomTimeManager.callDurationSecond.value
    val rtcVcResManager:RoomVCResManager = RoomVCResManager()
    private var _myMicStatus = MutableStateFlow(MicStatus.OPEN)
    private var _myCamStatus = MutableStateFlow(CameraStatus.CLOSE)
    private var _myCamState = MutableStateFlow(CameraState.FRONT)
    private var _myNetwork = MutableStateFlow(NetworkStatus.GOOD)
    val myMicStatus get() = _myMicStatus.asStateFlow()
    val myCamStatus get() = _myCamStatus.asStateFlow()
    val myCamState get() = _myCamState.asStateFlow()
    val myNetwork get() = _myNetwork.asStateFlow()
    private var hasOpenedCamera = false // 打点用，只要有打开摄像头，就置为true
    private var channelId: Long? = null
    val roomChannelId: Long? get() = channelId
    val myRtcChannelId: Int get() = roomSeatManager.myRtcUserId

    private var lastUpdateSpeakingStateTime = 0L
    private val updateSpeakingStateSample = 300L
    private val lifecycleThread = newSingleThreadContext("RoomLifecycle")
    private val _roomDestroyReason = MutableSharedFlow<Int>(replay = 1)
    val roomDestroyReason: SharedFlow<Int> = _roomDestroyReason
    private val _lieErrorCode = MutableSharedFlow<Int>()
    val lieErrorCode: SharedFlow<Int> = _lieErrorCode
    private val _isPeripheralForAgora = MutableSharedFlow<Boolean>(replay = 1)
    private val audioManager = appContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private val _speakingFlow = MutableSharedFlow<List<SeatRtcVolumeInfo>>()
    val speakingFlow: Flow<List<SeatRtcVolumeInfo>> = _speakingFlow
    val isPeripheralForAgora: SharedFlow<Boolean> = _isPeripheralForAgora
    private var joinSource: Int? = null
    private var audioQuality = NetworkStatus.BEST
    private var networkQuality = NetworkStatus.BEST

    val currentLifecycle get() = getLifeCycle().state

    // 下个版本找机会删掉
    val bluetoothNameBeforeConnected get() = roomAudioOutputManager.bluetoothName

    //打点用
    private var callStartTime: Long = 0L
    private var initCallMemberCount = 0
    // 累计加入通话的人，不包含自己
    private val joinedMemberSet = mutableSetOf<Long>()

    // 标记当前是否正在进入画中画模式
    var isPipModeOpening  = false

    // 对方加入，或者重新加入监听
    private val _userJoinedStateFlow = MutableStateFlow<Set<Long>>(emptySet())
    val userJoinedStateFlow: StateFlow<Set<Long>> = _userJoinedStateFlow.asStateFlow()

    // 自己加入，或者重新加入监听
    private val _myJoinedStateFlow = MutableStateFlow<Boolean>(true)
    val myJoinedStateFlow: StateFlow<Boolean> = _myJoinedStateFlow.asStateFlow()

    // 当前房间是否存在画中画
    private val _isPipModeShowing = MutableStateFlow(false)
    val isPipModeShowingFlow: StateFlow<Boolean> = _isPipModeShowing.asStateFlow()

    var isPipModeShowing
        set(value) {
            _isPipModeShowing.value = value
            if (!value) {
                isPipModeOpening = false
            }
            logInfo(TAG, "VoiceCallRoom#isPipModeEnabled:${value}", logLine = LogLine.RTC_CALL)
        }
        get() = _isPipModeShowing.value


    init {
        //roomLifecycleRegistry 必需是第一个，注册在它前面的manager生命周期会不准
        roomLifecycleRegistry.bind(this)
        roomAudioOutputManager.bind(this)
        roomSeatManager.bind(this)
        roomGuarder.bind(this)
        roomTimeManager.bind(this)
        roomSensorManager.bind(this)
        roomPipModeManager.bind(this)
        rtcVcResManager.bind(this)

        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        scope.launch {
            AudioManagerHelper.muteChange(false)
        }
    }

    fun onStart() = invokeLifecycle(START) {
        logInfo(TAG, "onStart: channelId = $channelId callType = $callType", logLine = LogLine.RTC_CALL)
        ChannelPendStatusManager.changeStatus(CallPendStatus.ANSWER)
        DoreRTCEnginManager.addEventHandler(this)
        managerRegister.onStart(channelId)
    }

    fun onWaiting(cid: Long) = invokeLifecycle(WAITING) {
        channelId = cid
        logInfo(TAG, "onWaiting: channelId = $channelId,callType = $callType",logLine = LogLine.RTC_CALL)
        managerRegister.onWaiting(channelId)
        lifecycleManager.onWaiting()
    }

    fun onConnecting() = invokeLifecycle(CONNECTING) {
        logInfo(TAG, "onConnecting: channelId = $channelId,callType = $callType",logLine = LogLine.RTC_CALL)
        if (callStartTime != 0L && roomLaunchMode == RoomLaunchMode.CREATE) {
            reportEvent(true, null)
        }

        ChannelInviteManager.dismiss(channelId.toString(), emitCancel = true)
        ChannelPendStatusManager.changeStatus(CallPendStatus.CONNECTING)
        managerRegister.onConnecting(channelId)
    }

    fun onConnected() = invokeLifecycle(CONNECTED) {
        RecordingConstant.tekTT2024071701("2")
        logInfo(TAG, "onConnected: channelId = $channelId,callType = $callType",logLine = LogLine.RTC_CALL)
        ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE)
        ChannelPendStatusManager.changeCallWaitingStatus(
            isWaiting = false, isCallConnected = true
        )

        managerRegister.onConnected(channelId)

        if (channelType == ChannelType.TYPE_VOICE_CALL_GROUP) {
            //The group call successfully establishes the RTC channel and has sound effects.
            OnlineChatRingtoneManager.playCallJoinSoundEffect()
        }
        //未知情况下(可能是系统bug)点击call style进入到通话中页面，呼叫通知栏不自动消失，所以在此处兜底检查一遍，如果通知栏还存在，则取消通知栏
        channelId?.let {
            checkAndDismissCallStyleNotification(it, channelType)
        }
    }

    private fun checkAndDismissCallStyleNotification(
        channelId: Long, channelType: @ChannelType Int
    ) {
        val voiceCallNotifiId = CallNotificationCache.getVoiceCallNotifiIdByChannelId(channelId)
        if (voiceCallNotifiId != null) {
            NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(
                voiceCallNotifiId, channelType
            )
        }
    }

    fun onDestroy(reason: Int) = invokeLifecycle(DESTROY) {
        logInfo(TAG, "VoiceCallRoom onDestroy START: reason=$reason, channelId=$channelId, " +
                "callType=$callType, timestamp=${System.currentTimeMillis()}")

        if (CallEndType.CallEndTypeListByStartCallDelay.contains(reason)){
            //这些CallEndType 是由于进房发起实时通话，ResponseStartRealTimeCall返回的异常码，
            //在code返回时，通话中页面可能还没onCreate, 导致进入页面无法再监听[VoiceCallRoom.roomDestroyReason]，所以事件emit需要做delay的特殊处理
            logInfo(TAG, "CallEndTypeListByStartCallDelay will delay(1000) onDestroy:reason = $reason, channelId = $channelId",
                logLine = LogLine.RTC_CALL)
            delay(1000)
        }
        logInfo(TAG, "onDestroy:reason = $reason channelId = $channelId,callType = $callType", logLine = LogLine.RTC_CALL)
        if (!getLifeCycle().state.isAtLeast(CONNECTING) && roomLaunchMode == RoomLaunchMode.CREATE) {
            reportEvent(false, reason)
        }
        GlobalScope.launch(Dispatchers.Main) {
            ProcessLifecycleOwner.get().lifecycle.removeObserver(this@VoiceCallRoom)
        }
        reportCallEnd(reason)
        handleVoiceAndVibrate(reason)
        viewModelStore.clear()
        _roomDestroyReason.emit(reason)

        logInfo(TAG, "VoiceCallRoom onDestroy: 开始调用 managerRegister.onDestroy")
        managerRegister.onDestroy(reason, channelId)
        logInfo(TAG, "VoiceCallRoom onDestroy: managerRegister.onDestroy 完成")

        _userJoinedStateFlow.emit(emptySet())
        _myJoinedStateFlow.emit(false)
        DoreRTCEnginManager.removeEventHandler(this)
        updateCallWaitingState(reason)
        updatePendingStateToIDLE()
        doInTryCatch { lifecycleThread.close() }
        if (scope.isActive) {
            scope.cancel()
        }
        if (reason == CallEndType.HANG_UP_BY_ME){
            mHangUpCallback?.invoke()
        }

        logInfo(TAG, "VoiceCallRoom onDestroy END: reason=$reason")
    }

    private fun handleVoiceAndVibrate(reason: Int) {
        when (reason) {
            CallEndType.HANG_UP -> {
                OnlineChatRingtoneManager.playCallLeaveSoundEffect()
            }

            CallEndType.EXC_BUSY, CallEndType.REJECT -> {
                OnlineChatRingtoneManager.playCallLeaveSoundEffect()
            }

            CallEndType.TIME_OUT -> {
                OnlineChatRingtoneManager.playNoAnswerSoundEffect()
            }
        }
    }

    private fun updateCallWaitingState(reason: Int) {
        //房间销毁发生在各种正常/异常情况下都需要确保呼叫邀请的状态重置
        ChannelPendStatusManager.changeCallWaitingStatus(
            isWaiting = false, isCallConnected = false
        )
    }

    private fun updatePendingStateToIDLE() {
        val isPendingValue = ChannelPendStatusManager.isPendingFlow.value
        //Answer和Connecting状态下对应的房间一定是当前destroy的房间，这个时候把状态值设回idle
        if (isPendingValue.second != BEING_INVITED && isPendingValue.second != DECLINE) {
            ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE, null)
        }
    }

    override fun onLIEAudioDeviceChangeForAgora(isPeripheral: Boolean) {
        super.onLIEAudioDeviceChangeForAgora(isPeripheral)
        scope.launch {
            logInfo(TAG, "onLIEAudioDeviceChangeForAgora :isPeripheral = $isPeripheral")
            _isPeripheralForAgora.emit(isPeripheral)
        }
    }

    override fun onLIEError(err: Int) {
        scope.launch {
            logWarn(TAG, "onLIEError:err = $err", logLine = LogLine.RTC_CALL)
            _lieErrorCode.emit(err)
        }
    }

    override fun onLIEWarn(err: Int) {
        if (err == LiveInteractiveConstant.WARN_LEAVE) {
            scope.launch {
                _userJoinedStateFlow.emit(emptySet())
                _myJoinedStateFlow.emit(false)
            }
        }
    }

    override fun onLIEJoinChannelSuccess(uid: Long) {
        logInfo(TAG, "onLIEJoinChannelSuccess:uid = $uid", logLine = LogLine.RTC_CALL)
        scope.launch {
            _myJoinedStateFlow.emit(true)
        }
        lifecycleManager.onLIEJoinChannelSuccess(uid)
    }

    override fun onLIERejoinChannelSuccess(uid: Long) {
        logInfo(TAG, "onLIERejoinChannelSuccess:uid = $uid", logLine = LogLine.RTC_CALL)
        scope.launch {
            _myJoinedStateFlow.emit(true)
        }
        lifecycleManager.onLIERejoinChannelSuccess(uid)
        roomGuarder.onLIERejoinChannelSuccess(uid)
    }

    override fun onLIEClientRoleChanged(oldRole: BaseRoleType?, newRole: BaseRoleType?) {

    }

    override fun onLIEUserJoined(uid: Long) {
        logInfo(TAG, "onLIEUserJoined:uid = $uid", logLine = LogLine.RTC_CALL)
        scope.launch {
            _userJoinedStateFlow.update { currentUsers ->
                currentUsers + uid
            }
            joinedMemberSet.add(uid)
            logInfo(TAG, "historyJoinedCount:${joinedMemberSet.size}", logLine = LogLine.RTC_CALL)
        }
        lifecycleManager.onLIEUserJoined(uid)
        roomGuarder.onUserJoinRoom(uid)
    }

    override fun onLIEUserOffline(uid: Long) {
        logInfo(TAG, "onLIEUserOffline:uid = $uid", logLine = LogLine.RTC_CALL)
        scope.launch {
            _userJoinedStateFlow.update { currentUsers ->
                currentUsers - uid
            }
        }
        roomGuarder.onUserLeaveRoom(uid)
    }

    fun startRealTimeCall(
        userList: List<CallRoomUser>,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        groupId: Long?,
        traceId: String
    ) {
        logInfo(
            TAG,
            "startRealTimeCall:userList = ${userList.map { "${it.userId}-${it.rtcUserId}-${it.cameraStatus}" }}, type = $channelType, callType = $callType, groupId = $groupId, traceId = $traceId",
            logLine = LogLine.START_RTCAll
        )
        callStartTime = System.currentTimeMillis()
        initCallMemberCount = userList.size

        roomLaunchMode = RoomLaunchMode.CREATE
        roomSeatManager.setInitData(userList)
        lifecycleManager.onStartRealTimeCall()
        val userIds = userList.map { it.userId }

        viewModel.startRealTimeCall(userIds, channelType, callType, groupId, traceId)
    }

    fun joinRealTimeCall(
        cid: Long,
        source: Int,
        callType: @CallType Int,
        cameraStatus: @CameraStatus Int?,
        micStatus: @MicStatus Int?,
        channelType: @ChannelType Int
    ) {
        logInfo(TAG, "joinRealTimeCall:cid = $cid, source = $source, cameraStatus = $cameraStatus, micStatus = $micStatus",
            logLine = LogLine.RTC_CALL)
        ChannelInviteManager.dismiss(channelId.toString(), emitCancel = true)
        joinSource = source
        roomLaunchMode = RoomLaunchMode.JOIN
        channelId = cid
        lifecycleManager.onJoinRealTimeCall()
        viewModel.joinRealTimeCall(
            channelId = cid,
            source = source,
            callType = callType,
            cameraStatus = cameraStatus,
            micStatus = micStatus,
            channelType = channelType
        )
    }

    //这个回调用于处理当正在通话中时有新的通话邀请进来，需要先挂断当前的通话（CallEndType.HANG_UP_BY_ME），再回调处理新的邀请加房逻辑
    private var mHangUpCallback: (() -> Unit)? = null

    fun hangUp(hangUpCallback: (() -> Unit)? = null) {
        mHangUpCallback = hangUpCallback
        DismissOnCallMinimizeEvent.post()
        logInfo(TAG, "hangUp:channelId = $channelId" )
        viewModel.hangUp(roomChannelId)
    }

    fun inviteJoinRoom(userList: List<Long>) {
        if (channelId.isNull()) {
            logError(TAG, "invite joinRoom, time error ${getLifeCycle().state}")
            return
        }
        logInfo(TAG, "inviteJoinRoom:userList = $userList, channelId = $channelId")
        viewModel.inviteJoinRoom(userList, channelId!!)
    }

    suspend fun inviteJoinRoomSuspend(userList: List<Long>): ITResponse<ResponseInviteRealTimeCall>? {
        if (channelId.isNull()) {
            logError(TAG, "invite joinRoom, time error ${getLifeCycle().state}")
            return null
        }
        logInfo(TAG, "inviteJoinRoom:userList = $userList, channelId = $channelId")
       return viewModel.inviteJoinRoomSuspend(userList, channelId!!)
    }

    fun onReceivePush(op: Int, sendTimestamp: Long, data: JSONObject?) {
        logInfo(TAG, "onReceivePush:op = $op, sendTimestamp = $sendTimestamp, data = $data", logLine = LogLine.START_RTCAll)
        managerRegister.onReceivePush(op, sendTimestamp, data)
        lifecycleManager.onReceivePush(op, sendTimestamp, data)
        roomGuarder.onReceivePush(op, sendTimestamp, data)
    }

    override fun getLifeCycle(): RoomLifecycleRegistry {
        return roomLifecycleRegistry
    }

    override fun getLifeCycleFlow(): StateFlow<RoomLifecycle> {
        return roomLifecycleRegistry.getLifeCycleFlow()
    }

    override fun getRoomManagerRegister(): RoomManagerRegister {
        return managerRegister
    }

    override fun onLIEAudioDeviceChange(routing: Int) {
        super.onLIEAudioDeviceChange(routing)
        logInfo(TAG, "onLIEAudioDeviceChange: $routing", logLine = LogLine.RTC_CALL)
        viewModel.onLIEAudioDeviceChange(routing)
    }

    override fun onLIERecvLyrics(
        uid: Long,
        type: Int,
        offset: Int,
        sid: String?,
        lyrics: String?
    ) {

    }

    override fun onLIERecorderVolume(seatState: LiveInteractiveSeatState?) {

    }

    fun registerMicStatusListener(lifecycleOwner: LifecycleOwner, listener: MicStatusListener) {
        roomSeatManager.registerMicStatusListener(lifecycleOwner, listener)
    }

    fun registerCamStatusListener(lifecycleOwner: LifecycleOwner, listener: CamStatusListener) {
        roomSeatManager.registerCamStatusListener(lifecycleOwner, listener)
    }

    fun registerNetworkStatusListener(lifecycleOwner: LifecycleOwner, listener: NetworkStatusListener) {
        roomSeatManager.registerNetworkStatusListener(lifecycleOwner, listener)
    }

    override fun onLIELocalAudioQuality(quality: Int) {
        setLIELocalAudioQuality(quality)
    }

    override fun onLIERemoteAudioQualityOfUid(uid: Long, quality: Int) {
        roomSeatManager.onUserNetworkChange(uid, quality)
    }

    override fun onLIESpeakingStates(states: MutableList<LiveInteractiveSeatState>?) {
        if (states.isNullOrEmpty()) return
        val currentTime = NtpTime.nowForce()
        if (currentTime < updateSpeakingStateSample + lastUpdateSpeakingStateTime) return

        lastUpdateSpeakingStateTime = currentTime
        roomSeatManager.onUserSpeakingStateChange(states)
        roomGuarder.onUserSpeakingStateChange(states)

        scope.launch {
            val seatStateList = states.map { seatState ->
                SeatRtcVolumeInfo(
                    rtcId = seatState.uniqueId,
                    speaking = seatState.volume > 0,
                    volume = seatState.volume
                )
            }
            _speakingFlow.emit(seatStateList)
        }
    }

    fun changeMyMicStatus(channelId: Long, micStatus: @MicStatus Int) {
        _myMicStatus.value = micStatus
        logInfo(TAG, "changeMicStatus my micStatus = $micStatus")
        roomSeatManager.onMyMicStatusChange(micStatus)
        viewModel.changeMicStatus(channelId, micStatus)
    }

    fun changeMyCamStatus(channelId: Long, cameraStatus: @CameraStatus Int, from: String = "") {
        _myCamStatus.value = cameraStatus
        if (cameraStatus == CameraStatus.OPEN) {
            hasOpenedCamera = true
        }
        logInfo(TAG, "changeCamStatus _myCamStatus = $cameraStatus, from=$from", logLine = LogLine.MY_RTC_CAMERA_STATE)
        roomSeatManager.onMyCamStatusChange(cameraStatus, from = from)
        viewModel.changeCamStatus(channelId, cameraStatus)
    }

    fun changeUserCamStatus(memberInfo: CallUserInfo) {
        logInfo(TAG, "changeUserCamStatus:userId = ${memberInfo.userId}, cameraStatus = ${memberInfo.cameraStatus}",
            logLine = LogLine.RTC_CALL)
        if (memberInfo.cameraStatus == CameraStatus.OPEN) {
            hasOpenedCamera = true
        }
    }


    private fun setLIELocalAudioQuality(quality: Int) {
        audioQuality = quality
        changeNetworkStatus(maxOf(audioQuality, networkQuality))
    }

    fun setNetworkQuality(quality: Int) {
        networkQuality = quality
        changeNetworkStatus(maxOf(audioQuality, networkQuality))
    }

    private fun changeNetworkStatus(networkStatus: @NetworkStatus Int) {
        _myNetwork.value = networkStatus
        roomSeatManager.onMyNetworkChange(networkStatus)
    }

    fun changeCamState(camState: @CameraState Int) {
        _myCamState.value = camState
        roomSeatManager.onMyCamStateChange(camState)
        logInfo(TAG, "changeCamState my cameraState = $camState")
    }

    fun switchCamState() {
        _myCamState.value = CameraState.toggle(myCamState.value)
        roomSeatManager.onMyCamStateChange(myCamState.value)
        logInfo(TAG, "switchCamState my cameraState = ${myCamState.value}")
    }

    fun switchAudioOutputDevice(routing: Int) {
        logInfo(TAG, "switchAudioOutputDevice:routing = $routing")
        roomAudioOutputManager.switchAudioOutputDevice(routing)
    }

    private fun invokeLifecycle(lifecycle: RoomLifecycle, block: suspend () -> Unit) {
        scope.launch(lifecycleThread) {
            if (getLifeCycle().state == lifecycle) return@launch
            block()
        }
    }

    private fun reportEvent(isSuccess: Boolean, reason: @CallEndType Int?) {
        logInfo(TAG, "reportEvent: reason = $reason $roomChannelId")
        val waitTime = System.currentTimeMillis() - callStartTime
        var failReason: String? = null
        if (!isSuccess) {
            failReason = when (reason) {
                CallEndType.HANG_UP_BY_ME -> "cancel"
                CallEndType.REJECT -> "reject"
                CallEndType.TIME_OUT -> "timeout"
                CallEndType.EXC_BUSY -> "busy"
                CallEndType.EXC_GROUP_ON_CALL -> "4002_did_exist_active_channel"
                CallEndType.EXC_ON_CALL -> "4003_did_exist_other_calling_channel"
                CallEndType.EXC_FINISH -> "4004_call_did_end"
                CallEndType.EXC_OVERFLOW -> "4005_calling_members_did_reach_maxcount"
                CallEndType.EXC_UNSUPPORTED_COUNTRY -> "4013_calling_unsupported_country"
                else -> "other"
            }
        }

        if (channelType == ChannelType.TYPE_VOICE_CALL_1V1) {
            CommonTracker.onStartPrivateVoiceCallResult(
                targetId = minimizeTargetId,
                channelId = roomChannelId ?: 0L,
                callType = callType,
                waitTime = waitTime,
                isSuccess = isSuccess,
                failReason = failReason
            )
        } else {
            CommonTracker.onStartGroupVoiceCallResult(
                targetId = minimizeTargetId,
                memberCount = initCallMemberCount,
                callType = callType,
                channelId = roomChannelId ?: 0L,
                waitTime = waitTime,
                isSuccess = isSuccess,
                failReason = failReason
            )
        }
    }

    private fun reportCallEnd(reason: Int) {
        VoiceCallNotificationTracker.onRTCRoomClosedEvent(
            channelType = channelType,
            channelId = channelId ?: 0,
            callType = callType,
            hasOpenedCamera = hasOpenedCamera,
            duration = callDurationSecond * 1000L,
            memberCount = joinedMemberSet.size + 1
        )
    }

    fun generateCallRoomInfo(reason: Int): CallRoomInfoBean {
        logInfo(TAG,"generateCallRoomInfo:joinedMemberSet = ${joinedMemberSet.size}")
        return CallRoomInfoBean(
            channelType = channelType,
            channelId = channelId ?: 0,
            callType = callType,
            memberCount = joinedMemberSet.size + 1,
            hasOpenedCamera = hasOpenedCamera,
            duration = callDurationSecond * 1000L,
            reason = reason.toString()
        )
    }

    override fun toString(): String {
        return "room@{targetId:${minimizeTargetId}, channelId:${channelId}, callType:${callType}, channelType:${channelType}}"
    }

    fun destroy() {
        logInfo(TAG, "room destroyed", logLine = LogLine.RTC_CALL)
        joinedMemberSet.clear()
    }

}

@Parcelize
data class CallRoomInfoBean(
    val channelType: @ChannelType Int,
    val callType: @CallType Int,
    val channelId: Long,
    val memberCount: Int = 1,
    val hasOpenedCamera: Boolean = false,
    val duration: Long = 0, // ms
    val reason: String=""
) : Parcelable