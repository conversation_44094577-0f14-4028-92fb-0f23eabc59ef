package com.interfun.buz.media.player.exo

import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.webkit.URLUtil
import androidx.annotation.OptIn
import androidx.annotation.UiThread
import androidx.media3.common.AudioAttributes
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.Player.Command
import androidx.media3.common.TrackSelectionParameters
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.ui.PlayerView
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.application
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.mainThreadHandler
import com.interfun.buz.media.R
import com.interfun.buz.media.bean.BuzMediaItem
import com.interfun.buz.media.player.manager.PagePlayerManager
import com.interfun.buz.media.player.view.IPlayTarget

/**
 * 单个页面的播放器（可播放视频 or 音频）
 */
class SinglePagePlayer {
    val TAG = "SinglePagePlayer"

    @get:UiThread
    val contentPosition: Long
        get() {
            return exoPlayer.contentPosition
        }

    @get:UiThread
    val currentPosition: Long
        get() {
            return exoPlayer.currentPosition
        }

    @get:UiThread
    val bufferedPosition: Long
        get() {
            return exoPlayer.bufferedPosition
        }

    @get:UiThread
    @set:UiThread
    var playWhenReady: Boolean
        set(value) {
            exoPlayer.playWhenReady = value
        }
        get() {
            return exoPlayer.playWhenReady
        }

    @get:UiThread
    @set:UiThread
    var repeatMode: Int
        set(value) {
            exoPlayer.repeatMode = value
        }
        get() {
            return exoPlayer.repeatMode
        }

    @set:UiThread
    var isMute = false
        set(value) {
            exoPlayer.volume = if (value) 0f else currentVolume
            field = value
        }

    @get:UiThread
    val duration get() = exoPlayer.duration
    var isPlaying = false
    var playTarget: IPlayTarget? = null // 当前正在播放的目标（针对视频播放，音频播放是null）
        set(value) {
            if (field != value) {
                field = value
                playTargetChangeCallback?.invoke(value)
            }
        }
    var playerView: PlayerView? = null //视频显示组件(同一个播放器共享)

    var buzMediaItem: BuzMediaItem? = null // 当前播放的媒体，每次播放的时候都会更新
    var playerControllerView: BuzPlayerControlView? = null //视频控制组件(同一个播放器共享)
        private set

    private var playTargetChangeCallback: OneParamCallback<IPlayTarget?>? = null

    // 当前音量
    private var currentVolume: Float = 0f

    // 播放器
    @OptIn(UnstableApi::class)
    private val exoPlayer: ExoPlayer by lazy {
        ExoPlayer.Builder(appContext)
            .setReleaseTimeoutMs(1000)
            .setHandleAudioBecomingNoisy(true) // 拔出耳机自动暂停
            // 设置默认的MediaSourceFactory，等setMediaItem的时候在判断是否需要支持缓存
            .setMediaSourceFactory(DefaultMediaSourceFactory(application)).build().apply {
                addAnalyticsListener(PlayerEventLogger())
                trackSelectionParameters = TrackSelectionParameters.Builder(appContext).build()
                setAudioAttributes(AudioAttributes.DEFAULT,  /* handleAudioFocus= */true)
                // 当前音量
                currentVolume = volume
            }
    }


    init {
        // 播放器界面
        initPlayerView()
    }

    private fun initPlayerView() {
        logDebug(TAG, "initPlayerView")
        playerView = LayoutInflater.from(appContext)
            .inflate(R.layout.media_exo_player_view_layout, null) as PlayerView
        playerView?.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
            }

            override fun onViewDetachedFromWindow(v: View) {
                buzMediaItem = null
            }
        })
    }

    fun isCommandAvailable(@Command command: Int): Boolean {
        return exoPlayer.isCommandAvailable(command)
    }


    /**
     * 跳到自定位置播放
     *
     * @param positionMs
     */
    fun seekTo(positionMs: Long) {
        runOnUiThread {
            exoPlayer.seekTo(positionMs)
        }
    }

    /**
     * 回退到上一个位置
     */
    fun seekBack() {
        runOnUiThread {
            exoPlayer.seekBack()
        }
    }

    /**
     * 跳转到下一个位置
     */
    fun seekToNext() {
        runOnUiThread {
            exoPlayer.seekToNext()
        }
    }

    /**
     * 跳转到上一个位置
     */
    fun seekToPrevious() {
        runOnUiThread {
            exoPlayer.seekToPrevious()
        }
    }

    /**
     * 快进到下一个位置
     */
    fun seekForward() {
        runOnUiThread {
            exoPlayer.seekForward()
        }
    }


    /**
     * 音量+
     */
    fun setVolumeUp() {
        runOnUiThread {
            currentVolume += 0.1f
            isMute = false
            exoPlayer.volume = currentVolume
        }
    }

    /**
     * 音量-
     */
    fun setVolumeDown() {
        runOnUiThread {
            currentVolume -= 0.1f
            isMute = false
            exoPlayer.volume = currentVolume
        }
    }

    /**
     * 自定义视频播放控制器布局id
     *
     * @param controllerLayoutId
     */
    fun setControllerLayoutId(controllerLayoutId: Int) {
        if (playerControllerView == null) {
            playerControllerView = BuzPlayerControlView(appContext)
        }
        playerControllerView?.setControllerLayoutId(controllerLayoutId)
        playerControllerView?.player = exoPlayer
    }

    /**
     * 自定义视频播放控制器布局View
     */
    fun setControllerLayoutView(
        controllerLayoutView: View,
        controlGravity: Int,
        controllerLayoutHeight: Int,
        controllerBottomMargin: Int
    ) {
        if (playerControllerView == null) {
            playerControllerView = BuzPlayerControlView(appContext)
        }
        playerControllerView?.setControllerView(
            controllerLayoutView,
            controlGravity,
            controllerLayoutHeight,
            controllerBottomMargin
        )
        playerControllerView?.player = exoPlayer
    }


    fun getControlView(): BuzPlayerControlView {
        if (playerControllerView == null) {
            playerControllerView = BuzPlayerControlView(appContext)
        }
        playerControllerView?.player = exoPlayer
        return playerControllerView!!
    }

    /**
     * 将播放器关联到指定的控制器上
     * 当非共享控制器模式的时候需要调用这个绑定播放器
     */
    fun bindTargetControllerView(buzPlayerControlView: BuzPlayerControlView) {
        buzPlayerControlView.player = exoPlayer
    }

    /**
     * 播放(视频/音频)
     */
    @UnstableApi
    fun play(mediaSource: MediaSource) {
        runOnUiThread {
            logDebug(TAG, "play")
            exoPlayer.setMediaSource(mediaSource)
            exoPlayer.prepare()
            exoPlayer.playWhenReady = true
        }
    }

    /**
     * 暂停播放(视频/音频)
     */
    fun pause() {
        runOnUiThread {
            if (isPlaying) {
                logDebug(TAG, "pause")
                exoPlayer.playWhenReady = false
                isPlaying = false
            }
        }
    }

    /**
     * 暂停视频播放,和pause的区别是，里面优先判断playTarget
     */
    fun pauseMediaPlay() {
        runOnUiThread {
            logDebug(TAG, "pauseMediaPlay==>playTarget=${playTarget},isPlaying=${isPlaying}")
            playTarget?.inActive() ?: pause()
            isPlaying = false
        }
    }

    /**
     * 恢复/开始 视频播放，和resume的区别是，里面优先判断playTarget
     */
    fun resumeMediaPlay() {
        runOnUiThread {
            logDebug(TAG, "resumeMediaPlay==>playTarget=${playTarget},isPlaying=${isPlaying}")
            if (playerView == null) {
                initPlayerView()
            }
            playTarget?.onActive() ?: resume()
            isPlaying = true
        }
    }

    /**
     * 切换控制器的展示/隐藏
     */
    fun toggleControllerViewShow(
        show: Boolean,
        withAnimation: Boolean = true,
        autoHideAfterShow: Boolean = true
    ) {
        playTarget?.toggleControllerViewShow(show, withAnimation, autoHideAfterShow)
    }

    /**
     * 恢复播放(视频/音频)
     */
    fun resume() {
        runOnUiThread {
            logDebug(TAG, "resume")
            val hasPlay = exoPlayer.contentPosition > 0
            if (hasPlay) {
                exoPlayer.playWhenReady = true
                isPlaying = true
            }
        }
    }

    /**
     * 释放资源(视频/音频)
     */
    fun release() {
        logDebug(TAG, "release")
        runOnUiThread {
            exoPlayer.apply {
                playWhenReady = false
                stop()
                release()
            }
        }
        playerView?.player = null
        playerControllerView = null
        playerView = null
        buzMediaItem = null
        isPlaying = false
        playTarget = null
    }

    fun play() {
        runOnUiThread {
            exoPlayer.play()
        }
    }

    /**
     * 切换与播放器exoplayer 绑定的exoplayerView。用于页面切换视频无缝续播的场景
     *
     * @param newPlayerView
     * @param attach        true 切换到新的播放页面,false 恢复原来的播放页面
     */
    fun switchPlayerView(newPlayerView: PlayerView, attach: Boolean) {
        if (newPlayerView == playerView) {
            if (newPlayerView.player != exoPlayer) {
                newPlayerView.player = exoPlayer
            }
            return
        }
        playerView?.player = if (attach) null else exoPlayer
        newPlayerView.player = if (attach) exoPlayer else null
    }

    fun detachedPlayerView() {
        playerView?.player = null
    }

    @OptIn(UnstableApi::class)
    fun setMediaItem(buzMediaItem: BuzMediaItem) {
        runOnUiThread {
            logInfo(TAG, "setMediaItem mediaItem ${buzMediaItem}")
            this.buzMediaItem = buzMediaItem
            val mediaItem = MediaItem.Builder().setUri(buzMediaItem.mediaUri)
                .setCustomCacheKey(buzMediaItem.mediaUri).build()

            val mediaSource = PagePlayerManager.createMediaSource(
                mediaItem = mediaItem,
                autoCache = URLUtil.isNetworkUrl(buzMediaItem.mediaUri)
            )
            exoPlayer.setMediaSource(mediaSource)
        }
    }

    fun prepare() {
        runOnUiThread {
            exoPlayer.prepare()
        }
    }


    fun addListener(listener: Player.Listener) {
        logInfo(TAG, "addListener ${listener.hashCode()}")
        exoPlayer.addListener(listener)
    }

    fun removeListener(listener: Player.Listener) {
        logInfo(TAG, "removeListener ${listener.hashCode()}")
        runOnUiThread {
            exoPlayer.removeListener(listener)
        }
    }

    @UiThread
    fun shouldShowPlayButton(): Boolean {
        return !exoPlayer.playWhenReady
                || exoPlayer.playbackState == Player.STATE_IDLE
                || exoPlayer.playbackState == Player.STATE_ENDED
                || exoPlayer.playbackSuppressionReason != Player.PLAYBACK_SUPPRESSION_REASON_NONE
    }

    /**
     * 注册播放器目标变化监听
     */
    fun registerPlayTargetChangeCallback(callback: OneParamCallback<IPlayTarget?>) {
        playTargetChangeCallback = callback
    }


    private fun runOnUiThread(runnable: Runnable) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run()
        } else {
            mainThreadHandler.post(runnable)
        }
    }
}