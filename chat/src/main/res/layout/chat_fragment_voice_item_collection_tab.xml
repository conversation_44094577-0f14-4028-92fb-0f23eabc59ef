<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/veBannerTips"
        layout="@layout/chat_item_ve_panel_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvCollection"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="10dp"
        android:splitMotionEvents="false"
        app:layout_constraintTop_toBottomOf="@id/veBannerTips"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.interfun.buz.common.widget.view.EmptyDataView
        android:id="@+id/emptyDataView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginBottom="@dimen/voice_item_panel_primary_tab_height"
        android:visibility="gone"
        app:empty_type="no_data"
        app:text="@string/ve_panel_no_fav_data"
        app:layout_constraintTop_toTopOf="@id/rvCollection"
        app:layout_constraintStart_toStartOf="@id/rvCollection"
        app:layout_constraintBottom_toBottomOf="@id/rvCollection"
        app:layout_constraintEnd_toEndOf="@id/rvCollection"/>

</androidx.constraintlayout.widget.ConstraintLayout>