package com.interfun.buz.common.widget.view

import androidx.media3.common.util.UnstableApi
import com.interfun.buz.common.manager.userLifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

// TODO VG 这里还发现存在一些exo的文件，不知道是怎么来的。要问一下基哥
@UnstableApi
object VoiceGifVideoCache {
    private const val TAG = "VoiceGifVideoCache"
    private val cacheManager = CacheManager(TAG,"voicegif")

    suspend fun cache(url: String): Pair<String,String?> = withContext(Dispatchers.IO) {
        val filePath = cacheManager.cache(url)
        Pair(url,filePath)
    }

    fun preloadCache(url: String) {

        userLifecycleScope?.launch {
            cacheManager.cache(url)
        }
    }

    fun getCacheSize() : Long {
        val file = cacheManager.getCopyCacheDir()
        // 文件夹自身会带3KB的大小。先不要计算进来，因为删除的时候不会删掉，给人的感觉是有3KB删除不干净
        var size =  0L // file.length().getLongDefault()
        if (file.isDirectory && file.isDirectory) {
            file.listFiles()?.forEach {
                if (it.exists()) {
                    size += it.length()
                }
            }
        }
        return size
    }

    fun deleteCacheFile(list:List<String>):List<String> {
        val deleteSuccessList = arrayListOf<String>()
        list.forEach {
            val result = cacheManager.getCachedFile(it)?.delete()
            if (result == true) {
                deleteSuccessList.add(it)
            }
        }
        return deleteSuccessList
    }

}

