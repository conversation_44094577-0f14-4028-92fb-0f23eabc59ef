package com.interfun.buz.chat.ai.invite.bean

import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.manager.cache.ai.GroupBotDataHelper

/**
 * Author: ChenYouSheng
 * Date: 2023/11/16
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * Desc:
 */
data class GroupListBean(
    val groupInfoBean: GroupInfoBean,
    var isSelected: Boolean = false,
    val isOverLimit: Boolean = false,
    val alreadyInGroupBotId: Long? = null,
    val currentInviteBotId: Long,
) {

     suspend fun getAlreadyInGroupBotName(): String? {
        return alreadyInGroupBotId?.let { userId ->
            val cache =
                GroupBotDataHelper.getFromCache(groupId = groupInfoBean.groupId)
                    ?.firstOrNull { it.userId == userId }?.userName
            if (cache.isNullOrEmpty()) {
                return GroupBotDataHelper.getFromNet(groupId = groupInfoBean.groupId)
                    ?.firstOrNull {
                        it.userId == userId
                    }?.userName
            }
            return cache
        }
    }

    fun isCurrentBotInGroup():Boolean = currentInviteBotId == alreadyInGroupBotId

}
