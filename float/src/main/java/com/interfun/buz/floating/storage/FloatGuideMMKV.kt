package com.interfun.buz.floating.storage

import com.interfun.buz.base.ktx.MMKVOwner
import com.interfun.buz.base.ktx.mmkvBool
import com.interfun.buz.base.ktx.mmkvInt
import com.interfun.buz.common.ktx.userMMKVBool

object FloatGuideMMKV : MMKVOwner {
    //true means guidance for new user has been shown.Otherwise false.
    var hasFloatNewUserGuidanceBeenShown by mmkvBool()

    //user can toggle it in the setting page.
    //when is true,float window can be shown in background,while false can't
    var isBuzOverlayEnable by userMMKVBool(true)

    //true means float window has been shown. Otherwise false.
    var hasFloatBeenShown by mmkvBool()
    var hasVideoCallFloatBeenShown by mmkvBool()

    var lastFloatVerticalY by mmkvInt(-999)
    var lastFloatHorizontalY by mmkvInt(-999)
    var isLastInLeft by mmkvBool()
}