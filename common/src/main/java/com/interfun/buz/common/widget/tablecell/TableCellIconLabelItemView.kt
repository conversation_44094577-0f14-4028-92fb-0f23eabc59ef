package com.interfun.buz.common.widget.tablecell

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.view.View
import androidx.annotation.ColorInt
import androidx.constraintlayout.widget.ConstraintLayout
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.getCompatColor
import com.interfun.buz.base.ktx.getString
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.common.R
import com.interfun.buz.common.databinding.CommonItemTableCellIconLabelBinding
import com.interfun.buz.common.ktx.getText

class TableCellIconLabelItemView @JvmOverloads constructor(
    context: Context, val attrs: AttributeSet? = null
): ConstraintLayout(context, attrs) {

    private var inflateView: View = inflate(context, R.layout.common_item_table_cell_icon_label, this)
    private val binding: CommonItemTableCellIconLabelBinding by lazy {
        CommonItemTableCellIconLabelBinding.bind(inflateView)
    }
    init {
        initAttr()
    }

    private fun initAttr(){
        val typedArray: TypedArray =
            context.obtainStyledAttributes(attrs, R.styleable.TableCellIconLabelItemView)
        val titleText = typedArray.getText(context = context,R.styleable.TableCellIconLabelItemView_title)
        val labelText = typedArray.getText(context = context,R.styleable.TableCellIconLabelItemView_labelText)
        val startIcon = typedArray.getText(context = context,R.styleable.TableCellIconLabelItemView_startIcon)
        val startIconColor = typedArray.getColor(R.styleable.TableCellIconLabelItemView_startIconColor, R.color.color_background_highlight_1_default.asColor())
        val endIcon = typedArray.getText(context = context,R.styleable.TableCellIconLabelItemView_endIcon) ?: getString(R.string.ic_arrow_right)
        val endIconColor = typedArray.getColor(R.styleable.TableCellIconLabelItemView_endIconColor, R.color.color_text_white_primary.asColor())
        val showRedDot = typedArray.getBoolean(R.styleable.TableCellIconLabelItemView_showRedDot, false)
        typedArray.recycle()

        binding.tvContent.text = titleText
        if (labelText.isNullOrEmpty().not()) {
            binding.tvLabel.visible()
            binding.tvLabel.text = labelText
        }else{
            binding.tvLabel.gone()
        }
        if (startIcon.isNullOrEmpty()){
            binding.iftvIcon.gone()
        }else{
            binding.iftvIcon.visible()
            binding.iftvIcon.text = startIcon
            binding.iftvIcon.setTextColor(startIconColor)
        }
        if(endIcon.isEmpty()){
            binding.iftvEndIcon.gone()
        }else{
            binding.iftvEndIcon.visible()
            binding.iftvEndIcon.text = endIcon
            binding.iftvEndIcon.setTextColor(endIconColor)
        }
        binding.redDot.visibleIf(showRedDot)
    }


    fun setTitle(title: CharSequence) {
        binding.tvContent.text = title
    }

    fun setLabelText(labelText: CharSequence) {
        binding.tvLabel.visible()
        binding.tvLabel.text = labelText
    }

    fun setStartIcon(iconFont: CharSequence) {
        binding.iftvIcon.visible()
        binding.iftvIcon.text = iconFont
    }

    fun setStartIconColor(@ColorInt color: Int){
        binding.iftvIcon.setTextColor(color)
    }

    fun setEndIcon(iconFont: CharSequence){
        binding.iftvEndIcon.visible()
        binding.iftvEndIcon.text = iconFont
    }

    fun setEndIconColor(@ColorInt color: Int){
        binding.iftvEndIcon.setTextColor(color)
    }

    fun showRedDot(redDotShown: Boolean){
        binding.redDot.visibleIf(redDotShown)
    }

    fun showSeparatorLine(isShow: Boolean){
        binding.separator.visibleIf(isShow)
    }

}