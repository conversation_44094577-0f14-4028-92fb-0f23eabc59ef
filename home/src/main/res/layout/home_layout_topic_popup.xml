<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#000"
    tools:ignore="MissingDefaultResource"
    tools:layout_height="40dp">

    <ImageView
        android:id="@+id/ivTopic"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/chat_icon_robot_topic_bubble"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.chat.ai.topic.popup.TopicMarqueWrapTextView
        android:id="@+id/tvTopic"
        android:layout_width="0dp"
        android:layout_marginStart="10dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.chat.ai.topic.popup.TopicMarqueWrapTextView
        android:id="@+id/tvTopic2"
        android:layout_width="0dp"
        android:layout_marginStart="10dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:text="@string/ic_exit"
        android:layout_marginEnd="12dp"
        android:gravity="center"
        android:textSize="14dp"
        android:paddingStart="10dp"
        android:textColor="@color/text_white_disable"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>