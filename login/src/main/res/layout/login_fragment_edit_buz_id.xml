<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/common_black_bg"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideStart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="40dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="40dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvBack"
        style="@style/iconfont_base"
        android:layout_width="64dp"
        android:layout_height="@dimen/title_bar_height"
        android:layout_marginStart="20dp"
        android:gravity="center"
        app:autoRTL="true"
        android:text="@string/ic_back"
        android:textColor="@color/color_text_white_secondary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_marginTop="44dp" />

    <TextView
        android:id="@+id/tvPage"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_text_white_disable"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/iftvBack"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toTopOf="@+id/iftvBack"
        tools:text="2/4" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvFeedbackIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="40dp"
        android:text="@string/ic_question_solid"
        android:textColor="@color/color_text_white_secondary"
        android:textSize="24dp"
        app:layout_constraintBottom_toBottomOf="@id/iftvBack"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iftvBack" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/text_title_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/buzid_title"
        android:textColor="@color/color_text_white_primary"
        app:layout_constrainedWidth="true"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/iftvBack"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvDesc"
        style="@style/text_body_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/buzid_desc"
        android:textColor="@color/color_text_white_tertiary"
        app:layout_constrainedWidth="true"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvBuzId"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/common_rect_overlay_white_10_radius_12"
        android:gravity="center_vertical"
        android:paddingEnd="41dp"
        android:paddingStart="52dp"
        android:singleLine="true"
        android:textColor="@color/basic_primary"
        android:textColorHint="@color/text_white_disable"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/tvDesc"
        app:layout_constraintVertical_chainStyle="packed"
        app:round_radius="4dp"
        tools:hint="BuzID654321" />

    <TextView
        android:id="@+id/tvSymbolAt"
        style="@style/regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:includeFontPadding="false"
        android:text="@string/common_symbol_at"
        android:textColor="@color/text_white_disable"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@+id/tvBuzId"
        app:layout_constraintStart_toStartOf="@+id/tvBuzId"
        app:layout_constraintTop_toTopOf="@+id/tvBuzId" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvEdit"
        style="@style/iconfont_base"
        android:layout_width="58dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/ic_edit"
        android:textColor="@color/text_white_main"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvBuzId"
        app:layout_constraintEnd_toEndOf="@+id/tvBuzId"
        app:layout_constraintTop_toTopOf="@+id/tvBuzId" />

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnNext"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:text="@string/next"
        app:type="secondary_larger" />


</androidx.constraintlayout.widget.ConstraintLayout>