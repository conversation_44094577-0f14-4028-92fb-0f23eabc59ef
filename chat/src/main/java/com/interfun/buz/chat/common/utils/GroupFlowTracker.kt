package com.interfun.buz.chat.common.utils

import com.interfun.buz.common.constants.CreateGroupSource
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.domain.social.helper.ShareType
import com.interfun.buz.domain.social.helper.ShareType.*

object GroupFlowTracker {

    fun onCreateGroupPageExposed(source: Int) {
        val sourceName = when (source) {
            CreateGroupSource.InviteBotToGroup.value -> "add_bot"
            CreateGroupSource.HomeList.value -> "homepage_create"
            CreateGroupSource.Contact.value -> "contact_page_create"
            CreateGroupSource.FriendProfile.value -> "personal_profile_page"
            else -> ""
        }
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2025041701")
            put(TrackConstant.KEY_TITLE, "create_group_naming")
            put(TrackConstant.KEY_PAGE_TYPE, "create_group")
            put(TrackConstant.KEY_SOURCE, sourceName)
        }
    }

    fun onGroupMemberSelectionPageExposed(friendCount: Int, isFromCreateGroup: Boolean) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2025041703")
            put(TrackConstant.KEY_TITLE, "group_member_selection_page")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_BUSINESS_NUM, friendCount)
            val source = if (isFromCreateGroup) {
                "creating_group"
            } else {
                "after_group_creation"
            }
            put(TrackConstant.KEY_SOURCE, source)
        }
    }

    fun onClickCreateGroupEntrance(source: Int) {
        //AC2025041701	create_group_entrance	create_group
        val sourceName = when (source) {
            CreateGroupSource.InviteBotToGroup.value -> "add_bot"
            CreateGroupSource.HomeList.value -> "homepage_create"
            CreateGroupSource.Contact.value -> "contact_page_create"
            //这个文档上没打，故意用了旧点位
            CreateGroupSource.FriendProfile.value -> "personal_profile_page"
            else -> ""
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041701")
            put(TrackConstant.KEY_TITLE, "create_group_entrance")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "create_group")
            put(TrackConstant.KEY_SOURCE, sourceName)
        }
    }

    fun onClickCreateGroupOrCancelCreateGroup(isCancel: Boolean) {
        //AC2025041702	create_group_naming	create_group_naming_action	group
        val actionName = if (isCancel) "cancel" else "create_group"
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041702")
            put(TrackConstant.KEY_TITLE, "create_group_naming")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "create_group_naming_action")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, actionName)
        }
    }

    /**
     * when user perform 'share' / 'cancel' / 'search' / 'okay' action in the group member selection page
     * @param action: 'share' / 'cancel' /'search' / 'okay'
     */
    fun onClickElementOnGroupSelectionPage(
        action: String,
        isFromCreateGroup: Boolean,
        shareType: ShareType? = null
    ) {
        //AC2025041703	create_group_member_selection	create_group_member_selection_action	group [isFromCreateGroup]
        //AC2025041706	group_member_selection_after_creation	group_member_selection_after_creation_action	group
        val shareTypeStr = when (shareType) {
            SYSTEM_SHARE -> "Share"
            QR_CODE -> "QRCode" //这个埋点文档没有，自己映射的
            COPY_LINK -> "Copylink"
            LINE -> "Line"
            INSTAGRAM -> "Instagram"
            WHATSAPP -> "WhatsApp"
            TELEGRAM -> "Telegram"
            MESSENGER -> "Messenger"
            MESSAGE -> "Message"
            SNAPCHAT -> "Snapchat"
            TIKTOK -> "TikTok" //这个埋点文档没有，自己映射的
            X -> "X" //这个埋点文档没有，自己映射的
            DISCORD -> "Discord" //这个埋点文档没有，自己映射的
            VIBER -> "Viber"//这个埋点文档没有，自己映射的
            FACEBOOK -> "Facebook"//这个埋点文档没有，自己映射的
            null -> null
            else -> null
        }
        if (isFromCreateGroup) {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041703")
                put(TrackConstant.KEY_TITLE, "create_group_member_selection")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "create_group_member_selection_action")
                put(TrackConstant.KEY_PAGE_TYPE, "group")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, action)
                if (shareTypeStr != null) {
                    put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, shareTypeStr)
                }
            }
        } else {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041706")
                put(TrackConstant.KEY_TITLE, "group_member_selection_after_creation")
                put(
                    TrackConstant.KEY_ELEMENT_CONTENT,
                    "group_member_selection_after_creation_action"
                )
                put(TrackConstant.KEY_PAGE_TYPE, "group")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, action)
                if (shareTypeStr != null) {
                    put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, shareTypeStr)
                }
            }
        }
    }

    fun onCreateGroupResult(isSuccess: Boolean, rcode: Int?, groupId: Long) {
        //RB2025041701	result_create_group_naming_next	group
        val result = if (isSuccess) "success" else "fail"
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"RB2025041701")
            put(TrackConstant.KEY_RESULT_TYPE, "result_create_group_naming_next")
            put(TrackConstant.KEY_PAGE_TYPE, "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$groupId")
            put(TrackConstant.KEY_IS_SUCCESS, result)
            if (!isSuccess) {
                put(TrackConstant.KEY_FAIL_REASON, rcode.toString())
            }
        }
    }

}