package com.interfun.buz.chat.common.view.widget

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import com.interfun.buz.base.ktx.IconFontAlignSpan
import com.interfun.buz.base.ktx.TypefaceSpanCompat
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.longClick
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatChatTranslationItemBinding
import com.interfun.buz.common.ktx.FontUtil
import java.util.Locale

sealed class TranslationState() {
    data object INIT : TranslationState()
    data object SHOW_TRANSLATION : TranslationState()
    data object SHOW_EMPTY_TRANSLATION : TranslationState()
    data object ERROR : TranslationState()
    data object LOADING : TranslationState()
}

class ChatTranslationItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    /**
     * 获取当前状态
     */
    var state: TranslationState = TranslationState.INIT
        private set

    val binding = ChatChatTranslationItemBinding.inflate(LayoutInflater.from(context), this)

    private val TAG = "ChatTranslationItemView"

    fun showLoading() {
        logInfo(TAG, "invoke showLoading ${this.hashCode()}")
        state = TranslationState.LOADING
        binding.translationLoading.visible()
        binding.contentFailure.gone()
        binding.clTranslation.gone()
    }

    fun showFailure() {
        logInfo(TAG, "invoke showFailure ${this.hashCode()}")
        state = TranslationState.ERROR
        binding.translationLoading.gone()
        binding.contentFailure.visible()
        binding.clTranslation.gone()
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
//        showNormalContent("hello")
    }

    fun showNormalContent(content: String?) {
        logInfo(TAG, "invoke showNormalContent ${this.hashCode()}")
        binding.translationLoading.gone()
        binding.contentFailure.gone()
        binding.clTranslation.visible()
        if (content.isNullOrEmpty()) {
            state = TranslationState.SHOW_EMPTY_TRANSLATION
            binding.tvTranslationContent.text = R.string.unable_transcribe.asString()
        } else {
            val rtl = isRtlFlag()
            state = TranslationState.SHOW_TRANSLATION
            val placeHolderMsg = R.string.ic_translate.asString()
            val contentSpannable =
                SpannableString(if (rtl) (content + placeHolderMsg) else (placeHolderMsg + content))
            val startIndexPlace = if (rtl) contentSpannable.lastIndex else 0
            val endIndexPlace =  if (rtl) contentSpannable.length else placeHolderMsg.length
            contentSpannable.setSpan(
                TypefaceSpanCompat(FontUtil.fontIcon!!), startIndexPlace, endIndexPlace,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            contentSpannable.setSpan(
                AbsoluteSizeSpan(18.dp), startIndexPlace, endIndexPlace,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            contentSpannable.setSpan(
                IconFontAlignSpan(color = R.color.text_white_disable.asColor()),
                startIndexPlace,
                endIndexPlace,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            binding.tvTranslationContent.text = contentSpannable
        }

    }

    private var retryCallBack: (() -> Unit)? = null
    private var retryLongCallBack: (() -> Unit)? = null

    fun setRetryClick(retryCallBack: (() -> Unit)?) {
        this.retryCallBack = retryCallBack
    }

    fun setRetryLongClick(retryLongCallBack: (() -> Unit)?) {
        this.retryLongCallBack = retryLongCallBack
    }


    init {
        binding.contentFailure.click { retryCallBack?.invoke() }
        binding.contentFailure.longClick {
            retryLongCallBack?.invoke()
        }
    }
    private fun isRtlFlag():Boolean{
        return Locale.getDefault().language == "ar"
    }
}