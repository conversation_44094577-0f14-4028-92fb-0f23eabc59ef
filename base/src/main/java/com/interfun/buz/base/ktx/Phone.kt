@file:Suppress("unused")

package com.interfun.buz.base.ktx

import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber

private val phoneUtil = PhoneNumberUtil.getInstance()

private fun parse(phone: String, region: String): PhoneNumber? {
    return try {
        phoneUtil.parse(phone, region)
    } catch (e: Exception) {
        e.log("phoneUtil")
        null
    }
}

fun String.isMobilePhoneNumber(defaultAreaCode: String): Bo<PERSON>an {
    try {
        val defaultRegion = phoneUtil.getRegionCodeForCountryCode(defaultAreaCode.toInt())
        val phoneNumber = phoneUtil.parse(this, defaultRegion)
        if (phoneUtil.isPossibleNumber(phoneNumber)) {
            return phoneUtil.getNumberType(phoneNumber) == PhoneNumberUtil.PhoneNumberType.MOBILE
        }
    } catch (e: Exception) {
        e.log("phoneUtil")
    }
    return false
}

fun String.normalizePhoneNumber(defaultAreaCode: String?): Pair<String, String>? {
    val phoneNumber = parse(this, "")
    return if (phoneNumber != null) {
        phoneNumber.getNormalizedPhoneNumber()
    } else {
        val defaultRegion = phoneUtil.getRegionCodeForCountryCode(defaultAreaCode?.toInt() ?: 1)
        val number = parse(this, defaultRegion)
        number.getNormalizedPhoneNumber()
    }
}

fun PhoneNumber?.getNormalizedPhoneNumber(): Pair<String, String>? {
    this ?: return null
    return if (phoneUtil.isPossibleNumber(this)) {
        val originalNumber = "${this.countryCode}-${this.nationalNumber}"
        val formattedNumber =
            phoneUtil.format(this, PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL)
        originalNumber to formattedNumber
    } else {
        null
    }
}