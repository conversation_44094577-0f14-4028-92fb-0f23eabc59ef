package com.interfun.buz.common.eventbus

import com.interfun.buz.base.utils.BusUtil

/**
 * @Desc the event for clicking start buzzing
 * @Author:<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * @Date: 2022/12/19
 */
class StartBuzzingEvent() : BaseEvent(){

    companion object{
        fun post(){
            BusUtil.post(StartBuzzingEvent())
        }
        // 点击 Start Buzzing 后，需要关闭首页相关的部分弹窗
    }
}