package com.interfun.buz.login.view.activity

import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import coil.load
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.activityViewModels
import com.interfun.buz.base.ktx.findNavController
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.PATH_LOGIN_UPDATE_ACCOUNT_ACTIVITY
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.manager.Area
import com.interfun.buz.login.R
import com.interfun.buz.login.databinding.LoginActivityBindingAccountBinding
import com.interfun.buz.login.utils.LoginTracker
import com.interfun.buz.login.viewmodel.AccountType
import com.interfun.buz.login.viewmodel.LoginViewModel
import com.interfun.buz.login.viewmodel.PageType

/**
 * <AUTHOR>
 * @date 2023/9/28
 * @desc include:
 * [com.interfun.buz.login.view.fragment.PhoneInputFragment]
 * [com.interfun.buz.login.view.fragment.EmailInputFragment]
 * [com.interfun.buz.login.view.fragment.ReceiveCodeFragment]
 */
@Route(path = PATH_LOGIN_UPDATE_ACCOUNT_ACTIVITY)
class BindingAccountActivity : BaseBindingActivity<LoginActivityBindingAccountBinding>() {

    private var currentFragmentId: Int = 0
    private val navViewModel by activityViewModels<LoginViewModel>()

    companion object{
        const val STATE_LOGIN_PHONE = "login_phone"
        const val STATE_LOGIN_AREA = "login_area"
    }
    override fun initArguments() {
        navViewModel.pageType = PageType.Binding
        when (intent.getStringExtra(RouterParamKey.Common.JUMP_INFO)) {
            RouterParamKey.Login.TYPE_BINDING_PHONE -> {
                navViewModel.setAccountType(AccountType.Phone)
            }
            RouterParamKey.Login.TYPE_BINDING_EMAIL -> {
                navViewModel.setAccountType(AccountType.Email)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (savedInstanceState!=null) {
            restore(savedInstanceState)
        }

    }

    override fun onSaveInstanceState(bundle: Bundle) {
        super.onSaveInstanceState(bundle)
        navViewModel.phoneLiveData.value?.let {
            bundle.putString(BindingAccountActivity.STATE_LOGIN_PHONE, it)
        }
        navViewModel.areaFlow.value?.let {
            bundle.putParcelable(BindingAccountActivity.STATE_LOGIN_AREA, it)
        }

    }

    override fun onRestoreInstanceState(bundle: Bundle) {
        super.onRestoreInstanceState(bundle)
        restore(bundle)
    }

    private fun restore(bundle: Bundle) {
        bundle.getString(STATE_LOGIN_PHONE)?.let {
            navViewModel.phoneLiveData.value = it
        }
        val area = if (VERSION.SDK_INT >= VERSION_CODES.TIRAMISU) {
            bundle.getParcelable(STATE_LOGIN_AREA, Area::class.java)
        } else {
            bundle.getParcelable(STATE_LOGIN_AREA)
        }
        area?.let {
            navViewModel.setCurrentArea(area)
        }
    }

    override fun initView() {
        initNavigation()
        binding.spaceStatusBar.initStatusBarHeight()
        binding.ivBg.load(R.drawable.common_black_bg)
    }

    private fun initNavigation() {
        findNavController(R.id.fragmentContainer)?.apply {
            addOnDestinationChangedListener { _, destination, _ ->
                currentFragmentId = destination.id
                when (currentFragmentId) {
                    R.id.phoneInputFragment -> {
                        navViewModel.setAccountType(AccountType.Phone)
                        val source = intent.getStringExtra(RouterParamKey.Common.KEY_SOURCE) ?: ""
                        LoginTracker.onBindingPhonePageView(source)
                    }
                    R.id.emailInputFragment -> navViewModel.setAccountType(AccountType.Email)
                    R.id.receiveCodeFragment -> LoginTracker.onBindingPhoneCodePageView()
                }
            }
            val navGraph = navInflater.inflate(R.navigation.login_nav)
            when (navViewModel.currentAccountType) {
                AccountType.Phone -> navGraph.setStartDestination(R.id.phoneInputFragment)
                AccountType.Email -> navGraph.setStartDestination(R.id.emailInputFragment)
            }
            graph = navGraph
            onBackPressedDispatcher.addCallback(
                this@BindingAccountActivity,
                object : OnBackPressedCallback(true) {
                    override fun handleOnBackPressed() {
                        when (currentFragmentId) {
                            R.id.receiveCodeFragment -> navigateUp()
                            R.id.phoneInputFragment,
                            R.id.emailInputFragment -> finish()
                        }
                    }
                })
        }
    }
}