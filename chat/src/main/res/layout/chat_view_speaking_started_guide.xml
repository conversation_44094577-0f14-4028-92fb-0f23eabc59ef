<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="50dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.interfun.buz.base.widget.round.RoundConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="12dp"
        android:background="@color/overlay_grey_20"
        app:round_radius="@dimen/guide_layout_radius_min"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <org.libpag.PAGView
            android:id="@+id/pagView"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginStart="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tvTips"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:textColor="@color/text_white_main"
            android:textSize="14sp"
            android:text="@string/chat_guide_two_desc"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/pagView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constrainedWidth="true"
            />

    </com.interfun.buz.base.widget.round.RoundConstraintLayout>

    <View
        android:id="@+id/lineBelow"
        android:layout_width="1dp"
        android:layout_height="20dp"
        android:background="@color/overlay_grey_20"
        app:layout_constraintStart_toStartOf="@+id/clContent"
        app:layout_constraintEnd_toEndOf="@+id/clContent"
        app:layout_constraintTop_toBottomOf="@+id/clContent" />

    <View
        android:id="@+id/pointBelow"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/common_oval_overlay_grey_20"
        app:layout_constraintStart_toStartOf="@+id/clContent"
        app:layout_constraintEnd_toEndOf="@+id/clContent"
        app:layout_constraintTop_toBottomOf="@+id/lineBelow"/>

</androidx.constraintlayout.widget.ConstraintLayout>