package com.interfun.buz.chat.group.view.itemdelegate

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.constants.FriendCheckBoxUIStatus.SELECTED_DISABLE
import com.interfun.buz.chat.databinding.GroupItemSelectedGroupMemberBinding
import com.interfun.buz.chat.group.viewmodel.ActivityGroupSelectMemberViewModel
import com.interfun.buz.chat.group.viewmodel.GroupSelectMemberViewModel

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/7/26
 */
class ActivityGroupSelectedMemberItemView(var callBackListener: OnCallBackListener?) :
    ItemViewBinder<ActivityGroupSelectMemberViewModel.GroupMember, ActivityGroupSelectedMemberItemView.MyViewHolder>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): MyViewHolder {
        return MyViewHolder(inflater.inflate(R.layout.group_item_selected_group_member, parent, false))
    }

    override fun onBindViewHolder(holder: MyViewHolder, user: ActivityGroupSelectMemberViewModel.GroupMember) {
        holder.setData(user)
    }

    inner class MyViewHolder constructor(holderItemView: View) : RecyclerView.ViewHolder(holderItemView) {

        var binding: GroupItemSelectedGroupMemberBinding = GroupItemSelectedGroupMemberBinding.bind(holderItemView)

        fun setData(user: ActivityGroupSelectMemberViewModel.GroupMember) {
            binding.ivPortrait.setPortrait(user.friend.portrait)
            binding.iftClose.visibleIf(editable(user))
            if (editable(user)) {
                binding.clRoot.click(clickIntervals = 500) {
                    callBackListener?.onContactClick(user)
                }
            }
        }

        private fun editable(user: ActivityGroupSelectMemberViewModel.GroupMember) = user.status != SELECTED_DISABLE
    }


    interface OnCallBackListener {
        fun onContactClick(user: ActivityGroupSelectMemberViewModel.GroupMember)
    }
}