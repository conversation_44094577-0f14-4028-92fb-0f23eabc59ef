package com.interfun.buz.common.eventbus.album

import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.eventbus.BaseEvent

/**
 * Author: LumJunYean
 * Date: 2024/5/7
 * Email: <EMAIL>
 * Desc: 通知更新相册数据列表，并触发UpdateAlbumListViewEvent来更新UI
 * PhotoSelectFragment onStart的时候，聊天记录里下载媒体时, 拍照保存后 触发
 * @param resetDefaultAlbum 刷新相册列表数据，是否重置默认选择相册，默认值false
 */
class UpdateAlbumListDataEvent(val resetDefaultAlbum: Boolean = false) : BaseEvent() {
    companion object {
        fun post(resetDefaultAlbum: Boolean = false) {
            BusUtil.post(UpdateAlbumListDataEvent(resetDefaultAlbum))
        }

        fun postDelay(delayTime: Long, resetDefaultAlbum: Boolean = false){
            BusUtil.postDelay(delayTime, UpdateAlbumListDataEvent(resetDefaultAlbum))
        }
    }
}