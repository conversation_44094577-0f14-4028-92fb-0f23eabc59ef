package com.interfun.buz.common.manager.cache.user

import com.interfun.buz.common.database.entity.UserRelationInfo

interface UserRepoCompat {
    fun isUserMyFriend(userId: Long?): Boolean

    suspend fun isUserOfficial(userId: Long?): Boolean

    suspend fun isUserBlocked(userId: Long?): Boolean

    fun getUserRelationInfoByUid(uid: Long): UserRelationInfo?

    /**
     * Get user relation info from cache > db > net sync
     */
    suspend fun getUserRelationInfoByUidSync(uid: Long): UserRelationInfo?

    suspend fun getUserFromServer(userId: Long): UserRelationInfo?

    suspend fun getUserRelationInfoFromCacheSync(uid: Long): UserRelationInfo?

    fun getUserRelationInfoFromCache(uid: Long): UserRelationInfo?

    suspend fun getUserRelationInfoFromCacheSuspend(uid: Long): UserRelationInfo?

    fun getUserRelationFromMem(uid: Long): UserRelationInfo?
}