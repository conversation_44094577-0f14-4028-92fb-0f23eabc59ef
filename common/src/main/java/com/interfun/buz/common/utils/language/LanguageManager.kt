package com.interfun.buz.common.utils.language

import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.util.Log
import androidx.core.content.ContextCompat.startActivity
import androidx.core.os.ConfigurationCompat
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.utils.language.LanguageProvider.locale
import com.lokalise.sdk.Lokalise
import java.util.Locale

/**
 * <AUTHOR>
 * @time 2023/5/29
 * @desc language manager
 **/
object LanguageManager {

    private const val TAG = "LanguageManager"

    private var cacheLanguageCode: String? = null


    /**
     * cache the last language
     */
    private fun setCacheLanguage(language: String) {
        Log.e("FirstActivity", "setCacheLanguage ${language}")
        CommonMMKV.appLanguageSetting = language
        cacheLanguageCode = language
    }

    /**
     * get language
     * first: get cache language,if not null,
     * second: get system language ,if not null,
     * third: get default language
     *
     * @return
     */
    fun getLocaleLanguageCode(): String {
        val test =  cacheLanguageCode?.takeIf(LanguageProvider::isSupportLanguage) ?: run {
            val cacheCode = CommonMMKV.appLanguageSetting
            val systemLanguage = getSystemLanguage()
            cacheLanguageCode = cacheCode?.takeIf(String::isNotBlank)
                ?.takeIf(LanguageProvider::isSupportLanguage)
                ?: systemLanguage.takeIf(LanguageProvider::isSupportLanguage)
                        ?: LanguageProvider.DEFAULT_LANGUAGE.code
            cacheLanguageCode!!
        }

        return test
    }

    /**
     * this code is for reporting to server side
     */
    fun getReportLanguageCode(languageCode: String = getLocaleLanguageCode()): String =
        when (languageCode) {
            language_in.code -> {
                "id"
            }

            language_zhTw.code -> {
                "zh-Hant"
            }

            else -> {
                languageCode
            }
        }

    /**
     * 这个仅用于翻译语言功能,因为翻译需求历史 id和 in 不一样的关系
     */
    fun getTranslationLanguageCode(languageCode: String = getLocaleLanguageCode()): String =
        when (languageCode) {
            language_zhTw.code -> {
                "zh-Hant"
            }
            else -> {
                languageCode
            }
        }

    /**
     * this code is for reporting to server side
     */
    private fun getLokaliseLanguageCode(languageCode: String = getLocaleLanguageCode()): String =
        when (languageCode) {
            language_zhTw.code -> {
                "zh"
            }

            else -> {
                languageCode
            }
        }

    private fun getLokaliseLanguageVariant(languageCode: String = getLocaleLanguageCode()): String {
        return when (languageCode) {
            language_zhTw.code -> "Hant"
            else -> ""
        }
    }

    fun getLocaleLanguageName(): String? {
        val code = getLocaleLanguageCode()
        return LanguageProvider.getLanguageName(code)
    }


    //getSystemLanguage
    fun getSystemLanguage(): String {
        /* val localLanguage = Locale.getDefault().language
         return if (localLanguage == "zh") {
             val country = Locale.getDefault().country
             if (country == "CN") {
                 language_zh.code
             } else language_zhTw.code // include TW（台湾）、HK（香港）、MO（澳门）
         } else localLanguage*/

        // cos the languageTag is zh-Hant-CN(繁体）, the country is also "CN", so comparing languageTag would be better
        return getResourceLanguage(Locale.getDefault(), fromResource = false)
    }

    /**
     * If the phone system change the language to traditional Chinese,
     * the {context.resources.configuration.locale.language} will get the "zh",
     * and {context.resources.configuration.locale.country} will get the "CN",
     * but that is the same with the simple Chinese(this will happen on the huawei device, cos their
     * languageTag is zh-Hant-CN)
     * in order to distinguish between simplified and traditional Chinese，
     * can use {context.resources.configuration.locale.toLanguageTag}
     */
    private fun getResourceLanguage(local: Locale, fromResource: Boolean = true): String {
        logInfo(TAG, "getResourceLanguage: language=${local.language}," +
                    "tag=${local.toLanguageTag()}," +
                    "country=${local.country}," +
                    "fromResource=${fromResource}")
        return if (local.language == "zh") {
            val tag = local.toLanguageTag()
            if (tag == "zh-Hans-CN" || tag == "zh-CN") {
                language_zh.code
            } else language_zhTw.code // include zh-Hant-TW、zh-Hant-HK、zh-Hant-MO、zh-Hant-CN
        } else local.language
    }

    /**
     * is support language
     */
    fun isSupportLanguage(language: String) = LanguageProvider.isSupportLanguage(language.locale())

    /**
     * is support language
     */
    fun isSupportLanguage(locale: Locale) = LanguageProvider.isSupportLanguage(locale)


    /**
     * restart app after update language
     */
    fun restartApp(context: Context) {
        Log.e("FirstActivity", "restart ${context.packageName}")
        TranslateLanguageManager.clearTranslateLanguageMap()
        cacheLanguageCode = null
        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
        intent?.apply {
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(context, this, null)
            android.os.Process.killProcess(android.os.Process.myPid())
            System.exit(0)
        }

    }

    /**
     * Update the locale of applicationContext.
     *
     * @param destLocale The dest locale.
     * @param consumer   The consumer.
     */
    private fun updateResourcesLegacy(
        context: Context,
        locale: Locale
    ): Context {
        val resources = context.resources
        val configuration = resources.configuration
        configuration.setLocale(locale)
        configuration.setLayoutDirection(locale)
        resources.updateConfiguration(configuration, resources.displayMetrics)
        if (context.applicationContext != null) {
            return context.applicationContext.createConfigurationContext(configuration)
        }
        return context
    }


    /**
     * change language  and save
     */
    fun changeLanguage(context: Context, targetLanguage: String): Context {
        context.resources ?: return context
        context.resources.configuration ?: return context
        try {
            val targetLocale = targetLanguage.locale()
            val resourceLocale = context.resources.configuration.locale
            if (getResourceLanguage(resourceLocale) == targetLanguage
                && getSystemLanguage() == targetLanguage
                && getLocaleLanguageCode() == targetLanguage
            ) {
                logInfo(TAG, "changeLanguage: no need to change，targetLanguage=$targetLanguage $context success")
                return context
            }
            Locale.setDefault(targetLocale)
            Lokalise.setLocale(language = getLokaliseLanguageCode(targetLanguage), country = targetLocale.country, variant = getLokaliseLanguageVariant(targetLanguage))
            val ctx = updateResourcesLegacy(context, targetLocale)
            setCacheLanguage(targetLanguage)
            logInfo(TAG, "changeLanguage: targetLanguage=$targetLanguage $context success")
            return ctx
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return context;
    }


    fun isArLanguage() = getLocaleLanguageCode() == language_ar.code
    fun isMyLanguage() = getLocaleLanguageCode() == language_my.code
    fun isInLanguage() = getLocaleLanguageCode() == language_in.code

    /**
     * 系统语言是美区或者英区
     */
    fun isSystemLanguageEnUSorEnGB(): Boolean {
        val currentLocale = ConfigurationCompat.getLocales(Resources.getSystem().getConfiguration()).get(0);
        // 检查当前Locale是否是en_US或en_GB
        val isEnUS = currentLocale?.language == "en" && currentLocale.country == "US"
        val isEnGB = currentLocale?.language == "en" && currentLocale.country == "GB"

        // 返回结果
        return isEnUS || isEnGB
    }
}