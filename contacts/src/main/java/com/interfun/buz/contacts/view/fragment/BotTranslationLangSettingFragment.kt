package com.interfun.buz.contacts.view.fragment

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.DiffUtil.ItemCallback
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.manager.router.converter.TranslatorLangSettingArgs
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.common.widget.recyclerview.adapter.AsyncDiffMultiTypeAdapter
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.databinding.ContactsFragmentAiTranslatorLangSettingBinding
import com.interfun.buz.contacts.utils.AiTracker
import com.interfun.buz.contacts.view.itemdelegate.LanguageSubTitleItemView
import com.interfun.buz.contacts.view.itemdelegate.TranslatorLanguageItemView
import com.interfun.buz.contacts.viewmodel.BotTranslatorLanguageSettingViewModel
import com.interfun.buz.contacts.viewmodel.BotTranslatorLanguageSettingViewModel.TranslatorSettingItem
import com.interfun.buz.contacts.viewmodel.BotTranslatorLanguageSettingViewModel.TranslatorSettingItem.Divider
import com.interfun.buz.contacts.viewmodel.BotTranslatorLanguageSettingViewModel.TranslatorSettingItem.SelectableLanguageItem
import dagger.hilt.android.AndroidEntryPoint

/**
 * 这个是翻译机器人的原语言跟目标语言设置，[ProfileBotLanguageSettingFragment] 是机器人通用的语言设置
 */
@Route(path = PATH_CONTACTS_FRAGMENT_TRANSLATION_LANGUAGE_SETTING)
@AndroidEntryPoint
class BotTranslationLangSettingFragment : BaseBottomSheetDialogFragment() {

    private val viewModel by viewModels<BotTranslatorLanguageSettingViewModel>()
    private val adapter = AsyncDiffMultiTypeAdapter<TranslatorSettingItem>(object :
        ItemCallback<TranslatorSettingItem>() {
        override fun areItemsTheSame(
            oldItem: TranslatorSettingItem,
            newItem: TranslatorSettingItem
        ): Boolean {
            when (oldItem) {
                is SelectableLanguageItem -> {
                    when (newItem) {
                        is Divider -> return false
                        is SelectableLanguageItem -> {
                            return oldItem.language.languageCode == newItem.language.languageCode
                                    && oldItem.isLastUsed == newItem.isLastUsed
                        }
                    }
                }

                is Divider -> {
                    return oldItem === newItem
                }
            }
        }

        override fun areContentsTheSame(
            oldItem: TranslatorSettingItem,
            newItem: TranslatorSettingItem
        ): Boolean {
            return oldItem == newItem
        }
    })
    private var source: Int = -1
    private val binding by lazy {
        ContactsFragmentAiTranslatorLangSettingBinding.inflate(
            layoutInflater
        )
    }

    companion object {
        /**
         * @param type 0 sourceLanguageSetting [RouterParamValues.AI.TYPE_SOURCE_LANGUAGE]
         * @param type 1 targetLanguageSetting [RouterParamValues.AI.TYPE_TARGET_LANGUAGE]
         *
         */
        fun newInstance(argsBean: TranslatorLangSettingArgs): BotTranslationLangSettingFragment {
            val args = Bundle()
            args.putLong(RouterParamKey.AI.USER_ID, argsBean.robotId)
            args.putInt(RouterParamKey.AI.KEY_TYPE, argsBean.type)
            args.putInt(RouterParamKey.AI.KEY_SOURCE, argsBean.source)
            val fragment = BotTranslationLangSettingFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.source = arguments?.getInt(RouterParamKey.AI.KEY_SOURCE, -1) ?: -1
    }

    override fun onResume() {
        super.onResume()
        AiTracker.onTranslationLanguageSettingPageExposed(isSourceLanguage(), source)
    }

    override fun onCreateView(): View {
        return binding.root
    }

    override fun initView(view: View?) {
        binding.ivHandle.click {
            removeSelf()
        }
        interceptOnBackPressed(fragment) {
            removeSelf()
        }
        if (isSourceLanguage()) {
            binding.tvSubtitle.text = R.string.source_language.asString()
        } else {
            binding.tvSubtitle.text = R.string.target_language.asString()
        }
        val translatorLanguageItemView = TranslatorLanguageItemView()
        translatorLanguageItemView.addOnItemClickListener { holder, item, pos ->
            save(item)
        }
        adapter.apply {
            register(LanguageSubTitleItemView())
            register(translatorLanguageItemView)
        }
        binding.rvOptions.layoutManager = LinearLayoutManager(context)
        binding.rvOptions.adapter = adapter
    }

    override fun initListener(view: View?) {
    }

    override fun initData() {
        super.initData()
        observeList()
        observerTranslationSetting()
        observeLoading()
    }

    private fun observeList() {
        viewModel.getData().collectIn(this) { list ->
            adapter.submitList(list)
        }
    }

    private fun observeLoading() {
        viewModel.showLoadingFlow.collectIn(fragment) {
            val activity = fragment.activity as? BaseActivity
            if (it) {
                activity?.showDataLoading()
            } else {
                activity?.hideDataLoading()
            }
        }
    }

    private fun observerTranslationSetting() {
        viewModel.saveTranslationSetting.collectIn(this) {
            if (it) {
                removeSelf()
            }
        }
    }

    private fun isSourceLanguage(): Boolean {
        return viewModel.type == RouterParamValues.AI.TYPE_SOURCE_LANGUAGE
    }

    private fun save(item: SelectableLanguageItem) {
        AiTracker.onSaveTranslationLanguage(
            isSetSourceLang = isSourceLanguage(),
            source = source,
            languageCode = item.language.languageCode,
            fromLastUsed = item.isLastUsed
        )
        viewModel.selectLanguage(item)
    }

    private fun removeSelf() {
        parentFragmentManager.beginTransaction()
            .setCustomAnimations(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
            .remove(this)
            .commitAllowingStateLoss()
    }

    override fun getHeight(): Int {
        return requireContext.screenHeight - 20.dp
    }
}