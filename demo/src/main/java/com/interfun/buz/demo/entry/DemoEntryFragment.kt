package com.interfun.buz.demo.entry

import android.Manifest
import android.net.Uri
import androidx.recyclerview.widget.GridLayoutManager
import coil.load
import coil.transform.RoundedCornersTransformation
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.eventbus.BaseEvent
import com.interfun.buz.common.ktx.loadWithThumbnail
import com.interfun.buz.common.ktx.shareChooser
import com.interfun.buz.common.utils.PermissionHelper
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.demo.R
import com.interfun.buz.demo.audio.AudioFocusTestActivity
import com.interfun.buz.demo.circleseekbar.CircleSeekBarActivity
import com.interfun.buz.demo.compose.ComposeActivity
import com.interfun.buz.demo.contacts.DemoContactInsertManager
import com.interfun.buz.demo.databinding.FragmentDemoEntryBinding
import com.interfun.buz.demo.databinding.ItemButtonBinding
import com.interfun.buz.demo.lottie.LottieActivity
import com.interfun.buz.demo.pag.PagActivity
import com.interfun.buz.demo.palette.PaletteColorActivity
import com.interfun.buz.demo.tekiplayer.TekiPlayerDemoActivity
import com.interfun.buz.demo.uikit.UIKitHomeActivity
import com.interfun.buz.demo.videocall.FlexBoxLayoutDemoActivity
import com.interfun.buz.demo.videocall.PipDemoActivity
import com.interfun.buz.demo.widget.PhoneInputDialog
import com.interfun.buz.demo.widget.ShowComposeWidgetsActivity
import com.interfun.buz.demo.widget.ToolTipsTestActivity
import com.interfun.buz.demo.widget.WidgetTestActivity
import com.interfun.buz.demo.wtlist.DemoWTListActivity
import kotlinx.coroutines.*
import java.io.*

/**
 * 示例模块
 */
class DemoEntryFragment : BaseBindingFragment<FragmentDemoEntryBinding>() {

    companion object {
        const val TAG = "DemoEntryFragment"
    }

    private val requestPermissionsLauncher = requestMultiplePermissionsLauncher(
        onAllGranted = {
            // 已全部同意
            toast("已全部同意")
        },
        onDenied = { deniedList ->
            // 部分权限已拒绝且不再询问，可弹框引导用户到设置里授权该权限
            // 弹框提示后可调用 launchAppSettings() 方法跳到设置页
            launchAppSettings()
            toast("部分权限已拒绝且不再询问: $deniedList")
        },
        onShowRequestRationale = { deniedList ->
            // 部分权限拒绝了一次，可弹框解释为什么要获取该权限
            // 弹框提示后可调用 requestDeniedPermissions() 方法请求拒绝的权限
            toast("部分权限拒绝了一次: $deniedList")
        })
    private val permissionHelper = PermissionHelper(this)

    override fun initView() {

        binding.spaceStatusBar.initStatusBarHeight()
        val url =
            "https://upload-images.jianshu.io/upload_images/5809200-a99419bb94924e6d.jpg?imageMogr2/auto-orient/strip%7CimageView2/2/w/1240"
        binding.ivTest.loadWithThumbnail(
            thumbnailUrl = "https://lmg.jj20.com/up/allimg/tp09/210H51R3313N3-0-lp.jpg",
            url = url
        ) {
            placeholder(R.mipmap.ic_launcher)
            transformations(
                RoundedCornersTransformation(
                    topLeft = 8.dpFloat,
                    topRight = 8.dpFloat,
                    bottomLeft = 8.dpFloat
                )
            )
        }
        binding.ivRound.load(url) {
            crossfade(true)
            placeholder(R.mipmap.ic_launcher)
        }

        val btnListAdapter = MultiTypeAdapter { register(ButtonDelegate()) }
        binding.rvBtn.apply {
            layoutManager = GridLayoutManager(requireContext(), 4)
            adapter = btnListAdapter
        }
        var testText = "test "
        val shareText = "Test share text from Buz"
        val shareUrl = "https://buzmenow.com/"
        val shareImage = getFileFromAssetsFile("test_share_img.png")
        val shareVideo = getFileFromAssetsFile("test_share_video.mp4")
        val shareChooserTitle = "请选择一个应用来分享"
        binding.btnNext.apply {
            click {
                CoroutineScope(Dispatchers.Main).launch {
                    showLoading()
                    delay(500)
                    setType(CommonButton.TYPE_SECONDARY_MEDIUM)
                    delay(500)
                    setType(CommonButton.TYPE_SECONDARY_SMALL)
                    delay(500)
                    setType(CommonButton.TYPE_TERTIARY_LARGER)
                    delay(500)
                    setType(CommonButton.TYPE_CONSEQUENTIAL_MEDIUM)
                    delay(500)
                    setType(CommonButton.TYPE_PRIMARY_LARGER)
                    delay(500)
                    hideLoading()
                }
            }
        }
        btnListAdapter.items = listOf(
            ButtonInfo("申请权限") {
                requestPermission()
            },
            ButtonInfo("Compose") {
                startActivity<ComposeActivity>()
            },
            ButtonInfo("分享图文") {
                shareTextAndImage(shareText, Uri.parse(url))
            },
            ButtonInfo("PAG动效") {
                startActivity<PagActivity>()
            },
            ButtonInfo("Lottie") {
                startActivity<LottieActivity>()
            },
            ButtonInfo("字体演示") {
                binding.clFontList.visibleIf(!binding.clFontList.isVisible())
            },
            ButtonInfo("Toast") {
                testText += testText
                testText.toast()
            },
            ButtonInfo("TekiPlayer") {
                startActivity<TekiPlayerDemoActivity>()
            },
            ButtonInfo("CircleSeekBar") {
                startActivity<CircleSeekBarActivity>()
            },
            ButtonInfo("WidgetTest") {
                startActivity<WidgetTestActivity>()
            },
            ButtonInfo("LiveEvent") {
                BusUtil.post(TestEvent())
            },
            ButtonInfo("WTList") {
                startActivity<DemoWTListActivity>()
            },
            ButtonInfo("Float") {
                // WTFloatTestManager.show()
            },
            ButtonInfo("Audio Focus") {
                startActivity<AudioFocusTestActivity>()
            },

            ButtonInfo("Tool Tips") {
                startActivity<ToolTipsTestActivity>()
            },
            ButtonInfo("composeWidget") {
                startActivity<ShowComposeWidgetsActivity>()
            },
            ButtonInfo("SMS Text") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- SMS Text ----")
                    shareChooser.sms.shareText(appContext, shareText + shareUrl)
                }
            },
            ButtonInfo("Instagram Feed Image") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Instagram Feed Image ----")
                    shareChooser.instagram.feedDestination.shareImage(
                        appContext,
                        shareImage,
                        shareChooserTitle
                    )
                }
            },
            ButtonInfo("Instagram Feed Video") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Instagram Feed Video ----")
                    shareChooser.instagram.feedDestination.shareVideo(
                        appContext,
                        shareVideo,
                        shareChooserTitle
                    )
                }
            },
            ButtonInfo("Instagram Stories") {

            },
            ButtonInfo("Messenger Text") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Messenger Text ----")
                    shareChooser.messenger.shareText(appContext, shareText)
                }
            },
            ButtonInfo("Messenger Image") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Messenger Image ----")
                    shareChooser.messenger.shareImage(appContext, shareImage, shareChooserTitle)
                }
            },
            ButtonInfo("Snapchat Text") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Snapchat Text ----")
                    shareChooser.snapchat.shareText(appContext, shareText)
                }
            },
            ButtonInfo("Snapchat Image") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Snapchat Image ----")
                    shareChooser.snapchat.shareImage(appContext, shareImage)
                }
            },
            ButtonInfo("WhatsApp Text") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- WhatsApp Text ----")
                    shareChooser.whatsapp.shareText(appContext, shareText)
                }
            },
            ButtonInfo("WhatsApp Image") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- WhatsApp Image ----")
                    shareChooser.whatsapp.shareImage(appContext, shareImage)
                }
            },
            ButtonInfo("Telegram Text&Url") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Telegram Text&Url ----")
                    shareChooser.telegram.shareTextAndUrl(appContext, shareText, shareUrl)
                }
            },
            ButtonInfo("Telegram Image") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Telegram Image ----")
                    shareChooser.telegram.shareImage(appContext, shareImage)
                }
            },
            ButtonInfo("Tiktok Image") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Tiktok Image ----")
                    shareChooser.tiktok.shareMultiImages(appContext, listOf(shareImage))
                }
            },
            ButtonInfo("Tiktok Video") {
                doInTryCatch("LzShare", true) {
                    log("LzShare", "\n\n---- Tiktok Video ----")
                    shareChooser.tiktok.shareVideo(appContext, shareVideo)
                }
            },
            ButtonInfo("Palette Color") {
                startActivity<PaletteColorActivity>()
            },
            ButtonInfo("Insert Contacts") {
                viewLifecycleScope.launchIO {
                    DemoContactInsertManager.insertContacts(contentResolver)
                }
            },
            ButtonInfo("Format Phone") {
                PhoneInputDialog(requireContext()).show()
            },
            ButtonInfo("UI Component") {
                startActivity<UIKitHomeActivity>()
            },
            ButtonInfo("FlexBox") {
                startActivity<FlexBoxLayoutDemoActivity>()
            },
            ButtonInfo("PipDemoActivity") {
                startActivity<PipDemoActivity>()
            },
        )
    }

    override fun initData() {
        super.initData()
        BusUtil.observe<TestEvent>(viewLifecycleOwner) {
            it.toString().toast()
        }
        BusUtil.observe<Int>("key_test", viewLifecycleOwner) {
            it.toString().toast()
        }
    }

    fun getFileFromAssetsFile(fileName: String): File {
        var file: File? = null
        var inputStream: InputStream? = null
        var outputStream: OutputStream? = null

        try {
            inputStream = appContext.assets.open(fileName)
            file = File(appContext.filesDir, fileName)
            outputStream = FileOutputStream(file)

            copyFile(inputStream, outputStream)
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            inputStream?.close()
            outputStream?.close()
        }

        return file ?: File("")
    }

    @Throws(IOException::class)
    fun copyFile(inputStream: InputStream, outputStream: OutputStream) {
        val buffer = ByteArray(1024)
        var read: Int
        while (inputStream.read(buffer).also { read = it } != -1) {
            outputStream.write(buffer, 0, read)
        }
    }

    private fun requestPermission() {
        if (activity.isNull()) return
        permissionHelper.request(
            requireActivity(),
            false,
            Manifest.permission.READ_CONTACTS,
            Manifest.permission.CAMERA
        ) { result ->
            if (result.isAllGranted) {
                toastLong("全部权限允许")
            } else {
                result.resultMap.forEach { (permission, item) ->
                    if (item.isGranted.not()) {
                        toastLong("$permission 权限不允许，是否有弹出系统弹框:${item.hasSysDialogShown}")
                        if (item.hasSysDialogShown.not()) {
                            CommonAlertDialog(
                                requireContext(),
                                title = "标题",
                                tips = "描述",
                                negativeText = "取消",
                                negativeCallback = {
                                    it.dismiss()
                                },
                                positiveText = "确定",
                                positiveCallback = {
                                    result.resultHelper.toSetting {
                                        log(TAG, "$permission 从设置页返回结果:$it")
                                    }
                                    it.dismiss()
                                }).show()
                        }
                    } else {
                        toastLong("$permission 权限已允许，是否有弹出系统弹框:${item.hasSysDialogShown}")
                    }
                }
            }
        }
    }

    override fun initBlock() {
        DemoFontTestBlock(this, binding).bind(viewLifecycleOwner)
    }

    data class ButtonInfo(val text: String, val action: DefaultCallback)

    class ButtonDelegate : BaseBindingDelegate<ButtonInfo, ItemButtonBinding>() {
        override fun onBindViewHolder(binding: ItemButtonBinding, item: ButtonInfo, position: Int) {
            binding.btn.text = item.text
            binding.btn.click { item.action.invoke() }
        }
    }

    class TestEvent : BaseEvent()
}

