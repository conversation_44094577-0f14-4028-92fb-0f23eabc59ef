package com.interfun.buz

import android.app.Application
import android.content.Context
import androidx.multidex.MultiDex
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.StartupTrace
import com.interfun.buz.common.widget.toast.BuzToast
import com.interfun.buz.common.utils.StartupCostTrace
import com.interfun.buz.startup.AppShellStartupManager
import com.interfun.buz.startup.entity.Config
import com.lizhi.component.basetool.env.AppEnvironment
import com.lizhi.component.basetool.env.Environments
import com.lizhi.component.basetool.ipc.ContentProviderIPCInterface
import com.lizhi.component.tekiapm.TekiApm
import com.lizhi.component.tekiapm.utils.isInMainProcess
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import com.yibasan.lizhifm.sdk.platformtools.ResUtil

open class AppShell : Application() {


    override fun attachBaseContext(base: Context) {
        StartupCostTrace.recordStart()
        StartupTrace.onStartUp()
        super.attachBaseContext(base)
        MultiDex.install(this)
        ApplicationContext.init(base)
        ApplicationContext.setApplication(this)
        appContext = base
        StartupCostTrace.recordAttach()
    }

    override fun onCreate() {
        StartupCostTrace.recordContentProvider()
        super.onCreate()
        val buildType = BuildConfig.BUILD_TYPE
        initApplication(buildType)
        Logz.tag("AppShell").i("appBuildType = $appBuildType")
        //releaseLog包也需要相当于debug模式，需要有日志输出
        Environments.setFlashDebugMode(isDebug || isReleaseLogBuildType)
        if (ApplicationContext.isInMainProcess()){
            //monkey包启动时默认切换为灯塔环境
            if (isDebug && BuildConfig.IS_DEFAULT_TOWER_ENV) {
                Environments.changeEnv(appContext, AppEnvironment.TOWER)
                Logz.tag("AppShell").i("change to tower env")
            } else if (BuildConfig.IS_DEFAULT_PRE_ENV) {
                Environments.changeEnv(appContext, AppEnvironment.PRE)
                Logz.tag("AppShell").i("change to pre env")
            }
        }else{
            //提供应用启动途中的崩溃监控能力,预初始化操作只需要在【非主进程】按需调用
            TekiApm.preInit(appContext)
            // 提供基础组件跨进程通信能力
            ContentProviderIPCInterface.init(appContext)
        }
        //todo: 放到合适位置
        appLogger = object : Logger {
            override fun log(level: Int, tag: String, msg: String?, t: Throwable?, vararg args: Any?) {
                if (t == null) {
                    Logz.tag(tag).log(level, msg, args)
                } else {
                    Logz.tag(tag).log(level, t, msg, args)
                }
            }
        }
        BuzToast.initAppToast()
        if (isInMainProcess()) {
            CommonTracker.onMainProcessLaunch()
        }
        StartupCostTrace.recordCreateApplication()
        //启动任务
        AppShellStartupManager.startupAppShell(
            Config(
                isBuildWithJenkins = BuildConfig.BUILD_WITH_JENKINS,
                isLogEnable = BuildConfig.LOG_FILE,
                enableBitmapCheck = BuildConfig.BUILD_OPEN_BITMAP_CHECK,
            )
        )
        StartupCostTrace.recordStartUpApp()
    }
}

