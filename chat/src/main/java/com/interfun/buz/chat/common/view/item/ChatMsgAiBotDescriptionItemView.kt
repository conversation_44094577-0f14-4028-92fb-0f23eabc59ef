package com.interfun.buz.chat.common.view.item

import com.interfun.buz.chat.common.entity.ChatMsgAiBotDescriptionItemBean
import com.interfun.buz.chat.databinding.ChatItemAibotDescriptionBinding
import com.interfun.buz.common.base.binding.BaseBindingDelegate

/**
 * Author: ChenYouSheng
 * Date: 2023/7/13
 * Email: chenyoush<PERSON>@lizhi.fm
 * Desc:
 */
class ChatMsgAiBotDescriptionItemView : BaseBindingDelegate<ChatMsgAiBotDescriptionItemBean, ChatItemAibotDescriptionBinding>() {

    override fun onBindViewHolder(binding: ChatItemAibotDescriptionBinding, item: ChatMsgAiBotDescriptionItemBean, position: Int) {
        super.onBindViewHolder(binding, item, position)
        binding.tvInfo.text = item.command.tipMsg
    }
}