package com.interfun.buz.common.manager.router.converter

import android.content.Intent
import android.os.Bundle
import com.alibaba.android.arouter.launcher.ARouter
import com.interfun.buz.common.bean.push.extra.BasePushExtra
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.manager.GlobalEventManager
import com.interfun.buz.common.service.ChatService
import org.json.JSONObject

class GroupChatRouterArgs(
    val groupId: Long,
    val serMsgId: String? = null,
    val reactionOpUserId: String? = null,
    val msgId: Long? = null
)

class GroupChatRouterConverter : RouterConverter<GroupChatRouterArgs> {
    override val scheme = RouterSchemes.Chat.GROUP_CHAT
    override val path = PATH_CHAT_ACTIVITY_GROUP

    private val serMsgId = RouterParamKey.Chat.KEY_SER_MSG_ID

    override fun convertToBundle(extraData: JSONObject?, result: Bundle): Boolean {
        if (extraData == null) {
            return false
        }
        return try {
            val serMsgId = if (extraData.has(serMsgId)) {
                extraData.getString(serMsgId)
            }else{
                null
            }
            val targetId = extraData.getString("targetId").toLong()
            if (targetId > 0){
                //该路由的跳转同时需要对首页的对讲机好友列表进行选中定位
                GlobalEventManager.homesRouterTarget.postValue(targetId to null)
            }
            val chatService = ARouter.getInstance().navigation(ChatService::class.java)
            val homeAddressUserInfo = chatService.getHomeAddressUserInfo(groupId = targetId)
            val reactionOpUserId = if (extraData.has(RouterParamKey.Chat.KEY_REACTION_OP_USER_ID)) {
                extraData.getString(RouterParamKey.Chat.KEY_REACTION_OP_USER_ID)
            } else {
                null
            }
            val msgId = extraData.optLong(RouterParamKey.Chat.KEY_MSG_ID)
            val jumpInfo = chatService.getGroupChatJumpInfo(
                targetId,
                result.getString(
                    RouterParamKey.Common.KEY_SOURCE,
                    RouterParamValues.Common.SOURCE_UNKNOWN
                ),
                serMsgId = serMsgId,
                addressUserInfo = homeAddressUserInfo,
                reactionOpUserId = reactionOpUserId,
                if (msgId == 0L) null else msgId
            )
            result.putParcelable(RouterParamKey.Chat.JUMP_INFO, jumpInfo)
            true
        } catch (t: Throwable) {
            false
        }
    }

    override fun convertToExtraData(args: GroupChatRouterArgs): JSONObject {
        return JSONObject().apply {
            put("targetId", args.groupId.toString())
            if (args.serMsgId != null){
                put(serMsgId, args.serMsgId)
            }
            if (args.reactionOpUserId != null){
                put(BasePushExtra.KEY_REACTION_OP_USER_ID, args.reactionOpUserId)
            }
            args.msgId?.let {
                put(RouterParamKey.Chat.KEY_MSG_ID, args.msgId)
            }
        }
    }

    override fun extraIntentFlags(): Int {
        return Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
    }
}