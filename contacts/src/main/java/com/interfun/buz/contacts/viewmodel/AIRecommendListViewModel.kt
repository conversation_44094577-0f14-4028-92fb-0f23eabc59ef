package com.interfun.buz.contacts.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.SelectableItem
import com.interfun.buz.common.constants.AddFriendSource
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.contacts.data.repository.AddFriendGuideRepository
import com.interfun.buz.contacts.data.repository.RecommendAIRepository
import com.interfun.buz.contacts.entity.SimpleAIInfo
import com.interfun.buz.contacts.utils.AiTracker
import com.interfun.buz.social.repo.BotRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class AIRecommendListViewModel @Inject constructor(
    private val botRepository: BotRepository
) : ViewModel() {

    private val addFriendGuideRepository = AddFriendGuideRepository.instance
    private val recommendAIRepository =
        RecommendAIRepository(viewModelScope, addFriendGuideRepository)
    private val selectableAiList = recommendAIRepository.selectableAiList.stateIn(
        viewModelScope, SharingStarted.Lazily,
        RecommendAIRepository.SelectableAiList(emptyList(), emptyList())
    )
    private val isAddingToChat = MutableStateFlow(false)
    private val showButtonLoading: Flow<Boolean> = isAddingToChat
    val nextStep = MutableStateFlow(false)
    val uiState = combine(
        selectableAiList,
        showButtonLoading
    ) { list, buttonLoading ->
        UIState(list.allList, !list.selectedList.isEmpty(), buttonLoading)
    }

    fun onViewExposed() {
        viewModelScope.launchIO {
            val selectedList =
                addFriendGuideRepository.recommendAiList.value?.filter { it.isSelected }
            if (selectedList?.isNotEmpty() == true) {
                selectedList.forEach {
                    AiTracker.onAiRecommendationGuideExposed(
                        recommendAIRepository.recommendAiSource ?: "",
                        it.data.userId.toString()
                    )
                }
            } else {
                AiTracker.onAiRecommendationGuideExposed(
                    recommendAIRepository.recommendAiSource ?: "", null
                )
            }
        }
    }

    fun addSelectedAiToChatList() {
        viewModelScope.launchIO {
            isAddingToChat.value = true
            val userIds = selectableAiList.value.selectedList.map { it.userId }
            val result = botRepository.addBotToChat(userIds)
            when (result) {
                is Resp.Error -> {
                    CommonTracker.onClickAddAiRootResult(
                        AddFriendSource.AIRecommendPage.value,
                        false,
                        result.code ?: 0
                    )
                }

                is Resp.Success -> {
                    userIds.forEach { uid ->
                        AiTracker.onAddAiInRecommendList(
                            recommendAIRepository.recommendAiSource ?: "",
                            uid.toString()
                        )
                    }
                    CommonTracker.onClickAddAiRootResult(
                        AddFriendSource.AIRecommendPage.value,
                        true
                    )
                }
            }
            isAddingToChat.value = false
            nextStep.value = true
        }
    }

    fun toggleSelectState(item: SelectableItem<SimpleAIInfo>) {
        viewModelScope.launchIO {
            if (item.isSelected) {
                recommendAIRepository.unSelectAI(item.data)
            } else {
                recommendAIRepository.selectAI(item.data)
            }
        }
    }

    data class UIState(
        val list: List<SelectableItem<SimpleAIInfo>>,
        val buttonEnable: Boolean,
        val buttonLoading: Boolean
    )
}