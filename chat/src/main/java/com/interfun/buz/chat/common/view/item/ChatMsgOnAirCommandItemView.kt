package com.interfun.buz.chat.common.view.item

import android.view.View
import com.interfun.buz.chat.common.entity.ChatMsgOnAirCommandItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.databinding.ChatItemVoicecallCommandNotifyBinding
import kotlinx.coroutines.CoroutineScope

class ChatMsgOnAirCommandItemView(itemCallback: ChatItemCallback) : BaseChatItemView<ChatMsgOnAirCommandItemBean, ChatItemVoicecallCommandNotifyBinding>(itemCallback) {

    override fun onBindViewHolder(
        scope: CoroutineScope?,
        binding: ChatItemVoicecallCommandNotifyBinding,
        item: ChatMsgOnAirCommandItemBean,
        position: Int
    ) {
        super.onBindViewHolder(scope, binding, item, position)
        binding.tvCenterMsg.text = item.command.tipMsg
    }

    override fun getAnchorView(binding: ChatItemVoicecallCommandNotifyBinding): View? {
        return null
    }

}