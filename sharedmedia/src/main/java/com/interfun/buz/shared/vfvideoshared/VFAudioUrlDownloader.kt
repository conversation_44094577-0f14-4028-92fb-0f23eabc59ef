package com.interfun.buz.shared.vfvideoshared

import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.ktx.globalOkHttpClient
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap

/**
 * 音频文件下载器
 *
 * 功能特性：
 * - 支持并发下载控制，相同URL的重复请求会等待现有下载完成
 * - 使用临时文件机制，确保下载完整性
 * - 提供详细的错误处理和日志记录
 * - 基于协程的异步下载
 */
object VFAudioUrlDownloader {

    private const val TAG = "VFAudioUrlDownloader"
    private const val TEMP_FILE_SUFFIX = ".tmp"

    // 用于管理正在进行的下载任务，防止重复下载
    private val downloadMap = ConcurrentHashMap<String, Deferred<Result<File>>>()

    /**
     * 下载音频文件
     *
     * @param url 下载地址
     * @param fileName 保存文件名
     * @param dir 保存目录
     * @return 下载结果，成功时返回文件对象，失败时返回错误信息
     */
    suspend fun download(url: String, fileName: String, dir: File): Result<File> = coroutineScope {
        logInfo(TAG, "开始下载音频文件: url=$url, fileName=$fileName, dir=${dir.absolutePath}")

        // 检查参数有效性
        if (url.isBlank()) {
            val error = "下载URL不能为空"
            logError(TAG, error)
            return@coroutineScope Result.failure(IllegalArgumentException(error))
        }

        if (fileName.isBlank()) {
            val error = "文件名不能为空"
            logError(TAG, error)
            return@coroutineScope Result.failure(IllegalArgumentException(error))
        }

        // 确保目录存在
        if (!dir.exists() && !dir.mkdirs()) {
            val error = "无法创建保存目录: ${dir.absolutePath}"
            logError(TAG, error)
            return@coroutineScope Result.failure(IOException(error))
        }

        if (!dir.isDirectory) {
            val error = "保存路径不是目录: ${dir.absolutePath}"
            logError(TAG, error)
            return@coroutineScope Result.failure(IOException(error))
        }

        // 使用URL作为唯一键来管理并发下载
        val downloadKey = url
        val deferred = downloadMap.getOrPut(downloadKey) {
            async(Dispatchers.IO) {
                performDownload(url, fileName, dir)
            }
        }

        try {
            val result = deferred.await()
            logInfo(TAG, "下载任务完成: url=$url, result=${if (result.isSuccess) "成功" else "失败"}")
            result
        } finally {
            // 清理已完成的下载任务
            downloadMap.remove(downloadKey)
        }
    }

    /**
     * 执行实际的文件下载操作
     */
    private suspend fun performDownload(url: String, fileName: String, dir: File): Result<File> {
        val targetFile = File(dir, fileName)
        val tempFile = File(dir, "$fileName$TEMP_FILE_SUFFIX")

        try {
            logInfo(TAG, "开始执行下载: $url -> ${tempFile.absolutePath}")

            // 如果目标文件已存在，直接返回
            if (targetFile.exists() && targetFile.length() > 0) {
                logInfo(TAG, "文件已存在，跳过下载: ${targetFile.absolutePath}")
                return Result.success(targetFile)
            }

            // 清理可能存在的临时文件
            if (tempFile.exists()) {
                tempFile.delete()
                logInfo(TAG, "清理旧的临时文件: ${tempFile.absolutePath}")
            }

            // 创建HTTP请求
            val request = Request.Builder()
                .url(url)
                .build()

            // 执行下载
            globalOkHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val error = "HTTP请求失败: ${response.code} ${response.message}"
                    logError(TAG, error)
                    return Result.failure(IOException(error))
                }

                val responseBody = response.body
                if (responseBody == null) {
                    val error = "响应体为空"
                    logError(TAG, error)
                    return Result.failure(IOException(error))
                }

                val contentLength = responseBody.contentLength()
                logInfo(TAG, "开始写入文件，内容大小: $contentLength bytes")

                // 写入临时文件
                responseBody.byteStream().use { inputStream ->
                    FileOutputStream(tempFile).use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }

                // 验证下载完整性
                if (contentLength > 0 && tempFile.length() != contentLength) {
                    val error = "文件下载不完整，期望: $contentLength bytes，实际: ${tempFile.length()} bytes"
                    logError(TAG, error)
                    tempFile.delete()
                    return Result.failure(IOException(error))
                }

                // 将临时文件重命名为最终文件
                if (!tempFile.renameTo(targetFile)) {
                    val error = "无法重命名临时文件: ${tempFile.absolutePath} -> ${targetFile.absolutePath}"
                    logError(TAG, error)
                    tempFile.delete()
                    return Result.failure(IOException(error))
                }

                logInfo(TAG, "文件下载成功: ${targetFile.absolutePath}, 大小: ${targetFile.length()} bytes")
                return Result.success(targetFile)
            }

        } catch (e: Exception) {
            logError(TAG, e, "下载过程中发生异常: url=$url")

            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete()
                logInfo(TAG, "清理异常产生的临时文件: ${tempFile.absolutePath}")
            }

            return Result.failure(e)
        }
    }
}