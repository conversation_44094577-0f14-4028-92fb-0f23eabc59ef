package com.interfun.buz.shared

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker

object SharedTracker {
    fun resultBackRB2024062503(sharedPkg: String? = null, type: String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024062503")
            put(TrackConstant.KEY_RESULT_TYPE, "system_share_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_STATUS, sharedPkg ?: "")
            put(TrackConstant.KEY_BUSINESS_TYPE, type)
        }
    }
}