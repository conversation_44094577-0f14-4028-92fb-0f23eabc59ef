package com.interfun.buz.common.manager

import android.app.Activity
import com.interfun.buz.base.ktx.addParameterToUrl
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.common.R
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.web.WebViewActivity
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.lzlogan.common.LogzConstant
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList

/**
 * <AUTHOR>
 * @date 2023/8/10
 * @desc
 */
object FeedbackManager {

    private var feedbackNotificationIds = CopyOnWriteArrayList<Int>()

    /**
     * @param source [com.interfun.buz.common.bean.feedback.H5FeedbackSource]
     */
    fun jumpToFeedbackWebView(activity: Activity?, source: Int) {
        val feedbackUrl = AppConfigRequestManager.feedbackUrl.addParameterToUrl("source", "$source")
        WebViewActivity.start(activity ?: appContext, feedbackUrl)
        activity?.overridePendingTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
        cancelFeedbackNotification()
        MainScope().launch(Dispatchers.IO) {
            Logz.send(
                System.currentTimeMillis(), LogzConstant.MODE_4G,
                force = false,
                carry = false
            )
        }
    }

    fun addFeedbackNotificationId(notificationId: Int) {
        feedbackNotificationIds.add(notificationId)
    }

    private fun cancelFeedbackNotification() {
        feedbackNotificationIds.forEach {
            NotificationUtil.cancelNotification(appContext, it)
        }
        feedbackNotificationIds.clear()
    }
}