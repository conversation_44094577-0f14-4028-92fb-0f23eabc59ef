package com.interfun.buz.contacts.view.itemdelegate

import com.interfun.buz.contacts.databinding.ContactsItemViewAllBinding
import com.interfun.buz.contacts.interfaces.BaseContactsDelegate
import com.interfun.buz.contacts.interfaces.ContactsItemCallback

/**
 * <AUTHOR>
 * @date 2023/5/9
 * @desc 通讯录首页-推荐列表-查看全部推荐入口
 * Contacts Home-Recommended List-View All Recommended Entry
 */
class ContactsRecommendEntryItemView(callback: ContactsItemCallback) :
    BaseContactsDelegate<ContactsItemViewAllBinding>(callback)