package com.interfun.buz.onair.ui.screen

import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.interfun.buz.common.bean.voicecall.AudioDevice
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.haze.hazeEffect
import com.interfun.buz.compose.ktx.LocalHazeState
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.onair.R
import com.interfun.buz.onair.ui.action.LocalOnAirInnerAction
import com.interfun.buz.onair.ui.action.OnAirInnerAction
import com.interfun.buz.onair.ui.action.OnAirInnerAction.*
import com.interfun.buz.onair.ui.state.OnAirControlBarUiState

/**
 * <AUTHOR>
 * @date 2024/12/31
 * @desc 
 */
@Composable
fun LivePlaceBottomBar(uiState: OnAirControlBarUiState) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp))
            .hazeEffect(state = LocalHazeState.current)
            .navigationBarsPadding()
            .padding(start = 40.dp, top = 20.dp, end = 40.dp, bottom = 10.dp),
        verticalAlignment = Alignment.Top
    ) {
        ControlItem(
            iconRes = uiState.speakerStatus.getIconFontResRef(),
            bgColorRes = R.color.color_background_light_default,
            iconColorRes = R.color.color_text_black_primary,
            action = HandleSpeaker
        )
        Spacer(Modifier.weight(1f))
        ControlItem(
            iconRes = if (uiState.micStatus) R.string.ic_mic_open else R.string.ic_mic_close,
            isSelect = uiState.micStatus,
            action = MicrophoneAdjustment(uiState.micStatus)
        )
        Spacer(Modifier.weight(1f))
        ControlItem(
            iconRes = R.string.ic_sound_board,
            action = ShowOrHideSoundBoardPage(true)
        )
        Spacer(Modifier.weight(1f))
        ControlItem(
            iconRes = R.string.ic_tap,
            action = ShowOrHideGiftBottomSheet(isShow = true)
        )
    }
}

@Composable
private fun ControlItem(
    @StringRes iconRes: Int,
    @ColorRes bgColorRes: Int = R.color.alpha_white_10,
    @ColorRes iconColorRes: Int = R.color.color_text_white_important,
    action: OnAirInnerAction
) {
    val onAction = LocalOnAirInnerAction.current
    IconFontText(
        modifier = Modifier
            .clip(CircleShape)
            .background(bgColorRes.asColor()),
        iconRes = iconRes,
        iconColor = iconColorRes.asColor(),
        iconSize = 26.dp,
        size = 52.dp,
    ) {
        onAction.invoke(action)
    }
}

@Composable
private fun ControlItem(
    @StringRes iconRes: Int,
    isSelect: Boolean,
    action: OnAirInnerAction
) {
    val bgColor by animateColorAsState(
        targetValue = if (isSelect) {
            R.color.secondary_button_main.asColor()
        } else {
            R.color.secondary_button_secondary.asColor()
        }
    )
    val iconColor by animateColorAsState(
        targetValue = if (isSelect) {
            R.color.text_black_main.asColor()
        } else {
            R.color.text_white_important.asColor()
        }
    )
    val onAction = LocalOnAirInnerAction.current
    IconFontText(
        modifier = Modifier
            .clip(CircleShape)
            .background(bgColor),
        iconRes = iconRes,
        iconColor = iconColor,
        iconSize = 26.dp,
        size = 52.dp,
    ) {
        onAction.invoke(action)
    }
}



@Composable
@Preview
internal fun PreviewBottomControlBar() {
    PreviewLivePlaceLocalProvider {
        LivePlaceBottomBar(
            OnAirControlBarUiState(
                speakerStatus = AudioDevice.AUDIO_ROUTE_HANDSET,
                micStatus = true,
            )
        )
    }
}