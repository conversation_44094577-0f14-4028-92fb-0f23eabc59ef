package com.interfun.buz.im.signal


import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.im.IMAgent
import com.interfun.buz.signal.CompensateCallBack
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.signal.SignalData
import com.interfun.buz.signal.TopiSubscribeListener
import kotlinx.coroutines.channels.ProducerScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.launch

/**
 * 被标注的类或者函数需要在IM信令全量后进行移出
 */
@Retention(AnnotationRetention.SOURCE)
annotation class IMSignalTransitCode(val des: String = "")

/**
 * 对外暴露的唯一函数,为什么不直接暴露[InnerSignalManagerPresenter]?
 * 因为开发看到的越少越好.
 *
 * **使用注意点**:
 *
 * 因为在线信令消息是双推的,会推向罗马和IM.因此是topic订阅,请在[InnerSignalManagerPresenter.topicCodeList]添加一个标记,
 * [InnerSignalManagerPresenter.topicCodeList]会被用于取反区分过滤重复在线消息
 *
 *
 * **使用方式**:
 *
 * ```kotlin
 *  SignalManagerPresenter.obtainInstance()
 *  .obtainSignalingFlow({data->
 *              //过滤期望数值
 *              data.topic == expectTopic
 *          }){
 *              //BUSINESS CODE
 *          }
 * ```
 *
 */
object SignalManagerPresenter {
    /**
     * 获取信令包装类,内部包含IM和罗马的融合.
     */
    fun obtainInstance(): ISignalManagerPresenter {
        return InnerSignalManagerPresenter
    }



}

/**
 * See [com.interfun.buz.signal.SignalManagerPresenter]
 */
private object InnerSignalManagerPresenter : ISignalManagerPresenter, CompensateCallBack {
    private val imSignal by lazy { val signal = IMAgent.obtainISignalManagerPresenter()
        signal.registerCompensateCallBack(this)
        signal
    }
    private const val TAG = "InnerSignalManagerPresenter"

    /**
     * 注意这个集合仅能保证单写入和单读取,但是对于循环遍历会有问题,所以未来的开发中迭代时进行拷贝也许时你最好的选择.
     */
    private val topicListenerSet =
        mutableSetOf<TopiSubscribeListener>()

    /**
     * 回调的时候提示你丢了信令需要补偿操作
     * 举个例子:
     *      信令没有投递到的时候会给你保留 5 分钟的时间,如果 5 分钟内没有投递成功,那么就会回调这个方法
     */
    private val compensateCallBack = mutableSetOf<CompensateCallBack>()


    private fun obtainSignal(): ISignalManagerPresenter {
        return imSignal
    }
    override fun subscribe(topic: String) {
        logI("invoke subscribe $topic")
        obtainSignal().subscribe(topic)
    }

    override fun unsubscribe(topic: String) {
        logI("invoke unsubscribe $topic")
        obtainSignal().unsubscribe(topic)
    }

    private fun logI(msg: String) {
        logInfo(
            TAG,
            "Thread:${Thread.currentThread().name}(${Thread.currentThread().id})  info: " + msg
        )
    }


    override fun obtainSignalingFlow(filter: ((SignalData) -> Boolean)): Flow<SignalData> {
        logI("invoke obtainSignalingFlow")
        return channelFlow {
            launch {
                observeOriginDataFlow(imSignal, filter)
            }
        }
    }

    @Synchronized
    override fun registerTopiSubscribeRet(listener: TopiSubscribeListener) {
        logI("registerTopiSubscribeRet invoke. listener")
        topicListenerSet.add(listener)
    }

    @Synchronized
    override fun unregisterTopiSubscribeRet(listener: TopiSubscribeListener) {
        logI("unregisterTopiSubscribeRet invoke. listener")
        topicListenerSet.remove(listener)
    }

    override fun ready() {
        imSignal.ready()
    }

    override fun unready() {
        logI("unready")
        imSignal.unready()
    }


    @Synchronized
    override fun registerCompensateCallBack(callBack: CompensateCallBack) {
        compensateCallBack.add(callBack)
    }

    @Synchronized
    override fun unregisterCompensateCallBack(callBack: CompensateCallBack) {
        compensateCallBack.remove(callBack)
    }

    private suspend fun ProducerScope<SignalData>.observeOriginDataFlow(
        signal: ISignalManagerPresenter,
        filter: (SignalData) -> Boolean,
    ) {
        signal.obtainSignalingFlow(filter)
            .collect { data ->
                logI("invoke observeOriginDataFlow collect signal = $signal data = $data")
                send(data)
            }
    }


    @Synchronized
    override fun onCompensateCallBack() {
        logI("onCompensateCallBack invoke")
        compensateCallBack.forEach {
            it.onCompensateCallBack()
        }
    }

}