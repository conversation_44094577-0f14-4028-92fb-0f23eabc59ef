<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:background="@drawable/common_bg_dialog_bottom_list"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="20dp"
    tools:background="@color/overlay_grey_14">

    <com.interfun.buz.base.widget.round.RoundImageView
        android:id="@+id/ivDialogItemImage"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="16dp"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvDialogItemTitle"
        app:layout_constraintTop_toTopOf="parent"
        app:round_radius="8dp"
        tools:background="@color/white"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvDialogItemIcon"
        style="@style/iconfont_base"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:paddingEnd="16dp"
        android:gravity="center"
        android:textColor="@color/text_white_main"
        android:textSize="22sp"
        android:visibility="gone"
        app:if_enablePressEffect="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/ic_blocklist"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrierEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="ivDialogItemImage, iftvDialogItemIcon" />

    <TextView
        android:id="@+id/tvDialogItemTitle"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="2"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/barrierEnd"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Block" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvDialogSelectIcon"
        style="@style/iconfont_base"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="16dp"
        android:text="@string/ic_check"
        android:textColor="@color/text_white_main"
        android:textSize="16sp"
        android:visibility="gone"
        app:if_enablePressEffect="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>