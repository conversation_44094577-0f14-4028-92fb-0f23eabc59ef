@file:UnstableApi

package com.interfun.buz.media.video.compressor

import androidx.media3.common.util.UnstableApi
import androidx.media3.transformer.Composition
import androidx.media3.transformer.ExportResult
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.media.BuildConfig
import com.tencent.mmkv.MMKV
import java.io.File
import java.io.FileNotFoundException
import java.util.concurrent.TimeUnit

/**
 * 压缩视频工具类。根据官方文档改动而来https://developer.android.com/media/media3/transformer?hl=zh-cn
 * 压缩并转化视频为h264格式(注意为了兼容IOS main profile的high不能为为6或者以上)，音频为aac
 * 食用方式：
 * ```kotlin
 *      val task = BuzVideoCompressorHelper.buildTask(inputFile,outputFile)
 *      //下面的API会阻塞协程返回直到压缩完成
 *      //如果需要限制最大压缩时间请使用task.startWithTimeOut
 *      task.start()
 *
 *      if(task.success){
 *          //压缩成功
 *      }else{
 *          //压缩失败 ，可以根据task.FailureInfo和[可以根据task.state]去综合处理
 *      }
 * ```
 */
object BuzVideoCompressorHelper {
    private val TAG = "BuzVideoCompressorHelper"

    /**
     * 默认30分钟
     */
    private val DEFAULT_TIME_OUT = TimeUnit.MINUTES.toMillis(30)

    /**
     * 构建一个压缩任务,调用后使用task.start即可开始压缩
     * @param inputFile 需要的压缩的视频文件路径
     * @param outputFile 存放的结果文件路径
     */
    fun buildTask(
        inputFile: String,
        outputFile: String
    ): CompressTask {
        val inFile = File(inputFile)
        //这里可以根据策略选择 Media3CompressTask 或 VideoEditCompressTask 进行压缩
        val compressMode = MMKV.defaultMMKV()?.decodeString("device_video_compress_mode")
        val useVideoEditCompressMode = ABTestManager.useVideoEditCompressMode
        val compressTask = if (useVideoEditCompressMode || (BuildConfig.DEBUG && compressMode == "VideoEdit")) {
            VideoEditCompressTask(
                false,
                TaskState.INIT,
                inputFile,
                outputFile,
                inFile.length(),
                SuccessInfo(),
                FailureInfo()
            )
        }else{
            Media3CompressTask(
                false,
                TaskState.INIT,
                inputFile,
                outputFile,
                inFile.length(),
                SuccessInfo(),
                FailureInfo(),
                supportCompressSwitch = true
            )
        }
        if (!inFile.exists()) {
            throw FileNotFoundException("${inFile} not found")
        }
        if (!inFile.canRead()) {
            throw RuntimeException("${inFile} not read")
        }

        return compressTask
    }

    enum class TaskState {
        /**
         * 什么都没做
         */
        INIT,

        /**
         * 正在压缩
         */
        DOING,

        /**
         * 压缩完成
         */
        DONE,

        /**
         * 压缩期间出现错误
         */
        ERR,

        /**
         * 取消压缩
         */
        CANCEL,

        /**
         * 在指定时间内压缩失败
         */
        TIME_OUT
    }

    data class SuccessInfo(
        var composition: Composition? = null,
        var exportResult: ExportResult? = null,
        var estimateTime: Long = -1
    )

    data class FailureInfo(
        var composition: Composition? = null,
        var exportResult: ExportResult? = null,
        var exportException: Throwable? = null
    )

    abstract class CompressTask(
        open var compressMode: String,
        open var success: Boolean,
        /*** 这个变量可能在多线程访问需要保证可见性*/
        @Volatile
        open var state: TaskState,
        open var inputFilePath: String,
        open var outFilePath: String,
        open var originFileSize: Long = -1,
        open var successInfo: SuccessInfo,
        open var failureInfo: FailureInfo,
        open var supportCompressSwitch: Boolean = false){

        var hasCompressSwitched = false
        var outputFileSize: Long = -1
        var videoParameters: VideoParameters? = null

        abstract suspend fun start(): CompressTask

        abstract suspend fun startWithTimeOut(timeOut: Long = DEFAULT_TIME_OUT): CompressTask

        abstract fun cancel()

        abstract fun release()
    }

}