package com.interfun.buz.biz.center.voicefilter.datasource

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.buz.idl.bot.response.ResponseGetVoiceFilterList
import com.google.gson.Gson
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.component.hilt.UserQualifier
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class LocalVoiceFilterDataSource @Inject constructor(
    @UserQualifier private val voiceFilterDataStore: DataStore<Preferences>,
    @UserQualifier private val userScope: CoroutineScope
) {
    companion object {
        private val KEY_LIBRARY = stringPreferencesKey("key_lib")
        private val KEY_LIBRARY_TIMESTAMP = stringPreferencesKey("key_library_timestamp")
        private const val TAG = "LocalVoiceFilterDataSource"
    }

    private val _voiceFilterLatestEntryNotifyTimestamp =
        MutableStateFlow(CommonMMKV.voiceFilterLatestEntryNotifyTimestamp) // ➕号按钮的红点时间戳
    private val _voiceFilterLatestTabNotifyTimestamp =
        MutableStateFlow(CommonMMKV.voiceFilterLatestTabNotifyTimestamp) // 滤镜入口的红点时间戳

    var voiceFilterCache: ResponseGetVoiceFilterList? = null

    private val gson = Gson()
    fun getVoiceFilterList(): Flow<ResponseGetVoiceFilterList?> {
        return voiceFilterDataStore.data.map {
            val jsonFromDataStore = it[KEY_LIBRARY]
            val json =
                if (jsonFromDataStore?.isNotEmpty() == true) jsonFromDataStore else null
            try {
                voiceFilterCache = gson.fromJson(json, ResponseGetVoiceFilterList::class.java)
                voiceFilterCache
            } catch (t: Throwable) {
                logError(TAG, t, "parse json error error")
                null
            }
        }.flowOn(Dispatchers.IO)
    }

    suspend fun saveVoiceFilterList(voiceFilterList: ResponseGetVoiceFilterList) =
        withContext(Dispatchers.IO) {
            val json = gson.toJson(voiceFilterList)
            logDebug(TAG, "saveVoiceFilterList:$json")
            voiceFilterDataStore.updateData { pref ->
                pref.toMutablePreferences().apply {
                    this[KEY_LIBRARY] = json
                }
            }
        }

    suspend fun clearVoiceFilterData() {
        voiceFilterDataStore.updateData { pref ->
            pref.toMutablePreferences().apply {
                this[KEY_LIBRARY] = ""
            }
        }
    }

    /**
     * 更新入口红点时间戳
     */
    suspend fun updateVFLatestEntryNotifyTimestamp(timestamp: Int) {
        withContext(Dispatchers.Default) {
            CommonMMKV.voiceFilterLatestEntryNotifyTimestamp = timestamp
            _voiceFilterLatestEntryNotifyTimestamp.emit(timestamp)
        }
    }

    /**
     * 获取入口红点时间戳
     */
    fun getVFLatestEntryNotifyTimestamp(): StateFlow<Int> {
        return _voiceFilterLatestEntryNotifyTimestamp
    }

    /**
     * 更新1级tab红点时间戳
     */
    suspend fun updateVFLatestTabTimestamp(timestamp: Int) {
        withContext(Dispatchers.Default) {
            logInfo(TAG, "updateVFLatestTabTimestamp: $timestamp")
            CommonMMKV.voiceFilterLatestTabNotifyTimestamp = timestamp
            _voiceFilterLatestTabNotifyTimestamp.emit(timestamp)
        }
    }

    /**
     * 获取1级tab红点时间戳
     */
    fun getVFLatestTabTimestamp(): StateFlow<Int> {
        return _voiceFilterLatestTabNotifyTimestamp
    }

    /**
     * 更新2级tab红点时间戳
     */
    fun updateVFLatestTimestamp(filterId: Long, timestamp: Int) {
//        val timestampList = getVFLatestTimestamp().toMutableList()

        // Remove any existing entry with the same category type
//        timestampList.removeAll { it.first == value.first }
//        // Add the new/updated pair
//        timestampList.add(value)

        // Save the updated list as string
//        CommonMMKV.voiceFilterLatestTimestamp = timestampList.joinToString(";") { "${it.first},${it.second}" }
        userScope.launch {
            val value = filterId to timestamp
            val oldDataList = getVFLatestTimestamp().first()

            val existing = oldDataList.firstOrNull { it.first == filterId }
            if (existing != null && existing.second == timestamp) {
                logDebug(TAG, "updateVFLatestTimestamp==>no need update, already the same: $existing")
                return@launch
            }
            logDebug(TAG, "updateVFLatestTimestamp==>need update: $value")
            val newList = oldDataList.removeAll { it.first == value.first }.add(value)
            voiceFilterDataStore.edit { preferences ->
                preferences[KEY_LIBRARY_TIMESTAMP] =
                    newList.joinToString(";") { "${it.first},${it.second}" }
            }
        }
    }

    // MMKV-->DataStore
    private suspend fun copy2DataStore() {
        val mmkvValue = CommonMMKV.voiceFilterLatestTimestamp
        val storedValue = voiceFilterDataStore.data.map { it[KEY_LIBRARY_TIMESTAMP] }.first()
        val needMigrate = storedValue.isNullOrEmpty() && !mmkvValue.isNullOrEmpty()
        if (needMigrate) {
            voiceFilterDataStore.edit { prefs ->
                prefs[KEY_LIBRARY_TIMESTAMP] = mmkvValue ?: ""
            }
            logDebug(TAG, "copy2DataStore==>mmkvValue=${mmkvValue}")
            CommonMMKV.voiceFilterLatestTimestamp = ""
        }
    }

    private fun getVFLatestTimestampFlow(): Flow<PersistentList<Pair<Long, Int>>> {
        return voiceFilterDataStore.data.map { preferences ->
            var timeDataJson = preferences[KEY_LIBRARY_TIMESTAMP] ?: return@map persistentListOf()
            logDebug(TAG, "getVFLatestTimestamp==>storedValue=${timeDataJson}")
            timeDataJson.splitToSequence(";")
                .mapNotNull { pair ->
                    val parts = pair.split(",")
                    if (parts.size == 2) {
                        val filterId = parts[0].toLongOrNull()
                        val latestTimestamp = parts[1].toIntOrNull()
                        if (filterId != null && latestTimestamp != null) {
                            filterId to latestTimestamp
                        } else null
                    } else null
                }.toPersistentList()
        }
    }

    /**
     * 获取2级tab红点时间戳
     */
    fun getVFLatestTimestamp(): Flow<PersistentList<Pair<Long, Int>>> = flow {
        copy2DataStore()
        emitAll(getVFLatestTimestampFlow())
//        return CommonMMKV.voiceFilterLatestTimestamp
//            ?.takeIf { it.isNotBlank() }
//            ?.split(";")
//            ?.mapNotNull { pair ->
//                val parts = pair.split(",")
//                if (parts.size == 2) {
//                    val filterId = parts[0].toLongOrNull()
//                    val latestTimestamp = parts[1].toIntOrNull()
//                    if (filterId != null && latestTimestamp != null) {
//                        filterId to latestTimestamp
//                    } else null
//                } else null
//            } ?: listOf()
    }
}