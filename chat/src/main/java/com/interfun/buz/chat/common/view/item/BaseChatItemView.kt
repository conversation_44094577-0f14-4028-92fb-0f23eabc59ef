package com.interfun.buz.chat.common.view.item

import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.annotation.CallSuper
import androidx.viewbinding.ViewBinding
import com.airbnb.lottie.LottieAnimationView
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.widget.round.IRoundLayout
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.*
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.common.ktx.handlePortraitInChatMsgItem
import com.interfun.buz.chat.common.ktx.updateLoading
import com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.ktx.getGroupMemberName
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.widget.view.IconFontTextView
import com.interfun.buz.common.widget.view.PortraitImageView
import com.interfun.buz.im.ktx.isPrivate
import com.interfun.buz.im.ktx.isReceive
import com.interfun.buz.im.ktx.isSend
import com.interfun.buz.im.ktx.userId
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.CoroutineScope

/**
 * <AUTHOR>
 * @date 2023/7/25
 * @desc
 */
abstract class BaseChatItemView<T : BaseChatMsgItemBean, VB : ViewBinding>(
    val itemCallback: ChatItemCallback
) : BaseBindingDelegate<T, VB>() {

    val isGroup get() = itemCallback.convType == IM5ConversationType.GROUP
    private val chatItemBgRadiusMax = R.dimen.chat_item_bg_radius.asDimension()
    private val chatItemBgRadiusMin = R.dimen.chat_item_bg_radius_min.asDimension()
    private val chatItemVerticalPaddingMax =
        R.dimen.chat_item_between_different_padding_top.asDimensionInt()
    private val chatItemVerticalPaddingMin =
        R.dimen.chat_item_between_same_padding_top.asDimensionInt()

    override fun onViewHolderCreated(holder: BindingViewHolder<VB>) {
        super.onViewHolderCreated(holder)
        getPortraitImageView(holder.binding)?.apply {
            if (isGroup) invisible() else gone()
        }
        val clickAreaView = getAnchorView(holder.binding)
        if (clickAreaView == null) {
            holder.onItemClick(this) { binding, item, pos ->
                if (item.canHandleClick()) {
                    itemCallback.onItemClick(binding, item)
                }
            }
        } else {
            holder.onClick(this, clickAreaView) { binding, item, pos ->
                if (item.canHandleClick()) {
                    itemCallback.onItemClick(binding, item)
                }
            }
            holder.onLongClick(this, clickAreaView) { binding, item, pos ->
                if (item.canHandleClick()) {
                    itemCallback.onLongClick(anchorView = clickAreaView, getPortraitImageView(binding), item = item)
                }
            }
            clickAreaView.tag = R.string.chat_item_anchor_view_tag.asString()
        }
        getResendIcon(holder.binding)?.let {
            holder.onClick(this, it) { binding, item, pos ->
                if (item.canHandleClick()) {
                    itemCallback.onResendClick(item)
                }
            }
            it.expandClickArea(10.dp, 10.dp)
        }
        getAsrButton(holder.binding)?.let {
            holder.onClick(this, it) { binding, item, pos ->
                if (it.isVisible() && item.canHandleClick()) {
                    itemCallback.onAsrClick(item)
                }
            }
        }
        getTranslateButton(holder.binding)?.let {
            holder.onClick(this, it) { binding, item, pos ->
                if (it.isVisible() && item.canHandleClick()) {
                    itemCallback.onOneClickTranslateClick(item)
                }
            }
        }
    }

    override fun onBindViewHolder(scope: CoroutineScope?, binding: VB, item: T, position: Int) {
        super.onBindViewHolder(scope, binding, item, position)
        updateReplyViewRadius(item, binding)
        updateItemPaddingAndRadius(item, binding)
        updateItemPortrait(binding, scope, item)
        updateGroupMemberName(item, binding)
        getResendIcon(binding)?.let { resendIcon ->
            getSendingLoading(binding)?.let { resendLoading ->
                item.msg.updateLoading(resendLoading, resendIcon)
            }
        }
        updateQuickReactInfo(binding, item)
        updateFlashBackground(binding, item)
    }

    private fun updateItemPortrait(
        binding: VB,
        scope: CoroutineScope?,
        item: T
    ) {
        val isLastItemInSameMsgGroup = item.isItemTheLastInConsecutiveMsg()
        getPortraitImageView(binding)?.let { ivPortrait ->
            if (isGroup.not()) {
                ivPortrait.gone()
                return
            }
            ivPortrait.handlePortraitInChatMsgItem(
                binding = binding,
                item = item,
                source = FriendApplySource.AVATAR_FROM_CHAT_HISTORY,
                showPortrait = isLastItemInSameMsgGroup
            ) {
                val userId = item.msg.userId
                userId?.let { uid ->
                    val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(uid)
                    itemCallback.onLongClickPortraitToMention(userInfo)
                }
            }
        }
    }

    private fun updateReplyViewRadius(item: T, binding: VB) {
        //如果当前显示回复那么圆角要特化处理
        if (this is IChatSupportReplyView<*, *>) {
            val replySupportView =
                this@BaseChatItemView as IChatSupportReplyView<T, VB>
            val replyItemView = replySupportView.getReplyItemView(binding)
            val showingReply = replySupportView.curShowReply(item, binding, replyItemView)
            if (null == replyItemView || !showingReply) return

            val isReceive = item.msg.isReceive
            replyItemView.apply {
                (this as? IRoundLayout)?.setRadius(
                    tlR = chatItemBgRadiusMax,
                    trR = chatItemBgRadiusMax,
                    brR = if (isReceive) chatItemBgRadiusMax else chatItemBgRadiusMin,
                    blR = if (isReceive) chatItemBgRadiusMin else chatItemBgRadiusMax
                )
            }
        }
    }

    internal fun updateItemPaddingAndRadius(item: T, binding: VB) {
        // modify radius
        val isSameItemGroup = item.isTheSameConsecutiveMessageGroupCompareWithThePrevItem()
        val replySupportView =
            this@BaseChatItemView as? IChatSupportReplyView<T, VB>
        val replyItemView = replySupportView?.getReplyItemView(binding)
        val curShowReply = replySupportView?.curShowReply(item, binding, replyItemView)
        val isShowingReply = null != replyItemView && curShowReply == true
        val radius = if (isSameItemGroup || isShowingReply) chatItemBgRadiusMin else chatItemBgRadiusMax
        val isReceive = item.msg.isReceive
        // VoiceGif 消息需要改变圆角的不是anchor view
        val anchorView = if (item.isVoiceGif) {
            val voiceGifItemView = this as? ChatMsgBaseVoiceGifItemView
            voiceGifItemView?.getVoiceGifContentView(binding) ?: getAnchorView(binding)
        } else {
            getAnchorView(binding)
        }
        anchorView?.apply {
            (this as? IRoundLayout)?.setRadius(
                tlR = if (isReceive) radius else chatItemBgRadiusMax,
                trR = if (isReceive) chatItemBgRadiusMax else radius,
                brR = if (isReceive) chatItemBgRadiusMax else chatItemBgRadiusMin,
                blR = if (isReceive) chatItemBgRadiusMin else chatItemBgRadiusMax
            )
            // When received message is private, there is no portrait display, hence margin end needs to be adjusted
            if (item.msg.isReceive && item.msg.isPrivate) {
                val layoutParams = this.layoutParams as? MarginLayoutParams
                layoutParams?.marginEnd = R.dimen.chat_item_text_horizontal_margin.asDimensionInt()
                this.layoutParams = layoutParams
            }
        }

        // modify padding
        val paddingTop =
            if (isSameItemGroup) chatItemVerticalPaddingMin else chatItemVerticalPaddingMax
        binding.root.setPadding(0, paddingTop, 0, 0)
    }

    private fun updateGroupMemberName(item: T, binding: VB) {
        getGroupMemberNameTextView(binding)?.apply {
            if (item.msg.isSend || item.msg.isPrivate) {
                gone()
                return
            }
            val userId = item.msg.userId
            userId?.let { uid ->
                val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(uid)
                val memberName = userInfo?.getGroupMemberName()
                text = if (memberName.isNullOrEmpty()) {
                    item.msg.userInfo.nickName
                } else {
                    memberName
                }
                visibleIf(item.isItemTheFirstInConsecutiveMsg())
            }
        }
    }

    /**
     * 所有 payload 处理请调用[handlePayload]方法
     */
    final override fun onBindViewHolder(
        holder: BindingViewHolder<VB>,
        item: T,
        payloads: List<Any>
    ) {
        val binding = holder.binding
        if (payloads.isEmpty()) {
            onBindViewHolder(holder, item)
        } else {
            prepareScopeForPayloads(holder)
            payloads.forEach { type ->
                if (type is ChatMsgItemPayloadType) {
                    if (type == ChatMsgItemPayloadType.UpdateQuickReact) {
                        val info = type.extra as? ChatQRPayloadInfo
                        updateQuickReactInfo(binding, item, info)
                        if (this@BaseChatItemView is IChatSupportTranslationView<*,*>) {
                            val chatSupportTranslationView =
                                this@BaseChatItemView as IChatSupportTranslationView<T,VB>
                            val translationView =
                                chatSupportTranslationView.getTranslationView(binding)
                            chatSupportTranslationView.translationUpdateByQR(binding, translationView, item)
                        }
                    }
                    handlePayload(binding, item, type)
                    handlePayload(holder, item, type)
                }
            }
        }
    }

    @CallSuper
    open fun handlePayload(binding: VB, item: T, type: ChatMsgItemPayloadType) {
        when (type) {
            ChatMsgItemPayloadType.UpdateSendFailedStatus -> {
                getResendIcon(binding)?.let { resendIcon ->
                    getSendingLoading(binding)?.let { resendLoading ->
                        item.msg.updateLoading(resendLoading, resendIcon)
                    }
                }
            }
            // insert, delete or recall message to refresh regionally
            ChatMsgItemPayloadType.UpdateItemLayout,
            ChatMsgItemPayloadType.RemoveItemPortraitChange-> {
                updateReplyViewRadius(item, binding)
                updateItemPaddingAndRadius(item, binding)
                updateItemPortrait(
                    binding,
                    binding.getHolder()?.getHolderScope(),
                    item
                )
                updateGroupMemberName(item, binding)
                extraHandle(binding = binding, item = item)
            }
            ChatMsgItemPayloadType.REFERENCE_UPDATE-> {
                if (this is IChatSupportReplyView<*, *>) {
                    val replySupportView =
                        this@BaseChatItemView as IChatSupportReplyView<T,VB>
                    replySupportView.updateReply(binding,getReplyItemView(binding),item,this)
                }
            }
            ChatMsgItemPayloadType.UpdateItemLayoutBackgroundFlash -> {
                updateFlashBackground(binding, item)
            }

            else -> {}
        }
    }

    @CallSuper
    open fun handlePayload(holder: BindingViewHolder<VB>, item: T, type: ChatMsgItemPayloadType) {
    }

    /**
     * 用于设置 ItemView 的点击区域，只有设值后才会触发长按事件
     * 该 View 也会作为长按弹出的菜单的锚点，并且该 View 的内容会被复制展示到长按页面里
     */
    abstract fun getAnchorView(binding: VB): View?
    open fun getPortraitImageView(binding: VB): PortraitImageView? = null
    open fun getResendIcon(binding: VB): IconFontTextView? = null
    open fun getSendingLoading(binding: VB): LottieAnimationView? = null
    open fun getAsrButton(binding: VB): View? = null
    open fun getTranslateButton(binding: VB): View? = null
    open fun getGroupMemberNameTextView(binding: VB): TextView? = null

    private fun updateQuickReactInfo(binding: VB, item: T, info: ChatQRPayloadInfo? = null) {
        (binding.root as? ChatMsgConstraintLayout)?.updateQuickReactView(item, info) {
            if (item.canHandleClick()) {
                itemCallback.onQRHistoryClick(item)
            }
        }
    }
    fun updateFlashBackground(binding: VB, item: T) {
        if (binding.root is ChatMsgConstraintLayout) {
            if (itemCallback.needFlashReplyBackground(item.msg)) {
                itemCallback.onClearFlashReplyBackground()
                (binding.root as? ChatMsgConstraintLayout)?.flashBackground()
            }
        }

    }

    override fun onViewRecycled(holder: BindingViewHolder<VB>) {
        super.onViewRecycled(holder)
        (holder.binding.root as? ChatMsgConstraintLayout)?.removeBackgroundView()
    }

    // 修复问题 “https://project.larksuite.com/yewutest/issue/detail/5702010”
    private fun extraHandle(binding: VB, item: T) {
        if (this is IChatSupportReplyView<*, *>) {
            val replySupportView =
                this@BaseChatItemView as IChatSupportReplyView<T,VB>
            getReplyItemView(binding)?.let {  replyItemView ->
                replySupportView.extraHandle(
                    itemView = replyItemView,
                    binding = binding,
                    item = item,
                    data = replyItemView.data
                )
            }

        }
    }

}