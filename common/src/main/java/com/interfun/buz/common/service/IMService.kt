package com.interfun.buz.common.service

import com.alibaba.android.arouter.facade.template.IProvider
import com.buz.idl.login.bean.ImDeviceParams

interface IMService : IProvider {
    fun onSessionUserLogin(userId: Long, useUnifyLogin: <PERSON><PERSON>an,  imUin: Long, session: String)
    fun onSessionUserLogout(userId: Long)

    fun onSessionRefresh(userId: Long, imUin:Long, session: String)
    fun getImDeviceParams(): ImDeviceParams
}