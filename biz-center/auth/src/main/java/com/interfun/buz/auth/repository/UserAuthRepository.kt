package com.interfun.buz.auth.repository

import com.interfun.buz.component.hilt.UserAuth
import kotlinx.coroutines.flow.StateFlow

/**
 * common 模块有一个[com.interfun.buz.common.manager.UserSessionManager],这个应该要迁移到biz-center/auth中，
 * 但是目前本期重构的内容主要是social模块，这部分暂时先不迁移，后续再迁移，但是对于外部的一些依赖注入，这期都先处理，让新业务可以使用新方式，
 * 等social模块重构完成之后，再把UserSessionManager迁移到biz-center/auth中，放到UserAuthManagerImpl中。
 */
interface UserAuthRepository {
    val userAuthFlow: StateFlow<UserAuth>
}