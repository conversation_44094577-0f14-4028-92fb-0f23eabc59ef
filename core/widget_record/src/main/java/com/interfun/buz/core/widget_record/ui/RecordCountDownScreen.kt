package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.interfun.buz.base.ktx.getOrNull
import com.interfun.buz.base.ktx.maxOf
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.common.constants.CommonConstant
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.InitPreview
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.RecordAreaType
import com.interfun.buz.core.widget_record.state.VoiceFilterRecordedBehavior
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import kotlinx.coroutines.delay
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2024/11/28
 * @desc
 */
@Composable
fun RecordCountDownScreen(
    modifier: Modifier = Modifier,
    centerSpaceHeight: Dp,
    cancelHint: String,
    previewHint: String,
    lockHint: String,
    hintTextStyle: TextStyle,
    showReleaseToSendHint: Boolean,
    isRecordingLambda: () -> Boolean,
    isLockingLambda: () -> Boolean,
    currentPressAreaLambda: () -> RecordAreaType,
    voiceFilterLambda: () -> VoiceFilterUiData?,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        RecordHintText(
            cancelHint = cancelHint,
            previewHint = previewHint,
            lockHint = lockHint,
            hintTextStyle = hintTextStyle,
            showReleaseToSendHint = showReleaseToSendHint,
            isLockingLambda = isLockingLambda,
            currentPressAreaLambda = currentPressAreaLambda,
            recordFinishBehavior = { voiceFilterLambda()?.itemConfig?.recordBehaviour ?: 0 },
        )
        VerticalSpace(centerSpaceHeight)
        CountDownRow(
            isRecording = isRecordingLambda,
            currentPressAreaLambda = currentPressAreaLambda,
            voiceFilter = voiceFilterLambda
        )
    }
}

@Composable
private fun RecordHintText(
    cancelHint: String,
    previewHint: String,
    lockHint: String,
    hintTextStyle: TextStyle,
    showReleaseToSendHint: Boolean,
    isLockingLambda: () -> Boolean,
    currentPressAreaLambda: () -> RecordAreaType,
    recordFinishBehavior:() -> Int,
    modifier: Modifier = Modifier,
) {
    if (showReleaseToSendHint && isLockingLambda()) {
        TapToSendRecordingHint(hintTextStyle,recordFinishBehavior)
        return
    }
    val currentPressArea = currentPressAreaLambda()
    val text = if (currentPressArea == RecordAreaType.Lock) {
        lockHint
    } else if (currentPressArea == RecordAreaType.Cancel) {
        cancelHint
    } else if (currentPressArea == RecordAreaType.Preview) {
        previewHint
    } else if (showReleaseToSendHint) {
        if (recordFinishBehavior() == VoiceFilterRecordedBehavior.Preview.value) {
            previewHint
        } else {
            R.string.home_release_to_send_recording.asString()
        }
    } else {
        return
    }
    Text(
        modifier = modifier,
        text = text,
        color = R.color.color_text_white_secondary.asColor(),
        style = hintTextStyle
    )
}

/**
 * 优化的占位符文本组件，支持多语言和健壮性处理
 */
@Composable
private fun TapToSendRecordingHint(
    hintTextStyle: TextStyle,
    recordedBehavior: () -> Int,
    modifier: Modifier = Modifier
) {
    val tapToSendHint = if (recordedBehavior() == VoiceFilterRecordedBehavior.Preview.value) {
        R.string.tap_button_preview.asString()
    }else{
        R.string.tap_to_send_recording.asString()
    }

    val textWidth = measureTextWidthDp(tapToSendHint, hintTextStyle)
    val showSingleLine = textWidth <= (getScreenWidth() - 80.dp)

    val parts = tapToSendHint.split("##", limit = 2)
    val start = parts.getOrNull(0) ?: return
    val end = parts.getOrNull(1) ?: return
    if (showSingleLine) {
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = start,
                color = R.color.color_text_white_secondary.asColor(),
                style = hintTextStyle
            )

            IconFontText(
                iconRes = if (recordedBehavior() == VoiceFilterRecordedBehavior.Preview.value) R.string.ic_preview else R.string.ic_message,
                iconSize = hintTextStyle.fontSize.textDp(),
                iconColor = R.color.color_text_white_secondary.asColor()
            )

            Text(
                text = end,
                color = R.color.color_text_white_secondary.asColor(),
                style = hintTextStyle
            )
        }
    } else {
        Column(
            modifier = modifier,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                modifier = modifier,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = start,
                    color = R.color.color_text_white_secondary.asColor(),
                    style = hintTextStyle
                )

                IconFontText(
                    iconRes = R.string.ic_message,
                    iconSize = hintTextStyle.fontSize.textDp(),
                    iconColor = R.color.color_text_white_secondary.asColor()
                )

            }
            Text(
                text = end,
                color = R.color.color_text_white_secondary.asColor(),
                style = hintTextStyle
            )
        }
    }
}

@Composable
private fun CountDownRow(
    isRecording: () -> Boolean,
    currentPressAreaLambda: () -> RecordAreaType,
    voiceFilter: () -> VoiceFilterUiData?,
    modifier: Modifier = Modifier,
) {
    val isInCancelArea = currentPressAreaLambda() == RecordAreaType.Cancel
    val isInPreviewArea = currentPressAreaLambda() == RecordAreaType.Preview
    val mainColor by animateColorAsState(
        targetValue = if (isInCancelArea) {
            R.color.color_text_consequential_default.asColor()
        } else if (isInPreviewArea) {
            R.color.color_text_white_important.asColor()
        } else {
            voiceFilter()?.backgroundColor ?: R.color.color_text_highlight_default.asColor()
        }
    )
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (isInCancelArea) {
            IconFontText(
                iconRes = R.string.ic_delete_solid,
                iconSize = 16.dp,
                iconColor = mainColor
            )
            HorizontalSpace(2.dp)
        }
        if (isInPreviewArea) {
            IconFontText(
                iconRes = R.string.ic_play_solid,
                iconSize = 16.dp,
                iconColor = mainColor
            )
            HorizontalSpace(2.dp)
        }
        voiceFilter()?.let {
            RecordVoiceFilterInfo(mainColor, it)
        }
        RecordCountDownText(isRecording(), mainColor)
    }
}

@Composable
private fun RecordCountDownText(
    isRecording: Boolean,
    color: Color,
    modifier: Modifier = Modifier,
) {
    var seconds by rememberMutableInt()
    var isCountDown by rememberMutableBoolean()
    LaunchedEffect(isRecording) {
        seconds = 0
        isCountDown = false
        if (isRecording) {
            repeat(CommonConstant.MAX_RECORD_DURATION) {
                delay(1000L)
                seconds += 1
                isCountDown = seconds >= (CommonConstant.MAX_RECORD_DURATION - 10)
            }
        }
    }

    val alphaAnim = remember { Animatable(1f) }
    LaunchedEffect(isCountDown) {
        if (isCountDown) {
            VibratorUtil.vibrator(from = "RecordCountDown")
            alphaAnim.animateTo(
                targetValue = 0.4f,
                animationSpec = infiniteRepeatable(
                    animation = tween(durationMillis = 900),
                    repeatMode = RepeatMode.Reverse
                )
            )
        } else {
            alphaAnim.snapTo(1f)
        }
    }

    val countDownText = if (isCountDown) {
        val countDownTime = (CommonConstant.MAX_RECORD_DURATION - seconds).maxOf(0)
        stringResource(R.string.chat_speak_time_left_tip, countDownTime)
    } else {
        String.format(Locale.ENGLISH, "0:%02d", seconds)
    }
    Text(
        modifier = modifier.graphicsLayer {
            alpha = alphaAnim.value
        },
        text = countDownText,
        color = color,
        style = TextStyles.bodyMedium()
    )
}

@Composable
@Preview
private fun PreviewTapToSendRecordingHint() {
    InitPreview()
    TapToSendRecordingHint(TextStyles.bodyMedium(), { VoiceFilterRecordedBehavior.Normal.value })
}

@Composable
@Preview(locale = "ar")
private fun PreviewTapToSendRecordingHintAr() {
    InitPreview()
    TapToSendRecordingHint(TextStyles.bodyMedium(), { VoiceFilterRecordedBehavior.Normal.value })
}

@Composable
@Preview(locale = "fr")
private fun PreviewTapToSendRecordingHintFr() {
    InitPreview()
    TapToSendRecordingHint(TextStyles.bodyMedium(), { VoiceFilterRecordedBehavior.Normal.value })
}
