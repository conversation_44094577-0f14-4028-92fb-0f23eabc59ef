package com.interfun.buz.common.net.dispatcher

import com.interfun.buz.base.ktx.logInfo
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

class MergedRequestDispatcher<Req>(
    private val scope: CoroutineScope,
    private val dispatchInterval: DispatchInterval<Req>,
    private val onUndeliveredElement: ((Req) -> Unit)?,
    private val debugTag: String = "RequestDispatcher",
    private val requestHandler: suspend (List<Req>) -> Boolean
) : RequestDispatcher<Req> {
    private val failedTimesFlow = MutableStateFlow(0)
    private val requestQueue =
        Channel<Req>(Channel.UNLIMITED, onUndeliveredElement = { request ->
            onUndeliveredElement?.invoke(request)
        })
    private val actionQueue =
        Channel<List<Req>>(Channel.UNLIMITED, onUndeliveredElement = { request ->
            request.forEach {
                onUndeliveredElement?.invoke(it)
            }
        })

    init {
        scope.coroutineContext[Job]?.invokeOnCompletion { ex ->
            requestQueue.close(ex)
            actionQueue.close(ex)
        }
        handleRequest()
        handleAction()
    }

    private fun handleRequest() {
        scope.launch {
            val waitingSet = arrayListOf<WaitingRequest<Req>>()
            val mutex = Mutex()
            while (isActive) {
                val request = requestQueue.receive()
                launch {
                    val job = coroutineContext.job
                    mutex.withLock {
                        waitingSet.add(WaitingRequest(job, request))
                    }
                    logInfo(debugTag,"start waiting,request:$request,job:$job")
                    dispatchInterval.waitInterval(request, failedTimesFlow.value)
                    ensureActive()
                    mutex.withLock {
                        //clear all job,except self
                        waitingSet.forEach {
                            if (it.waitingJob != job) {
                                logInfo(debugTag,"cancel job:${it.waitingJob},canceled request:${it.request}")
                                it.waitingJob.cancelAndJoin()
                            }
                        }
                        val actionRequest = waitingSet.map { it.request }
                        waitingSet.clear()
                        actionQueue.trySend(actionRequest)
                    }
                }
            }
        }
    }

    private fun handleAction() {
        scope.launch {
            while (isActive) {
                val actionRequest = actionQueue.receive()
                logInfo(debugTag,"start action,request:$actionRequest")
                val result = requestHandler(actionRequest)
                if (!result) {
                    failedTimesFlow.value++
                } else {
                    failedTimesFlow.value = 0
                }
            }
        }
    }

    override fun request(request: Req) {
        scope.launch {
            requestQueue.send(request)
        }
    }


    class WaitingRequest<Req>(val waitingJob: Job, val request: Req)

}