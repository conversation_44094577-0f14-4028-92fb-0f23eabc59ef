<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_r8_overlay_white_6"
    android:paddingBottom="10dp"
    tools:background="@color/black_90"
    tools:layout_width="112dp">

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@drawable/common_user_default_portrait_round" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvEmail"
        style="@style/iconfont_base"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="36dp"
        android:layout_marginTop="33dp"
        android:background="@drawable/common_oval_basic_primary_stroke_444444"
        android:gravity="center"
        android:text="@string/ic_email_solid"
        android:textColor="@color/text_black_main"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait"
        tools:ignore="SpUsage"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvContent"
        style="@style/main_describe"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="10dp"
        android:ellipsize="end"
        android:gravity="center"
        android:lineSpacingMultiplier="0.95"
        android:maxLines="3"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toTopOf="@+id/btnCommon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivPortrait"
        app:layout_constraintVertical_bias="0.4"
        tools:text="Buz Name Buz Name" />

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnCommon"
        android:layout_width="0dp"
        android:layout_height="30dp"
        android:layout_marginHorizontal="10dp"
        android:layout_marginTop="57dp"
        app:iconFont="@string/ic_contact_add"
        app:iconSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivPortrait"
        app:text="@string/add"
        app:type="primary_small"
        tools:background="@drawable/common_r8_basic_primary" />

</androidx.constraintlayout.widget.ConstraintLayout>