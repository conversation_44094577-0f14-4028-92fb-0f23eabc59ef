package com.interfun.buz.push.util

import com.google.gson.Gson
import com.interfun.buz.push.model.GroupPushExtra
import com.interfun.buz.push.model.PushActionOld
import com.interfun.buz.push.model.PushExtra
import com.interfun.buz.push.model.PushGroupInfo
import com.interfun.buz.push.model.PushIMInfo
import com.interfun.buz.push.model.PushPayloadNew
import com.interfun.buz.push.model.PushPayloadOld
import com.interfun.buz.push.model.PushPayloadType
import com.interfun.buz.push.model.PushUserInfo

fun PushActionOld.toPushPayloadNew(gson: Gson = Gson()): PushPayloadNew {
    val payloadOld = gson.fromJson(this.appData, PushPayloadOld::class.java)
    val im5 = this.im5
    val senderUserInfo = if (payloadOld.senderUserInfo == null) {
        null
    } else {
        PushUserInfo(
            payloadOld.senderUserInfo.userId,
            payloadOld.senderUserInfo.userName,
            payloadOld.senderUserInfo.portrait
        )
    }
    val groupInfo = if (payloadOld.groupBaseInfo != null) {
        val name = if (payloadOld.groupBaseInfo.groupName.isNullOrEmpty()) {
            payloadOld.groupBaseInfo.members?.joinToString { it.userName } ?: ""
        } else {
            payloadOld.groupBaseInfo.groupName
        }
        PushGroupInfo(payloadOld.groupBaseInfo.groupId, name, "")
    } else if (payloadOld.type == PushPayloadType.TYPE_GROUP) {
        val groupExtra: GroupPushExtra = payloadOld.pushExtra!!
        val portrait = if (groupExtra.portrait.isNullOrEmpty()) {
            groupExtra.serverPortrait
        } else {
            groupExtra.portrait
        }
        PushGroupInfo(groupExtra.groupId, groupExtra.name ?: "", portrait ?: "")
    } else {
        null
    }
    val oldPushExtra = payloadOld.pushExtra
    val imMsgActionType = if (oldPushExtra?.isAsrType == true) {
        PushIMInfo.IM_MSG_ACTION_ASR
    } else {
        null
    }
    val imInfo =
        if (im5 == null) null else PushIMInfo(
            im5.svrMsgId,
            im5.convType,
            im5.msgType,
            orgSvrMsgId = oldPushExtra?.orgSvrMsgId?.toLongOrNull(),
            msgActionType = imMsgActionType
        )
    val pushExtra = if (oldPushExtra == null) {
        null
    } else PushExtra(
        mentionedUsers = oldPushExtra.mentionedUsers,
        textMentioned = oldPushExtra.textMentioned,
        mentionMap = oldPushExtra.mentionMap,
        filterId = oldPushExtra.filterId,
        orgSvrMsgId = oldPushExtra.orgSvrMsgId,
        reactionType = oldPushExtra.reactionType,
        reactionOpUserId = oldPushExtra.reactionOpUserId,
        reactionEmoji = oldPushExtra.reactionEmoji,
        oriConvType = oldPushExtra.convType,
        channelId = oldPushExtra.channelId,
        timeout = oldPushExtra.timeout,
        channelType = oldPushExtra.channelType,
        callType = oldPushExtra.callType,
        topic = oldPushExtra.topic,
        fileName = oldPushExtra.fileName
    )
    val type = if (payloadOld.type == PushPayloadType.TYPE_QUICK_REACT && !payloadOld.pushExtra?.reactionType.isNullOrEmpty()) {
        if ((oldPushExtra?.groupId ?: 0) > 0) {
            PushPayloadType.TYPE_GROUP
        } else {
            PushPayloadType.TYPE_PRIVATE
        }
    } else {
        payloadOld.type
    }
    val payloadNew = PushPayloadNew(
        type = type,
        ver = 1,
        router = payloadOld.router,
        senderUserInfo = senderUserInfo,
        groupInfo = groupInfo,
        imInfo = imInfo,
        titleReplaceInfo = payloadOld.titleReplaceInfo,
        contentReplaceInfo = payloadOld.contentReplaceInfo,
        pushExtra = pushExtra
    )
    return payloadNew
}