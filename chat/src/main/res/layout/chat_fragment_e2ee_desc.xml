<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background"
    tools:ignore="SpUsage">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="@dimen/status_bar_height" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvBack"
        style="@style/iconfont_24"
        app:autoRTL="true"
        android:layout_marginStart="4dp"
        android:text="@string/ic_back"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/e2ee_help_title"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/spaceTitleBar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:paddingBottom="20dp"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/imageview"
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="e2ee"/>
            <TextView
                style="@style/regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/e2ee_help_main"
                android:layout_marginTop="20dp"
                android:textColor="@color/text_white_default" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@drawable/common_rect_overlay_white_4_radius_12">
                <TextView
                    style="@style/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/e2ee_help_what_title"
                    android:textColor="@color/text_white_main" />
                <TextView
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/e2ee_help_what"
                    android:textColor="@color/text_white_default" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@drawable/common_rect_overlay_white_4_radius_12">
                <TextView
                    style="@style/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/e2ee_help_how_title"
                    android:textColor="@color/text_white_main" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:orientation="horizontal">
                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginTop="7dp"
                        android:layout_marginEnd="7dp"
                        android:background="@drawable/chat_circle_overlay_white_60"/>

                    <TextView
                        style="@style/body"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/e2ee_help_how_content1"
                        android:textColor="@color/text_white_default" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:orientation="horizontal">
                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginTop="7dp"
                        android:layout_marginEnd="7dp"
                        android:background="@drawable/chat_circle_overlay_white_60"/>

                    <TextView
                        style="@style/body"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/e2ee_help_how_content2"
                        android:textColor="@color/text_white_default" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:orientation="horizontal">
                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginTop="7dp"
                        android:layout_marginEnd="7dp"
                        android:background="@drawable/chat_circle_overlay_white_60"/>

                    <TextView
                        style="@style/body"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/e2ee_help_how_content3"
                        android:textColor="@color/text_white_default" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@drawable/common_rect_overlay_white_4_radius_12">
                <TextView
                    style="@style/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/e2ee_help_connect_us"
                    android:layout_marginBottom="8dp"
                    android:textColor="@color/text_white_main" />
                <TextView
                    style="@style/body"
                    android:layout_marginTop="3dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/e2ee_help_connect_us_content"
                    android:textColor="@color/text_white_default" />

                <LinearLayout
                    android:id="@+id/lyFeedback"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:background="@drawable/common_rect_overlay_white_4_radius_8">

                    <TextView
                        style="@style/main_describe"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="@string/contact_robot_feedback_entrance"
                        android:textColor="@color/text_white_main" />

                    <com.interfun.buz.common.widget.view.IconFontTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:text="@string/ic_edit"
                        android:textColor="@color/text_white_main" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>