package com.interfun.buz.contacts.view.itemdelegate

import androidx.compose.ui.platform.ViewCompositionStrategy
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.constants.AddFriendPageSource
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.contacts.databinding.ContactsItemNewVerticalShareListBinding
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.interfaces.ContactsItemCallback
import com.interfun.buz.domain.social.components.VerticalShareListScreen
import com.interfun.buz.domain.social.helper.ShareType

class ContactsVerticalShareListItemViewNew( private val callback: ContactsItemCallback
) : BaseBindingDelegate<ContactsItemBean, ContactsItemNewVerticalShareListBinding>() {


    override fun onBindViewHolder(
        binding: ContactsItemNewVerticalShareListBinding,
        item: ContactsItemBean,
        position: Int
    ) {
        super.onBindViewHolder(binding, item, position)
        val list = mutableListOf<Int>()
        list.add(ShareType.SYSTEM_SHARE.type)
        if (item.extra.source == AddFriendPageSource.RegisterInviteDialog.value){
            if (ABTestManager.isAddFriendDialogJapanExperimental) {
                list.add(ShareType.QR_CODE.type)
            }else{
                list.add(ShareType.COPY_LINK.type)
            }
        }else if (item.extra.source == AddFriendPageSource.Contacts.value){
            list.add(ShareType.QR_CODE.type)
        }
        list.addAll(AppConfigRequestManager.sharePlatformList)
        val dataList = ShareType.getShareTypeList(list)

        binding.cvContainer.apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                VerticalShareListScreen(dataList) {
                    callback.onNewShareItemClick(it)
                }
            }
        }
    }

}