package com.interfun.buz.login.view.block

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.constants.PATH_CHAT_ACTIVITY_HOME
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.manager.af.AFManager
import com.interfun.buz.login.constants.LoginMMKV
import com.interfun.buz.login.utils.LoginTracker
import com.interfun.buz.login.viewmodel.LoginNavViewModel
import com.interfun.buz.login.viewmodel.LoginViewModel
import com.interfun.buz.login.viewmodel.pojo.NeedJumpVerify

abstract class LoginRedirectBlock<T : ViewBinding>(
    private val fragment: Fragment,
    binding: T,
) : BaseBindingBlock<T>(binding) {

    private val loginViewModel by fragment.activityViewModels<LoginViewModel>()

    override fun initData() {
        super.initData()
        loginViewModel.loginRedirectFlow.collectLatestIn(fragment) { redirect ->
            if (redirect == loginViewModel.redirectDefaultValue) return@collectLatestIn
            NeedJumpVerify.clearNeedJumpVerifyInfo()
            // type：0 登录成功，进入主页 1 未完成注册，进入资料填写页
            if (redirect == LoginNavViewModel.REGISTER) {
                if (AFManager.userRegisterSource.isNotNull() && AFManager.inviterUserId.isNotNull()) {
                    LoginTracker.onRegisterFinish(
                        AFManager.userRegisterSource!!,
                        AFManager.inviterUserId!!
                    )
                }

                // 跳到校验成功界面
                fragment.navigationTo(currentId(), verifySuccessActionId())
            } else {
                if (LoginMMKV.hadShownSignUpPermission) {
                    //跳转首页
                    startActivityByRouter(PATH_CHAT_ACTIVITY_HOME)
                    fragment.finishActivity()
                } else {
                    //还没有弹过申请权限页，去申请权限
                    fragment.navigationTo(currentId(), permissionActionId(),
                        Bundle().apply {
                            putBoolean(RouterParamKey.Login.KEY_IS_LOGIN, true)
                        })
                }
            }
            loginViewModel.loginRedirectFlow.emit(loginViewModel.redirectDefaultValue)
        }
    }


    abstract fun currentId(): Int

    abstract fun permissionActionId(): Int

    abstract fun verifySuccessActionId(): Int


}