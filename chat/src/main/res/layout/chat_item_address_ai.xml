<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="70dp"
    android:background="@drawable/common_selector_press_overlay_grey_26"
    tools:background="@color/overlay_grey_20">

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivContactPortrait"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvName"
        style="@style/main_body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_white_main"
        android:textSize="16sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivAIFlag"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivContactPortrait"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="333333333333333333333333333333333333" />

    <ImageView
        android:id="@+id/ivAIFlag"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_gravity="center"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="10dp"
        android:visibility="visible"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/common_icon_ai_flag"/>

</androidx.constraintlayout.widget.ConstraintLayout>