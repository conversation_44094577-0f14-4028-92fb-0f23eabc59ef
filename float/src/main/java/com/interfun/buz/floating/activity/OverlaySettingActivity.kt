package com.interfun.buz.floating.activity

import android.os.Build
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.launch
import com.interfun.buz.base.ktx.overlayPermissionLauncher
import com.interfun.buz.base.utils.FloatPermissionUtils
import com.interfun.buz.common.constants.PATH_FLOAT_OVERLAY_SETTING
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.compose.base.BaseComposeActivity
import com.interfun.buz.compose.components.CommonButton
import com.interfun.buz.compose.components.CommonButtonType
import com.interfun.buz.compose.components.CommonSwitch
import com.interfun.buz.compose.components.CommonTitleBar
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.floating.guide.R
import com.interfun.buz.floating.log.FloatTracker
import com.interfun.buz.floating.manager.FLOAT_GUIDANCE_PAG_PATH
import com.interfun.buz.floating.storage.FloatGuideMMKV
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import org.libpag.PAGView

@Route(path = PATH_FLOAT_OVERLAY_SETTING)
@AndroidEntryPoint
class OverlaySettingActivity : BaseComposeActivity() {

    private val hasPermission get() = FloatPermissionUtils.checkPermission(this)
    private val permissionState by lazy { MutableStateFlow(hasPermission) }

    private val permissionLauncher =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) overlayPermissionLauncher {
            CommonTracker.postOverlayPermissionResult(FloatPermissionUtils.checkPermission())
            permissionState.value = FloatPermissionUtils.checkPermission(this)
        } else null

    override fun onResume() {
        super.onResume()
        FloatTracker.onOverlaySettingPageExpose(FloatPermissionUtils.checkPermission(this))
        permissionState.value = FloatPermissionUtils.checkPermission(this)
    }

    @Composable
    override fun ComposeContent() {
        OverlaySettingPage(permissionState)
    }

    @Composable
    private fun OverlaySettingPage(permissionState: MutableStateFlow<Boolean>) {
        val hasPermission by permissionState.collectAsState()
        val scrollState = rememberScrollState()
        val coroutineScope = rememberCoroutineScope()

        // Scroll to the bottom when the page init (So the text at the bottom can be displayed)
        LaunchedEffect(Unit) {
            coroutineScope.launch {
                scrollState.animateScrollTo(scrollState.maxValue)
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            CommonTitleBar(
                title = stringResource(id = R.string.wt_overlay_name),
                onBackClick = { finish() }
            )
            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(scrollState),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Content(hasPermission)
            }
            if (hasPermission.not()) {
                CommonButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    type = CommonButtonType.PRIMARY_LARGER,
                    showLoading = false,
                    text = stringResource(id = R.string.wt_enable_overlay)
                ) {
                    FloatTracker.onEnableOverlayButtonClickInSettingPage()
                    permissionLauncher?.launch()
                }
                Spacer(modifier = Modifier.height(10.dp))
            }
        }
    }

    @Composable
    private fun Content(hasPermission: Boolean) {
        AndroidView(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f),
            factory = { PAGView(it) },
            update = {
                it.path = FLOAT_GUIDANCE_PAG_PATH
                it.setRepeatCount(0)
                it.play()
            }
        )
        Spacer(modifier = Modifier.height(16.dp))
        if (hasPermission) {
            ContentWithPermission()
        } else {
            ContentWithoutPermission()
        }
        Text(
            modifier = Modifier.padding(20.dp),
            text = R.string.wt_buz_overlay_description.asString(),
            style = TextStyles.bodyLarge(),
            color = R.color.text_white_default.asColor()
        )
    }

    @Composable
    private fun ContentWithPermission() {
        var isBuzOverlayEnable by remember { mutableStateOf(FloatGuideMMKV.isBuzOverlayEnable) }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
                .background(
                    color = R.color.overlay_white_4.asColor(),
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 20.dp, vertical = 14.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = R.string.wt_overlay_name.asString(),
                style = TextStyles.labelLarge(),
                color = R.color.text_white_main.asColor()
            )
            Spacer(Modifier.height(2.dp))
            CommonSwitch(isBuzOverlayEnable) { isChecked ->
                isBuzOverlayEnable = isChecked
                FloatGuideMMKV.isBuzOverlayEnable = isChecked
                FloatTracker.onOverlayToggleButtonClick(isChecked)
            }
        }
    }

    @Composable
    private fun ContentWithoutPermission() {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
                .background(
                    color = R.color.overlay_white_4.asColor(),
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 20.dp, vertical = 14.dp),
        ) {
            Text(
                text = R.string.wt_overlay_name.asString(),
                style = TextStyles.labelLarge(),
                color = R.color.text_white_main.asColor()
            )
            Spacer(Modifier.height(2.dp))
            Text(
                text = R.string.wt_buz_overlay_permission_is_not_enable.asString(),
                style = TextStyles.bodyLarge(),
                color = R.color.text_white_secondary.asColor()
            )
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}