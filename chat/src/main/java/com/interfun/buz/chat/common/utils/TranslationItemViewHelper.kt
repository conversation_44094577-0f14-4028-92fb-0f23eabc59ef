package com.interfun.buz.chat.common.utils

import android.content.res.ColorStateList
import android.graphics.PorterDuff
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.viewbinding.ViewBinding
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.getItem
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.isVisible
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.longClick
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.BaseChatMsgItemBean
import com.interfun.buz.chat.common.entity.ISupportTranslation
import com.interfun.buz.chat.common.entity.asr.AsrInfo
import com.interfun.buz.chat.common.entity.asr.AsrState
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.common.manager.TranslationMessageManager.translateResult
import com.interfun.buz.chat.common.view.item.BaseChatItemView
import com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout
import com.interfun.buz.chat.common.view.widget.ChatTranslationItemView
import com.interfun.buz.im.entity.translation.TranslateState
import com.interfun.buz.im.ktx.isRecallMsg

class TranslationItemViewHelper<T : BaseChatMsgItemBean, VB : ViewBinding> {

    private val TAG = "TranslationItemViewHelper"


    fun handleTranslation(
        binding: VB,
        translationView: ChatTranslationItemView?,
        item: T,
        asrInfo: AsrInfo? = null,
        itemCallback: ChatItemCallback
    ) {
        logInfo(
            TAG,
            "handleTranslation translationView ${translationView.hashCode()} item ${item.hashCode()} asrInfo ${asrInfo} binding ${binding.hashCode()}"
        )
        if (translationView == null || item !is ISupportTranslation) {
            return
        }
        val msg = item.obtainMsg()
        val translateResult = msg.translateResult

        //当前和翻译语言一直不要处理
//        if (!translateResult.isShow && msg.translateResult.userShowTranslateText() != ShowTranslateTextOp.MANUAL_OPEN) {
//            logInfo(
//                TAG,
//                "handleTranslation updateTranslationState return !translateResult.isShow = true "
//            )
//            translationView.gone()
//            return
//        }

        logInfo(
            TAG,
            "handleTranslation updateTranslationState translateResult = ${translateResult} translationView = ${translationView.hashCode()} asrInfo = ${asrInfo}"
        )
        //如果当前没有显示 asr 信息那么不做处理
        if (asrInfo != null) {
            if (!asrInfo.showAsrText || asrInfo.asrState == AsrState.LOADING || asrInfo.asrText.isNullOrEmpty() || msg.isRecallMsg) {
                logInfo(
                    TAG,
                    "handleTranslation return !asrInfo.showAsrText=${!asrInfo.showAsrText}  asrInfo.asrState == AsrState.LOADING ${asrInfo.asrState == AsrState.LOADING}  asrInfo.asrText.isNullOrEmpty() ${asrInfo.asrText.isNullOrEmpty()}"
                )
                translationView.gone()
                return
            }
        }

        //没有启用直接返回即可
        if (!msg.translateResult.shouldBeTranslation()) {
            logInfo(
                TAG,
                "handleTranslation return  !msg.shouldBeTranslation() = ${!msg.translateResult.shouldBeTranslation()}"
            )
            translationView.gone()
            return
        }

        val qrView = binding.root.findViewWithTag<View>(ChatMsgConstraintLayout.QUICK_VIEW_TAG)
        val layoutParams = translationView.layoutParams as ConstraintLayout.LayoutParams
        if (qrView != null && qrView.isVisible()) {
            layoutParams.topToBottom = qrView.id
        }else{
            layoutParams.topToBottom = R.id.spaceContent
        }
        translationView.layoutParams = layoutParams

        val translateText = translateResult.translateText
        if (translateResult.state == TranslateState.TranslateSuccess) {
            translationView.visible()
            itemCallback.onTranslationExpandAni(item)
            translationView.showNormalContent(translateText)
            return
        }

        if (translateResult.state == TranslateState.Idle) {
            translationView.gone()
            return
        }

        if (translateResult.state == TranslateState.Translating) {
            translationView.visible()
            translationView.showLoading()
            itemCallback.onTranslationExpandAni(item)
        }

        if (translateResult.state == TranslateState.TranslateFail) {
            translationView.visible()
            translationView.showFailure()
            itemCallback.onTranslationExpandAni(item)
        }
    }

    private fun obtainItem(
        baseItemView: BaseChatItemView<T, VB>,
        holder: BindingViewHolder<VB>
    ): T? {
        val absoluteAdapterPosition = holder.absoluteAdapterPosition
        if (absoluteAdapterPosition in 0 until baseItemView.adapterItems.size) {
            val item = baseItemView.getItem(absoluteAdapterPosition)
            return item
        } else {
            return null
        }
    }

    fun handleClickNew(
        baseItemView: BaseChatItemView<T, VB>,
        holder: BindingViewHolder<VB>,
        translationView: ChatTranslationItemView?,
        portraitView: View?,
        itemCallback: ChatItemCallback,
    ) {

        if (translationView != null) {
            val longClickCallBack = {
                val item = obtainItem(baseItemView, holder)
                if (item != null) {
                    logInfo(
                        TAG,
                        "item=${item.hashCode()} translationView=${translationView.hashCode()}"
                    )
                    itemCallback.onTranslationLongClick(translationView, portraitView, item)
                    translationView.backgroundTintMode = PorterDuff.Mode.SRC_ATOP
                    translationView.backgroundTintList = ColorStateList.valueOf(
                        ContextCompat.getColor(
                            translationView.context,
                            R.color.overlay_grey_10
                        )
                    )
                }
            }

            translationView.longClick {
                longClickCallBack.invoke()
            }
            translationView.setRetryClick {
                val item = obtainItem(baseItemView, holder)
                if (item != null) {
                    itemCallback.onRetryTranslation(item)
                }
            }
            //由于setRetryClick点击事件消费后,会默认消费长按事件,
            //因此这里补充 translationView.longClick 长按相关逻辑
            translationView.setRetryLongClick {
                longClickCallBack.invoke()
            }
        }


    }

}