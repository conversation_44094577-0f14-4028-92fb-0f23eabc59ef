package com.interfun.buz.compose.components

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.BoxWithConstraintsScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.interfun.buz.base.ktx.logWarn

/**
 * 智能定位容器组件
 *
 * 该组件能够将子组件智能定位在父布局的指定水平和垂直位置，并在边界情况下进行自适应调整。
 *
 * @param targetRatioX 水平方向目标位置比例，0.0f表示最左侧，1.0f表示最右侧，0.57f表示57%位置
 * @param targetRatioY 垂直方向目标位置比例，0.0f表示顶部，1.0f表示底部，0.5f表示垂直居中
 * @param modifier 修饰符，应用于最外层容器
 * @param content 子组件内容
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun SmartPositionContainer(
    modifier: Modifier = Modifier,
    targetRatioX: Float = 0.5f,
    targetRatioY: Float = 0.5f,
    content: @Composable () -> Unit
) {
    val density = LocalDensity.current
    var childSize by remember { mutableStateOf(IntSize.Zero) }
    var offsetX by remember { mutableStateOf(0.dp) }
    var offsetY by remember { mutableStateOf(0.dp) }

    BoxWithConstraints(modifier = modifier.fillMaxSize()) sds@{
        // 获取父布局的可用宽度和高度
        val parentWidth = maxWidth
        val parentHeight = maxHeight

        // 当子组件尺寸或父布局尺寸变化时重新计算偏移量
        LaunchedEffect(childSize, parentWidth, parentHeight, targetRatioX, targetRatioY) {
            if (childSize.width > 0 && childSize.height > 0) {
                val childWidth = with(density) { childSize.width.toDp() }
                val childHeight = with(density) { childSize.height.toDp() }

                offsetX = calculateSmartOffsetX(
                    parentWidth = parentWidth,
                    childWidth = childWidth,
                    targetRatio = targetRatioX
                )

                offsetY = calculateSmartOffsetY(
                    parentHeight = parentHeight,
                    childHeight = childHeight,
                    targetRatioY = targetRatioY
                )
            }
        }

        // 使用计算出的偏移量定位子组件
        Box(modifier = Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .offset(x = offsetX, y = offsetY)
                    .onSizeChanged { size ->
                        if (size != childSize) {
                            childSize = size
                        }
                    }
            ) {

                content()
            }
        }
    }
}

/**
 * 计算水平方向智能定位的偏移量
 * @param parentWidth 父布局宽度
 * @param childWidth 子组件宽度
 * @param targetRatio 目标位置比例（0.0f-1.0f）
 * @return 计算后的水平偏移量
 */
private fun calculateSmartOffsetX(
    parentWidth: Dp,
    childWidth: Dp,
    targetRatio: Float
): Dp {
    val logTag = "SmartPositionContainer"

    // 参数验证
    if (parentWidth <= 0.dp || childWidth <= 0.dp) {
        logWarn(logTag, "无效的尺寸参数: parentWidth=$parentWidth, childWidth=$childWidth")
        return 0.dp
    }

    if (targetRatio < 0f || targetRatio > 1f) {
        logWarn(logTag, "targetRatio 超出有效范围 [0.0, 1.0]: $targetRatio")
        return 0.dp
    }

    // 如果子组件比父组件还宽，直接居中显示
    if (childWidth >= parentWidth) {
        val centeredOffset = (parentWidth - childWidth) / 2
        Log.d(logTag, "子组件宽度超过父组件，居中显示: $centeredOffset")
        return centeredOffset
    }

    // 计算目标中心位置
    val targetCenterX = parentWidth * targetRatio
    Log.d(logTag, "父布局宽度: $parentWidth, 子组件宽度: $childWidth, 目标比例: $targetRatio")
    Log.d(logTag, "目标中心位置: $targetCenterX")

    // 计算子组件左边缘位置（中心位置 - 一半宽度）
    val childLeftEdge = targetCenterX - childWidth / 2
    Log.d(logTag, "理想左边缘位置: $childLeftEdge")

    // 计算子组件右边缘位置（中心位置 + 一半宽度）
    val childRightEdge = targetCenterX + childWidth / 2
    Log.d(logTag, "理想右边缘位置: $childRightEdge")

    // 边界检测和自适应处理
    val adjustedLeftEdge = when {
        // 如果左边缘超出左边界，调整到左边界
        childLeftEdge < 0.dp -> {
            Log.d(logTag, "左边缘超出边界，调整到0")
            0.dp
        }
        // 如果右边缘超出右边界，向左偏移
        childRightEdge > parentWidth -> {
            val adjusted = parentWidth - childWidth
            Log.d(logTag, "右边缘超出边界，调整到: $adjusted")
            adjusted
        }
        // 否则使用计算的位置
        else -> {
            Log.d(logTag, "位置正常，使用理想位置")
            childLeftEdge
        }
    }

    // 最终边界检查
    val finalOffset = when {
        adjustedLeftEdge < 0.dp -> 0.dp
        adjustedLeftEdge > parentWidth - childWidth -> parentWidth - childWidth
        else -> adjustedLeftEdge
    }

    Log.d(logTag, "最终水平偏移量: $finalOffset")
    return finalOffset
}

/**
 * 计算垂直方向智能定位的偏移量
 * @param parentHeight 父布局高度
 * @param childHeight 子组件高度
 * @param targetRatioY 目标位置比例（0.0f-1.0f）
 * @return 计算后的垂直偏移量
 */
private fun calculateSmartOffsetY(
    parentHeight: Dp,
    childHeight: Dp,
    targetRatioY: Float
): Dp {
    val logTag = "SmartPositionContainer-Y"

    // 参数验证
    if (parentHeight <= 0.dp || childHeight <= 0.dp) {
        logWarn(logTag, "无效的尺寸参数: parentHeight=$parentHeight, childHeight=$childHeight")
        return 0.dp
    }

    if (targetRatioY < 0f || targetRatioY > 1f) {
        logWarn(logTag, "targetRatioY 超出有效范围 [0.0, 1.0]: $targetRatioY")
        return 0.dp
    }

    // 如果子组件比父组件还高，直接垂直居中显示
    if (childHeight >= parentHeight) {
        val centeredOffset = (parentHeight - childHeight) / 2
        Log.d(logTag, "子组件高度超过父组件，垂直居中显示: $centeredOffset")
        return centeredOffset
    }

    // 计算目标中心位置
    val targetCenterY = parentHeight * targetRatioY
    Log.d(logTag, "父布局高度: $parentHeight, 子组件高度: $childHeight, 目标比例: $targetRatioY")
    Log.d(logTag, "目标中心位置: $targetCenterY")

    // 计算子组件上边缘位置（中心位置 - 一半高度）
    val childTopEdge = targetCenterY - childHeight / 2
    Log.d(logTag, "理想上边缘位置: $childTopEdge")

    // 计算子组件下边缘位置（中心位置 + 一半高度）
    val childBottomEdge = targetCenterY + childHeight / 2
    Log.d(logTag, "理想下边缘位置: $childBottomEdge")

    // 边界检测和自适应处理
    val adjustedTopEdge = when {
        // 如果上边缘超出上边界，调整到上边界
        childTopEdge < 0.dp -> {
            Log.d(logTag, "上边缘超出边界，调整到0")
            0.dp
        }
        // 如果下边缘超出下边界，向上偏移
        childBottomEdge > parentHeight -> {
            val adjusted = parentHeight - childHeight
            Log.d(logTag, "下边缘超出边界，调整到: $adjusted")
            adjusted
        }
        // 否则使用计算的位置
        else -> {
            Log.d(logTag, "位置正常，使用理想位置")
            childTopEdge
        }
    }

    // 最终边界检查
    val finalOffset = when {
        adjustedTopEdge < 0.dp -> 0.dp
        adjustedTopEdge > parentHeight - childHeight -> parentHeight - childHeight
        else -> adjustedTopEdge
    }

    Log.d(logTag, "最终垂直偏移量: $finalOffset")
    return finalOffset
}
