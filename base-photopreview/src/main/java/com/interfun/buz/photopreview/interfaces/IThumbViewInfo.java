package com.interfun.buz.photopreview.interfaces;

import android.graphics.Rect;
import android.os.Parcelable;
import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 *         date 2017/4/26
 *         E-Mail:<EMAIL>
 *         Deprecated: 图片预览接口
 */
public interface IThumbViewInfo extends Parcelable {

    /****
     * 图片地址
     * @return String
     * ****/
    String getUrl();

    /**
     * 记录坐标
     * @return Rect
     ***/
    Rect getBounds();


    /**
     * 获取视频链接
     * ***/
    @Nullable
     String getVideoUrl();


}
