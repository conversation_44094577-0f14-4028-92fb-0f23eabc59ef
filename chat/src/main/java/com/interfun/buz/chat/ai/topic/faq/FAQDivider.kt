package com.interfun.buz.chat.ai.topic.faq

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.interfun.buz.base.ktx.dp

class FAQDivider : ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val childAdapterPosition = parent.getChildAdapterPosition(view)
        //
        if (childAdapterPosition == 0) {
            outRect.bottom = 20.dp
            return
        }

        if (childAdapterPosition + 1 == parent.adapter?.itemCount) {
            return
        }
        outRect.bottom = 10.dp
    }
}