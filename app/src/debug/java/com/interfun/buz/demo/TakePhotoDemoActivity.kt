package com.interfun.buz.demo

import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.click
import com.interfun.buz.chat.R
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.widget.media.CameraMode
import com.interfun.buz.databinding.ActivityDemoCameraCaptureButtonAnimBinding

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/9/23
 */
class TakePhotoDemoActivity: BaseBindingActivity<ActivityDemoCameraCaptureButtonAnimBinding>() {

    override fun initView() {
        super.initView()
        binding.tvPhoto.click {
            binding.tvPhoto.setTextColor(R.color.text_white_secondary.asColor())
            binding.tvVideo.setTextColor(R.color.text_white_main.asColor())
            binding.btnCapture.updateState(CameraMode.PHOTO)
        }
        binding.tvVideo.click {
            binding.tvPhoto.setTextColor(R.color.text_white_main.asColor())
            binding.tvVideo.setTextColor(R.color.text_white_secondary.asColor())
            binding.btnCapture.updateState(CameraMode.VIDEO)
//            binding.btnCapture.setMaxTime(30)
        }

        binding.tvPhoto.setTextColor(R.color.text_white_secondary.asColor())
        binding.tvVideo.setTextColor(R.color.text_white_main.asColor())
        binding.btnCapture.updateState(CameraMode.PHOTO)
    }
}