package com.interfun.buz.compose.ktx

import androidx.compose.animation.core.AnimationState
import androidx.compose.animation.core.DecayAnimationSpec
import androidx.compose.animation.core.animateDecay
import androidx.compose.animation.rememberSplineBasedDecay
import androidx.compose.foundation.gestures.FlingBehavior
import androidx.compose.foundation.gestures.ScrollScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import kotlin.math.abs

/**
 * <AUTHOR>
 * @date 2024/12/4
 * @desc
 */
@Composable
fun rememberSpeedFlingBehavior(speed: Float = 0.5f): FlingBehavior {
    val flingSpec = rememberSplineBasedDecay<Float>()
    return remember(flingSpec) {
        ScrollSpeedFlingBehavior(speed, flingSpec)
    }
}

private class ScrollSpeedFlingBehavior(
    private val speed: Float,
    private val flingDecay: DecayAnimationSpec<Float>,
) : FlingBehavior {
    override suspend fun ScrollScope.performFling(initialVelocity: Float): Float {
        // Prevent very fast scroll
        val newVelocity = if (initialVelocity > 0F) {
            minOf(initialVelocity * speed, 5000F)
        } else {
            maxOf(initialVelocity * speed, -5000F)
        }

        return if (abs(newVelocity) > 1f) {
            var velocityLeft = newVelocity
            var lastValue = 0f
            AnimationState(
                initialValue = 0f,
                initialVelocity = newVelocity,
            ).animateDecay(flingDecay) {
                val delta = value - lastValue
                val consumed = scrollBy(delta)
                lastValue = value
                velocityLeft = this.velocity
                // avoid rounding errors and stop if anything is unconsumed
                if (abs(delta - consumed) > 0.5f) {
                    this.cancelAnimation()
                }
            }
            velocityLeft
        } else {
            0f
        }
    }
}