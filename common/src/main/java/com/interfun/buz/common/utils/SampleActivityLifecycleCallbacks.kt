package com.interfun.buz.common.utils

import android.app.Activity
import android.app.Application
import android.os.Bundle
/**
 * <AUTHOR>
 * @time 2023/5/25
 * @desc empty implementation of ActivityLifecycleCallbacks
 **/
open class SampleActivityLifecycleCallbacks : Application.ActivityLifecycleCallbacks {
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityResumed(activity: Activity) {
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
    }

}