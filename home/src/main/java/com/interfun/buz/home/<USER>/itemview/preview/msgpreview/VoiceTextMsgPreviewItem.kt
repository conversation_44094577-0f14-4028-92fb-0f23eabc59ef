package com.interfun.buz.home.view.itemview.preview.msgpreview

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.PreviewPayloadType
import com.interfun.buz.chat.wt.entity.TranscribeState
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.ChatPreviewItemVoiceMessageBinding
import kotlinx.coroutines.CoroutineScope

/**
 * <AUTHOR>
 * @date 2022/10/25
 * @desc
 */
class VoiceTextMsgPreviewItem @JvmOverloads constructor
    (context: Context, attrs: AttributeSet? = null) :
    BaseMsgPreviewItem<HomeMsgPreviewModel.VoiceTextPreview>(context, attrs),
    BaseMsgPreviewItem.IASRTextPreviewItem,
    BaseMsgPreviewItem.IPlayablePreviewItem{
    companion object {
        const val TAG = "VoiceTextMsgPreviewItem"
    }

    private val binding by lazy {
        ChatPreviewItemVoiceMessageBinding.inflate(LayoutInflater.from(context), this)
    }

    private var duration = 0

//    private val initialText by lazy {
//        buildSpannedString {
//            append(R.string.chat_pop_msg_voice_tag.asString())
//            if (!isShowDuringInVoiceBtn()) {
//                append(" ")
//                append(duration.voiceTimeStr())
//            }
//        }
//    }

    override fun setMessage(previewData: HomeMsgPreviewModel.VoiceTextPreview, scope: CoroutineScope?,convInfo: HomeMsgPreviewModel.ConvInfo) {
        logDebug(TAG, "setMessage: ")
        setContent(previewData)
        super.setMessage(previewData, scope,convInfo)
    }

    private fun setContent(previewData: HomeMsgPreviewModel.VoiceTextPreview) {
        binding.apply {
            animLoading.gone()
            vVoiceButton.click {
                onPreviewClickListener?.invoke(previewData)
            }
        }

        duration = previewData.duration
        setASRStatus(previewData.asrText, previewData.asrState,previewData.voiceFilterName,previewData.voiceFilterColor)
        setPlayStatus(previewData.isPlaying)
        binding.tvDuration.text = duration.voiceTimeStr()
    }

    override fun updateContent(previewData: HomeMsgPreviewModel.VoiceTextPreview, types: List<PreviewPayloadType>) {
        types.forEach { type ->
            when (type) {
                PreviewPayloadType.UPDATE_PLAYING_STATE -> setPlayStatus(previewData.isPlaying)
                PreviewPayloadType.UPDATE_ASR_STATE -> setASRStatus(previewData.asrText, previewData.asrState,previewData.voiceFilterName,previewData.voiceFilterColor)
                else -> {}
            }
        }
        binding.vVoiceButton.click {
            onPreviewClickListener?.invoke(previewData)
        }
    }

    override fun setPlayStatus(isPlaying: Boolean) {
        if (isPlaying) {
            playVoiceAnim()
        } else {
            stopVoiceAnim()
        }
    }

    override fun playVoiceAnim() {
        binding.apply {
            vVoiceButton.background = ColorDrawable(R.color.color_background_highlight_2_default.asColor())
            iftvVoiceWave.setColorFilter(R.color.color_foreground_highlight_default.asColor())
            animPlaying.loadAnim("lottie/voice_msg_playing.json") {
                iftvVoiceWave.invisible()
                tvDuration.setTextColor(R.color.color_text_highlight_default.asColor())
                animPlaying.apply {
                    visible()
                    setColor(R.color.color_foreground_highlight_default.asColor())
                    setLoop(true)
                    playAnim()
                }
            }
        }
    }

    override fun stopVoiceAnim() {
        binding.apply {
            if (animPlaying.isPlaying()) {
                animPlaying.stopAnim()
                animPlaying.gone()
            }
            iftvVoiceWave.visible()
            vVoiceButton.background = ColorDrawable(R.color.color_background_3_default.asColor())
            iftvVoiceWave.setColorFilter(R.color.color_text_white_secondary.asColor())
            tvDuration.setTextColor(R.color.color_text_white_secondary.asColor())
        }
    }

    override fun setASRStatus(asrText: String?, asrState: TranscribeState, filterName:String?, filterColor: Int?) {
        logDebug(TAG, "asrState: $asrState asrText: $asrText")
        binding.animLoading.apply {
            gone()
            if (isPlaying()) stopAnim()
        }
        val initialText by lazy {
            buildSpannedString {
                if (filterName.isNullOrEmpty()) {
                    append(R.string.chat_pop_msg_voice_tag.asString())
                } else {
                    color(filterColor ?: R.color.color_text_white_secondary.asColor()) {
                        append("[$filterName]")
                    }
                }
                if (!isShowDuringInVoiceBtn()) {
                    append(" ")
                    append(duration.voiceTimeStr())
                }

            }
        }
        binding.apply {
            tvTextMessage.text = buildSpannedString {
                append(initialText)
                if (asrState == TranscribeState.TranscribeSuccess) {
                    if (asrText.isNullOrEmpty().not()) {
                        append(" ")
                        append(asrText)
                    }
                } else if (asrState == TranscribeState.Transcribing) {
                    append(" ")
                    append(R.string.home_preview_transcribing.asString())
                }
            }
        }
        updateTextHeight()
    }
}