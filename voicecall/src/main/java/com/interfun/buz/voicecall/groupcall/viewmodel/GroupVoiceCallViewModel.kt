package com.interfun.buz.voicecall.groupcall.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.bean.voicecall.NetworkStatus
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.bean.voicecall.VoiceStatus
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.voicecall.common.model.RealTimeCallUser
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class GroupVoiceCallViewModel : ViewModel() {

    companion object{
        private const val TAG = "GroupVoiceCallViewModel"
    }

    var groupId: Long = 0L
        set(value) {
            field = value
            fetchBaseInformation(value)
        }

    private val _baseInformation: MutableStateFlow<BaseInformation?> = MutableStateFlow(null)
    val baseInformation: StateFlow<BaseInformation?> = _baseInformation

    private val _userList = MutableStateFlow<List<RealTimeCallUser>>(emptyList())
    val userList: StateFlow<List<RealTimeCallUser>> = _userList

    suspend fun listenMembers(room: VoiceCallRoom){
        room.members.collect { members ->
            //产品临时解决方案：如果群通话中其他成员都是弱网状态，则自己也展示弱网提示
            val weakConnectionSize = members.filter { cRoomUser ->
                cRoomUser.userId != UserSessionManager.uid && cRoomUser.netWorkStatus >= NetworkStatus.NOT_GOOD
            }.size
            val fakeMySelfPoorNetwork = weakConnectionSize == members.size - 1 && weakConnectionSize > 0
            if (fakeMySelfPoorNetwork){
                logWarn(TAG, "fakeMySelfPoorNetwork, listenMembers weak connection members(exclusive myself) size = $weakConnectionSize, and room all members count = ${members.size}")
            }
            _userList.value = members.map { it.convertToRealTimeCallUser(fakeMySelfPoorNetwork) }
            logDebug(TAG, "User: ${_userList.value}")
        }
    }

    private fun CallRoomUser.convertToRealTimeCallUser(fakeMySelfPoorNetwork: Boolean): RealTimeCallUser {
        val existRgbColor = _userList.value.find { it.userId == userId }?.rgbColor
        return RealTimeCallUser(
            channelUserId = rtcUserId.toLong(),
            userId = userId,
            portrait = portrait,
            userName = userName,
            micStatus = micStatus,
            cameraStatus = cameraStatus,
            voiceStatus = when (callStatus) {
                CallStatus.CALLING -> VoiceStatus.STATUS_JOINED
                CallStatus.BE_CALLED -> VoiceStatus.STATUS_PENDING
                CallStatus.ON_CALL -> VoiceStatus.STATUS_JOINED
                CallStatus.HANG_UP -> VoiceStatus.STATUS_LEFT_CALL
                CallStatus.REJECT -> VoiceStatus.STATUS_REJECTED
                CallStatus.TIMEOUT -> VoiceStatus.STATUS_NO_ANSWER
                else -> VoiceStatus.STATUS_PENDING
            },
            isSpeaking = isSpeaking,
            poorNetwork = if (userId == UserSessionManager.uid && fakeMySelfPoorNetwork){
                true
            } else {
                netWorkStatus >= NetworkStatus.NOT_GOOD
            },
            rgbColor = existRgbColor
        )
    }

    data class BaseInformation(
        val groupName: String,
        val groupInfoBean: GroupInfoBean
    )

    /**
     * Fetch base information of the group
     */
    private fun fetchBaseInformation(groupId: Long) {
        viewModelScope.launch {
            GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId)?.let {
                _baseInformation.value = BaseInformation(
                    groupName = it.groupName?: "",
                    groupInfoBean = it
                )
            }
        }
    }
}