package com.interfun.buz.chat.common.entity

import com.interfun.buz.common.ktx.getStringDefault
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.entity.ServerExtra
import com.interfun.buz.im.message.VoiceTextMsg
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IM5Message
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.profile.UserInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Author: ChenYouSheng
 * Date: 2023/7/10
 * Email: <EMAIL>
 * Desc:
 */
class ChatMsgReceiveVoiceTextItemBean(
    override var msg: IMessage,
    val msgUrl: String = "",
    val text: String = "",
    var autoPlay: Boolean = false,
    val duration: Int = 0,
    override val msgType: ChatMsgType = ChatMsgType.VoiceText,
    val isSendToBot: Boolean = false
) : BaseChatMsgItemBean() {

    // 本地使用,用来判断是否要显示Tap to Listener字样
    var showTapToListener: Boolean = autoPlay.not()
    // waiting ai response
    var isWaitingResponse = false

    // 本地使用，是否展示翻译评分按钮
    var showTranslateRating = false

    var ttsState = TtsState.Waiting
        get() {
            return when {
                text == "[error]" -> TtsState.Error
                text == "[empty]" -> TtsState.Empty
                text.isNotEmpty() -> TtsState.Success
                else -> TtsState.Waiting
            }
        }
        private set


    suspend fun getReplyMsgId(): String {
        val extra = withContext(Dispatchers.IO) {
            IMMessageContentExtra.parseFromJson(msg.content.extra)
        }
        return extra.serverExtra?.replyMsgId.getStringDefault()
    }

    suspend fun getLocalReplyMsgId(): String {
        val extra = withContext(Dispatchers.IO) {
            IMMessageContentExtra.parseFromJson(msg.content.extra)
        }
        return extra.serverExtra?.localReplyMsgId.getStringDefault()
    }

    suspend fun updateReplyMsgId(replyMsgId: String) {
        val extra = withContext(Dispatchers.IO) {
            IMMessageContentExtra.parseFromJson(msg.content.extra)
        }
        extra.serverExtra?.replyMsgId = replyMsgId
        msg.content.extra = extra.toJson()
    }


    override fun toString(): String {
        val serverExtra = IMMessageContentExtra.parseFromJson(msg.content.extra).serverExtra
        return "ChatMsgReceiveVoiceTextItemBean:{ttsState:${ttsState},text:${text},serMsgId:${msg.serMsgId},msgId:${msg.msgId}," + "replyMsgId=${serverExtra?.replyMsgId},localReplyMsgId=${serverExtra?.localReplyMsgId}}"
    }
}
