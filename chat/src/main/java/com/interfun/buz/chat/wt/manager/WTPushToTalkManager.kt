package com.interfun.buz.chat.wt.manager

import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.coroutine.CloseableCoroutineScope
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.chat.R
import com.interfun.buz.chat.di.OverlayEntryPoint
import com.interfun.buz.chat.wt.entity.WTFloatTarget
import com.interfun.buz.chat.wt.service.WalkieTalkieService
import com.interfun.buz.common.constants.CommonConstant.MAX_RECORD_DURATION
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.RecordingConstant
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.ktx.toastSolidWarning
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.chat.WTGuidanceManager
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.ClientTracker
import com.interfun.buz.common.utils.RtpTracker
import com.interfun.buz.domain.im.social.entity.CommonMsgParams
import com.interfun.buz.domain.im.social.entity.VoiceMsgParams
import com.interfun.buz.domain.record.block.BaseRecordBlock
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.domain.record.helper.TraceUtils
import com.interfun.buz.domain.record.viewmodel.VadRecordSegment
import com.interfun.buz.domain.record.viewmodel.VadRecordViewModel
import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.im.entity.IMSendFrom.OVERLAY
import com.interfun.buz.im.entity.SendMsgResult
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.record.simplerecord.VadRecordEngine
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import java.io.File

object WTPushToTalkManager {

    private const val START_TYPE_NONE = 0
    private const val START_TYPE_VAD_RECORD = 1
    private const val TAG = "WTPushToTalkManager"
    val recordViewModel by lazy { VadRecordViewModel() }
    private val scope = CloseableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    private var target: WTFloatTarget? = null
    val msgSentFlow = MutableSharedFlow<WTFloatTarget>()
    val recordTimeOutFlow = MutableSharedFlow<Any>()
    @Volatile
    private var startType = START_TYPE_NONE
    @Volatile
    private var mTraceId: String? = null

    init {
        initVadRecordSegmentObserver()
        initVadRecordErrorObserver()
    }

    fun stopRecord() {
        logInfo(TAG,"stopRecord,startType:${startType}")
        recordViewModel.stopRecord()
    }

    fun cancel() {
        recordViewModel.destroyRecord()
    }

    fun pushToTalk(target: WTFloatTarget) {
        mTraceId = ClientTracker.generateTraceId()
        logInfo(TAG,"pushToTalk, traceId:$mTraceId serviceFlag:${WalkieTalkieService.launchWalkietalkieFlag}")
        if (!WalkieTalkieService.isStartedForeground()) {
            BuzTracker.onForegroundServiceNotStated(
                "1",
                WalkieTalkieService.launchWalkietalkieFlag,
                WalkieTalkieService.isServiceStartForeground
            )
        }
        startType = START_TYPE_VAD_RECORD
        val dir = File(appContext.filesDir, "record")
        if (!dir.exists()) {
            dir.mkdirs()
        }
        this.target = target
        RtpTracker.onEventRtpRecordStart(
            0L, UserSessionManager.uid, target.targetId, target.isGroup, false, mTraceId, OVERLAY.code
        )
        //如果是机器人，使用机器人的采样率和码率
        if (target.isUser && target.userInfo?.isRobot==true) {
            recordViewModel.startRecord(
                MAX_RECORD_DURATION * 1000,
                2500,
                dir.absolutePath,
                false,
                CommonMMKV.rAISamplingRate,
                CommonMMKV.rAIBitRate,
                traceId = mTraceId,
                isPrivateChat = true,
                pageBusinessId = target.targetId.toString()
            )
        }else{
            recordViewModel.startRecord(
                MAX_RECORD_DURATION * 1000,
                2500,
                dir.absolutePath,
                false,
                traceId = mTraceId,
                isPrivateChat = target.isGroup.not(),
                pageBusinessId = target.targetId.toString()
            )
        }
    }

    fun vadError(errCode:Int){
        logInfo(TAG,"vadRecordErrorFlow erro ${errCode}")
        val target = <EMAIL>
        RtpTracker.onEventVoiceRecordResult(
            mTraceId,
            "0",
            UserSessionManager.uid.toString(),
            target?.targetId?.toString() ?: "",
            if (target?.isUser?.not() == true) 1 else 0,
            false,
            false,
            errCode,
            OVERLAY.code
        )
    }
    private fun initVadRecordErrorObserver() {

    }

    private fun initVadRecordSegmentObserver() {
        scope.launch {
            recordViewModel.vadRecordSegmentFlow.collect { segment ->
                RecordStatusHelper.setRecordingStatus(false)
                val target = <EMAIL>
                    ?: throw IllegalArgumentException("cannot send msg to a empty WTFloatTarget")
                if (segment.durationMs <= BaseRecordBlock.MIN_RECORD_DURATION) {
                    toastSolidWarning(R.string.chat_tap_and_hold_to_speak)
                    recordViewModel.viewModelScope.launch(Dispatchers.IO) {
                        File(segment.aacFilePath).delete()
                    }
                    RtpTracker.onEventVoiceRecordResult(
                        mTraceId,
                        "0",
                        UserSessionManager.uid.toString(),
                        target.targetId.toString(),
                        if (target.isUser.not()) 1 else 0,
                        false,
                        false,
                        1,
                        OVERLAY.code
                    )
                    return@collect
                }
                if (segment.endType == VadRecordEngine.VadEndType.Vad_End_OverMaxDuration) {
                    toast(ResUtil.getString(R.string.wt_record_max_time_limit_tip, MAX_RECORD_DURATION))
                    recordTimeOutFlow.emit(Any())
                }
                onSegmentReceived(target, segment)
            }
        }
    }

    private fun onSegmentReceived(target: WTFloatTarget, segment: VadRecordSegment) {
        val targetId = target.targetId.toString()
        val convType =
            if (target.isUser) IM5ConversationType.PRIVATE else if (target.isGroup) IM5ConversationType.GROUP else null
        if (convType == null){
            RtpTracker.onEventVoiceRecordResult(
                mTraceId,
                "0",
                UserSessionManager.uid.toString(),
                target.targetId.toString(),
                if (target.isUser.not()) 1 else 0,
                false,
                false,
                10002,
                OVERLAY.code
            )
            return
        }
        logInfo(TAG, "send wt-count msg")
        TraceUtils.voiceFileInfo("2",mTraceId?:"",segment.durationMs.toLong(),segment.aacFilePath)
        RecordingConstant.selfReportSilent(mTraceId?:"", recordViewModel.selfReportSilent)
        WTGuidanceManager.setStartRecordInGuidancePTT(true)
        val sendIMRepository = EntryPointAccessors.fromApplication<OverlayEntryPoint>(appContext).getSendImRepository()
        sendIMRepository.launchInUserScope {
            val voiceMsgParams = VoiceMsgParams(
                targetId = targetId,
                path = segment.aacFilePath,
                convType = convType,
                duration = segment.durationMs,
                commonMsgParams = CommonMsgParams(
                    traceId = mTraceId,
                    replyId = null,
                    eventTrackExtra = EventTrackExtra(sendFrom = OVERLAY.code)
                ),
            )
            val result = sendIMRepository.sendVoiceMessage(voiceMsgParams)
            if (result is SendMsgResult.SendMsgFailed && result.error.errorCode == 3 && result.error.errorType == 0) {
                target.dispatchWithCache(user = { (id, userInfo) ->
                    toast(
                        ResUtil.getString(
                            R.string.rejects_receive_your_msg,
                            userInfo?.getContactFirstName() ?: ""
                        )
                    )
                }, group = { (id, groupInfo) ->
                    toast(
                        ResUtil.getString(
                            R.string.rejects_receive_your_msg,
                            groupInfo?.groupName ?: ""
                        )
                    )
                })
            }
        }
        //埋点
        TraceUtils.reportReduceNoiseData(mTraceId?:"", segment.audioProcessMetrics)

        scope.launch {
            msgSentFlow.emit(target)
        }
        RtpTracker.onEventVoiceRecordResult(
            mTraceId,
            "0",
            UserSessionManager.uid.toString(),
            targetId,
            if (convType == IM5ConversationType.GROUP) 1 else 0,
            false,
            true,
            null,
            OVERLAY.code
        )
    }

}