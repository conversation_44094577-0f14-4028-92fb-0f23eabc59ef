package com.interfun.buz.user.view.fragment

import android.annotation.SuppressLint
import android.os.Build
import android.text.InputFilter
import android.text.InputType
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.widget.Toast.Callback
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ime.IMEMarginAdjustment
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterParamKey.User.UPDATE_BUZ_ID_IN_PROFILE
import com.interfun.buz.common.constants.RouterParamKey.User.UPDATE_BUZ_ID_IN_REGISTRATION
import com.interfun.buz.common.constants.USER_NAME
import com.interfun.buz.common.ktx.buzId
import com.interfun.buz.common.ktx.setStyleBodyMedium
import com.interfun.buz.common.ktx.setStyleTitleMedium
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.TimeUtils
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.dialog.delegate.DefaultAlertDialogDelegate
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentEditBuzIdBinding
import com.interfun.buz.user.log.UserTracker
import com.interfun.buz.user.view.dialog.BuzIdModifyWarningDialog
import com.interfun.buz.user.viewmodel.BuzIDVerifyState
import com.interfun.buz.user.viewmodel.UserProfileUpdateViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class UpdateBuzIdFragment: BaseBindingFragment<UserFragmentEditBuzIdBinding>() {
    private val TAG = "UpdateBuzIdFragment"
    private val updateViewModel by activityViewModels<UserProfileUpdateViewModel>()
    private var warningDialog:BuzIdModifyWarningDialog? = null
    private var source:Int = UPDATE_BUZ_ID_IN_PROFILE
    private var suggestionBuzId: String? = null
    private var verifyLegalJob: Job? = null
    private val verifyDelayTime = 500L
    private val allowedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_.1234567890"
    private val currentToast =ArrayList<String>()
    private val toastTime = 1500

    private val trackSource
        get() = when(source){
        UPDATE_BUZ_ID_IN_REGISTRATION -> "register"
        UPDATE_BUZ_ID_IN_PROFILE -> "settings"
        else -> "settings"
    }

    override fun initArguments() {
        source = arguments?.getInt(RouterParamKey.Common.KEY_SOURCE, UPDATE_BUZ_ID_IN_PROFILE)
            ?: UPDATE_BUZ_ID_IN_PROFILE
        suggestionBuzId = arguments?.getString(RouterParamKey.Common.USER_NAME)
    }

    override fun initData() {
        lifecycleScope.launch {
            val result = updateViewModel.getLastModifyTime(UserSessionManager.buzId)
            val lastModifyTime = result.second
            logInfo(TAG,"initData: lastModifyTime = $lastModifyTime isSuccess = ${result.first}")

            if (result.first){
                if (System.currentTimeMillis() - lastModifyTime  > millsOneYear){
                    binding.etBuzId.setAutoShowKeyboard(this@UpdateBuzIdFragment)
                    return@launch
                }
                showModifyWarning(lastModifyTime)
            }
        }
    }

    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()
        binding.cbSave.setDisable(true)
        binding.cbSave.adjustViewAboveIME(
            adjustment = IMEMarginAdjustment(40.dp),
            gapBetweenViewAndIME = 20.dp
        )

        val tips = when(source){
            UPDATE_BUZ_ID_IN_REGISTRATION -> R.string.login_info_register_edit_username_rule.asString()
            UPDATE_BUZ_ID_IN_PROFILE -> R.string.login_info_edit_buz_id_rule.asString()
            else -> R.string.login_info_edit_buz_id_rule.asString()
        }
        binding.tvOnceAYearTip.text = tips

        binding.etBuzId.apply {
            setText(suggestionBuzId ?: UserSessionManager.buzId)
            setSelection(binding.etBuzId.text.length)

            val formatFilter = InputFilter { source, start, end, dest, dstart, dend ->
                val originText = dest.replaceRange(dstart,dend,source.subSequence(start,end)).toString()
                logInfo(TAG,"originText = : $originText")
                if (!originText.isNullOrEmpty() && !originText.first().isLetter()){
                    if (source.length == 1){
                        toastError(R.string.login_info_user_name_must_begin_with_letter.asString())
                    }
                    return@InputFilter getCorrectBuzId(source,start,end,dest,dstart,dend)
                }else if (originText.containsNotSupportedChar(allowedChars)){
                    if (source.length == 1){
                        toastError(R.string.login_info_user_name_not_support_letter.asString())
                    }
                    return@InputFilter getCorrectBuzId(source,start,end,dest,dstart,dend)
                }else if (originText.isContainsContinuousChar('.')){
                    if (source.length <= 1){
                        toastError(R.string.login_info_username_cannot_more_than_one_period.asString())
                    }
                    if (source.isNullOrEmpty()){
                        return@InputFilter dest.subSequence(dstart,dend)
                    }
                    return@InputFilter getCorrectBuzId(source,start,end,dest,dstart,dend)
                }

                if (source.toString() != source.toString().lowercase()){
                    val lowerText = source.toString().lowercase()
                    if(source is Spanned){
                        val sp = SpannableString(lowerText)
                        TextUtils.copySpansFrom(source,start,lowerText.length,null,sp,0)
                        return@InputFilter sp
                    }else{
                        return@InputFilter lowerText
                    }
                }

                return@InputFilter null
            }

            val lengthFilter = InputFilter { source, start, end, dest, dstart, dend ->
                var keep = 20 - (dest.length - (dend - dstart))
                if (keep <= 0) {
                    toastError(R.string.login_info_user_name_limit.asString())
                    return@InputFilter ""
                } else if (keep >= end - start) {
                    null // keep original
                } else {
                    keep += start
                    if (Character.isHighSurrogate(source[keep - 1])) {
                        --keep
                        if (keep == start) {
                            return@InputFilter ""
                        }
                    }
                    toastError(R.string.login_info_user_name_limit.asString())
                    return@InputFilter source.subSequence(start, keep)
                }
            }
            filters = arrayOf(formatFilter,lengthFilter)
            inputType = InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS or InputType.TYPE_TEXT_VARIATION_FILTER
        }
        initListener()
        UserTracker.onUpdateBuzIdViewScreen(trackSource)
    }


    private fun getCorrectBuzId(
        source: CharSequence, start: Int, end: Int,
        dest: Spanned, dstart: Int, dend: Int
    ): CharSequence {
        val textBuilder = StringBuilder()
        val checkText = source.subSequence(start, end)
        checkText.forEach {
            if (allowedChars.contains(it)
                && !(it == '.' && textBuilder.isNotEmpty() && textBuilder.last() == '.')){
                textBuilder.append(it)
            }
        }

        if (dstart == 0){
            val tmpText = textBuilder.toString()
            val fromIndex = textBuilder.indexOfFirst { it.isLetter() }
            textBuilder.clear()
            if (fromIndex>=0){
                textBuilder.append(tmpText.substring(fromIndex))
            }
        }
        if (dstart>0 && dest[dstart-1] == '.' && textBuilder.isNotEmpty() && textBuilder.first() == '.'){
            textBuilder.deleteAt(0)
        }
        if (dend<dest.length-1 && dest[dend] == '.' && textBuilder.isNotEmpty() && textBuilder.last() == '.'){
            textBuilder.deleteAt(textBuilder.lastIndex)
        }

        val text = textBuilder.toString().lowercase()
        if (source is Spanned){

            val sp = SpannableString(text)
            TextUtils.copySpansFrom(source,start,text.length,null,sp,0)
            return sp
        }
        return  text
    }

    @SuppressLint("ResourceAsColor")
    private fun initListener() {
        binding.etBuzId.doAfterTextChanged {
            val currentText = binding.etBuzId.textWithoutSpace.lowercase()
            if (currentText.isEmpty() || UserSessionManager.buzId == currentText){
                binding.cbSave.setDisable(true)
            }else{
                binding.cbSave.setDisable(false)
            }
            changeTipStateToDefault()
            binding.iftvClearBuzId.visibleIf(currentText.isNotEmpty())
            verifyLegalJob?.cancel()
            verifyLegalJob = updateViewModel.verifyBuzID(currentText,verifyDelayTime)
        }

        binding.iftvClearBuzId.click {
            binding.etBuzId.clearText()
        }

        binding.iftvLeftBack.click {
            onBackPressed()
        }

        updateViewModel.updateBuzIdCode.observe(this) {
            logInfo(TAG, "initListener: code = ${it.first} msg = ${it.second}")
            UserTracker.onUpdateBuzNameResult(trackSource,it.second,it.first)
            if (source == UPDATE_BUZ_ID_IN_REGISTRATION){
                binding.cbSave.hideLoading()
            }
            if (it.first == 0){
                activity?.finish()
                return@observe
            }else{
                val errorTip = UserProfileUpdateViewModel.getErrorTip(it.first)
                if (errorTip.isNull()) return@observe
                toastError(errorTip!!)
            }

        }

        updateViewModel.verifyBuzIdResult.observe(this) {
            log(TAG, "initListener: on verifyBuzIdResult = ${it.first} ${it.second}")
            val currentEditUserName = binding.etBuzId.textWithoutSpace.lowercase()
            if (currentEditUserName != it.first) return@observe

            when (it.second) {
                BuzIDVerifyState.NOT_VERIFY.value -> changeTipStateToDefault()
                BuzIDVerifyState.SUCCESS.value -> changeTipStateToSuccess()
                BuzIDVerifyState.CHECKING.value -> changeTipStateToChecking()
                BuzIDVerifyState.EXIST.value,
                BuzIDVerifyState.FORMAT_ERROR.value,
                BuzIDVerifyState.OFFENSIVE_WORD_ERROR.value,
                BuzIDVerifyState.TOO_MORE_TIMES_ERROR.value,
                BuzIDVerifyState.END_ERROR.value,
                BuzIDVerifyState.TOO_SHORT_ERROR.value-> changeTipStateToError(UserProfileUpdateViewModel.getErrorTip(it.second)?:"")
                else -> changeTipStateToDefault()
            }
        }

        binding.cbSave.click {
            if (activity.isNull()) return@click
            UserTracker.onUpdateBuzIdSaveClick()

            val username = binding.etBuzId.textWithoutSpace.lowercase()
           val localVerifyResult = updateViewModel.localVerifyBuzId(username)
            if (localVerifyResult != BuzIDVerifyState.SUCCESS){
                toastError(UserProfileUpdateViewModel.getErrorTip(localVerifyResult.value)?:"")
                return@click
            }

            if (source == UPDATE_BUZ_ID_IN_PROFILE){
                warningDialog = BuzIdModifyWarningDialog.newInstance(
                    buzId = username
                )
                warningDialog?.showDialog(activity)
                return@click
            }

            if (source == UPDATE_BUZ_ID_IN_REGISTRATION){
                updateViewModel.updateBuzId(username,UPDATE_BUZ_ID_IN_REGISTRATION)
                binding.cbSave.showLoading()
            }
        }
    }

    @SuppressLint("StringFormatMatches")
    private fun showModifyWarning(lastModifyTime:Long){
        val modifyTime = TimeUtils.formatTimestamp(lastModifyTime)
        val nextEnableEditTime = TimeUtils.formatTimestamp(lastModifyTime + millsOneYear)
        val title = getString(R.string.login_info_buz_id_you_change_name_to_xxxx, modifyTime)
        val content =
            getString(R.string.login_info_buz_id_change_name_to_xxxx_once_a_year, nextEnableEditTime)
        val dialog = CommonAlertDialog(
            requireActivity(),
            canceledOnTouchOutside = false,
            title = title,
            tips = content,
            positiveText = R.string.ok.asString(),
            positiveCallback = { dialog ->
                dialog.dismiss()
                finishActivity()
            },

            cancelable = false
        )
        val dialogBinding = (dialog.delegate as DefaultAlertDialogDelegate).binding
        dialogBinding.btnPositive.background = R.color.secondary_button_main.asDrawable()
        dialogBinding.tvTitle.setStyleTitleMedium()
        dialogBinding.tvTitle.setTextColor(R.color.text_white_main.asColor())
        dialogBinding.tvTips.setStyleBodyMedium()
        dialogBinding.tvTips.setTextColor(R.color.text_white_default.asColor())
        dialog.show()
    }

    private fun changeTipStateToChecking(){
        logInfo(TAG,"changeTipStateToChecking: ")
        if (binding.pagLoading.isVisible()) return
        binding.iftvError.gone()
        binding.pagLoading.visible()
        binding.pagLoading.startLoading()
        binding.tvTips.setTextColor(R.color.text_white_default.asColor())
        binding.tvTips.visible()
        binding.tvTips.text = R.string.login_info_edit_checking_user_name.asString()
    }

    private fun changeTipStateToError(tip:String){
        logInfo(TAG,"changeTipStateToError: ")
        binding.pagLoading.stopLoading()
        binding.pagLoading.gone()
        binding.iftvError.visible()
        binding.iftvError.text = R.string.ic_warning_solid.asString()
        binding.iftvError.setTextColor(R.color.secondary_error.asColor())
        binding.tvTips.setTextColor(R.color.secondary_error.asColor())
        binding.tvTips.visible()
        binding.tvTips.text = tip
    }

    private fun changeTipStateToSuccess() {
        logInfo(TAG,"changeTipStateToSuccess: ")
        binding.pagLoading.stopLoading()
        binding.pagLoading.gone()
        binding.iftvError.visible()
        binding.iftvError.text = R.string.ic_correct_solid.asString()
        binding.iftvError.setTextColor(R.color.basic_primary.asColor())
        binding.tvTips.visible()
        binding.tvTips.text = getString(R.string.login_info_user_name_avaliable)
        binding.tvTips.setTextColor(R.color.basic_primary.asColor())
    }

    private fun changeTipStateToDefault(){
        logInfo(TAG,"changeTipStateToDefault: ")
        binding.pagLoading.stopLoading()
        binding.pagLoading.gone()
        binding.iftvError.gone()
        binding.tvTips.gone()
    }

    private fun toastError(tips:String){
        logInfo(TAG,"toastError:currentToast = ${currentToast.size} ")
        if (currentToast.contains(tips)) return
        currentToast.add(tips)
        var callback:Callback? = null

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R){
            callback =  object :Callback(){
                override fun onToastHidden() {
                    currentToast.remove(tips)
                }
            }
        }else{
            delayInViewScope(this,toastTime.toLong()){
                currentToast.remove(tips)
            }
        }

        toastIconFontMsg(
            message = tips,
            textColor = R.color.secondary_error.asColor(),
            iconFont = R.string.ic_warning_solid.asString(),
            iconFontColor = R.color.secondary_error.asColor(),
            duration = toastTime,
            callback = callback
        )
    }
}