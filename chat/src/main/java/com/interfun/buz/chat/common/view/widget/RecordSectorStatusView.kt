package com.interfun.buz.chat.common.view.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.chat.R

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/9/26
 */
class RecordSectorStatusView @JvmOverloads constructor
    (context: Context, attrs: AttributeSet? = null): View(context, attrs) {

    private var statusDisable = false

    private val paint: Paint by lazy {
        Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL
        }
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (statusDisable){
            paint.shader = LinearGradient(
                width/2f,
                0f,
                width/2f,
                height.toFloat(),
                R.color.overlay_grey_20.asColor(),
                R.color.overlay_grey_20.asColor(),
                Shader.TileMode.CLAMP
            )
        }else{
            paint.shader = LinearGradient(
                width/2f,
                0f,
                width/2f,
                height.toFloat(),
                R.color.basic_primary.asColor(),
                R.color.white.asColor(),
                Shader.TileMode.CLAMP
            )
        }
        val rectF = RectF(
            -150.dp.toFloat(),
            0.dp.toFloat(),
            (width + 150.dp).toFloat(),
            (height + 20.dp).toFloat()
        )
        canvas.drawOval(rectF, paint)
    }

    fun setRecordStatus(disable: Boolean){
        statusDisable = disable
        invalidate()
    }

}