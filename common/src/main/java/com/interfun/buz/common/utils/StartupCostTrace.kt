package com.interfun.buz.common.utils

import com.interfun.buz.common.manager.launchAsync
import com.lizhi.component.tekiapm.TekiApm
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.rds.RDSAgent
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay

object StartupCostTrace {
    
    private const val TAG = "StartupCostTrace"

    private var hasReportTaskTime = false
    private var hasReportTime = false

    private var startTime = 0L
    private var attachEndTime = 0L
    private var contentProviderEndTime = 0L
    private var createApplicationEndTime = 0L
    private var startupApplicationEndTime = 0L
    private var createBuzAppEndTime = 0L
    private var createActivityStartTime = 0L

    var arouterCostTime = 0L
    var baseInitCostTime = 0L
    var initImCostTime = 0L
    var itNetInitCostTime = 0L
    var lzTrackerCostTime = 0L
    var pushCostTime = 0L
    var tekiApmCostTime = 0L
    var userProfileInitCostTime = 0L



    fun reportAppStartUpTime() {
        if (!ApplicationContext.isInMainProcess()) {
            return
        }
        if (hasReportTime) {
            return
        }
        hasReportTime = true
        HashMap<String, Any?>().apply {
            put("apm_session", TekiApm.sessionId)
            put("attach", attachEndTime - startTime)
            put("content_provider", contentProviderEndTime - attachEndTime)
            put("create_application", createApplicationEndTime - contentProviderEndTime)
            put("start_up", startupApplicationEndTime - createApplicationEndTime)
            put("create_buz_app", createBuzAppEndTime - startupApplicationEndTime)
            put("create_activity", createActivityStartTime - createBuzAppEndTime)
            put("type",FirstInstallLaunchHelper.status.toInt())
        }.also {
            RDSAgent.postEvent(
                "EVENT_BUZ_START_UP_COST",
                it
            )
            if (ApplicationContext.isInMainProcess())
                Logz.tag(TAG).i("EVENT_BUZ_START_UP_COST: $it")
        }
    }

    fun recordStart() {
        startTime = System.currentTimeMillis()
    }
    fun recordAttach() {
        attachEndTime = System.currentTimeMillis()
    }

    fun recordContentProvider() {
        contentProviderEndTime = System.currentTimeMillis()
    }

    fun recordCreateApplication() {
        createApplicationEndTime = System.currentTimeMillis()
    }

    fun recordStartUpApp() {
        startupApplicationEndTime = System.currentTimeMillis()
    }

    fun recordCreateBuzApp() {
        createBuzAppEndTime = System.currentTimeMillis()
    }

    fun recordCreateActivity() {
        createActivityStartTime = System.currentTimeMillis()
        GlobalScope.launchAsync {
            delay(3000)
            reportAppStartUpTime()
        }
    }

    fun reportMainTaskCostTime() {
        if (!ApplicationContext.isInMainProcess()) {
            return
        }
        if (hasReportTaskTime) {
            return
        }
        hasReportTaskTime = true
        HashMap<String, Any?>().apply {
            put("apm_session", TekiApm.sessionId)
            put("arouter", arouterCostTime)
            put("base_init", baseInitCostTime)
            put("init_im", initImCostTime)
            put("it_net_init", itNetInitCostTime)
            put("lz_tracker", lzTrackerCostTime)
            put("push", pushCostTime)
            put("teki_apm", tekiApmCostTime)
            put("user_profile_init", userProfileInitCostTime)
            put("type",FirstInstallLaunchHelper.status.toInt())
        }.also {
            RDSAgent.postEvent(
                "EVENT_BUZ_START_TASK_COST",
                it
            )
            Logz.tag(TAG).i("EVENT_BUZ_START_TASK_COST: $it")
        }
    }

}