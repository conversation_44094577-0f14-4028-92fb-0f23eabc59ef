package com.interfun.buz.contacts.view.itemdelegate

import com.interfun.buz.base.ktx.layoutSize
import com.interfun.buz.contacts.databinding.ContactsItemSpaceBinding
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.interfaces.BaseContactsDelegate
import com.interfun.buz.contacts.interfaces.DefaultContactsItemCallback

/**
 * <AUTHOR>
 * @date 2023/5/9
 * @desc 通用间距类型
 * General Spacing Type
 */
class ContactsSpaceItemView :
    BaseContactsDelegate<ContactsItemSpaceBinding>(DefaultContactsItemCallback()) {

    override fun onBindViewHolder(
        binding: ContactsItemSpaceBinding,
        item: ContactsItemBean,
        position: Int
    ) {
        binding.space.layoutSize(item.extra.spaceSize, item.extra.spaceSize)
    }
}