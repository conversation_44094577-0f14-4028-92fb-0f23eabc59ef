package com.interfun.buz.common.voicecall

import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.chat.JumpStartRealTimeCallEntrance
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.CallConflictState.*
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.convert2RTCUser
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.VoiceCallNotificationTracker

class PrivateCallStaterImpl(fragment: Fragment) : CallStarter(fragment),
    PrivateCallStater {
    private val TAG = "PrivateVoiceCallStaterImpl"

    override fun startRealTimeCall(
        targetId: Long,
        callType: @CallType Int,
        entrance: @JumpStartRealTimeCallEntrance Int,
        callStartResult: OneParamCallback<CallStartResult>?
    ) {
        val traceId = generateTraceId()
        // 点击前打点
        VoiceCallNotificationTracker.onClickCallEntranceEvent(
            traceId = traceId,
            targetId = targetId,
            channelType = ChannelType.TYPE_VOICE_CALL_1V1,
            callType = callType,
            entrance = entrance
        )
        val user =
            UserRelationCacheManager.getUserRelationInfoByUid(targetId)?.convert2RTCUser() ?: return
        val userList = listOf(user)

        val (callJumper, callStater) = createStartHelper(
            userList = userList,
            targetId = targetId,
            callType = callType,
            channelType = ChannelType.TYPE_VOICE_CALL_1V1,
            traceId = traceId,
            onCallStarted = { callConflictState ->
                if (callConflictState != NO_CONFLICT) {
                    handleRealTimeCallErrorAndTrackEvent(
                        targetId = targetId,
                        callType = callType,
                        error = callConflictState,
                        actionType = ActionType.START
                    )
                }
            }
        )

        doPreCheck(
            targetId = targetId,
            channelType = ChannelType.TYPE_VOICE_CALL_1V1,
            callType = callType,
            userList = userList,
            onResult = { conflictState ->
                // 点击后打点
                VoiceCallNotificationTracker.onClickStartRealTimeCallResult(
                    traceId = traceId,
                    callType = callType,
                    channelType = ChannelType.TYPE_VOICE_CALL_1V1,
                    entrance = entrance,
                    conflictState = conflictState
                )
                // 结果回调
                callStartResult?.invoke(
                    CallStartResult(
                        startHelper = callStater,
                        jumpHelper = callJumper,
                        curCallConflictState = conflictState
                    )
                )
            },
            actionType = ActionType.START
        )
    }


    override fun handlePreCheckResult(
        targetId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        callConflictState: CallConflictState,
        actionType: ActionType

    ) {
        handleRealTimeCallErrorAndTrackEvent(
            targetId = targetId,
            callType = callType,
            error = callConflictState, actionType = actionType
        )
    }


    private fun handleRealTimeCallErrorAndTrackEvent(
        targetId: Long,
        callType: @CallType Int,
        error: CallConflictState,
        actionType: ActionType
    ) {
//        if (error == NOT_FRIEND) {
//            val tip = ResUtil.getString(
//                R.string.not_firend_some,
//                UserRelationCacheManager.getUserRelationInfoByUid(targetId)?.getDisplayName()
//            )
//            toast(tip)
//            trackStartCallEvent(targetId, error)
//            return
//        }
        handleRealTimeCallError(error, actionType, targetId, callType)
        trackStartCallEvent(targetId = targetId, callType = callType, conflict = error)
    }

    private fun trackStartCallEvent(
        targetId: Long,
        callType: @CallType Int,
        conflict: CallConflictState
    ) {
        logInfo(TAG, "trackStartCallEvent:targetId = $targetId, conflict = $conflict")
        val failReason = when (conflict) {
            BEING_INVITED -> "ongoing_call" //发起呼叫的时候，有一个未处理的通话邀请
            ON_CALL -> "realTimeCalling" //正在Buz与其他人通话
            NO_RECORD_PERMISSION -> "no_record_permission" //没有录音权限
            NO_CAMERA_PERMISSION -> "no_camera_permission" // 没有相机权限
            NOT_FRIEND -> "notfriend" //不是好友
            NETWORK_ERROR -> "other" //其他如网络错误
            RECORD_IN_USE -> "systemCalling" //正在系统通话
            CAMERA_IN_USE -> "camera_in_use" //相机正在使用
            else -> return
        }
        CommonTracker.onStartPrivateVoiceCallResult(
            targetId = targetId,
            channelId = 0L,
            callType = callType,
            waitTime = 0L,
            isSuccess = false,
            failReason = failReason
        )
    }
}
