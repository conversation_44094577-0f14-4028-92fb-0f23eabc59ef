package com.interfun.buz.chat.common.view.item

import android.text.method.LinkMovementMethod
import android.view.ViewGroup
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.utils.ViewBindingUtil
import com.interfun.buz.chat.common.entity.BaseChatMsgItemBean
import com.interfun.buz.chat.common.entity.ChatMsgSendImageItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.common.utils.ReplyItemViewHelper
import com.interfun.buz.chat.common.view.widget.ReplyItemView
import com.interfun.buz.chat.common.view.widget.ReplyPreviewData
import com.interfun.buz.chat.databinding.ChatItemSendImageBinding
import com.interfun.buz.chat.databinding.ChatItemSendTextBinding
import kotlinx.coroutines.CoroutineScope

/**
 * Author: ChenYouSheng
 * Date: 2023/6/6
 * Email: <EMAIL>
 * Desc:
 */
class ChatMsgSendImageItemView(
    itemCallback: ChatItemCallback
) : ChatMsgSendMediaItemView<ChatMsgSendImageItemBean, ChatItemSendImageBinding>(itemCallback), IChatSupportReplyView<ChatMsgSendImageItemBean,ChatItemSendImageBinding> {

    private val replyItemViewHelper = ReplyItemViewHelper<ChatMsgSendImageItemBean, ChatItemSendImageBinding>()

    override fun onViewHolderCreated(holder: BindingViewHolder<ChatItemSendImageBinding>) {
        super.onViewHolderCreated(holder)
        replyItemViewHelper.handleClickNew(baseItemView = this,
            holder = holder,
            replyItemView = holder.binding.replyView,
            itemCallback = itemCallback)

    }

    override fun onBindViewHolder(scope: CoroutineScope?, binding: ChatItemSendImageBinding, item: ChatMsgSendImageItemBean, position: Int) {
        super.onBindViewHolder(scope, binding, item, position)
        replyItemViewHelper.handleReply(binding, binding.replyView, item,this)
    }

    override fun createViewHolder(parent: ViewGroup): ChatMsgSendImageViewHolder {
        return ChatMsgSendImageViewHolder(ViewBindingUtil.inflateWithGeneric(this, parent),itemCallback)
    }

    override fun getResendIcon(binding: ChatItemSendImageBinding) = binding.iftvSendFailed

    override fun getSendingLoading(binding: ChatItemSendImageBinding) = binding.lottieLoading

    override fun getAnchorView(binding: ChatItemSendImageBinding) = binding.flImage
    override fun getReplyItemView(binding: ChatItemSendImageBinding): ReplyItemView? {
        return binding.replyView
    }

    override fun extraHandle(
        itemView: ReplyItemView,
        binding: ChatItemSendImageBinding,
        item: BaseChatMsgItemBean,
        data: ReplyPreviewData
    ) {
    }

    override fun updateReply(
        binding: ChatItemSendImageBinding,
        replyItemView: ReplyItemView?,
        item: ChatMsgSendImageItemBean,
        supportView: IChatSupportReplyView<ChatMsgSendImageItemBean, ChatItemSendImageBinding>
    ) {
        replyItemViewHelper.handleReply(binding, replyItemView, item, supportView)
    }


}