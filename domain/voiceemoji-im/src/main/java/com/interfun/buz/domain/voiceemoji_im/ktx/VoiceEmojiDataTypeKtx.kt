package com.interfun.buz.domain.voiceemoji_im.ktx

import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategoryType


fun VoiceEmojiCategory.isBlindBox(): Boolean {
    return type == VoiceEmojiCategoryType.BlindBox.type
}

fun VoiceEmojiEntity.isBlindBox(): Boolean {
    return category.type == VoiceEmojiCategoryType.BlindBox.type
}
