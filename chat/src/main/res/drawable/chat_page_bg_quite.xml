<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 边框层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:centerX="0.25"
                android:centerY="0"
                android:endColor="@color/color_background_3_default"
                android:gradientRadius="50%"
                android:type="radial"
                android:startColor="#998F7DFF"
                android:centerColor="#2E8F7DFF"
                android:useLevel="false" />
            <corners
                android:topLeftRadius="30dp"
                android:topRightRadius="30dp" />
        </shape>
    </item>
    <!-- 背景层 -->
    <item
        android:bottom="0dp"
        android:left="0dp"
        android:right="0dp"
        android:top="1dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/color_background_3_default" />
            <corners
                android:topLeftRadius="30dp"
                android:topRightRadius="30dp" />
        </shape>
    </item>
</layer-list>
