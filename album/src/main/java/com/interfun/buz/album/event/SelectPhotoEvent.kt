package com.interfun.buz.album.event

import com.interfun.buz.album.bean.MediaItem
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.eventbus.BaseEvent

/**
 * 选中通知
 * @param item hasChoose是选中/取消选中更新后的值
 * <AUTHOR>
 * @date 2024/4/11
 */
class SelectPhotoEvent(
    val item: MediaItem,
    val position: Int
) : BaseEvent() {
    companion object {
        fun post(item: MediaItem, position: Int) {
            BusUtil.post(SelectPhotoEvent(item, position))
        }
    }
}