package com.interfun.buz.common.widget.portrait.group.fetcher

import android.graphics.Bitmap
import androidx.core.graphics.drawable.toDrawable
import coil.annotation.ExperimentalCoilApi
import coil.decode.DataSource
import coil.decode.ImageSource
import coil.disk.DiskCache
import coil.fetch.DrawableResult
import coil.fetch.FetchResult
import coil.fetch.Fetcher
import coil.fetch.SourceResult
import coil.request.Options
import coil.size.Size
import coil.size.pxOrElse
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.widthInt
import com.interfun.buz.base.utils.ScreenUtil
import com.interfun.buz.common.ktx.SingletonDiskCache
import com.interfun.buz.common.widget.portrait.PortraitUtil.defaultPortraitSize
import com.interfun.buz.common.widget.portrait.group.GroupPortraitHelper
import okhttp3.internal.closeQuietly
import java.text.DecimalFormat

/**
 * <AUTHOR>
 * @date 2023/8/17
 * @desc
 */
@OptIn(ExperimentalCoilApi::class)
abstract class GroupPortraitFetcher(internal val options: Options) : Fetcher {

    companion object {
        const val TAG = "GroupPortraitFetcher"
    }

    internal val diskCache get() = SingletonDiskCache.get(appContext)
    private val fileSystem get() = diskCache.fileSystem
    // Only when the size is equal to 90dp, then written to the disk cache
    // Because in most scenarios, the size of group portrait is 90dp
    // That`s no need to be cached if size too small or too large
    private val isSizeCanBeDiskCachedWrite = run {
        (options.size.widthInt - 1) <= defaultPortraitSize && options.diskCachePolicy.writeEnabled
    }
    private val isSizeCanBeDiskCachedRead = run {
        (options.size.widthInt - 1) <= defaultPortraitSize && options.diskCachePolicy.readEnabled
    }

    internal suspend fun fetch(
        urlList: List<String?>?,
        onWriteToDiskCacheCallback: DefaultCallback? = null
    ): FetchResult? {
        urlList ?: return null
        // Check disk cache before generate bitmap
        var snapshot: DiskCache.Snapshot? = null
        try {
            snapshot = readFromDiskCache()
            if (snapshot != null) {
                return SourceResult(
                    source = snapshot.toFileImageSource(),
                    mimeType = "image/png",
                    dataSource = DataSource.DISK
                )
            } else {
                var dataSource = DataSource.NETWORK
                val size = if (options.size.widthInt < defaultPortraitSize) {
                    Size(defaultPortraitSize, defaultPortraitSize)
                } else {
                    options.size
                }
                val bitmap = GroupPortraitHelper().getBitmap(size, urlList) { isAllCache ->
                    if (isAllCache) dataSource = DataSource.MEMORY_CACHE
                }
                snapshot = writeToDiskCache(bitmap)
                if (snapshot != null) {
                    onWriteToDiskCacheCallback?.invoke()
                }
                return DrawableResult(
                    drawable = bitmap.toDrawable(appContext.resources),
                    isSampled = true,
                    dataSource = dataSource
                )
            }
        } catch (e: Exception) {
            log(TAG, "fetch e:${e.message}")
            throw e
        } finally {
            snapshot?.closeQuietly()
        }
    }

    private fun readFromDiskCache(): DiskCache.Snapshot? {
        return if (isSizeCanBeDiskCachedRead) {
            options.diskCacheKey?.let { diskCache[it] }
        } else {
            null
        }
    }

    private fun writeToDiskCache(bitmap: Bitmap): DiskCache.Snapshot? {
        val key = options.diskCacheKey
        if (!isSizeCanBeDiskCachedWrite || key == null) {
            return null
        }
        log(TAG, "writeToDiskCache size: ${ScreenUtil.px2dp(options.size.width.pxOrElse { 0 }.toFloat())} ")

        // Open a new editor. Return `null` if we're unable to write to this entry.
        val editor = diskCache.edit(key) ?: return null
        try {
            // Write the bitmap to the disk cache.
            fileSystem.write(editor.data) {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream())
                log(TAG, "cache size: ${format(diskCache.size)}/${format(diskCache.maxSize)}")
            }
            return editor.commitAndGet()
        } catch (e: Exception) {
            editor.abortQuietly()
            log(TAG, "writeToDiskCache e:${e.message}")
            throw e
        }
    }

    private fun DiskCache.Editor.abortQuietly() {
        try {
            abort()
        } catch (e: Exception) {
            log(TAG, "abortQuietly e:${e.message}")
        }
    }

    private fun DiskCache.Snapshot.toFileImageSource(): ImageSource {
        return ImageSource(data, fileSystem, options.diskCacheKey, this)
    }

    private fun format(size: Long): String {
        val maxCacheSizeInMB = size.toDouble() / (1024 * 1024)
        val decimalFormat = DecimalFormat("#.##")
        return decimalFormat.format(maxCacheSizeInMB) + "MB"
    }
}