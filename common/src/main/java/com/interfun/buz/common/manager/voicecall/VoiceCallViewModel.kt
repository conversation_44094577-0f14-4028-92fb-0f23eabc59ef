package com.interfun.buz.common.manager.voicecall

import android.content.Context
import android.media.AudioManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.realtimecall.request.*
import com.buz.idl.realtimecall.response.*
import com.buz.idl.realtimecall.service.BuzNetRealTimeCallServiceClient
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.application
import com.interfun.buz.base.ktx.launchDelay
import com.interfun.buz.base.ktx.launch
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.manager.retry.IntervalStrategy
import com.interfun.buz.base.manager.retry.RetryAction
import com.interfun.buz.base.manager.retry.RetryManager
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.bean.voicecall.AudioDevice.AUDIO_ROUTE_BLUETOOTH_DEVICE
import com.interfun.buz.common.ktx.CODE_NO_REAL_TIME_CALL
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.VoiceCallNotificationTracker
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.itnet.lthrift.service.ITResponse
import com.yibasan.lizhifm.audio.BaseAudioRouterType
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withTimeout

class VoiceCallViewModel(val callType: @CallType Int) : ViewModel() {
    private val TAG = "VoiceCallViewModel"

    private val client by lazy {
        BuzNetRealTimeCallServiceClient().withConfig()
    }

    private val retryTime = 10

    private val _startRealTimeCallResult =
        MutableStateFlow<ITResponse<ResponseStartRealTimeCall>?>(null)
    private val _joinRoomResult = MutableStateFlow<ITResponse<ResponseJoinRealTimeCall>?>(null)
    private val _hangUpResult = MutableSharedFlow<ITResponse<ResponseHangUpRealTimeCall>>()
    private val _fetchUserListResult =
        MutableSharedFlow<ITResponse<ResponseGetRealTimeCallUserList>>()
    private val _inviteJoinRoomResult = MutableSharedFlow<ITResponse<ResponseInviteRealTimeCall>>()
    private val _changeMicStateResult = MutableSharedFlow<ITResponse<ResponseSetMicrophoneStatus>>()
    private val _changeCameraStatusResult = MutableSharedFlow<ITResponse<ResponseSetCameraStatus>>()
    private val _hangupByException = MutableSharedFlow<Int>()


    val startRealTimeCallResult: StateFlow<ITResponse<ResponseStartRealTimeCall>?> =
        _startRealTimeCallResult
    val joinRoomResultStateFlow: StateFlow<ITResponse<ResponseJoinRealTimeCall>?> = _joinRoomResult
    val hangUpResultStateFlow: SharedFlow<ITResponse<ResponseHangUpRealTimeCall>> = _hangUpResult
    val fetchUserListResult: SharedFlow<ITResponse<ResponseGetRealTimeCallUserList>> =
        _fetchUserListResult
    val inviteJoinRoomResult: SharedFlow<ITResponse<ResponseInviteRealTimeCall>> =
        _inviteJoinRoomResult
    val changeMicStateResult: SharedFlow<ITResponse<ResponseSetMicrophoneStatus>> =
        _changeMicStateResult
    val hangupByException: SharedFlow<Int> = _hangupByException


    val onAudioDeviceChangeFlow by lazy {
        if (callType == CallType.TYPE_VIDEO) {
            MutableStateFlow(AudioDevice.AUDIO_ROUTE_SPEAKER)//
        }else{
            MutableStateFlow(AudioDevice.AUDIO_ROUTE_HANDSET)//默认听筒模式
        }
    }
    val audioDeviceSwitchable = MutableStateFlow(true)
    private val _audioDeviceRouters = MutableStateFlow<Array<BaseAudioRouterType>?>(null)
    val audioDeviceRouters: StateFlow<Array<BaseAudioRouterType>?> = _audioDeviceRouters
    val showVoiceDeviceSelectorFlow = MutableSharedFlow<Array<BaseAudioRouterType>?>()

    private val deviceSwitchMutex = Mutex()
    @Volatile
    var callHangup = false

    private val changeMicStatusRetryManager by lazy { RetryManager() }
    private val changeCamStatusRetryManager by lazy { RetryManager() }

    private val audioManager by lazy { application.getSystemService(Context.AUDIO_SERVICE) as AudioManager }

    fun startRealTimeCall(
        userList: List<Long>,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        groupId: Long?,
        traceId: String
    ) = launchIO {
        val request = RequestStartRealTimeCall(userList, groupId, channelType, callType, traceId)
        val response = client.startRealTimeCall(request)
        VoiceCallNotificationTracker.onRequestStartRealTimeCallResult(
            channelId = response.data?.channelId ?: 0L,
            traceId = traceId,
            channelType = channelType,
            callType = callType,
            rCode = response.code
        )
        logInfo(TAG, "startRealTimeCall result code:${response.code}", logLine = LogLine.START_RTCAll)
        _startRealTimeCallResult.emit(response)
    }

    /**
     * Join the room that existed
     * @param source 1:Join by calling 2:Join by group conversation
     */
    fun joinRealTimeCall(
        channelId: Long,
        source: Int,
        callType: @CallType Int,
        cameraStatus: @CameraStatus Int?,
        micStatus: @MicStatus Int?,
        channelType: @ChannelType Int,
    ) = launchIO {
        logInfo(TAG, "joinRealTimeCall:channelId = $channelId, source = $source, callType = $callType", logLine = LogLine.START_RTCAll)
        val camStatus = cameraStatus ?: if (CallType.isVideoCall(callType)) CameraStatus.OPEN else CameraStatus.CLOSE
        val micState = micStatus ?: MicStatus.OPEN
        val request = RequestJoinRealTimeCall(channelId, source, micState, camStatus)
        val response = client.joinRealTimeCall(request)
        logInfo(TAG, "joinRoom: response.code = ${response.code}")
        VoiceCallNotificationTracker.onJoinChannelResult(
            channelType = channelType,
            channelId = channelId,
            callType = callType,
            success = response.code.isSuccess,
            failReason = if (!response.code.isSuccess) response.code.toString() else null
        )
        _joinRoomResult.emit(response)
    }

    fun hangUp(channelId: Long?) {
        callHangup = true
        logInfo(TAG, "hangUp:channelId = $channelId", logLine = LogLine.START_RTCAll)
        VoiceCallPortal.voiceCallScope.launchIO {
            _hangUpResult.emit(ITResponse())
            if (channelId == null) return@launchIO
            val request = RequestHangUpRealTimeCall(channelId, 1)
            var responseCode = -1
            /**
             * 大佬说要强化感知退房请求
             * 如果弱网或者无网需要重复请求 代码不优雅但是可以解决问题。
             * 优雅解决方案：给我时间
             */
            try {
                //30s是因为心跳有效期是30s
                withTimeout(30*1000){
                    while (responseCode != 0 && isActive) {
                        val response = client.hangUpRealTimeCall(request)
                        responseCode = response.code
                        logInfo(TAG, "hangUpRTC: code = ${response.code}", logLine = LogLine.START_RTCAll)
                        //融合一下断网？
                        kotlinx.coroutines.delay(500)
                    }
                }
            } catch (e: Exception) {
                logError(TAG,e)
            }

        }
    }

    /**
     * @param exception: 1:timeout
     * 目前没有其他场景，先不创建枚举
     */
    fun hangUpByException(channelId: Long, exception: Int) {
        VoiceCallPortal.voiceCallScope.launchIO {
            _hangupByException.emit(exception)
            logInfo(TAG, "hangupByException:channelId = $channelId")
            val request = RequestHangUpRealTimeCall(channelId, 1)
            val response = client.hangUpRealTimeCall(request)
            logInfo(TAG, "hangupByException: code = ${response.code}", logLine = LogLine.START_RTCAll)
            _hangUpResult.emit(response)
        }

    }

    fun fetchRTCRoomUserList(channelId: Long, from: String) = launchIO {
        logInfo(TAG, "fetchRTCRoomUserList:channelId = $channelId from = $from")
        val request = RequestGetRealTimeCallUserList(channelId)
        val response = client.getRealTimeCallUserList(request)
        logInfo(TAG, "fetchRTCRoomUserLists:code = ${response.code}", logLine = LogLine.START_RTCAll)
        _fetchUserListResult.emit(response)

    }

    fun inviteJoinRoom(userList: List<Long>, channelId: Long) = launchIO {
        logInfo(TAG, "inviteJoinRoom:channelId = $channelId  userList = $userList")
        val request = RequestInviteRealTimeCall(userList, channelId)
        val response = client.inviteRealTimeCall(request)
        logInfo(TAG,"inviteJoinRoom,code = ${response.code}", logLine = LogLine.START_RTCAll)
        _inviteJoinRoomResult.emit(response)
    }
    suspend fun inviteJoinRoomSuspend(userList: List<Long>, channelId: Long): ITResponse<ResponseInviteRealTimeCall> {
        logInfo(TAG, "inviteJoinRoom:channelId = $channelId  userList = $userList")
        val request = RequestInviteRealTimeCall(userList, channelId)
        val response = client.inviteRealTimeCall(request)
        _inviteJoinRoomResult.emit(response)
     return response
    }

    fun changeMicStatus(channelId: Long, micStatus: @MicStatus Int) = launchIO {
        logInfo(TAG, "changeMicStatus:channelId = $channelId, micStatus = $micStatus")
        DoreRTCEnginManager.changeMicStatus(viewModelScope, micStatus, channelId)
        val request = RequestSetMicrophoneStatus(channelId, micStatus, NtpTime.nowForce())
        val retryStrategy = object : IntervalStrategy {
            override fun interval(time: Int): Long {
                return 2 * 1000L
            }

            override fun reset() {}
        }

        val retryAction = RetryAction {
            if (it > retryTime) return@RetryAction true
            logInfo(TAG, "changeMicStatus: start")
            val response = client.setMicrophoneStatus(request)
            _changeMicStateResult.emit(response)
            logInfo(TAG, "changeMicStatus: response = ${response.code} ")
            return@RetryAction response.isSuccess || response.code == CODE_NO_REAL_TIME_CALL
        }
        changeMicStatusRetryManager.retryImmediately(retryStrategy, retryAction)
    }

    fun changeCamStatus(channelId: Long, cameraStatus: @CameraStatus Int) = launchIO {
        logInfo(TAG, "setCameraStatus:channelId = $channelId, cameraStatus = $cameraStatus")
        val request = RequestSetCameraStatus(channelId, cameraStatus, NtpTime.nowForce())
        val retryStrategy = object : IntervalStrategy {
            override fun interval(time: Int): Long {
                return 2 * 1000L
            }
            override fun reset() {}
        }

        val retryAction = RetryAction {
            if (it > retryTime) return@RetryAction true
            logInfo(TAG, "setCameraStatus: start")
            val response = client.setCameraStatus(request)
            _changeCameraStatusResult.emit(response)
            logInfo(
                TAG,
                "setCameraStatus: response = ${response.code} cameraStatus = $cameraStatus",
                logLine = LogLine.START_RTCAll + LogLine.MY_RTC_CAMERA_STATE
            )
            return@RetryAction response.isSuccess || response.code == CODE_NO_REAL_TIME_CALL
        }
        changeCamStatusRetryManager.retryImmediately(retryStrategy, retryAction)
    }

    fun getDeviceRoutes(openSelector: Boolean = false) {
        launchIO {
            val deviceRouters = DoreRTCEnginManager.getDeviceRoutes()
            logInfo(
                TAG,
                "getDeviceRoutes,${deviceRouters?.joinToString { it.getName() }}, openSelector = $openSelector",
                logLine = LogLine.RTC_CALL
            )
            _audioDeviceRouters.emit(deviceRouters)
            if (openSelector) {
                showVoiceDeviceSelectorFlow.emit(deviceRouters)
            }
        }
    }

    private var switchableRegainJob: Job? = null

    fun switchAudioDevice(routing: Int) {
        logInfo(
            TAG,
            "switchAudioDevice, routing = ${AudioDevice.fromRouting(routing)}, type = $routing",
            logLine = LogLine.RTC_CALL
        )
        /*if (audioManager != null){
            audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
        }*/
        launch {
            switchableRegainJob?.cancel()
            deviceSwitchMutex.withLock {
                val switchResult = DoreRTCEnginManager.switchCallRouter(routing)
                logInfo(
                    TAG,
                    "switchAudioDevice result = $switchResult, isSuccess = ${switchResult >= 0}",
                    logLine = LogLine.RTC_CALL
                )
                routerServices<RealTimeCallService>().value?.onClickDeviceSwitchEvent(routing)
                if (switchResult >= 0) {
                    val audioDevice = AudioDevice.fromRouting(routing)
                    audioDeviceSwitchable.emit(false)
                    //因蓝牙的切换连接时间较长，针对蓝牙不做切换的假成功
                    if (audioDevice != AUDIO_ROUTE_BLUETOOTH_DEVICE) {
                        onAudioDeviceChangeFlow.emit(audioDevice)
                    }
                }
            }
            switchableRegainJob = launchDelay(3000) {
                if (!audioDeviceSwitchable.value) {
                    launch {
                        audioDeviceSwitchable.emit(true)
                    }
                }
            }
        }
    }

    fun onLIEAudioDeviceChange(routing: Int) {
        launch {
            deviceSwitchMutex.withLock {
                logInfo(
                    TAG,
                    "onLIEAudioDeviceChange, routing = ${AudioDevice.fromRouting(routing)}, type = $routing",
                    logLine = LogLine.RTC_CALL
                )
                val deviceRouters = DoreRTCEnginManager.getDeviceRoutes()
                logInfo(TAG, "[device] getDeviceRoutes,${deviceRouters?.joinToString { it.getName()}}",
                    logLine = LogLine.RTC_CALL)
                _audioDeviceRouters.emit(deviceRouters)
                onAudioDeviceChangeFlow.emit(AudioDevice.fromRouting(routing))
                audioDeviceSwitchable.emit(true)
            }
        }
    }
}