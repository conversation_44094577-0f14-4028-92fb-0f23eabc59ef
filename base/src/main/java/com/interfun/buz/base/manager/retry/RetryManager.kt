package com.interfun.buz.base.manager.retry

import com.interfun.buz.base.ktx.doInMainThread
import com.interfun.buz.base.manager.retry.impl.FibonacciInterval
import kotlinx.coroutines.*
import kotlin.coroutines.coroutineContext

class RetryManager {
    private var time = 0
    @Volatile
    private var isCanceled = false

    /**
     * @param block see[RetryAction.invoke]
     * @param intervalStrategy retry interval
     */
    suspend fun retry(
        intervalStrategy: IntervalStrategy = FibonacciInterval(),
        block: RetryAction
    ) {
        time = 0
        do {
            delay(intervalStrategy.interval(time + 1))
        } while (isCanceled.not() && coroutineContext.isActive && !block.invoke(++time))
    }

    suspend fun retryImmediately(
        intervalStrategy: IntervalStrategy = FibonacciInterval(),
        block: RetryAction
    ) {
        time = 0
        while (isCanceled.not() && coroutineContext.isActive && !block.invoke(++time)){
            delay(intervalStrategy.interval(time))
        }
    }

    /**
     * @see retry
     */
    fun retryAsync(
        scope: CoroutineScope = GlobalScope,
        intervalStrategy: IntervalStrategy = FibonacciInterval(),
        resetTrigger: ResetTrigger? = null,
        block: RetryAction
    ) {
        val resetHandler = object : ResetHandler {
            @Volatile
            var innerJob: Job? = null

            @Volatile
            var onCompletionHandler: DisposableHandle? = null

            override fun reset() {
                if (innerJob != null) {
                    onCompletionHandler?.dispose()
                }
                innerJob?.cancel()
                intervalStrategy.reset()
                if (scope.isActive.not()){
                    doInMainThread { resetTrigger?.onStop() }
                    return
                }
                innerJob = scope.launch {
                    retry(intervalStrategy, block)
                    if (isActive) {
                        doInMainThread { resetTrigger?.onStop() }
                    }
                }
                onCompletionHandler = innerJob?.invokeOnCompletion {
                    doInMainThread { resetTrigger?.onStop() }
                }
            }
        }
        resetHandler.reset()
        doInMainThread {
            resetTrigger?.onStart(resetHandler)
        }
    }

    fun cancel() {
        isCanceled = true
    }
}