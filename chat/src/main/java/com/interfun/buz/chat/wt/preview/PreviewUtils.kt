package com.interfun.buz.chat.wt.preview

import com.interfun.buz.base.ktx.asString
import com.interfun.buz.chat.R
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.*
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.im.ktx.getConversationId
import com.lizhi.im5.sdk.message.IMessage
import okhttp3.internal.toLongOrDefault

object PreviewUtils {
    fun createUnSupportMsgPreview(msg: IMessage) : UnSupportMsgPreview {
        return UnSupportMsgPreview(createBaseMsgInfo(msg))
    }

    fun createBaseMsgInfo(msg: IMessage): BaseMsgInfo {
        val fromId = msg.fromId.toLongOrDefault(0L)
        val sendFrom = if (msg.msgType == IMType.TYPE_COMMAND) {
            null
        } else if (fromId == UserSessionManager.uid) {
            R.string.you.asString()
        } else {
            val userInfo = UserRelationCacheManager.getUserRelationInfoFromCache(fromId)
            userInfo?.getContactFirstName() ?: msg.userInfo?.nickName
        }
        return BaseMsgInfo(
            convTargetId = msg.getConversationId().toLongOrDefault(0),
            convType = msg.conversationType,
            msgId = msg.msgId,
            fromUid = fromId,
            sendFrom = sendFrom,
            msgState = msg.status,
            time = msg.createTime,
            msgType = msg.msgType,
            serMsgId = msg.serMsgId ?: ""
        )
    }
}