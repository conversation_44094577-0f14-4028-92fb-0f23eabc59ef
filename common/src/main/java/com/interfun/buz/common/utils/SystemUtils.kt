package com.interfun.buz.common.utils

import android.app.KeyguardManager
import android.content.Context
import android.os.Build
import android.os.PowerManager
import android.webkit.WebSettings
import com.interfun.buz.base.ktx.logInfo

object SystemUtils {

    private var ua: String? = null

    fun getUa(context: Context): String? {
        try {
            if (ua == null) {
                ua = WebSettings.getDefaultUserAgent(context)
                logInfo("getUa:$ua")
            }
            return ua
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun isScreenLocked(context: Context): Boolean {
        val keyguardManager = context.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return keyguardManager.isDeviceLocked
        } else {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            return !powerManager.isScreenOn
        }
    }

    fun isScreenOff(context: Context): Bo<PERSON>an {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        return !powerManager.isInteractive
    }
}