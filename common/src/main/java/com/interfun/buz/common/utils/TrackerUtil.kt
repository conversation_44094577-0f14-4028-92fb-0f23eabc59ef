package com.interfun.buz.common.utils

import android.os.Bundle
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustEvent
import com.appsflyer.AppsFlyerLib
import com.google.firebase.analytics.ktx.ParametersBuilder
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import com.interfun.buz.base.ktx.appContext

val ADJUST_EVENT_MAP = HashMap<String,String>().apply {
    put("af_01_retain","a3jysr")
    put("af_03_retain","1q70bb")
    put("af_07_retain","ccbyyi")
    put("af_chat","ydz6c7")
    put("af_complete_registration","idp34c")
    put("af_group","612kh5")
    put("af_invite","6mloaq")
    put("af_login","47qn1c")
    put("af_permission","at5r4x")
    put("af_profile","u7lrm5")
    put("Day 1 Retention","hv5yd9")
    put("Launch_app","42rj9r")
    put("Re-register","fk1oqn")
    put("Retention-1","s8j1al")
    put("Retention-3","ylxcr9")
    put("Retention-7","370f2n")
    put("View","ngimx0")
}

/**
 * 将map数据转成firebase相关接口。需注意，该接口的类型有限制，详情看 ParametersBuilder的param方法
 * <note>
 * firebase不支持部分特殊字符，包括：'.', '#', '$', '[',  ']'，包含这部分字符的key上传的value
 * 会失败。checkKey方法并不完善，后续可以根据产品需求进行完善
 * <note/>
 */
fun Map<String,Any>.toFirebaseParams():ParametersBuilder.()->Unit{
    val block:ParametersBuilder.()->Unit = {
        <EMAIL> { (key, value) ->
            when (value) {
                is Long -> this.param(checkKey(key),value)
                is String -> this.param(checkKey(key),value)
                is Bundle -> this.param(checkKey(key),value)
                is Double -> this.param(checkKey(key),value)
            }
        }
    }
    return block
}

private fun checkKey(key:String):String{
    if (key.startsWith("$")){
        return key.replace("$","")
    }
    return key
}

fun getFireBaseParams(init:MutableMap<String, Any>.() -> Unit):ParametersBuilder.()->Unit{
    val map = mutableMapOf<String, Any>()
    init(map)
    return map.toFirebaseParams()
}

fun logFireBaseEvent(needLog: Boolean,event: String,init:MutableMap<String, Any>.() -> Unit){
    if (needLog){ Firebase.analytics.logEvent(event, getFireBaseParams(init)) }
}

fun logFireBaseEvent(needLog: Boolean,event: String,map:MutableMap<String, Any>){
    if (needLog){ Firebase.analytics.logEvent(event, map.toFirebaseParams()) }
}

fun logAFEvent(needLog: Boolean,event: String,init:MutableMap<String, Any>.() -> Unit){
    if (needLog){
        val map = mutableMapOf<String,Any>()
        init(map)
        AppsFlyerLib.getInstance().logEvent(appContext, event, map)
        ADJUST_EVENT_MAP[event]?.let {
            val adjustEvent = AdjustEvent(it)
            Adjust.trackEvent(adjustEvent)
        }
    }
}

fun logAFEvent(needLog: Boolean,event: String,map:MutableMap<String, Any>){
    if (needLog){ AppsFlyerLib.getInstance().logEvent(appContext, event, map) }
}

/**
 * 为AF和FireBase打点，init为null代表如果无需上传参数
 */
fun logAFAndFireBaseEvent(afEvent:String,fEvent:String,needLog: Boolean,init: (MutableMap<String, Any>.() -> Unit)? = null){
    val map = mutableMapOf<String,Any>()
    if (init != null) { init(map) }
    logFireBaseEvent(needLog,fEvent,map)
    logAFEvent(needLog,afEvent,map)
    ADJUST_EVENT_MAP[afEvent]?.let {
        val adjustEvent = AdjustEvent(it)
        Adjust.trackEvent(adjustEvent)
    }
}