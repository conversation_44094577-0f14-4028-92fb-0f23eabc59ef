<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    android:background="@color/overlay_grey_20">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivCover"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="8dp"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/tvFolderName"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="14dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/album_recents"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toTopOf="@+id/tvCount"
        app:layout_constraintEnd_toStartOf="@+id/iftvChoose"
        app:layout_constraintStart_toEndOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvCount"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="14dp"
        android:text="1234"
        android:textColor="@color/text_white_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivCover"
        app:layout_constraintTop_toBottomOf="@+id/tvFolderName" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvChoose"
        style="@style/iconfont_base"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="20dp"
        android:gravity="center"
        android:text="@string/ic_check"
        android:textColor="@color/basic_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>