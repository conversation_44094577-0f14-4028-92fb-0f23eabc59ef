package com.interfun.buz.chat.map.send.util

import android.graphics.Bitmap
import androidx.lifecycle.lifecycleScope
import coil.imageLoader
import coil.request.ImageRequest
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.Marker
import com.google.android.gms.maps.model.MarkerOptions
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.launchMain
import com.interfun.buz.base.ktx.withIOContext
import com.interfun.buz.chat.R
import com.interfun.buz.chat.map.send.DataEvent
import com.interfun.buz.chat.map.send.model.BuzLocation
import com.interfun.buz.chat.map.send.model.convertMap
import com.interfun.buz.chat.map.send.view.cutom.listitem.SearchPoiBean
import com.interfun.buz.common.base.BaseFragment
import com.interfun.buz.common.base.BaseManualBlock
import com.interfun.buz.common.constants.MapLocationHelper
import com.interfun.buz.common.ktx.toBitmapSafe
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive

data class CacheMark(val searchPoiBean: SearchPoiBean, val mark: Marker)
class SearchEventHelper(
    val fragment: BaseFragment,
) : BaseManualBlock() {

    private val searchPoiZIndex = 1f
    private val searchCenterZIndex = searchPoiZIndex + 1f
    private val searchSelectPoiZIndex = searchCenterZIndex + 1f
    private val cachePoiMarkerMap = mutableMapOf<SearchPoiBean, Marker>()
    private var cacheSelectMarker: CacheMark? = null

    private fun cleanMarkRes(map: GoogleMap?) {
        map?.clear()
        cacheSelectMarker = null
        cachePoiMarkerMap.clear()
    }

    fun detectEventChange(map: GoogleMap?, cur: DataEvent, before: DataEvent?) {
        //exit search
        if (before is DataEvent.SearchFlag && cur !is DataEvent.SearchFlag) {
            cleanMarkRes(map)
        }
        if (before !is DataEvent.SearchFlag && cur is DataEvent.SearchFlag) {
            cleanMarkRes(map)
        }
        cancelRequestMark()
    }

    fun addMapMark(
        map: GoogleMap,
        bitmap: Bitmap,
        buzLocation: BuzLocation,
        zIndex: Float
    ): Marker? {
        return map.addMarker(
            MarkerOptions()
                .position(buzLocation.convertMap())
                .icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                .zIndex(zIndex)
        )
    }

    private fun addSearchPoiMark(map: GoogleMap, list: List<SearchPoiBean>) {
        val newCachePoiMarkerMap = mutableMapOf<SearchPoiBean, Marker>()
        list.forEach { searchPoiBean ->
            val marker = addSearchPoiMark(map, searchPoiBean)
            if (marker != null) {
                newCachePoiMarkerMap[searchPoiBean] = marker

            }
        }
        for (oldMark in cachePoiMarkerMap) {
            if (!newCachePoiMarkerMap.containsKey(oldMark.key)) {
                oldMark.value.remove()
            }
        }
        //置换
        cachePoiMarkerMap.clear()
        cachePoiMarkerMap.putAll(newCachePoiMarkerMap)
    }

    private var selectItemMarkJob: Job? = null

    private fun addSearchPoiMark(map: GoogleMap, item: SearchPoiBean): Marker? {
        val cacheMarker = cachePoiMarkerMap[item]
        if (cacheMarker == null) {
            val createMapMark = MapLocationHelper.createPoiBitmap(
                16.dp,
                16.dp,
            )
            return addMapMark(map, createMapMark, item.buzLocation, searchPoiZIndex)
        } else {
            return cacheMarker
        }
    }

    private fun addSearchSelectPoiMark(map: GoogleMap, item: SearchPoiBean) {
        if (cacheSelectMarker?.searchPoiBean != item) {
            //先清空
            updateSelectPoiMarkCache(null, item)
            if (item.foregroundImgUrl.isNotEmpty()) {
                cancelRequestMark()

                selectItemMarkJob = this.fragment.lifecycleScope.launchMain {
                    val request = ImageRequest.Builder(fragment.requireContext())
                        .data(item.foregroundImgUrl)
                        .allowHardware(false) // Disable hardware bitmaps.
                        .build()
                    val drawable =
                        withIOContext { fragment.requireContext().imageLoader.execute(request)?.drawable }
                    val bitmap = drawable?.toBitmapSafe()
                    val createMapMark = MapLocationHelper.createMapMark(
                        60.dp,
                        66.1.dp,
                        R.drawable.chat_map_center_anchor,
                        R.drawable.chat_ico_map_pin,
                        bitmap,
                        true
                    )
                    if (!isActive) {
                        return@launchMain
                    }
                    val marker =
                        addMapMark(map, createMapMark, item.buzLocation, searchSelectPoiZIndex)
                    updateSelectPoiMarkCache(marker, item)
                }
            } else {
                val createMapMark = MapLocationHelper.createMapMark(
                    60.dp,
                    66.1.dp,
                    R.drawable.chat_map_center_anchor,
                    R.drawable.chat_ico_map_pin,
                    null,
                    true
                )
                val marker = addMapMark(map, createMapMark, item.buzLocation, searchSelectPoiZIndex)
                updateSelectPoiMarkCache(marker, item)
            }
        }
    }

    private fun updateSelectPoiMarkCache(
        marker: Marker?,
        item: SearchPoiBean
    ) {
        cacheSelectMarker?.mark?.remove()
        cacheSelectMarker = null
        if (marker != null) {
            cacheSelectMarker = CacheMark(item, marker)
        }
    }

    private fun addSearchCenterMark(map: GoogleMap, loc: BuzLocation) {
        map.addMarker(
            MarkerOptions()
                .position(loc.convertMap())
                .icon(BitmapDescriptorFactory.fromResource(R.drawable.chat_icon_map_location_search_center))
                .zIndex(searchCenterZIndex)
        )
    }

    fun updateSearchMark(
        map: GoogleMap?,
        select: SearchPoiBean? = null,
        searchBuzLocation: BuzLocation,
        allSearchList: List<SearchPoiBean>
    ) {
        if (map == null) {
            return
        }
        addSearchCenterMark(map, searchBuzLocation)
        if (select != null) {
            addSearchPoiMark(map, allSearchList.filter { it != select })
            addSearchSelectPoiMark(map, select)
        } else {
            addSearchPoiMark(map, allSearchList)
        }
    }

    private fun cancelRequestMark() {
        selectItemMarkJob?.cancel()
        selectItemMarkJob = null
    }
}