package com.interfun.buz.album.manager

import android.graphics.Bitmap
import com.interfun.buz.album.bean.MediaItem
import com.interfun.buz.album.bean.containsMediaItemByMediaId
import com.interfun.buz.album.bean.indexOfMediaItemByMediaId
import com.interfun.buz.album.bean.removeMediaItemByMediaId
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.manager.TempImageManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 相册数据管理层
 * <AUTHOR>
 * @date 2024/4/2
 */

class AlbumManager private constructor() {

    companion object {
        //选择状态
        const val INVALID = -1
        const val ADD_SUCCESS = 0
        const val REMOVE = 1
        //选择最大张数
        const val MAX_SELECTED_COUNT = 10
        private const val TAG = "AlbumManager"

        @JvmStatic
        val instance: AlbumManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            AlbumManager()
        }
    }

    //已选择媒体队列
    private var hasChooseList: MutableList<MediaItem> = mutableListOf()
    //当前媒体列表队列
    private var curAllMediaItemList: MutableList<MediaItem> = mutableListOf()
    //是否选择原图发送
    private var selectedHD = false
        set(value) {
            field = value
            _selectedHDStateFlow.value = value
        }
    private var numberOfVideo: Int = 0
    // 缓存相册/预览 选中的视频封面，用于发送（发送视频的时候如果没有缓存，则通过视频去取首帧图片）
    private val hasChooseVideoCoverHM = ConcurrentHashMap<Long, String>()
    private val saveCoverMutex = Mutex()

    private val _selectedHDStateFlow = MutableStateFlow(selectedHD)
    val selectedHDStateFlow = _selectedHDStateFlow.asStateFlow()

    fun setHDSelect(isSelected: Boolean){
        logInfo(TAG, "setHDSelect = $isSelected")
        selectedHD = isSelected
    }

    fun isHDSelected():Boolean{
        return selectedHD
    }

    fun clearAllMediaItemList() {
        curAllMediaItemList.clear()
    }

    fun addMediaItemList(item: MediaItem) {
        curAllMediaItemList.add(item)
    }

    fun getAllMediaItemList(): List<MediaItem> {
        return curAllMediaItemList
    }


    fun addSelectMediaList(item: MediaItem) {
        if (hasChooseList.containsMediaItemByMediaId(item).not()){
            hasChooseList.add(item)
            if (item.isVideo) numberOfVideo++
        }
    }

    fun removeSelectMediaList(item: MediaItem) {
        val result = hasChooseList.removeMediaItemByMediaId(item)
        if (result && item.isVideo){
            numberOfVideo--
        }
    }

    fun getCurrentSelectMediaList(): List<MediaItem> {
        return hasChooseList
    }

    fun exceedSelectedSize(): Boolean {
        return hasChooseList.size >= MAX_SELECTED_COUNT
    }

    fun getIndexOfMediaList(item: MediaItem): Int {
        val index = hasChooseList.indexOfMediaItemByMediaId(item)
        if (index == -1) {
            return hasChooseList.size + 1
        }
        return index + 1
    }

    /** [PhotoSelectFragment] 销毁时清理资源
     * 这里有一个坑点，PhotoSelectFragment可能会同时存在多个实例页面，所以其中一个被销毁，清除了单例的数据会对另一个PhotoSelectFragment里的数据造成影响
     * [https://project.larksuite.com/yewutest/issue/detail/2124528]
     */
    fun release() {
        curAllMediaItemList.clear()
        clearSelectMediaList()
    }

    fun clearSelectMediaList() {
        logDebug(TAG, "clearSelectMediaList")
        numberOfVideo = 0
        hasChooseList.clear()
        selectedHD = false
    }

    /**
     * Return number of videos in selected media list
     */
    fun getNumberOfVideoInSelectMediaList(): Int {
        return numberOfVideo
    }

    suspend fun getVideoItemBitmapCache(mediaId: Long): String? {
        hasChooseVideoCoverHM[mediaId]?.let { path ->
            val result = withContext(Dispatchers.IO) {
                val file = File(path)
                if (file.exists()) {
                    path
                } else {
                    null
                }
            }
            logInfo(TAG, "getVideoItemBitmapCache," +
                    " cache size ${hasChooseVideoCoverHM.size}," +
                    " mediaId= $mediaId," +
                    " path= ${result}")
            return result
        }
        return null
    }
    suspend fun addVideoItemBitmapCache(mediaId: Long, bitmap:Bitmap?) {
        if (bitmap == null || bitmap.width == 0 || bitmap.height == 0 || bitmap.isRecycled) {
            logInfo(TAG, "addVideoItemBitmapCache mediaId = $mediaId bitmap is null")
            return
        }
        saveCoverMutex.withLock {
            bitmap?.let {
               TempImageManager.saveBitmapInCache(it)?.let { path ->
                   hasChooseVideoCoverHM[mediaId] = path
                   logInfo(TAG, "addVideoItemBitmapCache, " +
                           "cache size ${hasChooseVideoCoverHM.size}" +
                           " mediaId = ${mediaId}," +
                           " path=$path")
               }
           }
       }
    }

    fun hasCacheVideoItemBitmap(mediaId: Long): Boolean {
        return hasChooseVideoCoverHM[mediaId]?.isNotNull() == true
    }


    suspend fun removeVideoItemBitmapCache(mediaId: Long) {
        logInfo(TAG, "removeVideoItemBitmapCache, mediaId = $mediaId")
        hasChooseVideoCoverHM[mediaId]?.let {
            hasChooseVideoCoverHM.remove(mediaId)
            saveCoverMutex.withLock {
                logInfo(TAG, "removeVideoItemBitmapCache, mediaId = $mediaId deleteFile")
                TempImageManager.deleteFile(it)
            }
        }
    }

    fun removeVideoItemBitmapCacheOnlyPath(mediaId: Long) {
        hasChooseVideoCoverHM.remove(mediaId)
    }

    suspend fun clearAllVideoItemBitmapCache() {
        logInfo(TAG, "clearAllVideoItemBitmapCache")
        saveCoverMutex.withLock {
            hasChooseVideoCoverHM.forEach{(key, value) ->
                TempImageManager.deleteFile(value)
            }
            hasChooseVideoCoverHM.clear()
        }
    }

}