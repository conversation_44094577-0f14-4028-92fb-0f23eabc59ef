package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.*
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asPainter
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData

/**
 * <AUTHOR>
 * @date 2025/6/12
 * @desc
 */
@Composable
internal fun RecordButtonBg(
    isShowAIBg: () -> <PERSON><PERSON>an,
    isRecording: () -> <PERSON><PERSON><PERSON>,
    isInActionBtnArea: () -> <PERSON>olean,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    currentVoiceFilter: () -> VoiceFilterUiData?,
    modifier: Modifier = Modifier,
) {
    SideEffect {
        RecomposeCountHelper.increment("RecordButtonBg")
    }
    val voiceFilter = currentVoiceFilter()
    val operateStatus = recordOperateStatusLambda()

    val isShowOriginalBg =
        operateStatus.isEnable
            && !isRecording()
            && !operateStatus.isLocking
            && !isInActionBtnArea()
            && voiceFilter == null
    if (isShowOriginalBg) {
        AnimatedContent(
            modifier = modifier,
            targetState = isShowAIBg(),
            transitionSpec = { ContentTransform(fadeIn(), fadeOut()) }
        ) { isAI ->
            if (isAI) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = R.drawable.bg_record_btn_ai.asPainter(),
                    contentDescription = null
                )
            } else {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = R.drawable.bg_record_btn_default.asPainter(),
                    contentDescription = null
                )
            }
        }
    } else {
        if (operateStatus.isLocking) {
            Image(
                modifier = modifier,
                painter = R.drawable.bg_record_btn_locking.asPainter(),
                contentDescription = null
            )
        } else {
            val bgColor by animateColorAsState(
                targetValue = if (!operateStatus.isEnable || isInActionBtnArea()) {
                    R.color.color_background_6_default.asColor()
                } else {
                    if (voiceFilter != null) {
                        voiceFilter.backgroundColor
                    } else {
                        if (isRecording()) {
                            R.color.color_background_highlight_1_default.asColor()
                        } else {
                            R.color.overlay_grey_26.asColor()
                        }
                    }
                }
            )
            Box(
                modifier = modifier
                    .clip(CircleShape)
                    .background(color = bgColor)
            )
        }
    }
}