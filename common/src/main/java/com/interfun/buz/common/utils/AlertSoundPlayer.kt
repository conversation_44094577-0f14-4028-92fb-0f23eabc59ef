package com.interfun.buz.common.utils

import android.media.SoundPool
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.mainThreadHandler
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.manager.AlertSoundLoadRes
import com.interfun.buz.common.manager.AlertSoundManager
import com.interfun.buz.common.manager.LoadSound
import com.interfun.buz.common.manager.loadAlertRes

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/9/1
 * https://blog.csdn.net/lovewaterman/article/details/38021305
 */
class AlertSoundPlayer private constructor() {

    private var alertSoundPool: SoundPool? = null

    private var alertSoundMap: MutableMap<Int, AlertSoundLoadRes?> = mutableMapOf()
    private val playIntervals : Long = 500
    private var currentStreamId: Int? = null

    companion object {
        const val TYPE_RETRO = 0
        const val TYPE_POPS_UP = 1
        const val TYPE_BELL = 2

        private var alertSoundPlayer: AlertSoundPlayer? = null

        fun getInstance(): AlertSoundPlayer {
            return alertSoundPlayer ?: AlertSoundPlayer().also { alertSoundPlayer = it }
        }
    }

    fun play(type: Int) {
        if (alertSoundPlayer.isNull()) prepare()
        currentStreamId?.let {
            alertSoundPool?.stop(it)
            currentStreamId = null
            mainThreadHandler.removeCallbacksAndMessages(null)
        }
        if (alertSoundMap.containsKey(type)) {
            alertSoundMap[type]?.let {
                playSound(it.loadStartSound)
                mainThreadHandler.postDelayed({
                    playSound(it.loadEndSound)
                }, playIntervals)
            }
        }
    }

    private fun playSound(sound: LoadSound) {
        currentStreamId = alertSoundPool?.play(sound.value, 1f, 1f, 0, 0, 1f).getIntDefault()
    }

    fun prepare() {
        alertSoundPool = SoundPool.Builder().setMaxStreams(2).build()
        alertSoundMap.apply {
            AlertSoundManager.alertSoundType.forEach { (key, value) ->
                this[key] = alertSoundPool?.loadAlertRes(value)
            }
        }
    }

    fun release() {
        alertSoundPool?.release()
        alertSoundPool = null
    }
}