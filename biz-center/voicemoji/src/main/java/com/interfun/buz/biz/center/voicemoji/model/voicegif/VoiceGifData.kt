package com.interfun.buz.biz.center.voicemoji.model.voicegif

import android.os.Parcelable
import kotlinx.parcelize.Parcelize


/**
 * Author: ChenYouSheng
 * Date: 2024/12/18
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 语音gif
 */
data class VoiceGifData(val voiceGifList: List<VoiceGifEntity>, val subTabId: Long)

/**
 * 最近使用的语音gif
 * 注意：如果要新增字段，记得要在[VoiceGifUsageMMKV]中的voiceGifUsageList#deserializer做解析处理
 * */
@Parcelize
data class VoiceGifUsage(
    val voiceGifEntity: VoiceGifEntity,
    var frequency: Int,
    var lastUsedTimestamp: Long
) : Parcelable