package com.interfun.buz.biz.center.voicemoji.model.blindbox

import com.buz.idl.voicemoji.bean.VoicemojiBlindBox
import com.buz.idl.voicemoji.response.ResponseGetBlindBoxes
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.convert
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.ktx.getStringDefault

// http://buz.pageweb.io/buz-doc/buz%E5%8D%8F%E8%AE%AE/IDL/service/voicemoji/BuzNetVoicemojiService.html?h=getblindboxes
data class VoiceEmojiBlindData(
    val blindBoxData: List<VoiceEmojiBlindBox>?, // 盲盒列表
    val category: VoiceEmojiCategory?, // 表情分类
    val status: BlindBoxTipsStatus?, // 盲盒的提示状态
    val tips: String?, // 当前状态提示文案
    val startedDays: Int, //  激活任务天数
    val helpPageUrl: String?, // 帮助页链接
    val delayRefreshMills: Long? // 延迟刷新时间
) {
    override fun toString(): String {
        return "{category:${category},status:${status},tips:${tips}," +
                "startedDays:${startedDays},helpPageUrl:${helpPageUrl},delayRefreshMills:${delayRefreshMills}," +
                "blindBoxData.size:${blindBoxData?.size}}"
    }
}

/**
 * status=-1时，盲盒不可点击，也不能长按
 * status=0时，盲盒可点击，点击跳去预览开盒子的动画
 * status=1时，盲盒点击就可以发送，长按可以预览emoji动画
 */
data class VoiceEmojiBlindBox(
    val index: Int = 0, // 盲盒在列表位置(开盲盒会用到，接口不返回）
    val status: Int, // 状态：-1未解锁，0-已解锁，1-已领取
    val unlockAnimationUrl: String, // 可领取状态动画
    val lockIconUrl: String?, // 不可领取静态图
    val openAnimationUrl: String?, // 领取动画
    val voiceEmojiEntity: VoiceEmojiEntity?, // 盲盒中的表情,仅status=1是有值，其他情况为null
    val blindBoxType: Int?
) {
    var delayPlayTime: Long = 0 // 延迟播放[unlockAnimationUrl]的时间 (接口不返回,用于盲盒在列表播放动画)
}

/**
 * -1- 不展示盲盒介绍弹窗
 * 0 - 盲盒数据不可用，需要屏蔽盲盒入口
 * 1 - 之前有领取过，现在可领取普通盲盒
 * 2 - 可领取最后一个普通盲盒
 * 3 - 可领取神秘款
 * 4 - 明天回来领取
 * 5 - 明天来领取隐藏款
 * 6 - 提醒邀请好友来补卡
 * 7 - 提醒邀请好友来补卡打开最后一个和隐藏款
 * 8 - 已完成
 */
sealed class BlindBoxTipsStatus(val status: Int) {
    data object CannotShowBlindBoxDialog : BlindBoxTipsStatus(-1)
    data object Unavailable : BlindBoxTipsStatus(0)
    data object NormalCollectTip : BlindBoxTipsStatus(1)
    data object NormalCollectLastTip : BlindBoxTipsStatus(2)
    data object SecretCollectTip : BlindBoxTipsStatus(3)
    data object NextDayNormalCollectTip : BlindBoxTipsStatus(4)
    data object NextDaySecretCollectTip : BlindBoxTipsStatus(5)
    data object InviteFriendNormalCollectTip : BlindBoxTipsStatus(6)
    data object InviteFriendSecretCollectTip : BlindBoxTipsStatus(7)
    data object NoTips : BlindBoxTipsStatus(-2)

    companion object {
        fun createTips(status: Int?): BlindBoxTipsStatus {
            return when (status) {
                -1 -> CannotShowBlindBoxDialog
                0 -> Unavailable
                1 -> NormalCollectTip
                2 -> NormalCollectLastTip
                3 -> SecretCollectTip
                4 -> NextDayNormalCollectTip
                5 -> NextDaySecretCollectTip
                6 -> InviteFriendNormalCollectTip
                7 -> InviteFriendSecretCollectTip
                else -> NoTips
            }
        }
    }

    /**
     * 是否有新的盲盒可以打开
     */
    fun hasNewBlindBox(): Boolean {
        return status in 1..3
    }
}

/** 请求类型：1-初始化请求；2-普通请求 */
sealed class RequestBlindType(val type: Int) {
    data object Init : RequestBlindType(1)
    data object Normal : RequestBlindType(2)
}

/** 盲盒类型： 1-普通盲盒， 2-隐藏盲盒 */
sealed class BlindBoxType(val type: Int) {
    data object NormalBox : BlindBoxType(1)
    data object SecretBox : BlindBoxType(2)
}

fun ResponseGetBlindBoxes.convert(): VoiceEmojiBlindData {
    val category = category?.let {
        VoiceEmojiCategory(
            name = it.category.getStringDefault(),
            categoryIconUrl = it.categoryIconUrl,
            type = it.categoryType.getIntDefault(),
            style = it.style
        )
    }
    return VoiceEmojiBlindData(
        blindBoxData = blindBoxes?.mapIndexed { index, it ->
            it.run {
                VoiceEmojiBlindBox(
                    index = index,
                    status = status,
                    unlockAnimationUrl = unlockAnimationUrl.getStringDefault(),
                    lockIconUrl = lockIconUrl,
                    openAnimationUrl = openAnimationUrl,
                    voiceEmojiEntity = category?.let { category -> voicemoji?.convert(category) },
                    blindBoxType = blindBoxType
                )
            }
        }?.also {
            val list = it.filter { it.status == 0 }
            list.withIndex().forEach { (index, item) ->
                item.delayPlayTime = 180L * index
            }
        },
        category = category,
        status = status?.let { BlindBoxTipsStatus.createTips(it) },
        tips = tips,
        startedDays = startedDays.getIntDefault(-1),
        helpPageUrl = helpPageUrl,
        delayRefreshMills = delayRefreshMills
    )
}

fun VoicemojiBlindBox.convert(index: Int, category: VoiceEmojiCategory): VoiceEmojiBlindBox {
    return VoiceEmojiBlindBox(
        index = index,
        status = status,
        unlockAnimationUrl = unlockAnimationUrl.getStringDefault(),
        lockIconUrl = lockIconUrl,
        openAnimationUrl = openAnimationUrl,
        voiceEmojiEntity = voicemoji?.convert(category),
        blindBoxType = blindBoxType
    )
}