package com.interfun.buz.base.utils

import android.content.Context
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.doInTryCatch
import com.interfun.buz.base.ktx.logDebug

/**
 * <AUTHOR>
 *
 * @date 2022/7/19
 *
 * @desc
 */
object VibratorUtil {

    private const val TAG = "VibratorUtil"
    private const val DEFAULT_MS = 5L
    private const val DEFAULT_AMPLITUDE = 100

    private var vibrator: Vibrator? = null

    /**
     * 默认的振动效果
     * @param amplitude 振动强度，范围应当是1-255，如果传null使用设备默认强度
     */
    fun vibrator(from: String? = null) {
        vibrator(appContext, DEFAULT_MS, DEFAULT_AMPLITUDE, from)
    }

    /**
     * 最大振动效果
     */
    fun vibratorMaxAmplitude(from: String? = null) {
        vibrator(appContext, DEFAULT_MS, 255, from)
    }

    /**
     * 触发振动
     * @param amplitude 振动强度，范围应当是1-255，如果传null使用设备默认强度
     */
    fun vibrator(
        context: Context = appContext,
        time: Long = DEFAULT_MS,
        amplitude: Int? = 100,
        from: String? = null
    ) {
        if (from != null) {
            logDebug(TAG, "vibrator from: $from")
        } else {
            logDebug(TAG, "vibrator")
        }
        doInTryCatch {
            if (VERSION.SDK_INT >= VERSION_CODES.O) {
                val vibe = VibrationEffect.createOneShot(time, amplitude ?: DEFAULT_AMPLITUDE)
                getVibrator(context)?.vibrate(vibe)
            } else {
                @Suppress("DEPRECATION")
                getVibrator(context)?.vibrate(time)
            }
        }
    }

    fun vibrator(context: Context = appContext, vibe: VibrationEffect) {
        doInTryCatch {
            if (VERSION.SDK_INT >= VERSION_CODES.O) {
                getVibrator(context)?.vibrate(vibe)
            } else {
                @Suppress("DEPRECATION")
                getVibrator(context)?.vibrate(DEFAULT_MS)
            }
        }
    }

    fun vibrateOnReceiveMsg(context: Context){
        logDebug(TAG, "vibrateOnReceiveMsg")
        val vibrator = getVibrator(context)
        if (vibrator?.hasVibrator() == true) {
            val customVibrationPattern = longArrayOf(0, 300, 150, 300) // 自定义的振动模式

            if (VERSION.SDK_INT >= VERSION_CODES.O) {
                val vibrationEffect = VibrationEffect.createWaveform(customVibrationPattern, -1)
                vibrator.vibrate(vibrationEffect)
            } else {
                vibrator.vibrate(customVibrationPattern, -1)
            }
        }
    }

    fun vibrator(
        context: Context = appContext,
        pattern: LongArray,
        repeat: Int,
        from: String? = null
    ) {
        if (from != null) {
            logDebug(TAG, "vibrator from: $from")
        } else {
            logDebug(TAG, "vibrator pattern")
        }
        doInTryCatch {
            if (VERSION.SDK_INT >= VERSION_CODES.O) {
                val vibe = VibrationEffect.createWaveform(pattern, repeat)
                getVibrator(context)?.vibrate(vibe)
            } else {
                @Suppress("DEPRECATION")
                getVibrator(context)?.vibrate(pattern, repeat)
            }
        }
    }

    fun cancel(context: Context = appContext) {
        logDebug(TAG, "cancel vibrator")
        doInTryCatch {
            getVibrator(context)?.cancel()
        }
    }

    private fun getVibrator(context: Context): Vibrator? {
        if (vibrator == null) {
            vibrator = if (VERSION.SDK_INT >= VERSION_CODES.S) {
                val vibratorManager =
                    context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            }
        }
        return vibrator
    }
}