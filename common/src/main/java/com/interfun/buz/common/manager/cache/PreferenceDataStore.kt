package com.interfun.buz.common.manager.cache

import android.content.Context
import androidx.datastore.core.DataMigration
import androidx.datastore.core.DataStore
import androidx.datastore.core.handlers.ReplaceFileCorruptionHandler
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStoreFile
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.userLifecycleScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.job
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

/**
 * 请不要使用这个了，改成用hilt注入到UserComponent中, 参考SocialUserModule.provideSocialDatastore
 */
@Deprecated(
    "Use hilt to inject a DataStore in UserComponent instead.Refer to SocialUserModule.provideSocialDatastore",
    ReplaceWith("")
)
fun userPreferencesDataStore(
    name: String,
    corruptionHandler: ReplaceFileCorruptionHandler<Preferences>? = null,
    produceMigrations: (Context) -> List<DataMigration<Preferences>> = { listOf() }
): ReadOnlyProperty<Context, DataStore<Preferences>> {
    return UserPreferenceDataStoreDelegate(name, corruptionHandler, produceMigrations)
}

private class UserPreferenceDataStoreDelegate(
    val name: String,
    val corruptionHandler: ReplaceFileCorruptionHandler<Preferences>? = null,
    val produceMigrations: (Context) -> List<DataMigration<Preferences>> = { listOf() }
) :
    ReadOnlyProperty<Context, DataStore<Preferences>> {

    @Volatile
    private var instance: DataStore<Preferences>? = null

    @Volatile
    private var currentInstanceUid: Long? = null

    override fun getValue(thisRef: Context, property: KProperty<*>): DataStore<Preferences> {
        val uid = UserSessionManager.uid
        val oldInstanceUid = currentInstanceUid
        val oldInstance = instance
        return if (uid == oldInstanceUid && oldInstance != null) {
            oldInstance
        } else {
            synchronized(UserPreferenceDataStoreDelegate::class.java) {
                if (currentInstanceUid == uid && instance != null) {
                    return instance!!
                }
                userLifecycleScope?.coroutineContext?.job?.invokeOnCompletion {
                    instance = null
                    currentInstanceUid = null
                }
                val userRelatedName = "$uid-$name"
                val applicationContext = thisRef.applicationContext
                val newInstance = PreferenceDataStoreFactory.create(
                    corruptionHandler = corruptionHandler,
                    migrations = produceMigrations(applicationContext),
                    scope = userLifecycleScope ?: CoroutineScope(Dispatchers.IO + SupervisorJob())
                ) {
                    applicationContext.preferencesDataStoreFile(userRelatedName)
                }
                instance = newInstance
                currentInstanceUid = uid
                newInstance
            }
        }
    }
}