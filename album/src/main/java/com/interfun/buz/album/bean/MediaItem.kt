package com.interfun.buz.album.bean

import android.net.Uri
import android.os.Parcelable
import com.interfun.buz.base.ktx.*
import com.interfun.buz.media.bean.BuzMediaItem
import com.interfun.buz.media.bean.MediaType
import com.luck.picture.lib.entity.LocalMedia
import kotlinx.parcelize.Parcelize

/**
 * 媒体实体
 * <AUTHOR>
 * @date 2024/4/3
 */
@Parcelize
open class MediaItem(
    override var mediaId: Long = 0,
    override var mediaUri: String = "",
    override var width: Int = 0,
    override var height: Int = 0,
    override var duration: Long = 0,
    override var type: MediaType = MediaType.Video
) : BuzMediaItem(
    mediaId, mediaUri, width, height, type = type, duration = duration
), Parcelable {
    var isVideo: Boolean = false

    //当前媒体在所有列表中的下标
    var index: Int = -1

    //当前媒体对应的相册文件夹
    var bucketId: Long = -1

    //选中顺序，未选中为-1
    var chooseNum = -1
    var hasChoose = false

    companion object {
        const val MIME_TYPE_PREFIX_VIDEO = "video"
        fun from(source: LocalMedia?): MediaItem = MediaItem().apply {
            var uriString:String? = null
            val usePath = when{
                !source?.realPath.isNullOrEmpty() -> source?.realPath
                source?.path?.isUriString() == true -> {
                    uriString = Uri.parse(source.path).getImageAbsolutePath(application)
                    uriString
                }
                else -> source?.path
            }

            source?.let {
                mediaId = it.id
                mediaUri = usePath?:""
                duration = it.duration
                bucketId = it.bucketId
                width = it.width
                height = it.height
                isVideo = it.mimeType?.startsWith(MIME_TYPE_PREFIX_VIDEO) == true
                type = if (isVideo) MediaType.Video else MediaType.Image
            }
        }
    }
}

fun MediaItem.copy(): MediaItem{
    val mediaItem =
        MediaItem(this.mediaId, this.mediaUri, this.width, this.height, this.duration, this.type)
    mediaItem.hasChoose = hasChoose
    mediaItem.isVideo = isVideo
    mediaItem.chooseNum = chooseNum
    mediaItem.bucketId = bucketId
    mediaItem.index = index
    mediaItem.remoteUrl = remoteUrl
    mediaItem.thumbnailUrl = thumbnailUrl
    mediaItem.hdCoverUrl = hdCoverUrl
    mediaItem.orientation = orientation
    return mediaItem
}

fun List<MediaItem>.containsMediaItemByMediaId(item: MediaItem): Boolean {
    return this.any { it.mediaId == item.mediaId }
}

fun List<MediaItem>.indexOfMediaItemByMediaId(item: MediaItem): Int {
    return this.indexOfFirst { it.mediaId == item.mediaId }
}

fun MutableList<MediaItem>.removeMediaItemByMediaId(item: MediaItem): Boolean {
    val index = this.indexOfFirst { it.mediaId == item.mediaId }
    if (index != -1) {
        this.removeAt(index)
        return true
    }
    return false
}

fun MediaItem.isDurationMoreThan(durationMs: Long): Boolean {
    if (isVideo) {
        return duration > durationMs
    }
    return false
}