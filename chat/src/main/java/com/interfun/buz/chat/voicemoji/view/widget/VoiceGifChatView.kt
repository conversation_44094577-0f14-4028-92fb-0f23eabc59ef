package com.interfun.buz.chat.voicemoji.view.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.MainThread
import androidx.constraintlayout.widget.ConstraintLayout
import com.interfun.buz.assertutil.buzAssertMain
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asDimension
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.invisible
import com.interfun.buz.base.ktx.layoutMargin
import com.interfun.buz.base.ktx.layoutSize
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.utils.ScreenUtil
import com.interfun.buz.base.utils.safe
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.view.item.IProgressSynchronizer
import com.interfun.buz.chat.databinding.ChatItemListVoiceGifBinding
import com.interfun.buz.common.ktx.setColor
import com.interfun.buz.common.widget.view.loading.BaseLoadingView.Companion.FILE_NAME_CIRCLE
import org.libpag.PAGFile
import kotlin.math.roundToInt

class VoiceGifChatView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val TAG = "VoiceGifChatView"

    companion object {
        private val gifMaxWidth = ScreenUtil.getScreenWidth() * 0.6
        private val gifMaxHeight = ScreenUtil.getScreenHeight() * 0.4
    }

    private var serMsgId: String? = null
    private var totalDuration: Float = 0f
    private var progressSynchronizer: IProgressSynchronizer? = null

    var voiceGifEntity: VoiceGifEntity? = null
        set(value) {
            buzAssertMain()
            field = value
        }

    fun updateViewSize(
        entity: VoiceGifEntity
    ) {

//        Chat页面Voicegif 的尺寸逻辑：
//        横向：最大宽度为屏宽的0.5
//        纵向：最大高度为屏高的0.5
        safe(entity.width, entity.height) { width, height ->
            if (width <= 0 || height <= 0) {
                return
            }
            var adjustWidth = gifMaxWidth
            var adjustHeight = gifMaxWidth / width * height
            if (adjustHeight > gifMaxHeight) {
                adjustHeight = gifMaxHeight
                adjustWidth = adjustHeight / height * width
            }
            binding.avVoiceGif.layoutSize(adjustWidth.roundToInt(), adjustHeight.roundToInt())
        }
    }

    fun getVoiceGifContentView() = binding.rfVoiceGif

    suspend fun updateVoiceGifEntity(
        entity: VoiceGifEntity,
        serMsgId: String?,
        totalDuration: Float,
        progressSynchronizer: IProgressSynchronizer
    ) {
        voiceGifEntity = entity
        this.serMsgId = serMsgId
        this.totalDuration = totalDuration
        this.progressSynchronizer = progressSynchronizer
        binding.loadingGroup.visible()
        entity.animationUrl?.let {
            binding.avVoiceGif.loadUrl(
                animationUrl = it,
                thumbnailUrl = entity.thumbnailUrl,
                onSuccess = {
                    binding.loadingGroup.gone()
                },
                onError = {},
                firstFrameProgress = {
                    progressSynchronizer.getProgress(serMsgId, totalDuration)
                }
            )
        }
    }

    fun clearGifData() {
        binding.avVoiceGif.clear()
    }

    var isRead: Boolean = false
        set(value) {
            buzAssertMain()
            field = value
            updateIsRead(value)
        }

    private var voiceEmojiMirrorEffect: Boolean = false
        set(value) {
            field = value
        }

    private val binding: ChatItemListVoiceGifBinding
    private val pagFile by lazy {
        PAGFile.Load(
            context.assets, "pag/pag_voicemoji_msg_playing.pag"
        )
    }
    private val loadingFile by lazy { PAGFile.Load(context.assets, FILE_NAME_CIRCLE) }
    private var initPageFlag = false

    init {

        val layoutInflater = LayoutInflater.from(context)
        //init attribute
        val attributeSet = context.obtainStyledAttributes(attrs, R.styleable.VoiceMojiView)
        voiceEmojiMirrorEffect = attributeSet.getBoolean(
            R.styleable.VoiceMojiView_voiceEmojiMirrorEffect, voiceEmojiMirrorEffect
        )
        attributeSet.recycle()

        //init view
        binding = ChatItemListVoiceGifBinding.inflate(layoutInflater, this, true)

        //切换左右镜像
        if (voiceEmojiMirrorEffect) {
            mirrorImageEffect()
        }

        val blR = if (voiceEmojiMirrorEffect) {
            if (isRtl) {
                R.dimen.chat_item_bg_radius_min.asDimension()
            } else {
                R.dimen.chat_item_bg_radius.asDimension()
            }
        } else {
            if (isRtl) {
                R.dimen.chat_item_bg_radius.asDimension()
            } else {
                R.dimen.chat_item_bg_radius_min.asDimension()
            }
        }
        val brR = if (blR == R.dimen.chat_item_bg_radius.asDimension()) {
            R.dimen.chat_item_bg_radius_min.asDimension()
        } else {
            R.dimen.chat_item_bg_radius.asDimension()
        }

        binding.rfVoiceGif.setRadius(
            blR = blR,
            brR = brR
        )
    }

    private fun mirrorImageEffect() {
        val vmtLp = binding.rfVoiceGif.layoutParams as LayoutParams
        vmtLp.endToEnd = LayoutParams.PARENT_ID
        vmtLp.startToStart = LayoutParams.UNSET

        val smLp = binding.cpiDownloadProgress.layoutParams as LayoutParams
        smLp.endToEnd = LayoutParams.UNSET
        smLp.endToStart = binding.rfVoiceGif.id

        val mcLp = binding.bigCircle.layoutParams as LayoutParams
        mcLp.startToEnd = LayoutParams.UNSET
        mcLp.endToStart = binding.rfVoiceGif.id

        val smcLp = binding.middleCircle.layoutParams as LayoutParams
        smcLp.startToStart = LayoutParams.UNSET
        smcLp.endToEnd = binding.bigCircle.id

        binding.bigCircle.layoutMargin(start = 0.dp, end = 4.dp)
    }

    @MainThread
    private fun ensurePagInit() {
        if (!initPageFlag) {
            initPag()
        }
    }

    @MainThread
    private fun initPag() {
        if (initPageFlag) {
            return
        }
        initPageFlag = true
        pagFile.apply {
            val pagColor = R.color.basic_primary.asColor()
            setColor(pagColor)
        }
        loadingFile.apply {
            val pagColor = R.color.color_text_white_secondary.asColor()
            setColor(pagColor)
        }
        binding.pagPlaying.setRepeatCount(0)
    }

    @MainThread
    fun changePlayingState() {
        buzAssertMain()
        ensurePagInit()
        binding.pagPlaying.composition = pagFile
        binding.iftvVoiceMojiLogo.invisible()
        binding.pagPlaying.visible()
        binding.pagPlaying.progress = 0.0
        binding.pagPlaying.play()
        binding.avVoiceGif.jumpFrame(progressSynchronizer?.getProgress(serMsgId, totalDuration)?:0f)
    }

    @MainThread
    fun changeLoadingState() {
        buzAssertMain()
        ensurePagInit()
        binding.pagPlaying.composition = loadingFile
        binding.iftvVoiceMojiLogo.invisible()
        binding.pagPlaying.visible()
        binding.pagPlaying.progress = 0.0
        binding.pagPlaying.play()
    }

    @MainThread
    fun changeNormalState() {
        buzAssertMain()
        binding.iftvVoiceMojiLogo.visible()
        binding.pagPlaying.stop()
        binding.pagPlaying.invisible()
    }

    fun updateIsRead(read: Boolean) {
        if (read) {
            binding.iftvVoiceMojiLogo.setImageResource(R.drawable.chat_ic_ve_read)
        } else {
            binding.iftvVoiceMojiLogo.setImageResource(R.drawable.chat_ic_ve_unread)
        }
    }

    fun stopAnim() {
        binding.avVoiceGif.stopAnim()
    }

    fun startAnim() {
        binding.avVoiceGif.startAnim()
    }

    fun jumpFrame(currentProgress:Float) {
        binding.avVoiceGif.jumpFrame(currentProgress)
    }
}