@file:Suppress("unused")

package com.interfun.buz.base.ktx

inline fun doInTryCatch(
    tag: String? = null,
    onlyLogErrorMsg: Boolean = false,
    crossinline action: () -> Unit
) {
    try {
        action()
    } catch (e: Exception) {
        if (tag.isNullOrEmpty()) {
            if (onlyLogErrorMsg.not()) {
                logError(e)
            } else {
                logError(e.message)
            }
        } else {
            if (onlyLogErrorMsg.not()) {
                logError(tag, e)
            } else {
                logError(tag, e.message)
            }
        }
    }
}