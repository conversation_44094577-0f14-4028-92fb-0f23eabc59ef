package com.interfun.buz.domain.chat.repo

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface MorePanelRepository {
    val isOptionListEmpty: StateFlow<Boolean>
    val voiceFilterReminderFlow: StateFlow<Boolean>
    val locationReminderFlow: StateFlow<Boolean>
    val showMoreButtonRedDotStateFlow: StateFlow<Boolean>
    fun groupMemberChangeFlow(): Flow<Long>
    suspend fun getOptionsList(isGroup: <PERSON>olean, targetId: Long): List<MorePanelOption>
    suspend fun updateVFRedDotStatus()
}