<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/overlay_grey_10"
    android:paddingBottom="14dp"
    app:round_top_left_radius="@dimen/bottom_sheet_dialog_top_radius"
    app:round_top_right_radius="@dimen/bottom_sheet_dialog_top_radius"
    tools:ignore="MissingConstraints"
    tools:layout_gravity="bottom">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideStart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="40dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="40dp" />

    <Space
        android:id="@+id/dragSpace"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:tag="@string/common_tag_drag_view"
        app:layout_constraintBottom_toTopOf="@+id/tvGuideContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="34dp"
        android:paddingHorizontal="20dp"
        android:src="@drawable/feedback_ic_arrow"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/large_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="@string/feedback_guide_title"
        android:textColor="@color/text_white_main"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/ivArrow" />

    <TextView
        android:id="@+id/tvDescribe"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="@string/feedback_guide_describe"
        android:textColor="@color/text_white_secondary"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvGuideContent"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:textColor="@color/text_white_main"
        android:textSize="16sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/tvDescribe"
        tools:text="If you like Buz, join us on Discord!" />

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnSubmit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/tvGuideContent"
        app:text="@string/ok"
        app:type="primary_medium"
        tools:background="@drawable/common_r8_basic_primary"
        tools:layout_height="48dp" />

    <TextView
        android:id="@+id/tvDismiss"
        style="@style/button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="20dp"
        android:text="@string/dismiss"
        android:textColor="@color/basic_primary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/btnSubmit"
        tools:visibility="visible" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/btnSubmit" />

</com.interfun.buz.base.widget.round.RoundConstraintLayout>