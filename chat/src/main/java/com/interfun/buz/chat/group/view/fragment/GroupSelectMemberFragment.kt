package com.interfun.buz.chat.group.view.fragment

import android.animation.Animator
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.view.animation.LinearInterpolator
import android.view.animation.OvershootInterpolator
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.databinding.GroupFragmentSelectGroupMemberBinding
import com.interfun.buz.chat.group.view.block.GroupSelectMemberSearchBarBlock
import com.interfun.buz.chat.group.view.itemdelegate.*
import com.interfun.buz.chat.group.viewmodel.GroupSelectMemberViewModel
import com.interfun.buz.common.arouter.navigationWithInterceptor
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_CURRENT_MEMBER_NUM_COUNT
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_GROUP_ID
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_GROUP_NAME
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_GROUP_PORTRAIT
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_MEMBER_USER_IDS
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_USER_IDS
import com.interfun.buz.common.eventbus.BackPressEvent
import com.interfun.buz.common.eventbus.BackPressKey
import com.interfun.buz.common.eventbus.FriendsSyncCompleteEvent
import com.interfun.buz.common.eventbus.group.GroupInviteMemberSuccessEvent
import com.interfun.buz.common.eventbus.user.CloseProfileDialogEvent
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.widget.recyclerview.itemanimators.ScaleInAnimator
import com.jeremyliao.liveeventbus.LiveEventBus
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 *
 * @date 2022/7/25
 *
 * @desc Fragment for group members select page
 * 宿主:[com.interfun.buz.contacts.view.activity.SelectGroupMemberActivity]
 * 旧页面，根据ab,a组只有创群时才会走这个页面，其他情况全部走AddGroupMembersFragment，需求文档：https://vocalbeats.sg.larksuite.com/wiki/Gg73w35OKivJM7kn1nXlnZUbgdm
 */
@AndroidEntryPoint
@Route(path = PATH_ADD_USER_TO_GROUP_DLG)
class GroupSelectMemberFragment : BaseBindingFragment<GroupFragmentSelectGroupMemberBinding>() {

    companion object {
        const val TAG = "GroupSelectMemberFragment"
    }

    lateinit var mAdapter: MultiTypeAdapter
    lateinit var mSelectedAdapter: MultiTypeAdapter

    private var isDialogFragment = false

    private val selectGroupMemberViewModel by fragment.viewModels<GroupSelectMemberViewModel>()

    private val source by lazy {
        arguments?.getInt(RouterParamKey.Common.KEY_SOURCE).getIntDefault(CreateGroupSource.Contact.value)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LiveEventBus.get(FriendsSyncCompleteEvent::class.java).observe(this){
            //Get recommend contacts user list
            selectGroupMemberViewModel.getFriendList()
        }
    }



    override fun initBlock() {
        super.initBlock()
        GroupSelectMemberSearchBarBlock(this, binding).bind(viewLifecycleOwner)
    }


    override fun initData() {
        super.initData()
        selectGroupMemberViewModel.contactListLiveData.observe(viewLifecycleOwner) {
            mAdapter.items = it
            mAdapter.notifyDataSetChanged()
            binding.llSearchBar.goneIf(
                it.filterIsInstance<GroupSelectMemberViewModel.EmptyItem>().isNotEmpty()
            )

            ChatTracker.onCreateChannelPageViewScreen(selectGroupMemberViewModel.myFriendsCount)
        }

        selectGroupMemberViewModel.contactUpdateLiveData.observe(viewLifecycleOwner) {
            mAdapter.notifyItemChanged(it)
        }
        selectGroupMemberViewModel.selectedRemoveLiveData.observe(viewLifecycleOwner) { index ->
            mSelectedAdapter.notifyItemRemoved(index)
            updateUI()
        }
        selectGroupMemberViewModel.selectedInsertLiveData.observe(viewLifecycleOwner) { list ->
            mSelectedAdapter.notifyItemInserted(list)

            delayInMainThread(50) {
                var distance = 100.dp
                val layoutManager = binding.rlvSelected.layoutManager as LinearLayoutManager
                layoutManager?.let {
                    val lastIndex = it.findLastVisibleItemPosition()
                    if (lastIndex > 0) {
                        if (lastIndex < (mSelectedAdapter.itemCount - 1)) {
                            distance = (mSelectedAdapter.itemCount - lastIndex) * 68.dp
                        }
                    }

                }

                binding.rlvSelected.smoothScrollBy(distance,0, LinearInterpolator(), 300)
            }

            updateUI()

        }
      
        selectGroupMemberViewModel.selectedUpdateLiveData.observe(viewLifecycleOwner){
            mSelectedAdapter.items = it
            mSelectedAdapter.notifyDataSetChanged()
            updateUI()
        }

        if (arguments?.containsKey(KEY_GROUP_ID) == true) {
            val groupId = arguments?.getLong(KEY_GROUP_ID)?:0
            val groupName = arguments?.getString(KEY_GROUP_NAME)
            val groupPortrait = arguments?.getString(KEY_GROUP_PORTRAIT)

            selectGroupMemberViewModel.setGroupMsg(groupId!!, groupName,groupPortrait)


            binding.tvTitle.text = ResUtil.getString(R.string.ftue_v3_addMembers)
            binding.btnNext.text = R.string.done.asString()


            binding.spaceStatusBar.visibility = View.GONE
            binding.clRootLayout.setPadding(0, 40.dp, 0, 0)
            binding.llSearchBar.setBackgroundColor(R.color.color_background_2_default.asColor(
                appContext))
            binding.clRootLayout.setBackgroundColor(R.color.color_background_2_default.asColor(
                appContext))
        }
        // 获取默认选中的群成员
        val userIds = arguments?.getString(KEY_MEMBER_USER_IDS)?.split(",")?.map {
            it.toLong()
        }
        userIds?.let {
            // 默认选中的群成员, 不包含自己
            setupGroupMember(it.filter { userId -> userId.isMe().not() })
            if (arguments?.containsKey(KEY_GROUP_ID) == false) {
                // 非[GroupInfoDialog]编辑群成员 进来,将默认选中的群成员添加到rlvSelected中
                setupSelectedMember(it)
            }
        }

        if (mAdapter.itemCount == 0) {
            routerServices<ContactsService>().value?.getFriendListAndUpdateDB()
        }

        val groupMemberCount = arguments?.getInt(KEY_CURRENT_MEMBER_NUM_COUNT).getIntDefault()
        selectGroupMemberViewModel.setCurrentGroupMemberCount(groupMemberCount)

        updateUI()

    }

    

    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftvLeftBack.click {
            back()
        }

        binding.btnNext.click {

            if (selectGroupMemberViewModel.isNewChannel()) {
                // new channel, goto eidt groupinfo page
                if (activity.isNotNull()){
                    ARouter.getInstance()
                        .build(PATH_CHAT_ACTIVITY_EDIT_GROUP_INFO)
                        .withString(KEY_USER_IDS, selectGroupMemberViewModel.selectedUserIds().joinToString(","))
                        .withInt(RouterParamKey.Common.KEY_SOURCE, source)
                        .withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
                        .navigationWithInterceptor(requireActivity(), 1)
                }

                if (source == CreateGroupSource.FriendProfile.value){
                    CloseProfileDialogEvent.post()
                }
                CommonTracker.postClickEvent(
                    "AC2022091419",
                    "创群选人页",
                    "下一步按钮",
                    "group"
                )
            } else {
                selectGroupMemberViewModel.launch {
                    val resp = selectGroupMemberViewModel.inviteToJoinGroup()
                    if (resp?.isSuccess == true && selectGroupMemberViewModel.getGroupId() != null) {
                        resp.data?.rejectedMsg?.let {
                            LiveEventBus.get(GroupInviteMemberSuccessEvent::class.java).post(
                                GroupInviteMemberSuccessEvent(selectGroupMemberViewModel.getGroupId()!!, it)
                            )
                        }
                        doInMainThread {
                            removeSelf()
                        }
                    }
                }
            }

        }

        mAdapter = MultiTypeAdapter(selectGroupMemberViewModel.contactList).apply {

            register(SelectGroupMemberItemView(object :
                SelectGroupMemberItemView.OnCallBackListener {
                override fun onContactClick(user: GroupSelectMemberViewModel.GroupMember) {
                    if (selectGroupMemberViewModel.updatUserSelected(user)) {
                        binding.etSearch.text = Editable.Factory.getInstance().newEditable("")
                    }

                }

                override fun getSearchText(): String {
                    return binding.etSearch.text.toString().trim()
                }
            }))

            register(NewChannelSectionItemView())
            register(GroupSearchEmptyItemView())
            register(EmptyMemberItemView())
            register(GroupLinkShareItemView())
        }

        binding.rlvContact.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL,false)
            adapter = mAdapter
            itemAnimator = ScaleInAnimator(OvershootInterpolator()).apply {
                addDuration = 0
                removeDuration = 0
                moveDuration = 0
                changeDuration = 0
            }
            click {
                binding.rlvContact.requestFocus()
            }
        }

        mSelectedAdapter = MultiTypeAdapter(selectGroupMemberViewModel.selectedUserList).apply {

            register(GroupSelectedMemberItemView(object : GroupSelectedMemberItemView.OnCallBackListener {
                override fun onContactClick(user: GroupSelectMemberViewModel.GroupMember) {
                    selectGroupMemberViewModel.updatUserSelected(user)
                }
            }))

            register(GroupSelectedMemberSpaceItemView())
        }

        binding.rlvSelected.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL,false)
            adapter = mSelectedAdapter
            addItemPadding(0  )
            itemAnimator = ScaleInAnimator(OvershootInterpolator()).apply {
                addDuration = 0
                removeDuration = 0
                moveDuration = 200
                changeDuration = 0

            }
        }

        binding.clRootLayout.click {  }

        BusUtil.observe<BackPressEvent>(BackPressKey.GROUP_INFO_DIALOG,this){
            back()
        }

    }

    private fun back(){
        if (selectGroupMemberViewModel.isNewChannel()) {
            finishActivity()
            activity?.overridePendingTransition(
                R.anim.anim_nav_enter_pop,
                R.anim.anim_nav_exit_pop
            )
        } else {
            removeSelf()
        }
    }

    private fun removeSelf() {
    parentFragmentManager.beginTransaction()
        .setCustomAnimations(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
        .remove(this)
        .commitAllowingStateLoss()

    }


    private fun updateUI() {
        if (mSelectedAdapter.itemCount == 2) {
            startSelectedAnimation(false)
            binding.btnNext.isEnabled = false
            binding.btnNext.setTextColor(ResUtil.getColor(R.color.secondary_primary_disable))
        } else {
            startSelectedAnimation(true)
            binding.btnNext.isEnabled = true
            binding.btnNext.setTextColor(ResUtil.getColor(R.color.basic_primary))
        }

        binding.tvMemberCount.text = "${selectGroupMemberViewModel.getGroupMemberCount()}/${selectGroupMemberViewModel.getMaxGroupMemberCount()}"
    }

    private fun setupGroupMember(userIds: List<Long>) {
        selectGroupMemberViewModel.setGroupMemberUserList(userIds)
    }

    private fun setupSelectedMember(userIds: List<Long>) {
        selectGroupMemberViewModel.setSelectedMemberUserList(userIds)
    }

    private var animator: ValueAnimator? = null

    private fun startSelectedAnimation(show: Boolean) {
        if (show && binding.llSelect.visibility == View.VISIBLE) {
            return
        }
        if (!show && binding.llSelect.visibility != View.VISIBLE) {
            return
        }

        val targetHeight = if (show) 80.dp else 1.dp

        animator = ObjectAnimator.ofInt(binding.llSelect.layoutParams.height, targetHeight).apply {
            duration = 300
            addUpdateListener {
                binding.llSelect.layoutParams.height = it.animatedValue as Int
                binding.llSelect.requestLayout()
            }
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(p0: Animator) {
                    binding.llSelect.layoutParams.height = if (show) 1.dp else 80.dp
                    binding.llSelect.requestLayout()
                    binding.llSelect.visibility = View.VISIBLE
                }

                override fun onAnimationEnd(p0: Animator) {
                    binding.llSelect.visibility = if (show) View.VISIBLE else View.INVISIBLE
                    binding.llSelect.layoutParams.height = if (show) 80.dp else 1.dp
                    binding.llSelect.requestLayout()
                }

                override fun onAnimationCancel(p0: Animator) {
                }

                override fun onAnimationRepeat(p0: Animator) {
                }
            })
        }
        animator?.start()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        animator?.cancel()
    }
}