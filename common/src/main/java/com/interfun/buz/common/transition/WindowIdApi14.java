/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.interfun.buz.common.transition;

import android.os.IBinder;

class WindowIdApi14 implements WindowIdImpl {

    private final IBinder mToken;

    WindowIdApi14(IBinder token) {
        mToken = token;
    }

    @Override
    public boolean equals(Object o) {
        return o instanceof WindowIdApi14 && ((WindowIdApi14) o).mToken.equals(this.mToken);
    }

    @Override
    public int hashCode() {
        return mToken.hashCode();
    }
}
