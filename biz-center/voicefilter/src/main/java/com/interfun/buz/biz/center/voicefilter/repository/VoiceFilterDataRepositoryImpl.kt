package com.interfun.buz.biz.center.voicefilter.repository

import com.buz.idl.bot.bean.VoiceFilterInfo
import com.buz.idl.bot.response.ResponseGetVoiceFilterList
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.biz.center.voicefilter.datasource.IRemoteVoiceFilterDataSource
import com.interfun.buz.biz.center.voicefilter.datasource.LocalVoiceFilterDataSource
import com.interfun.buz.biz.center.voicefilter.model.VoiceFilterOriginData
import com.interfun.buz.common.bean.LoadingState
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.component.hilt.UserQualifier
import kotlinx.collections.immutable.PersistentList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

class VoiceFilterDataRepositoryImpl @Inject constructor(
    @UserQualifier val userScope: CoroutineScope,
    @UserQualifier private val localVoiceFilterDS: LocalVoiceFilterDataSource,
    @UserQualifier private val remoteVoiceFilterDS: IRemoteVoiceFilterDataSource
) : VoiceFilterDataRepository {

    companion object {
        const val TAG = "VoiceFilterDataRepository"
    }

    private val _voiceFilterListLoadStatusFlow = MutableStateFlow<LoadingState>(LoadingState.Initial)
    override val voiceFilterListLoadStatusFlow = _voiceFilterListLoadStatusFlow

    override fun initRepository() {
        userScope.launch {
            syncVoiceFilterList()
        }
    }

    override fun getVoiceFilterOriginData(): Flow<VoiceFilterOriginData> {
        return localVoiceFilterDS.getVoiceFilterList().map { resp ->
            if (resp == null) {
                logInfo(TAG, "getVoiceFilterList resp: null")
                syncVoiceFilterList()
                VoiceFilterOriginData(
                    emptyList(),
                    emptyList()
                )
            } else {
                _voiceFilterListLoadStatusFlow.emit(LoadingState.Success)
                logInfo(
                    TAG, "getVoiceFilterList " +
                        "list size: ${resp.voiceFilterInfos?.size ?: 0}, " +
                        "tab size: ${resp.voiceFilterTabItems?.size ?: 0}"
                )
                VoiceFilterOriginData(
                    voiceFilterList = resp.voiceFilterInfos ?: emptyList(),
                    voiceFilterTabList = resp.voiceFilterTabItems ?: emptyList()
                )
            }
        }.flowOn(Dispatchers.IO)
    }

    override suspend fun syncVoiceFilterList() {
        _voiceFilterListLoadStatusFlow.emit(LoadingState.Loading)
        val remote =
            remoteVoiceFilterDS.getVoiceFilterList()
        when (remote) {
            is Error -> {
                _voiceFilterListLoadStatusFlow.emit(LoadingState.Failed(remote.code))
                logInfo(TAG, "syncVoiceFilterList errorCode: ${remote.code}")
            }

            is Success -> {
                _voiceFilterListLoadStatusFlow.emit(LoadingState.Success)
                localVoiceFilterDS.saveVoiceFilterList(remote.data)
                initVFListLatestTimestamp(remote.data)
                logInfo(TAG, "syncVoiceFilterList success size: ${remote.data.voiceFilterInfos?.size}")
            }
        }
    }

    override suspend fun getCachedVoiceFilterInfoById(voiceFilterId: Long): VoiceFilterInfo? {
        return localVoiceFilterDS.getVoiceFilterList().map {
            it?.voiceFilterInfos?.firstOrNull { it.filterId == voiceFilterId }
        }.firstOrNull()
    }

    override suspend fun clearVoiceFilterData() {
        localVoiceFilterDS.clearVoiceFilterData()
    }

    private fun initVFListLatestTimestamp(data: ResponseGetVoiceFilterList) {
        userScope.launch {
            val cacheDataList = localVoiceFilterDS.getVFLatestTimestamp().first()
            if (cacheDataList.isEmpty()){
                data.voiceFilterInfos?.forEach {item->
                    val filterId = item.filterId
                    val latestTimestamp = item.latestTimestamp
                    if (filterId !=null && latestTimestamp != null){
                        localVoiceFilterDS.updateVFLatestTimestamp(filterId, latestTimestamp)
                    }
                }
            }
        }
//        if (CommonMMKV.voiceFilterLatestTimestamp.isNullOrEmpty()) {
//            val list = data.voiceFilterInfos?.map {
//                it.filterId.getLongDefault() to it.latestTimestamp.getIntDefault()
//            }
//            if (list != null) {
//                CommonMMKV.voiceFilterLatestTimestamp =
//                    list.joinToString(";") { "${it.first},${it.second}" }
//            }
//        }
    }

    /**
     * 更新入口红点时间戳
     */
    override suspend fun updateVFLatestEntryForMoreButtonTimestamp() {
        localVoiceFilterDS.updateVFLatestEntryNotifyTimestamp(AppConfigRequestManager.voiceFilterLatestTimestamp)
    }

    /**
     * 获取入口红点时间戳
     */
    override fun getVFLatestEntryNotifyTimestamp(): StateFlow<Int> {
        return localVoiceFilterDS.getVFLatestEntryNotifyTimestamp()
    }

    /**
     * 获取1级tab红点时间戳
     */
    override fun getVFLatestTabTimestamp(): StateFlow<Int> {
        return localVoiceFilterDS.getVFLatestTabTimestamp()
    }

    /**
     * 更新2级tab红点时间戳
     */
    override fun updateVFLatestTimestamp(filterId: Long, timestamp: Int) {
        localVoiceFilterDS.updateVFLatestTimestamp(filterId, timestamp)
    }

    /**
     * 获取2级tab红点时间戳
     */
    override fun getVFLatestTimestamp(): Flow<PersistentList<Pair<Long, Int>>> {
        return localVoiceFilterDS.getVFLatestTimestamp()
    }

    override fun getVoiceFilterCache(): ResponseGetVoiceFilterList? {
        return localVoiceFilterDS.voiceFilterCache
    }

    override suspend fun updateVFEntryRedDotStatus() {
        localVoiceFilterDS.updateVFLatestTabTimestamp(AppConfigRequestManager.voiceFilterLatestTimestamp)
    }
}