package com.interfun.buz.im.constants

/**
 * @Desc:
 * @Author: <EMAIL>
 * @Date: 2022/6/9
 */

//灯塔key（对应IM的灯塔环境）
const val IM5_APP_KEY_PRODUCT_DOCKER: String = "ef77e66f4a322ce0a5eff8e1d144be70"

//开发key（其实也是对应IM的线上环境）
const val IM5_APP_KEY_DEVELOP: String = "111c05f2f02ea70aa87f665af0059dcf"

//生产key
const val IM5_APP_KEY_PRODUCTION: String = "a94767967bc6e3e3122d98a75b789682"

// for e2ee
const val E2EE_APP_KEY_PRODUCTION: String = "24498ecf8da72ef6432ead2a1793328a"
const val E2EE_APP_KEY_DEVELOP: String = "763ee101bbdfdd02792fc4a36cd0f2ba"

const val IM5_APP_HOST: String = "buz-app.com"