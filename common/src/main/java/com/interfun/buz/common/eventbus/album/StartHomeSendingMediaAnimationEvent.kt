package com.interfun.buz.common.eventbus.album

import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.eventbus.BaseEvent

class StartHomeSendingMediaAnimationEvent(val multipleImages: Boolean, val firstItem: Any, val source: Int?): BaseEvent() {
    companion object{
        fun post(multipleImages: Boolean, firstItem: Any, source: Int?) {
            BusUtil.post(StartHomeSendingMediaAnimationEvent(multipleImages, firstItem, source))
        }
    }
}