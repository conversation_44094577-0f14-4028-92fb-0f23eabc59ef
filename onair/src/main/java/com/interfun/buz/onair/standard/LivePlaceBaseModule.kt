package com.interfun.buz.onair.standard

import android.os.Bundle
import androidx.fragment.app.Fragment
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.liveplace.viewmodel.LivePlaceModelFactory
import com.interfun.buz.onair.datasource.IRemoteSoundBoardDataSource
import com.interfun.buz.onair.datasource.LocalOnAirGiftDataSource
import com.interfun.buz.onair.datasource.RemoteOnAirGiftDataSource
import com.interfun.buz.onair.datasource.RemoteSoundBoardDataSource
import com.interfun.buz.onair.repository.ISoundBoardRepository
import com.interfun.buz.onair.repository.LivePlaceShareRepository
import com.interfun.buz.onair.repository.OnAirBottomSheetRepository
import com.interfun.buz.onair.repository.OnAirGiftRepository
import com.interfun.buz.onair.repository.SoundBoardRepository
import com.interfun.buz.onair.view.activity.LivePlaceActivity
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityRetainedComponent
import dagger.hilt.android.components.FragmentComponent
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.scopes.ActivityRetainedScoped
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
abstract class ALivePlaceBaseAppModule {


    @Binds
    @Singleton
    abstract fun bindRemoteSoundBoardDataSource(remoteSoundBoardDataSource: RemoteSoundBoardDataSource): IRemoteSoundBoardDataSource


    @Binds
    @Singleton
    abstract fun bindISoundBoardRepository(soundBoardRepository: SoundBoardRepository): ISoundBoardRepository

}

@Module
@InstallIn(SingletonComponent::class)
class LivePlaceBaseAppModule {


    @Provides
    @Singleton
    fun provideOnAirGiftRepository(
        localDS: LocalOnAirGiftDataSource,
        remoteDs: RemoteOnAirGiftDataSource
    ): OnAirGiftRepository {
        return OnAirGiftRepository(localDS, remoteDs)
    }

}

@Module
@InstallIn(ViewModelComponent::class)
class LivePlaceBaseVMModule {

    @Provides
    @LivePlaceScoped
    fun provideOnAirBottomSheetRepository(@ActivityRetainedScoped data: OnAirBottomSheetRepository): OnAirBottomSheetRepository {
        return data
    }
}

@Module
@InstallIn(ActivityRetainedComponent::class)
class LivePlaceBaseActivityModule {

    @Provides
    @ActivityRetainedScoped
    fun provideOnAirBottomSheetRepository(): OnAirBottomSheetRepository {
        return OnAirBottomSheetRepository()
    }

    @Provides
    @ActivityRetainedScoped
    fun provideLivePlaceShareRepository(): LivePlaceShareRepository {
        return LivePlaceShareRepository()
    }
}

@Module
@InstallIn(FragmentComponent::class)
abstract class LivePlaceBaseModule {

    companion object {


        @Provides
        fun provideOnRoomParam(
            fragment: Fragment,
        ): RoomParam {
            return LivePlaceActivity.obtainRoomParam(fragment.arguments ?: Bundle())!!
        }

        @Provides
        fun provideLivePlaceModelFactory(
            fragment: Fragment,
            roomParam: RoomParam,
        ): LivePlaceModelFactory {
            return LivePlaceModelFactory(
                fragment,
                roomParam,
                fragment.defaultViewModelProviderFactory
            )
        }

        @Provides
        fun provideOnAirRoom(
            roomParam: RoomParam
        ): OnAirRoom? {
            val curPresentOnAirRoom =
                routerServices<IGlobalOnAirController>().value?.curOnAirContext()
            val onAirRoom = if (curPresentOnAirRoom?.createParam?.isSameRoom(roomParam) == true) {
                curPresentOnAirRoom
            } else {
                null
            }
            return onAirRoom
        }

    }
}