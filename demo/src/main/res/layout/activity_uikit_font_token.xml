<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="24dp">

    <TextView
        android:id="@+id/tvTitleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_title_large:"
        android:layout_marginTop="150dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_title_large"
        android:text="Title/Large"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvTitleLarge"
        app:layout_constraintTop_toTopOf="@+id/tvTitleLarge"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitleLarge"/>

    <!-- text_title_medium -->
    <TextView
        android:id="@+id/tvTitleMedium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_title_medium:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleLarge"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_title_medium"
        android:text="Title/Medium"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvTitleMedium"
        app:layout_constraintTop_toTopOf="@+id/tvTitleMedium"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitleMedium"/>

    <!-- text_title_small -->
    <TextView
        android:id="@+id/tvTitleSmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_title_small:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleMedium"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_title_small"
        android:text="Title/Small"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvTitleSmall"
        app:layout_constraintTop_toTopOf="@+id/tvTitleSmall"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitleSmall"/>


    <!-- text_label_large -->
    <TextView
        android:id="@+id/tvLabelLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_label_large:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleSmall"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_label_large"
        android:text="Label/Large"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvLabelLarge"
        app:layout_constraintTop_toTopOf="@+id/tvLabelLarge"
        app:layout_constraintBottom_toBottomOf="@+id/tvLabelLarge"/>

    <!-- text_label_medium -->
    <TextView
        android:id="@+id/tvLabelMedium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_label_medium:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvLabelLarge"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_label_medium"
        android:text="Label/Medium"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvLabelMedium"
        app:layout_constraintTop_toTopOf="@+id/tvLabelMedium"
        app:layout_constraintBottom_toBottomOf="@+id/tvLabelMedium"/>

    <!-- text_label_small -->
    <TextView
        android:id="@+id/tvLabelSmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_label_small:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvLabelMedium"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_label_small"
        android:text="Label/Small"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvLabelSmall"
        app:layout_constraintTop_toTopOf="@+id/tvLabelSmall"
        app:layout_constraintBottom_toBottomOf="@+id/tvLabelSmall"/>

    <!-- text_body_large -->
    <TextView
        android:id="@+id/tvBodyLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_body_large:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvLabelSmall"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_body_large"
        android:text="Body/Large"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvBodyLarge"
        app:layout_constraintTop_toTopOf="@+id/tvBodyLarge"
        app:layout_constraintBottom_toBottomOf="@+id/tvBodyLarge"/>

    <!-- text_body_medium -->
    <TextView
        android:id="@+id/tvBodyMedium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_body_medium:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvBodyLarge"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_body_medium"
        android:text="Body/Medium"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvBodyMedium"
        app:layout_constraintTop_toTopOf="@+id/tvBodyMedium"
        app:layout_constraintBottom_toBottomOf="@+id/tvBodyMedium"/>

    <!-- text_body_medium_italic -->
    <TextView
        android:id="@+id/tvBodyMediumItalic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_body_medium_italic:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvBodyMedium"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_body_medium_italic"
        android:text="Body/Medium/Italic"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvBodyMediumItalic"
        app:layout_constraintTop_toTopOf="@+id/tvBodyMediumItalic"
        app:layout_constraintBottom_toBottomOf="@+id/tvBodyMediumItalic"/>

    <!-- text_body_small -->
    <TextView
        android:id="@+id/tvBodySmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_body_small:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvBodyMediumItalic"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_body_small"
        android:text="Body/Small"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvBodySmall"
        app:layout_constraintTop_toTopOf="@+id/tvBodySmall"
        app:layout_constraintBottom_toBottomOf="@+id/tvBodySmall"/>

    <!-- text_body_small_prominent -->
    <TextView
        android:id="@+id/tvBodySmallProminent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:text="text_body_small_prominent:"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvBodySmall"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/text_body_small_prominent"
        android:text="Body/Small/Prominent"
        android:textColor="@color/neutral_white"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/tvBodySmallProminent"
        app:layout_constraintTop_toTopOf="@+id/tvBodySmallProminent"
        app:layout_constraintBottom_toBottomOf="@+id/tvBodySmallProminent"/>

</androidx.constraintlayout.widget.ConstraintLayout>