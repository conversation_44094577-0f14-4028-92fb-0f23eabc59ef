package com.interfun.buz.storage.cleaner.other

import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.service.MediaService
import com.interfun.buz.storage.cleaner.ICacheCleaner

/**
 * Author: <PERSON><PERSON>ouSheng
 * Date: 2024/6/24
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 视频分享缓存清理
 */
internal class VideoShareCacheCleaner : ICacheCleaner {

    private val mediaService by lazy { routerServices<MediaService>().value }

    override suspend fun getSize(): Long {
        return mediaService?.getAllDownloadFileSize().getLongDefault()
    }

    override suspend fun delete() {
        mediaService?.deleteAllDownloadFile()
    }

    override suspend fun autoDelete(day: Int) {
        delete()
    }

}