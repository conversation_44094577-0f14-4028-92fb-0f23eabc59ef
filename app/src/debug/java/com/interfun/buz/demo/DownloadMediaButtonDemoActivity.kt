package com.interfun.buz.demo

import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.widget.media.MediaDownloadState
import com.interfun.buz.databinding.ActivityDemoDownloadButtomBinding

class DownloadMediaButtonDemoActivity :BaseBindingActivity<ActivityDemoDownloadButtomBinding>() {
    override fun initView() {
        super.initView()

        binding.download.updateState(MediaDownloadState.LOADING)
    }
}