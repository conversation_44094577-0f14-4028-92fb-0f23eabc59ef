package com.interfun.buz.common.widget.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.doOnPreDraw
import androidx.core.view.drawToBitmap
import androidx.core.view.setPadding
import coil.load
import coil.request.ErrorResult
import coil.request.ImageRequest
import coil.request.ImageRequest.Listener
import coil.request.SuccessResult
import coil.transform.CircleCropTransformation
import coil.transition.CrossfadeTransition
import com.buz.idl.group.bean.GroupBaseInfo
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.ScreenUtil
import com.interfun.buz.common.R
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.ktx.getGroupName
import com.interfun.buz.common.ktx.getPortraitsByFewMembers
import com.interfun.buz.common.ktx.setStyleTitleLarge
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.utils.ImageUtils

/**
 * @Desc A view that show user portrait,
 * if the url is null or empty then show textPortrait(for example user firstname)
 * @Author:<EMAIL>
 * @Date: 2022/7/12
 */
data class PortraitBean(
    val userId: Long?,
    val url: String? = null,
    val firstName: String? = null,
    val lastName: String? = null
)

val PortraitBean.isEmpty get() = url.isNullOrEmpty() && firstName.isNullOrEmpty() && lastName.isNullOrEmpty()

class PortraitImageViewOld @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attributeSet, defStyleAttr) {

    enum class ColorType{
        Normal,
        GroupInfoDialog,
        PushDialog
    }

    companion object{
        const val TAG = "PortraitImageView"
        const val DURATION_CROSS_FADE = 300
        fun getSmallImageSize(): Int {
            return when (screenWidth) {
                in 320..480 -> 90
                in 480..720 -> 150
                in 720..1080 -> 225
                in 1080..1440 -> 300
                in 1440..2000 -> 400
                in 2000..3000 -> 600
                in 3000..4000 -> 800
                else -> 300
            }
        }

        fun getBigImageSize(): Int {
            return getSmallImageSize() / 3 * 8
        }
    }

    val constraintLayout = ConstraintLayout(context).apply {
        id = View.generateViewId()
        invisible()
        <EMAIL>(this, MATCH_PARENT, MATCH_PARENT)
    }

    val imageView = ImageFilterView(context).apply {
        id = View.generateViewId()
        roundPercent = 1f
        <EMAIL>(this, MATCH_PARENT, MATCH_PARENT)
    }

    val textView = TextView(context).apply {
        id = View.generateViewId()
        background = R.drawable.common_contacts_name_image_bg.asDrawable()
        gravity = Gravity.CENTER
        setStyleTitleLarge()
        setTextColor(R.color.overlay_white_10.asColor())
        gone()
        <EMAIL>(this, MATCH_PARENT, MATCH_PARENT)
    }

    private val errorRes = R.drawable.common_user_default_portrait_round
    private var portraitsLoadFinishedCount = 0
    private var portraitsBitmap: Bitmap? = null
    private var lastPortraitsList = emptyList<String?>()
    private var colorType = ColorType.Normal
    private var needDrawToBitmap = false
    private var finishedCallback :DefaultCallback?=null

    fun resetViewsVisibility(){
        constraintLayout.invisible()
        imageView.gone()
        textView.gone()
    }

    fun setUserInfo(userInfo: UserRelationInfo?, crossFadeEnable: Boolean = false){
        userInfo?.apply {
            setContactsPortrait(portrait, firstName, lastName, crossFadeEnable)
        }
    }

    fun setTextColor(color:Int){
        textView.setTextColor(color)
    }

    fun setPortraitBean(bean: PortraitBean, crossFadeEnable: Boolean = false) {
        if (bean.isEmpty) {
            UserRelationCacheManager.getUserRelationInfoByUid(bean.userId ?: 0)?.apply {
                setContactsPortrait(portrait, firstName, lastName, crossFadeEnable)
            }
        }
        setContactsPortrait(bean.url, bean.firstName, bean.lastName, crossFadeEnable)
    }

    fun setContactsPortrait(urlPortrait: String?, textPortrait: String) {
        //show default portrait placeholder when urlPortrait is empty and textPortrait is empty
        if (urlPortrait.isNullOrEmpty() && textPortrait.isNotEmpty()) {
            setText(textPortrait)
        } else {
            setPortrait(urlPortrait)
        }
    }

    fun setContactsPortrait(
        urlPortrait: String?,
        firstName: String?,
        lastName: String?,
        crossFadeEnable: Boolean = false
    ) {
        if (urlPortrait.isNullOrEmpty()
            && (firstName.isNullOrEmpty().not() || lastName.isNullOrEmpty().not())
        ) {
            val text = firstName.getFirstUppercaseLetter() + lastName.getFirstUppercaseLetter()
            setText(text)
        } else {
            setPortrait(urlPortrait, crossFadeEnable)
        }
    }

    fun setPortrait(urlPortrait: String?, crossFadeEnable:Boolean = false) {
        log(TAG, "setPortrait $urlPortrait")
        val data :Any = if (urlPortrait.isNullOrEmpty()) {
            errorRes
        } else {
            val w = getBigImageSize()
            ImageUtils.getImageThumbUrl(urlPortrait, w, w, ImageUtils.OriginSizeStrategy())
        }
        loadImage(data, crossFadeEnable)
    }

    fun setDefaultPortrait() {
        setPortrait(null)
    }

    fun setText(textPortrait: String) {
        imageView.gone()
        textView.visible()
        textView.text = textPortrait
    }

    fun setGroupBaseInfo(
        groupBaseInfo: GroupBaseInfo,
        crossFadeEnable: Boolean = false,
        needDrawToBitmap: Boolean = false,
        colorType: ColorType = ColorType.Normal
    ) {
        if (!groupBaseInfo.portraitUrl.isNullOrEmpty()) {
            setPortrait(groupBaseInfo.portraitUrl, crossFadeEnable)
        } else {
            setPortraitList(
                groupBaseInfo.firstFewMemberInfos?.getPortraitsByFewMembers(),
                crossFadeEnable,
                needDrawToBitmap,
                colorType
            )
        }
    }

    fun setGroupInfoBean(
        groupInfoBean: GroupInfoBean,
        crossFadeEnable: Boolean = false,
        needDrawToBitmap: Boolean = false,
        colorType: ColorType = ColorType.Normal,
        onFinishCallback: DefaultCallback? = null
    ) {
        log(TAG, "setGroupInfoBean groupName = ${groupInfoBean.groupName},  portraitUrl= " +
                "${groupInfoBean.portraitUrl} list = ${groupInfoBean.firstFewPortraits}")
        finishedCallback = onFinishCallback
        if (!groupInfoBean.portraitUrl.isNullOrEmpty()) {
            setPortrait(groupInfoBean.portraitUrl, crossFadeEnable)
        } else {
            setPortraitList(
                groupInfoBean.firstFewPortraits,
                crossFadeEnable,
                needDrawToBitmap,
                colorType
            )
        }
    }

    fun setPortraitList(
        urlList: List<String?>?,
        crossFadeEnable: Boolean = false,
        needDrawToBitmap: Boolean = false,
        colorType: ColorType = ColorType.Normal,
        backgroundCreator: (ColorType) -> Drawable?,
        itemBackgroundCreator: (ColorType) -> Drawable?
    ) {
        log(TAG, "setPortraitList $urlList")
        if (urlList.isNullOrEmpty()) {
            setDefaultPortrait()
            return
        }
        this.colorType = colorType
        this.needDrawToBitmap = needDrawToBitmap
        val newList = urlList.take(6)
        if (needDrawToBitmap && portraitsBitmap != null && newList.isSameList(lastPortraitsList)) {
            log(TAG, "setPortraitList hasCache and portraitsBitmap is $portraitsBitmap")
            loadImage(portraitsBitmap, crossFadeEnable)
            return
        }
        doOnPreDraw {
            constraintLayout.apply {
                removeAllViews()
                portraitsLoadFinishedCount = 0
                id = View.generateViewId()
                if (width == 0){
                    return@apply
                }
                val count = newList.size
                val w = if (count < 5) (width * 0.41f).toInt() else (width * 0.38f).toInt()
                val sW = (width * 0.03f).toInt()
                val startAngle = when (count) {
                    2 -> -90f
                    4 -> -45f
                    else -> 0f
                }
                val sweepAngle = 360f / count
                val l = when (count) {
                    0, 1 -> 0f
                    2 -> width * 0.33f / 2f
                    3 -> width * 0.39f / 2f
                    4, 5 -> width * 0.47f / 2f
                    else -> width * 0.51f / 2f
                }.toInt()

                val viewBg = View(context).apply {
                    background = backgroundCreator(colorType)
                    id = View.generateViewId()
                }
                addView(viewBg, LayoutParams(0, 0).apply {
                    startToStart = LayoutParams.PARENT_ID
                    endToEnd = LayoutParams.PARENT_ID
                    topToTop = LayoutParams.PARENT_ID
                    bottomToBottom = LayoutParams.PARENT_ID
                })

                (0 until count).reversed().forEach {
                    val imageView = generateImageView(w, sW, newList, it,itemBackgroundCreator)
                    val params = LayoutParams(w, w).apply {
                        circleConstraint = viewBg.id
                        circleRadius = l
                        circleAngle = startAngle + sweepAngle * it
                    }
                    addView(imageView, params)
                }

                val firstView = getChildAt(1) as ImageView
                if (count > 2) {
                    val coverWidth = when (count) {
                        3 -> width * 0.38f
                        4 -> width * 0.25f
                        5 -> width * 0.155f
                        else -> width * 0.16f
                    }.toInt()
                    val coverHeight = when (count) {
                        3 -> width * 0.118f
                        4 -> width * 0.08f
                        5 -> width * 0.215f
                        else -> width * 0.25f
                    }.toInt()

                    val imageView = generateImageView(w, sW, newList, count - 1,itemBackgroundCreator)
                    val frameLayout = FrameLayout(context).apply {
                        id = View.generateViewId()
                        addView(imageView, FrameLayout.LayoutParams(w, w).apply {
                            gravity = when (count) {
                                3 -> Gravity.START
                                4 -> Gravity.CENTER_HORIZONTAL
                                else -> Gravity.END
                            }
                        })
                    }
                    val params = LayoutParams(coverWidth, coverHeight).apply {
                        topToTop = firstView.id
                        if (count == 4) startToStart = firstView.id
                        if (count == 3) {
                            startToStart = firstView.id
                        } else {
                            endToEnd = firstView.id
                        }
                    }
                    addView(frameLayout, params)
                }
            }
        }
    }

    fun setPortraitList(
        urlList: List<String?>?, crossFadeEnable: Boolean = false,
        needDrawToBitmap: Boolean = false,
        colorType: ColorType = ColorType.Normal
    ) {

        val backgroundCreator:(ColorType)->Drawable? = {
            when (it) {
                ColorType.GroupInfoDialog -> R.drawable.common_oval_overlay_grey_20_stroke_1.asDrawable()
                ColorType.PushDialog -> R.drawable.common_oval_444444_stroke_1.asDrawable()
                else -> R.drawable.common_oval_overlay_gery_10_stroke_1.asDrawable()
            }
        }

        val itemBackgroundCreator:(ColorType)->Drawable? =  {
            when(it){
                ColorType.GroupInfoDialog -> R.drawable.common_oval_overlay_grey_20.asDrawable()
                ColorType.PushDialog -> R.drawable.common_oval_444444.asDrawable()
                else -> R.drawable.common_oval_overlay_grey_10.asDrawable()
            }
        }

        setPortraitList(
            urlList,
            crossFadeEnable,
            needDrawToBitmap,
            colorType,
            backgroundCreator,
            itemBackgroundCreator
        )
    }

    private fun generateImageView(
        imageWidth: Int,
        strokeWidth: Int,
        list: List<String?>,
        index: Int,
        backgroundCreator:(ColorType) -> Drawable?
    ): ImageView {
        return ImageView(context).apply {
            id = View.generateViewId()
            background = backgroundCreator(colorType)
            setPadding(strokeWidth)
            val w = getSmallImageSize()
            val url = ImageUtils.getImageThumbUrl(list[index], w, w, ImageUtils.OriginSizeStrategy())
            val data = url.ifEmpty { errorRes }
            load(data) {
                size(imageWidth)
                crossfade(false)
                error(errorRes)
                transformations(CircleCropTransformation())
                listener(listener = object : Listener {
                    var startTime = 0L
                    override fun onStart(request: ImageRequest) {
                        startTime = System.currentTimeMillis()
                        log("PrivatePortraitDataSource","onStart: ${request.data}")
                    }

                    override fun onError(request: ImageRequest, result: ErrorResult) {
                        log("PrivatePortraitDataSource", "onError: ${result.throwable.message}")
                    }

                    override fun onSuccess(request: ImageRequest, result: SuccessResult) {
                        log(
                            "PrivatePortraitDataSource", "result dataSource: ${result.dataSource} " +
                                "time: ${System.currentTimeMillis() - startTime} "+
                                "size: ${ScreenUtil.px2dp((result.drawable as BitmapDrawable).bitmap.width.toFloat())} " +
                                "isSampled: ${result.isSampled} " +
                                "data: $data")
                    }
                })
                target(onSuccess = { drawable ->
                    setImageDrawable(drawable)
                    doOnPreDraw {
                        onPortraitsLoadFinished(list)
                    }
                }, onError = {
                    load(it){ transformations(CircleCropTransformation()) }
                    doOnPreDraw {
                        onPortraitsLoadFinished(list)
                    }
                })
            }
        }
    }

    private fun onPortraitsLoadFinished(list: List<String?>) {
        val count = list.size
        portraitsLoadFinishedCount += 1
        val realCount = if (count > 2) count + 1 else count
        if (portraitsLoadFinishedCount == realCount) {
            textView.gone()
            lastPortraitsList = list
            if (needDrawToBitmap) {
                try {
                    portraitsBitmap = constraintLayout.drawToBitmap()
                } catch (e: Exception) {
                    e.log(TAG)
                }
                loadImage(portraitsBitmap)
                constraintLayout.removeAllViews()
            } else {
                imageView.invisible()
                constraintLayout.visible()
                constraintLayout.requestLayout()
            }
            finishedCallback?.invoke()
        }
    }

    fun loadImage(data:Any?, crossFadeEnable:Boolean = false){
        log(TAG, "loadImage data:$data")
        imageView.load(data) {
            error(errorRes)
            transformations(CircleCropTransformation())
            listener(listener = object : ImageRequest.Listener {
                override fun onError(request: ImageRequest, result: ErrorResult) {
                    log("PrivatePortraitDataSource", "onError: ${result.throwable.message}")
                }

                override fun onSuccess(request: ImageRequest, result: SuccessResult) {
                    log(
                        "PrivatePortraitDataSource", "result dataSource: ${result.dataSource} " +
                        "size: ${ScreenUtil.px2dp((result.drawable as BitmapDrawable).bitmap.width.toFloat())} " +
                        "isSampled: ${result.isSampled} " +
                        "data: $data")
                }
            })
            if (crossFadeEnable) {
                transitionFactory { target, result ->
                    CrossfadeTransition(target, result, DURATION_CROSS_FADE)
                }
            }
        }
        textView.gone()
        constraintLayout.invisible()
        imageView.visible()
        imageView.alpha = 1f
    }

}