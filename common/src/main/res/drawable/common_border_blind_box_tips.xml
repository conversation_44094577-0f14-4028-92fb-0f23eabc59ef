<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Tips border color with gradient -->
    <item>
        <shape>
            <gradient
                android:type="linear"
                android:angle="0"
                android:startColor="@color/white_30"
                android:endColor="@color/transparent"
                android:centerX="0.7" />
            <corners
                android:topLeftRadius="12dp"
                android:bottomLeftRadius="12dp" />
        </shape>
    </item>

    <!-- Set the same background color as the VE panel background colour -->
    <item
        android:bottom="0.5dp"
        android:left="0.5dp"
        android:top="0.5dp">
        <shape>
            <solid android:color="@color/overlay_grey_14" />
            <corners
                android:topLeftRadius="12dp"
                android:bottomLeftRadius="12dp" />
        </shape>
    </item>
</layer-list>
