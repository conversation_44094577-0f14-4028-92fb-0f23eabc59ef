@file:Suppress("unused")

package com.interfun.buz.base.ktx

import okhttp3.internal.toLongOrDefault
import kotlin.math.roundToInt
import kotlin.random.Random

/**
 * What is Value class: https://juejin.cn/post/7113704624422387720
 * */

val randomInt
    get() = run {
        var random = (Int.MIN_VALUE..Int.MAX_VALUE).random()
        if (random == 0) {
            random = (Int.MIN_VALUE..Int.MAX_VALUE).random()
        }
        random
    }

val randomBoolean get() = randomInt % 2 == 0

val randomFloat get() = Random.nextFloat()

fun Int.maxOf(max: Int): Int = maxOf(this, max)
fun Int.minOf(min: Int): Int = minOf(this, min)

fun Float.maxOf(max: Float): Float = maxOf(this, max)
fun Float.minOf(min: Float): Float = minOf(this, min)

fun Double.maxOf(max: Double): Double = maxOf(this, max)
fun Double.minOf(min: Double): Double = minOf(this, min)

fun Boolean.getIfTrueOrFalse(ifTrueValue: Any, ifFalseValue: Any) =
    if (this) ifTrueValue else ifFalseValue

fun Boolean.getIfTrueOrFalse(ifTrueValue: String, ifFalseValue: String) =
    if (this) ifTrueValue else ifFalseValue

fun Boolean.getIfTrueOrFalse(ifTrueValue: Int, ifFalseValue: Int) =
    if (this) ifTrueValue else ifFalseValue

fun Boolean.getIfTrueOrFalse(ifTrueValue: Float, ifFalseValue: Float) =
    if (this) ifTrueValue else ifFalseValue

fun Boolean.getIfTrueOrFalse(ifTrueValue: Long, ifFalseValue: Long) =
    if (this) ifTrueValue else ifFalseValue

fun String?.toSafeLong(): Long {
    return this?.toLongOrDefault(0L) ?: 0L
}

fun Float.roundToOneDecimal(): Float {
    return (this * 10).roundToInt() / 10f
}

/**
 * Convert Long to Int, only lower 32 bits
 */
fun Long.toIntLower32Bits(): Int {
    return (this and 0xFFFFFFFF).toInt()
}

fun calculateValue(start: Float, end: Float, progress: Float) = start + (end - start) * progress