package com.interfun.buz.common.database.entity

import androidx.room.TypeConverter
import com.buz.idl.user.bean.UserInfo
import com.interfun.buz.common.utils.gsonInstance

class DbConvertor {
    @TypeConverter
    fun jsonToUserInfo(userInfoJson: String?): UserInfo? {
        if (userInfoJson.isNullOrEmpty()) {
            return null
        }
        return gsonInstance.fromJson<UserInfo>(userInfoJson, UserInfo::class.java)
    }

    @TypeConverter
    fun userInfoToJson(info: UserInfo?): String? {
        if (info == null) {
            return null
        }
        return gsonInstance.toJson(info)
    }



}