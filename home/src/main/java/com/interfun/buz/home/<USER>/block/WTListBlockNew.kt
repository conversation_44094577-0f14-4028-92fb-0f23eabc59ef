package com.interfun.buz.home.view.block

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.View
import android.widget.Toast
import androidx.fragment.app.viewModels
import androidx.lifecycle.*
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.chat.ChatNavigation
import com.interfun.buz.chat.R
import com.interfun.buz.chat.ai.topic.RobotStatusHelper
import com.interfun.buz.chat.common.manager.ChatGlobalInfoRecorder
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.common.utils.GroupFlowTracker
import com.interfun.buz.chat.group.view.activity.GroupChatActivity
import com.interfun.buz.chat.group.view.dialog.GroupInfoDialog
import com.interfun.buz.chat.map.receive.view.LocationDetailActivity
import com.interfun.buz.chat.map.receive.view.model.LocationDetailInfo
import com.interfun.buz.chat.media.view.fragment.ChatMediaPreviewListFragment
import com.interfun.buz.chat.privy.view.activity.PrivateChatActivity
import com.interfun.buz.chat.wt.entity.*
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.*
import com.interfun.buz.chat.wt.manager.MessageState
import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.chat.wt.manager.WTSelectedItemManager
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.chat.wt.utils.WTTracker
import com.interfun.buz.chat.wt.viewmodel.HomeAIViewModel
import com.interfun.buz.common.R.anim
import com.interfun.buz.common.arouter.NavManager
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.BaseVoiceCallBindingBlock
import com.interfun.buz.common.bean.chat.ChatJumpType.WT
import com.interfun.buz.common.bean.chat.GroupChatJumpInfo
import com.interfun.buz.common.bean.chat.PrivateChatJumpInfo
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.constants.QRCodeScanSource.Home
import com.interfun.buz.common.constants.RouterParamKey.Common
import com.interfun.buz.common.constants.RouterParamKey.Group
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupUserStatus
import com.interfun.buz.common.eventbus.MuteStatusUpdateEvent
import com.interfun.buz.common.eventbus.ProfilePageEvent
import com.interfun.buz.common.eventbus.QueryGroupInfoSuccessEvent
import com.interfun.buz.common.eventbus.QueryUserInfoSuccessEvent
import com.interfun.buz.common.eventbus.group.GroupAddressUserInfoChangeEvent
import com.interfun.buz.common.eventbus.group.GroupInfoDidUpdateEvent
import com.interfun.buz.common.eventbus.group.GroupLeaveSuccessEvent
import com.interfun.buz.common.eventbus.user.AddBuzAccountEvent
import com.interfun.buz.common.eventbus.user.AddFriendEvent
import com.interfun.buz.common.eventbus.user.BlockUserEvent
import com.interfun.buz.common.eventbus.user.UserInfoUpdateEvent
import com.interfun.buz.common.eventbus.wt.WTDeleteFriendEvent
import com.interfun.buz.common.eventbus.wt.WTGroupMemberChangeEvent
import com.interfun.buz.common.eventbus.wt.WTListScrollEvent
import com.interfun.buz.common.gns.GmsStateHelper
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.utils.GooglePlayUtils
import com.interfun.buz.common.utils.TimeUtils
import com.interfun.buz.common.utils.TranslateTracker
import com.interfun.buz.common.utils.startLinkInBrowser
import com.interfun.buz.common.voicecall.GroupCallStaterImpl
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.common.widget.dialog.AddFriendBottomListDialog
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.dialog.EnableNotificationPromptDialog
import com.interfun.buz.common.widget.dialog.bottomlist.BottomListDialogOption
import com.interfun.buz.common.widget.dialog.bottomlist.CommonBottomListDialog
import com.interfun.buz.common.widget.dialog.bottomlist.DialogOptionType
import com.interfun.buz.common.widget.media.MediaUploadState
import com.interfun.buz.domain.record.entity.RecordBgType
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.domain.record.viewmodel.RecordVoiceViewModel
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.databinding.HomeItemWtGroupNewBinding
import com.interfun.buz.home.entity.NormalUrlResult
import com.interfun.buz.home.entity.RouterUrlResult
import com.interfun.buz.home.manager.WTLayoutManagerNew
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import com.interfun.buz.home.view.itemview.callback.WTGroupItemCallbackNew
import com.interfun.buz.home.view.itemview.callback.WTRobotItemCallbackNew
import com.interfun.buz.home.view.itemview.callback.WTUserItemCallbackNew
import com.interfun.buz.home.view.itemview.preview.HomeAddFriendPreviewItemView
import com.interfun.buz.home.view.itemview.preview.HomeMessagePreviewItemView
import com.interfun.buz.home.view.itemview.wtlist.WTAddFriendItemViewNew
import com.interfun.buz.home.view.itemview.wtlist.WTGroupItemViewNew
import com.interfun.buz.home.view.itemview.wtlist.WTRobotItemViewNew
import com.interfun.buz.home.view.itemview.wtlist.WTUserItemViewNew
import com.interfun.buz.home.view.utils.HomeEventTracker
import com.interfun.buz.home.view.utils.WTListHelperNew
import com.interfun.buz.home.view.utils.WTRvConstant
import com.interfun.buz.home.view.viewmodel.WTViewModelNew
import com.interfun.buz.im.signal.HeartBeatManager
import com.interfun.buz.im.signal.HeartBeatType
import com.interfun.buz.im.util.generatePushSenderInfo
import com.interfun.buz.liveplace.manager.LivePlaceCacheHelper
import com.interfun.buz.onair.bean.isChannelOpen
import com.interfun.buz.voicecall.util.VoiceCallTracker
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlin.LazyThreadSafetyMode.NONE

/**
 * <AUTHOR>
 * @date 2022/10/21
 * @desc
 */
class WTListBlockNew(
    private val fragment: ChatHomeFragmentNew,
    binding: ChatFragmentHomeNewBinding,
    private val joinVoiceCallAction: (channelId: Long, groupId: Long,  callType:Int, joinResultCallback: ((CallConflictState) -> Unit)?) -> Unit
) : BaseVoiceCallBindingBlock<ChatFragmentHomeNewBinding>(binding) {
    companion object {
        const val TAG = "WTListBlockNew"
        private const val TAG_DIALOG_ADD_FRIEND = "TAG_DIALOG_ADD_FRIEND"
    }

    private val bindingTop get() = fragment.bindingTop
    private val bindingCenter get() = fragment.bindingCenter
    private val bindingBottom get() = fragment.bindingBottom
    private val groupCallStaterImpl by lazy { GroupCallStaterImpl(fragment) }
    private lateinit var adapter: MultiTypeAdapter
    private val previewAdapter by lazy(NONE) { createPreviewAdapter() }
    private val listHelper = WTListHelperNew()
    private lateinit var layoutManager: WTLayoutManagerNew
    private val wtViewModel by fragment.viewModels<WTViewModelNew>()
    private val aiSelectorViewModel by fragment.activityViewModels<HomeAIViewModel>()
    private val recordViewModel by fragment.viewModels<RecordVoiceViewModel>()

    @SuppressLint("MissingPermission")
    private val networkWatcher = NetworkAvailableLiveData()

    private val singleContext = newSingleThreadContext("WTListBlock")

    private val rvWtList get() = bindingCenter.rvWtList
    private val rvMsgPreviewList get() = bindingCenter.rvMsgPreviewList
    private val rvConstant = WTRvConstant.instance

    private var lastNetState = true
    private var currentJumpTargetId: Long? = null

    private var addFriendDialog: AddFriendBottomListDialog? = null
    private val afDialogIsVisible
        get() = fragment.isAdded && fragment.childFragmentManager.findFragmentByTag(
            TAG_DIALOG_ADD_FRIEND
        ) != null

    private var unReadCountChangeObserver: Observer<Long>? = null
    private var isShowingAddFriendDialog = false

    private var currentPos = 0
        set(value) {
            log(TAG, "currentPos = $value")
            field = value
            updateSkipNextHaveUnreadMsgItemUi()

            if (value in 0 until adapter.itemCount) {
                val bean = adapter.getItem(value)
                if (bean is WTItemBean) {
                    if (!bean.isAddBtn) {
                        WTSelectedItemManager.setCurrentSelectedItem(bean)
                        if (afDialogIsVisible) addFriendDialog?.dismissAllowingStateLoss() // Prevent dialog from showing after creating a group via the dialog option
                    } else {
                        // Resumed state ==> Prevent from reopening add friend dialog at Profile/Contacts page
                        if (value == 0 && adapter.itemCount > 1 && fragment.lifecycle.currentState == Lifecycle.State.RESUMED) {
                            showAddFriendDialog()
                        } else if (afDialogIsVisible) {
                            addFriendDialog?.dismissAllowingStateLoss()
                        }
                    }
                    wtViewModel.wtCurrentItemStateFlow.emitInScope(
                        fragment.lifecycleScope,
                        bean
                    )
                    wtViewModel.setCurrentPos(value)
                }
            }
        }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        super.onStateChanged(source, event)
        logInfo(TAG, "onStateChanged==>fragment:${fragment},event:$event")
        when (event) {
            Lifecycle.Event.ON_PAUSE -> { // close panel when activity is paused
                removeAddFriendDialog()
            }

            else -> {}
        }
    }

    /**
     * 生命周期进入 pause 时，把动画播放全部暂停
     */
    suspend fun stopAllRobotAnime() {
        adapter.items.forEachIndexed { index, item ->
            if (item is WTItemBean && item.isRobot()) {
                item.userInfo?.let {
                    log(
                        TAG,
                        "stopAllRobotAnime index: $index, userName: ${it.userName} isPlaying: ${item.isPlaying} "
                    )
                }
                item.userStatusInfo.isPlaying = false
                adapter.notifyItemChanged(index)
            }
        }
    }

    override fun initView() {
        bindingCenter.viewWtCenterCircle.constraintSize(
            WTRvConstant.instance.portraitSize.toInt()
        )

        bindingCenter.clLeftJumpUnread.expandClickArea(10.dp, 15.dp)
        bindingCenter.clLeftJumpUnread.click {
            // 当 ptt 按钮已经按下的时候，不要继续执行点击事件
            if (RecordStatusHelper.isPressingOrLocking) {
                return@click
            }
            skipToNextLeftHaveUnreadMsgItem()
            ChatTracker.onClickSkipToHaveUnreadMsgConversation()
        }

        bindingCenter.clRightJumpUnread.expandClickArea(10.dp, 15.dp)
        bindingCenter.clRightJumpUnread.click {
            // 当 ptt 按钮已经按下的时候，不要继续执行点击事件
            if (RecordStatusHelper.isPressingOrLocking) {
                return@click
            }
            skipToNextRightHaveUnreadMsgItem()
            ChatTracker.onClickSkipToHaveUnreadMsgConversation()
        }
        removeAddFriendDialog()

        initAdapter()
        initRecyclerView()
    }

    private fun initRecyclerView() {
        rvWtList.apply {
            layoutManager = WTLayoutManagerNew(context, fragment) { pos ->
                log(TAG, "onSelectedListener pos: $pos")
                if (!fragment.isAdded) {
                    logInfo(TAG, "onSelectedListener !fragment.isAdded is true")
                    return@WTLayoutManagerNew
                }
                if (fragment.isDetached) {
                    logInfo(TAG, "onSelectedListener fragment.isDetached is true")
                    return@WTLayoutManagerNew
                }
                if (!rvWtList.isAttachedToWindow) {
                    logInfo(TAG, "onSelectedListener !binding.rvWtList.isAttachedToWindow is true")
                    return@WTLayoutManagerNew
                }
                currentPos = pos
            }.apply {
                <EMAIL> = this
            }
            layoutSize(
                width = rvConstant.rvWidth,
                height = rvConstant.rvHeight
            )
            val paddingWidth = ((rvConstant.rvWidth - rvConstant.itemWidth) / 2f).toInt()
            setPaddingRelative(paddingWidth, rvConstant.rvPaddingTop.toInt(), paddingWidth, 0)
            clipToPadding = false
            // addItemDecoration(LinearHorizontalSpaceItemDecoration(0.dp, paddingWidth, paddingWidth))
            itemAnimator = null
            itemAnimator?.moveDuration = 300
            adapter = <EMAIL>
        }
        rvMsgPreviewList.apply {
            layoutHeight(rvConstant.previewMsgListHeight)
            adapter = previewAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }
    }

    private fun initAdapter() {
        val wtUserItemView = WTUserItemViewNew(object : WTUserItemCallbackNew {
            override fun onItemClick(item: WTItemBean, pos: Int) {
                if (!checkIsItemInCenter(item, pos)) {
                    return
                }

                item.dispatch(user = { info ->
                    if (info.userId.isMe()) return@dispatch

                    if (info.serverRelation == BuzUserRelationValue.FRIEND.value) {
                        showPersonalProfile(info.userId)
                    } else {
                        showStrangerDialog(info)
                    }
                })
                ChatTracker.postContactFriendClickEvent(
                    if ((item.data.conversation?.unreadCount ?: 0) > 0) "unread" else "read"
                )
            }

            override fun onAddClick(item: WTItemBean, pos: Int) {
                logInfo(TAG, "onAddClick, id:${item.targetId}, pos: $pos")
                if (!checkIsItemInCenter(item, pos)) {
                    logInfo(TAG, "checkIsItemInCenter return false")
                    return
                }
                item.dispatch(user = { info ->
                    ChatTracker.onClickAddFriendInHomePage(info.userId)
                    logInfo(
                        TAG,
                        "onAddClick, id:${item.targetId}, pos: $pos, relation: ${info.serverRelation}"
                    )
                    when (info.serverRelation) {
                        BuzUserRelationValue.NO_RELATION.value -> {
                            wtViewModel.requestAddFriend(info.userId)
                        }

                        BuzUserRelationValue.BEING_FRIEND_REQUEST.value -> {
                            wtViewModel.requestAcceptFriend(info.userId)
                        }

                        else -> {}
                    }
                })
            }
        })
        val wtRobotItemView = WTRobotItemViewNew(object : WTRobotItemCallbackNew {
            override fun onItemClick(item: WTItemBean, pos: Int) {
                if (!checkIsItemInCenter(item, pos)) {
                    return
                }
                item.dispatch(user = { info ->
                    showPersonalProfile(info.userId)
                })
                ChatTracker.postContactFriendClickEvent(
                    if ((item.data.conversation?.unreadCount ?: 0) > 0) "unread" else "read"
                )
            }
        },wtViewModel)
        val wtGroupItemView = WTGroupItemViewNew(object : WTGroupItemCallbackNew {
            override fun onItemClick(item: WTItemBean, pos: Int) {
                if (!checkIsItemInCenter(item, pos)) {
                    return
                }
                item.dispatch(group = { info ->
                    if (info.userStatus == GroupUserStatus.InGroup.value) {
                        LivePlaceCacheHelper.updateExistsInfoToMem(
                            targetId = info.groupId,
                            placeType = PlaceType.GROUP
                        )
                        val dialog = GroupInfoDialog.newInstance(info.groupId)
                        dialog.showDialog(fragment.activity)
                    } else {
                        showRemoveGroupDialog(info.groupId)
                    }

                })
                ChatTracker.onHomeGroupClick()
            }


            override fun onJoinRealTimeCall(
                binding: HomeItemWtGroupNewBinding,
                item: WTItemBean,
                pos: Int,
                joinCallback: ((CallConflictState) -> Unit)?
            ) {

                val groupChannelInfo =
                    ChannelStatusManager.getConvChannelInfo(item.targetId ?: 0) ?: return
                val channelId = groupChannelInfo.channelId
                val groupId = item.targetId ?: return
                val maxNumber = AppConfigRequestManager.groupCallMaxMemberNum

                VoiceCallTracker.appClickAC2025031205(groupChannelInfo.vcCallType,"homepage","${channelId}")
                VoiceCallTracker.onClickGroupRealTimeCallHome(
                    groupId.toString()
                )

                // 如果网络不可用
                if (!isNetworkAvailable) {
                    toastSolidWarning(R.string.network_error)
                    return
                }

                // 检查人数并toast
                if ((groupChannelInfo.channelInfo?.memberCount ?: 0) >= maxNumber) {
                    val message =  ResUtil.getString(R.string.call_supports_maximum_tips,maxNumber.toString())
                    toastIconFontMsg(
                        message = message,
                        textColor = com.interfun.buz.voicecall.R.color.text_white_default.asColor(),
                        iconFont = com.interfun.buz.voicecall.R.string.ic_warning_solid.asString(),
                        iconFontColor = com.interfun.buz.voicecall.R.color.text_white_important.asColor(),
                        gravity = Gravity.CENTER,
                        duration = Toast.LENGTH_SHORT,
                        style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
                    )
                    // wtViewModel.getGroupRealTimeCallInfo(groupId)
                    return
                }



                binding.tvCallConnecting.visible()
                binding.flGroupJoinRealTimeCall.gone()
                val flOnlineMembers = listOf(
                    binding.flGroupOnlineMember1,
                    binding.flGroupOnlineMember2,
                    binding.flGroupOnlineMember3,
                    binding.flGroupOnlineMember4,
                    binding.flGroupOnlineMember5,
                    binding.flGroupRealTimeCallNumber
                )
                flOnlineMembers.forEach {
                    it.gone()
                }
                binding.tvGroupMemberCount.gone()

                joinVoiceCallAction(channelId, groupId,groupChannelInfo.vcCallType) {
                    joinCallback?.invoke(it)
//                    if (it != CallConflictState.NO_CONFLICT) {
                        // 失败，刷新
                        wtViewModel.getConvLivePlacePreview(groupId, 2)
//                    }
                }
            }
        })
        val wtAddFriendItemView = WTAddFriendItemViewNew(
            openAddFriendDialogCallback = { pos ->
                // 当 ptt 按钮已经按下的时候，不要继续执行点击事件
                if (RecordStatusHelper.isPressingOrLocking) {
                    return@WTAddFriendItemViewNew
                }
                scrollByPosition(pos)
                WTTracker.onClickAddFriendWTPage()
                VibratorUtil.vibrator("showAddFriendDialog")
                showAddFriendDialog()
            },
            object : WTAddFriendItemViewNew.GetAddFriendDialogStateCallback {
                override fun isDialogAdded() = afDialogIsVisible
            }
        )
        log(TAG, "wtListContainer list size:${wtViewModel.wtListContainer.size}")
        val list = ArrayList(wtViewModel.wtListContainer.list)
        adapter = MultiTypeAdapter(list).apply {
            register(WTItemBean::class.java)
                .to(wtUserItemView, wtGroupItemView, wtRobotItemView, wtAddFriendItemView)
                .withKotlinClassLinker { _, item ->
                    if (item.type == WTItemType.AddFriend) {
                        WTAddFriendItemViewNew::class
                    } else {
                        if (item.isGroup) {
                            WTGroupItemViewNew::class
                        } else {
                            val robot = item.userInfo?.isRobot
                            if (robot == true) {
                                WTRobotItemViewNew::class
                            } else {
                                WTUserItemViewNew::class
                            }
                        }
                    }
                }
            var lastSize = -1
            fun onItemCountChange() {
                if (adapter.itemCount != lastSize) {
                    lastSize = adapter.itemCount
                    logInfo(TAG, "adapter.itemCount change size: $lastSize")
                    rvWtList.invalidateItemDecorations()
                }
                (adapter.itemCount > 1).let { hasFriend ->
                    bindingCenter.viewWtCenterCircle.inVisibleIf(!hasFriend)
                }
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onChanged() {
                    super.onChanged()
                    log(TAG, "onChanged")
                    scrollByTargetId()
                    onItemCountChange()
                }

                override fun onItemRangeChanged(positionStart: Int, itemCount: Int) {
                    super.onItemRangeChanged(positionStart, itemCount)
                    if (itemCount > 1) {
                        scrollByTargetId()
                    } else if (positionStart == currentPos) {
                        log(TAG, "onItemRangeChanged positionStart:$positionStart")
                        currentPos = positionStart
                    }
                }

                override fun onItemRangeChanged(positionStart: Int, itemCount: Int, payload: Any?) {
                    super.onItemRangeChanged(positionStart, itemCount, payload)
                    log(
                        TAG,
                        "onItemRangeChanged positionStart:$positionStart itemCount:$itemCount payload:${(payload as? WTPayloadTypes)?.types}"
                    )
                }

                override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                    super.onItemRangeInserted(positionStart, itemCount)
                    log(
                        TAG,
                        "onItemRangeInserted positionStart:$positionStart itemCount:$itemCount"
                    )
                    scrollByTargetId()
                    onItemCountChange()
                }

                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    log(
                        TAG,
                        "onItemRangeMoved fromPosition:$fromPosition toPosition:$toPosition itemCount:$itemCount"
                    )
                    scrollByTargetId()
                }

                override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
                    super.onItemRangeRemoved(positionStart, itemCount)
                    log(TAG, "onItemRangeRemoved positionStart:$positionStart itemCount:$itemCount")
                    scrollByTargetId()
                    onItemCountChange()
                }
            })
        }
    }

    private fun bindPreviewExposed() {
        combine(
            fragment.viewLifecycleOwner.lifecycle.eventFlow,
            wtViewModel.currentPreviewItem.distinctUntilChanged { old, new ->
                if (old !is ConversationPreview || new !is ConversationPreview) {
                    return@distinctUntilChanged old == new
                } else {
                    old.msgPreview?.baseMsgInfo?.msgId == new.msgPreview?.baseMsgInfo?.msgId
                }
            }) { lifecycleState, preview ->
            if (lifecycleState.targetState.isAtLeast(Lifecycle.State.RESUMED)) {
                preview
            } else {
                null
            }
        }.collectIn(fragment.viewLifecycleOwner) { preview ->
            if (preview !is ConversationPreview) {
                return@collectIn
            }
            val msgPreview = preview.msgPreview ?: return@collectIn
            when (msgPreview) {
                is HomeMsgPreviewModel.HyperlinkPreview,
                is HomeMsgPreviewModel.ImageVoiceEmojiPreview,
                is HomeMsgPreviewModel.UnicodeVoiceEmojiPreview,
                is HomeMsgPreviewModel.VoicePreview,
                is HomeMsgPreviewModel.VoiceTextPreview,
                is HomeMsgPreviewModel.ImagePreview,
                is HomeMsgPreviewModel.LocationPreview,
                is HomeMsgPreviewModel.UnSupportMsgPreview,
                is HomeMsgPreviewModel.VideoPreview -> {
                    HomeEventTracker.onPreviewAreaExposed(msgPreview)
                }

                else -> {}
            }
        }
    }

    private fun updateSkipNextHaveUnreadMsgItemUi() {
        if (!fragment.lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) return

        fragment.viewLifecycleScope.launch {
            val leftItemLastUnreadIndex = withIOContext {
                adapter.items.lastIndexOf(0, currentPos - 1) {
                    it is WTItemBean && isItemHaveUnreadMsg(it)
                }
            }
            val leftItemLastMentionMeIndex = withIOContext {
                adapter.items.lastIndexOf(0, currentPos - 1) {
                    it is WTItemBean && isItemHasMentionedMe(it)
                }
            }
            bindingCenter.clLeftJumpUnread.visibleIf(leftItemLastUnreadIndex>=0)
            bindingCenter.iftvLeftSkipUnreadItemMsg.text = if (leftItemLastMentionMeIndex > 0) {
                R.string.ic_at_icon.asString()
            } else {
                R.string.ic_wt.asString()
            }
            bindingCenter.iftvLeftSkipUnreadItemMsg.setTextColor(if (leftItemLastMentionMeIndex > 0) {
                R.color.color_text_white_important.asColor()
            } else {
                R.color.color_text_white_tertiary.asColor()
            })

            val jumpToLeftIndex = when {
                leftItemLastUnreadIndex > 0 && leftItemLastMentionMeIndex <= 0 -> leftItemLastUnreadIndex
                leftItemLastMentionMeIndex < leftItemLastUnreadIndex -> leftItemLastMentionMeIndex
                else -> leftItemLastUnreadIndex
            }

            bindingCenter.clLeftJumpUnread.tag = jumpToLeftIndex

            if (currentPos >= adapter.itemCount - 1) {
                bindingCenter.clRightJumpUnread.gone()
                bindingCenter.clRightJumpUnread.tag = -1
                return@launch
            }

            val rightItemFirstUnreadIndex = withIOContext {
                adapter.items.firstIndexOf(currentPos + 1, adapter.itemCount-1) {
                    it is WTItemBean && isItemHaveUnreadMsg(it)
                }
            }

            val rightItemFirstMentionMeIndex = withIOContext {
                adapter.items.firstIndexOf(currentPos + 1, adapter.itemCount-1) {
                    it is WTItemBean && isItemHasMentionedMe(it)
                }
            }
            bindingCenter.clRightJumpUnread.visibleIf(rightItemFirstUnreadIndex >= 0)
            bindingCenter.iftvRightSkipUnreadItemMsg.text = if (rightItemFirstMentionMeIndex > 0) {
                R.string.ic_at_icon.asString()
            } else {
                R.string.ic_wt.asString()
            }
            bindingCenter.iftvRightSkipUnreadItemMsg.setTextColor(if (rightItemFirstMentionMeIndex > 0) {
                R.color.color_text_white_important.asColor()
            } else {
                R.color.color_text_white_tertiary.asColor()
            })

            val jumpToRightIndex = when {
                rightItemFirstUnreadIndex >= 0 && rightItemFirstMentionMeIndex < 0 -> rightItemFirstUnreadIndex
                rightItemFirstMentionMeIndex > rightItemFirstUnreadIndex -> rightItemFirstMentionMeIndex
                else -> rightItemFirstUnreadIndex
            }

            bindingCenter.clRightJumpUnread.tag = jumpToRightIndex

            if (!bindingCenter.clLeftJumpUnread.isVisible() && !bindingCenter.clRightJumpUnread.isVisible()) {
                return@launch
            }

            val currentYMD = TimeUtils.formatTimestamp(System.currentTimeMillis())
            if (CommonMMKV.trackSkipUnreadConvViewTime != currentYMD) {
                CommonMMKV.trackSkipUnreadConvViewTime = currentYMD
                ChatTracker.onSkipToHaveUnreadMsgConversationViewExposure()
            }
        }
    }

    private fun bindLinkPreview() {
        wtViewModel.loadingUrlParsing.collectIn(fragment.viewLifecycleOwner) {
            if (it) {
                fragment.showDataLoading()
            } else {
                fragment.hideDataLoading()
            }
        }
        wtViewModel.parseUrlResult.collectIn(fragment.viewLifecycleOwner) { result ->
            val activity = fragment.activity ?: return@collectIn
            when (result) {
                is NormalUrlResult -> startLinkInBrowser(link = result.url, context = activity)
                is RouterUrlResult -> RouterManager.handle(activity, result.router)
            }
        }
    }

    private fun bindJumpToChatList() {
        wtViewModel.toChatListFlow.collectIn(fragment.viewLifecycleOwner) { (convType, targetId) ->
            goToChatList(convType, targetId = targetId)
        }
    }

    private fun createPreviewAdapter(): MultiTypeAdapter {
        val adapter = MultiTypeAdapter().apply {
            register(
                ConversationPreview::class.java,
                HomeMessagePreviewItemView(wtViewModel.wtCurrentItemStateFlow).apply {
                    addOnItemClickListener { holder, item, pos ->
                        goToChatList(item.convType, item.convInfo.convTargetId)
                        VibratorUtil.vibrator("MessagePreviewItemClick")
                        HomeEventTracker.onClickPreviewItem(
                            item.convType == IM5ConversationType.GROUP,
                            "whole_dialog"
                        )
                    }
                    onClickChat = { item ->
                        goToChatList(item.convType, item.convInfo.convTargetId)
                        VibratorUtil.vibrator("MessagePreviewItemClick")
                        HomeEventTracker.onClickPreviewItem(
                            item.convType == IM5ConversationType.GROUP,
                            "pressing_chat"
                        )
                    }
                    onClickUnread = { item ->
                        goToChatList(item.convType, item.convInfo.convTargetId)
                        VibratorUtil.vibrator("MessagePreviewItemClick")
                        HomeEventTracker.onClickPreviewItem(
                            item.convType == IM5ConversationType.GROUP,
                            "pressing_unread"
                        )
                    }
                    onClickLink = { linkPreview ->
                        linkPreview.linkUrl?.let { wtViewModel.parseIMLink(it) }
                        wtViewModel.clickPreviewArea(linkPreview)
                    }
                    onClickTextLink = { link ->
                        link?.let { wtViewModel.parseIMLink(it) }
                    }
                    onClickItem = { item ->
                        goToChatList(item.baseMsgInfo.convType, item.baseMsgInfo.convTargetId)
                        VibratorUtil.vibrator("MessagePreviewItemClick")
                    }
                    onPreviewAreaClickListener = { previewMsg ->
                        wtViewModel.clickPreviewArea(previewMsg)
                        when (previewMsg) {
                            is UnSupportMsgPreview -> {
                                showGoGooglePlayDialog()
                            }
                            is FilePreview -> {
                                wtViewModel.previewFile(fragment.requireContext, previewMsg)
                            }

                            else -> {}
                        }
                    }
                    onTranslate = { baseMsgInfo ->
                        wtViewModel.translateMessage(baseMsgInfo, true)
                        TranslateTracker.onClickHomeTranslateButton(
                            baseMsgInfo.convType,
                            baseMsgInfo.convTargetId.toString()
                        )
                    }
                    onClickFileUploadButton = { uploadState, filePreview ->
                        when(uploadState) {
                            MediaUploadState.PAUSE -> {
                                // resume upload
                                // Currently, only file message has the logic of resending message in the home page
                                wtViewModel.resendMessage(
                                    convType = filePreview.baseMsgInfo.convType,
                                    msgId = filePreview.baseMsgInfo.msgId
                                )
                            }
                            MediaUploadState.LOADING, MediaUploadState.PROGRESSING -> {
                                // pause upload
                                wtViewModel.cancelSendingMessage(
                                    convType = filePreview.baseMsgInfo.convType,
                                    msgId = filePreview.baseMsgInfo.msgId
                                )
                            }
                            else -> {
                                //do nothing
                            }
                        }
                    }
                    onClickFileDownloadButton = { filePreview ->
                        wtViewModel.onClickFileDownload(fragment.requireContext, filePreview)
                    }
                })
            register(
                HomeAddFriendPreviewItemView(
                    openAddFriendDialogCallback = {
                        WTTracker.onClickAddFriendWTPage()
                        showAddFriendDialog()
                    },
                    object : WTAddFriendItemViewNew.GetAddFriendDialogStateCallback {
                        override fun isDialogAdded() = afDialogIsVisible
                    })
            )
        }
        return adapter
    }

    private fun showConfirmDownloadFileDialog(onConfirm: () -> Unit) {
        CommonAlertDialog(
            context = fragment.requireContext,
            tips = R.string.confirm_download_dialog_title.asString(),
            positiveText = R.string.download_anyway.asString(),
            negativeText = R.string.cancel.asString(),
            positiveButtonType = CommonButton.TYPE_SECONDARY_MEDIUM,
            multiOptions = listOf(
                {
                    setText(R.string.download_anyway.asString())
                    setType(CommonButton.TYPE_SECONDARY_MEDIUM)
                    click {
                        it.dismiss()
                        onConfirm.invoke()
                    }
                },
                {
                    setText(R.string.cancel.asString())
                    setBackgroundColor(R.color.overlay_grey_10.asColor())
                    click {
                        it.dismiss()
                    }
                },
            )
        ).show()
    }

    private fun goToChatList(conversationType: IM5ConversationType, targetId: Long) {
        val activity = fragment.activity ?: return
        if (conversationType == IM5ConversationType.GROUP) {
            NavManager.startGroupChatActivity(
                activity,
                GroupChatJumpInfo(
                    targetId,
                    type = WT,
                    addressUserInfo = aiSelectorViewModel.getCurrentAddressBot(targetId)
                )
            )
        } else {
            NavManager.startPrivateChatActivity(
                activity,
                PrivateChatJumpInfo(targetId, type = WT)
            )
        }
    }

    private fun showGoGooglePlayDialog() {
        val context = fragment.context ?: return
        CommonAlertDialog(context,
            title = R.string.chat_update_require.asString(),
            tips = R.string.chat_update_description.asString(),
            negativeText = R.string.cancel.asString(),
            negativeCallback = {
                it.dismiss()
            },
            positiveText = R.string.sure.asString(),
            positiveCallback = {
                GooglePlayUtils.openGooglePlayStore(context)
                it.dismiss()
            }).show()
    }

    private fun skipToNextLeftHaveUnreadMsgItem() {
        val leftItemIndex = bindingCenter.clLeftJumpUnread.tag as? Int ?: return
        logInfo(TAG, "skipToNextLeftHaveUnreadMsgItem: leftItemIndex:$leftItemIndex")
        if (leftItemIndex < 0 || leftItemIndex > adapter.items.size - 1) return
        scrollByPosition(pos = leftItemIndex)
        VibratorUtil.vibrator(from = "$TAG skipToNextLeftHaveUnreadMsgItem")
    }

    private fun skipToNextRightHaveUnreadMsgItem() {
        val rightItemIndex = bindingCenter.clRightJumpUnread.tag as? Int ?: return
        logInfo(TAG, "skipToNextRightHaveUnreadMsgItem:rightItemIndex:$rightItemIndex ")
        if (rightItemIndex < 0 || rightItemIndex > adapter.items.size - 1) return
        scrollByPosition(pos = rightItemIndex)
        VibratorUtil.vibrator(from = "$TAG skipToNextRightHaveUnreadMsgItem")
    }

    private suspend fun isItemHaveUnreadMsg(item: WTItemBean): Boolean {
        if (!item.isConversation) return false
        val conversation = item.data.conversation
        if (conversation.isNull()) return false
        val haveNotPlayCount = conversation!!.notPlayedCount > 0
        return haveNotPlayCount && !item.isRobotNShowTopic() && !item.isTranslator()
    }

    private suspend fun isItemHasMentionedMe(item: WTItemBean): Boolean {
        if (!item.isConversation) return false
        val conversation = item.data.conversation
        if (conversation.isNull()) return false
        val hasMentionedMe = conversation!!.unreadCount > 0 && conversation!!.mentionMeCount > 0
        return hasMentionedMe && !item.isRobotNShowTopic() && !item.isTranslator()
    }

    private fun checkIsItemInCenter(item: WTItemBean, pos: Int): Boolean {
        // 当 ptt 按钮已经按下的时候，不要继续执行点击事件
        if (RecordStatusHelper.isPressingOrLocking) {
            return false
        }
        val currentTargetId = wtViewModel.getCurrentItemTargetId()
        return if (currentTargetId != item.targetId) {
            scrollByPosition(pos = pos)
            false
        } else {
            true
        }
    }


    private fun startLeaveMsgChatPage(item: WTItemBean) {
        item.dispatch(user = {
            if (topActivity is PrivateChatActivity && currentJumpTargetId == item.targetId)
                return@dispatch
            NavManager.startPrivateChatActivity(
                fragment.activity,
                PrivateChatJumpInfo(it.userId, WT, it)
            )
            WTTracker.postUnreadMsgClick("private", it.userId.toString())
        }, group = {
            if (topActivity is GroupChatActivity && currentJumpTargetId == item.targetId)
                return@dispatch
            NavManager.startGroupChatActivity(
                fragment.activity,
                GroupChatJumpInfo(it.groupId, WT, it)
            )
            WTTracker.postUnreadMsgClick("group", it.groupId.toString())
        })
        currentJumpTargetId = item.targetId
    }

    private fun showStrangerDialog(userInfo: UserRelationInfo) {
        CommonBottomListDialog.build {
            addDefaultOption(R.string.ic_contacts_solid, R.string.profile) {
                showPersonalProfile(userInfo.userId)
            }
            if (!UserRelationCacheManager.isUserMyFriend(userInfo.userId)) {
                addWarningOption(
                    R.string.ic_remove_form_chat_list,
                    R.string.profile_remove_from_list,
                    iconSize = 22f
                ) {
                    wtViewModel.deleteItemByTargetId(userInfo.userId, IM5ConversationType.PRIVATE)
                }
            }
        }.showDialog(fragment.activity)
    }

    override fun initData() {
        super.initData()
        WTStatusManager.updateWtList(wtViewModel.wtListContainer.list)
        /**
         * update list when app kill by user and network available
         */
        networkWatcher.observe(fragment.viewLifecycleOwner) { hasNetwork ->
            log(TAG, "initData: on network change $hasNetwork")
            if (lastNetState == false && hasNetwork) {
                routerServices<ContactsService>().value?.getFriendListAndUpdateDB(true)
            }
            lastNetState = hasNetwork
        }
        routerServices<ContactsService>().value?.getFriendListAndUpdateDB(true)
        initEventBus()
        wtViewModel.addConversationsObserver()
        wtViewModel.homeList.collectLatestIn(fragment.viewLifecycleOwner) { homeList ->
            val (mainList, previewList) = homeList
            log(TAG, "WTViewModel notify ${mainList.size}，previewistSize:${previewList.size}")
            listHelper.submitItems(adapter, previewAdapter, homeList)
            WTStatusManager.firstItemInList = mainList.firstOrNull()
            //首页列表加载刷新完成后，需要判断当前是否有正在播放中的item, 如果有则需要更新播放状态显示
            delayInViewScope(fragment, 100) {
                val (msg, state) = WTMessageManager.messageFlow.value
                if (msg != null && state == MessageState.PLAYING && !msg.isDirectionSend()) {
                    log(TAG, "首页列表刷新后 检查到有正在播放中的item...")
                    updateTargetPlayingStatusFromWTMessageFlow(msg)
                }
            }
            if (wtViewModel.requestChatListStatus == WTViewModelNew.RequestChatListStatus.REQUEST_DONE) {
                if (GlobalEventManager.homesRouterTarget.value.isNotNull()) {
                    wtViewModel.lastSelectTargetId =
                        GlobalEventManager.homesRouterTarget.value?.first
                    scrollByTargetId(
                        wtViewModel.lastSelectTargetId,
                        scrollToFirstIfNotFound = false
                    )
                    GlobalEventManager.homesRouterTarget.postValue(null)
                }
            }
        }

        wtViewModel.wtItemChangeLiveData.observe(fragment.viewLifecycleOwner) {
            wtViewModel.needUpdateList.forEach { updateItem ->
                logInfo(
                    TAG,
                    "initData:on wtItemChangeLiveData change ${updateItem.first} ${updateItem.second}"
                )
                if (updateItem.first in 0 until adapter.itemCount) {
                    adapter.notifyItemChanged(updateItem.first, updateItem.second)
                }
            }
            wtViewModel.needUpdateList.clear()
        }

        val observer = Observer<Long> {
            val updateIndex = adapter.items.indexOfFirst { item ->
                item is WTItemBean && item.targetId == it
            }
            log(TAG, "initListener: wtItemUnReadCountChangeLiveData $it,index:$updateIndex")
            if (updateIndex < 0) return@Observer
            adapter.notifyItemChanged(updateIndex, WTPayloadType.UpdateUnreadCount)
            updateSkipNextHaveUnreadMsgItemUi()
        }
        unReadCountChangeObserver = observer
        wtViewModel.wtItemUnReadCountChangeLiveData.observeForever(observer)

        wtViewModel.wtCurrentItemStateFlow.collectIn(
            fragment.viewLifecycleOwner,
            lifecycleState = Lifecycle.State.CREATED
        ) {
            if (it == null) {
                logInfo(TAG,"wtCurrentItemStateFlow return it == null")
                return@collectIn
            }
            val trackItemType = if (it.userInfo?.isOfficialAccount.getBooleanDefault()) {
                "official_account"
            } else {
                if (it.userInfo?.isRobot == true) {
                    "robot"
                } else if (it.type == WTItemType.ConversationFriend) {
                    "private"
                } else {
                    "group"
                }
            }

            if (trackItemType.isNotEmpty()) {
                WTTracker.onScrollOrClickWTItem(trackItemType, it.targetId)
            }

            log(TAG, "currentItem = ${it.targetId} pos = ${findPositionByTargetId(it.targetId)}")
            WTStatusManager.currentSelectedItem = it
            if (wtViewModel.lastSelectTargetId != null || it.targetId != WTListContainer.ADD_BTN_ID) {
                wtViewModel.lastSelectTargetId = it.targetId
            }
            HeartBeatManager.unsubscribe(HeartBeatType.WT_ONLINE_GROUP_SELECTED)
            HeartBeatManager.unsubscribe(HeartBeatType.T_GROUP_CHAT)
            if (it.isGroup && it.targetId != null) {
                wtViewModel.getGroupOnlineMembers(it.targetId.getLongDefault())
                HeartBeatManager.subscribe(
                    HeartBeatType.WT_ONLINE_GROUP_SELECTED,
                    it.targetId.toString()
                )
                HeartBeatManager.subscribe(HeartBeatType.T_GROUP_CHAT, it.targetId.toString())
                // update on air info
                wtViewModel.getConvLivePlacePreview(it.targetId.getLongDefault(), 2)
            }
            if (it.targetId != null && it.isUser) {
                wtViewModel.getUserRelation(it.targetId.getLongDefault(), it.isStranger)
            }
            wtViewModel.updateSpeakingStatus(layoutManager.isSpeaking)

            val itemView = layoutManager.findViewByPosition(currentPos)
            if (itemView?.findViewById<View>(R.id.flGroupJoinRealTimeCall)?.isVisible() == true) {
                logDebug(TAG, "initData: itemView(flGroupJoinRealTimeCall) is visible")
                VoiceCallTracker.onHomeGroupJoinExposure(it.targetId.toString())
            }

            if (!it.isAddBtn && afDialogIsVisible) addFriendDialog?.dismissAllowingStateLoss()
            updateWalkieTalkieBackground(it)
        }

        wtViewModel.wtGroupMemberChangeSharedFlow.collectIn(fragment.viewLifecycleOwner) {
            val pos = findPositionByTargetId(it)
            log(TAG, "updateGroupMember pos = ${pos}")
            if (pos in 0 until adapter.itemCount) {
                adapter.notifyItemChanged(pos, WTPayloadType.UpdateGroupMemberStatus)
            }
        }

        wtViewModel.wtStrangerAddFriendStateFlow.collectIn(fragment.viewLifecycleOwner) {
            fragment.lifecycleScope.launch {
                delay(500)
                if (isActive) {
                    fragment.activity?.let { EnableNotificationPromptDialog.showIfValid(it) }
                }
            }
        }
        wtViewModel.wtRelationStateFlow.collectIn(fragment.viewLifecycleOwner) { relationInfo ->
            val uid = relationInfo.userId
            val relation = relationInfo.serverRelation
            logInfo(
                TAG,
                "updateStrangerAddFriendState pos = ${findPositionByTargetId(uid)}, and relation = ${relation}"
            )
            val item = findItemByTargetId(uid)
            if (item != null && item is WTItemBean) {
                item.changeType(relation)
                val pos = adapter.items.indexOf(item)
                if (pos in 0 until adapter.itemCount) {
                    adapter.notifyItemChanged(pos, WTPayloadType.UpdateUserRelation)
                }
            }
        }

        recordViewModel.isRecordingFlow.collectIn(fragment.viewLifecycleOwner) { isSpeaking ->
            if (layoutManager.isSpeaking != isSpeaking) {
                wtViewModel.updateSpeakingStatus(isSpeaking)
            }
            layoutManager.isSpeaking = isSpeaking
        }

        //Monitor and display the item animation during playback
        WTMessageManager.messageFlow.collectIn(fragment.viewLifecycleOwner) { (msg, state) ->
            if (msg.isDirectionSend()) return@collectIn
            if (msg == null || state != MessageState.PLAYING) {
                updatePlayingStatus(null)
            } else {
                updateTargetPlayingStatusFromWTMessageFlow(msg)
            }
        }
        observeAddressedInfo()
        observeGroupWaitingAIState()
        observeLivePlaceStatus()
        bindLocationPreview()
        bindMediaPreview()
        bindPreviewExposed()
        bindJumpToChatList()
        bindLinkPreview()
        bindFileDownloadConfirmDialog()
    }

    private fun bindMediaPreview() {
        wtViewModel.previewMediaFlow.collectIn(fragment.viewLifecycleOwner) { args ->
            val mediaFragment = ChatMediaPreviewListFragment.newInstance(args)
            fragment.childFragmentManager.beginTransaction()
                .replace(binding.flMediaPreview.id, mediaFragment, "videoPreview").commitNow()
        }
    }

    private fun bindLocationPreview() {
        wtViewModel.previewLocationFlow.collectIn(fragment.viewLifecycleOwner) { (targetId, location) ->
            openLocation(targetId, location)
        }
    }

    private fun openLocation(targetId: String, item: LocationDetailInfo) {
        if (GmsStateHelper.gmsAvailable()) {
            val intent = LocationDetailActivity.newInstance(
                fragment.requireContext,
                item, targetId, item.conversationType
            )
            fragment.requireActivity().startActivity(intent)
        } else {
            MapLocationHelper.openMap(
                fragment.requireActivity(),
                item.buzLocation.lat,
                item.buzLocation.lon,
                item.buzAddressBean.featureName
            )
        }
    }

    private fun observeLivePlaceStatus() {
        fragment.viewLifecycleScope.launch {
            ChannelStatusManager.convChannelInfoChangeList.collect { changeInfoList->
                val hasChangedIdList = mutableListOf<Long>()
                changeInfoList.forEach { changeInfo ->
                    logInfo(TAG, "observeLivePlaceStatus: $changeInfo")
                    if (changeInfo.hasChanged) {
                        hasChangedIdList.add(changeInfo.convTargetId)
                    }
                    val updateIndex = adapter.items.indexOfFirst { item ->
                        item is WTItemBean && item.targetId == changeInfo.convTargetId
                    }
                    if (updateIndex in 0 until adapter.itemCount) {
                        adapter.notifyItemChanged(updateIndex, WTPayloadType.UpdateLivePlaceStatus)
                    }
                }
                val openedList = ChannelStatusManager.getConvChannelInfoList(hasChangedIdList)
                    .filter { it.isChannelOpen() && it.convType == IM5ConversationType.PRIVATE.value }

                // 私聊会话在开启LP时，客态自己插入一条居中消息，告知空间开启，并且顺便将会话移到首位
                // 群聊不需要这么做，因为已经有居中消息了
                wtViewModel.updateLivePlaceOpenStatus(
                    openedList = openedList,
                    lifecycleOwner = fragment.viewLifecycleOwner,
                )
                // IMAgent.updateMultipleConversationsTime(updateConvTimeList)
            }
        }
    }

    override fun doOnRoomExist(room: VoiceCallRoom) {
        super.doOnRoomExist(room)
        // if room state changed, update
        val pos = currentPos
        val item = adapter.items.getOrNull(pos) as? WTItemBean ?: return
        if (item.isGroup) {
            // update voice call state
            adapter.notifyItemChanged(pos, WTPayloadType.UpdateRealTimeCallUserList)
        }
    }

    private suspend fun updateWalkieTalkieBackground(item: WTItemBean) {
        if (item.isGroup.not() && item.targetId.isRobotSuspend()) {
            recordViewModel.updateRecordBgType(RecordBgType.Robot)
            logInfo(TAG, "updateWalkieTalkieBackground return 1")
            return
        }

        val groupId = item.groupInfo?.groupId
        if (groupId.isNotNull() && null != aiSelectorViewModel.getCurrentAddressBot(groupId!!)) {
            recordViewModel.updateRecordBgType(RecordBgType.AddressBot)
            logInfo(TAG, "updateWalkieTalkieBackground return 2")
            return
        }

        recordViewModel.updateRecordBgType(RecordBgType.Normal)
        logInfo(TAG, "updateWalkieTalkieBackground showNormalBackground")
    }

    private fun observeGroupWaitingAIState() {
        fragment.viewLifecycleScope.launch {
            RobotStatusHelper.groupWaitingChangeFlow.collect {
                wtViewModel.updateGroupWaitingAIState(it)
            }
        }
        fragment.viewLifecycleScope.launch {
            RobotStatusHelper.groupWaitingClearFlow.collect {
                adapter.notifyDataSetChanged()
            }
        }
    }

    private fun observeAddressedInfo() {
        combine(
            wtViewModel.currentTargetIdFlow,
            aiSelectorViewModel.addressedBotInfoFlow
        ) { currentId, addressedAI ->
            if (currentId != addressedAI?.groupId) {
                null
            } else {
                addressedAI
            }
        }.collectIn(fragment.viewLifecycleOwner) { info ->
            updateAddressedUser(info?.groupId, info?.botUserInfo)
        }
    }

    private fun initEventBus() {
        // Subscribe to the event of block user
        BusUtil.observe<BlockUserEvent>(fragment.viewLifecycleOwner) {
            log(TAG, "Subscribe to the event of block userId: ${it.userId}, type: ${it.type}")
            wtViewModel.updateBlockUser(it.userId, it.type)
        }

        // Subscribe the event for leave group
        BusUtil.observe<GroupLeaveSuccessEvent>(fragment.viewLifecycleOwner) {
            log(TAG, "Subscribe the event for leave group, groupId = ${it.groupId}")
            wtViewModel.deleteItemByTargetIdList(listOf(it.groupId), IM5ConversationType.GROUP)
        }
        // Subscribe the event of groupInfo change or group members change
        BusUtil.observe<GroupInfoDidUpdateEvent>(fragment.viewLifecycleOwner) {
            val groupId = it.group.groupBaseInfo.groupId
            log(TAG, "Subscribe groupInfo change or group members change, id: $groupId")
            wtViewModel.updateConversationInfo(groupId)

            if (groupId == wtViewModel.getCurrentItemTargetId()){
                val isInGroup = GroupInfoCacheManager.getGroupInfoBeanById(groupId)?.isInGroup() ?: false
                if (isInGroup){
                    HeartBeatManager.subscribe(HeartBeatType.T_GROUP_CHAT, groupId.toString())
                }else{
                    HeartBeatManager.unsubscribe(HeartBeatType.T_GROUP_CHAT)
                }
            }
        }
        // Subscribe to the event of successful query of group information
        BusUtil.observe<QueryGroupInfoSuccessEvent>(fragment.viewLifecycleOwner) {
            log(TAG, "Subscribe to the event of successful query of group information")
            if (it.noExistGroupList.isNullOrEmpty().not()) {
                wtViewModel.deleteItemByTargetIdList(it.noExistGroupList, IM5ConversationType.GROUP)
            }
            val targetIdList = it.groupInfoList.map { groupInfo ->
                groupInfo.groupId
            }
            wtViewModel.updateConversationInfoList(targetIdList)
        }
        // Subscribe to the event of online group member change
        BusUtil.observe<WTGroupMemberChangeEvent>(fragment.viewLifecycleOwner) { event ->
            log(TAG, "Subscribe to the event of online group member change")
            wtViewModel.notifyGroupOnlineMemberChange(event)
        }
        // Remove friend
        BusUtil.observe<WTDeleteFriendEvent>(fragment.viewLifecycleOwner) {
            log(TAG, "initEventBus: on receive event:WTDeleteFriendEvent ")
            wtViewModel.deleteItemByTargetId(it.userId, IM5ConversationType.PRIVATE)
        }

        // Subscribe to the event of successful query of user information
        BusUtil.observe<QueryUserInfoSuccessEvent>(fragment.viewLifecycleOwner) {
            log(TAG, "Subscribe to the event of successful query of user information")
            if (it.noExistUserList.isNullOrEmpty().not()) {
                wtViewModel.deleteItemByTargetIdList(
                    it.noExistUserList,
                    IM5ConversationType.PRIVATE
                )
            }
            val targetIdList = it.userInfoList.map { userInfo ->
                userInfo.userId
            }
            wtViewModel.updateConversationInfoList(targetIdList)
        }

        // Subscribe event user/friend info update
        BusUtil.observe<UserInfoUpdateEvent>(fragment.viewLifecycleOwner) {
            logInfo(TAG, "Subscribe event user/friend info remark update")
            wtViewModel.updateUserRelationInfo(it.userId)
        }
        //Subscribe event request add friend
        BusUtil.observe<AddFriendEvent>(fragment.viewLifecycleOwner) {
            logInfo(TAG, "Subscribe event request add friend, userId = ${it.userId}")
            if (wtViewModel.lastSelectTargetId == it.userId) {
                wtViewModel.getUserRelation(it.userId, true)
            }
        }
        BusUtil.observe<WTListScrollEvent>(fragment.viewLifecycleOwner) {
            log(TAG, "Subscribe the event for WTListChangeEvent and targetId = ${it.targetId}")
            /*
             *Preset lastSelectTargetId, when the list data has not been loaded before
             * (eg: new create group, new add friend, app scheme route cold launch)
             * [预先设置 lastSelectTargetId, 当列表数据还未加载到之前]
             */
            wtViewModel.lastSelectTargetId = it.targetId
            scrollByTargetId(it.targetId, scrollToFirstIfNotFound = false)
        }
        BusUtil.observe<GroupAddressUserInfoChangeEvent>(fragment.viewLifecycleOwner) { event ->
            logInfo(TAG, "event observed ==> groupId: ${event.groupId} bot: ${event.userAddressInfo?.userName}")
            aiSelectorViewModel.changeSelectedBot(
                groupId = event.groupId,
                info = event.userAddressInfo,
                source = UpdateAiSelectedSource.ChatPage
            )
        }
        BusUtil.observe<MuteStatusUpdateEvent>(fragment.viewLifecycleOwner) {
            log(TAG, "Subscribe the event for mute status change, targetId = ${it.targetId}")
            wtViewModel.updateMuteStatus(it.targetId)
        }
        GlobalEventManager.homesRouterTarget.observe(fragment.viewLifecycleOwner) { data ->
            if (data == null) {
                return@observe
            }
            val (id, info) = data
            if (id != null) {
                log(TAG, "GlobalEvent scrollToTarget : targetId = $id")
                if (info != null) {
                    //部分场景有数据的直接插入列表中
                    wtViewModel.addUserToFirst(info)
                }
                //先设置一个优先使用的target，如果当前页面数据还没有的话，会等数据加载完再定位
                wtViewModel.lastSelectTargetId = id
                if (wtViewModel.requestChatListStatus == WTViewModelNew.RequestChatListStatus.REQUEST_DONE) {
                    scrollByTargetId(id, scrollToFirstIfNotFound = false)
                    GlobalEventManager.homesRouterTarget.postValue(null)
                }
            }
        }

        wtViewModel.scrollToTargetSharedFlow.collectLatestIn(fragment.viewLifecycleOwner) {
            scrollByTargetId(it)
        }

        BusUtil.observe<AddBuzAccountEvent>(fragment.viewLifecycleOwner) {
            wtViewModel.addUserToFirst(it.userInfo)
            wtViewModel.lastSelectTargetId = it.userInfo.userId
            scrollByTargetId(it.userInfo.userId, scrollToFirstIfNotFound = false)
        }

        ChatGlobalInfoRecorder.nowChatListTargetId.observe(fragment.viewLifecycleOwner) {
            if (it != null) {
                wtViewModel.lastSelectTargetId = it
                scrollByTargetId(it, scrollToFirstIfNotFound = false)
            }
        }

        BusUtil.observe<ProfilePageEvent>(fragment.viewLifecycleOwner) {
            layoutManager.animManager.updateLivePlaceTagTextVisibility(!it.isOpen)
        }
    }

    private fun updateTargetPlayingStatusFromWTMessageFlow(msg: RealTimeMessage) {
        log(
            TAG,
            "当前有实时播放的消息，需要更新首页列表播放中状态 updateTargetPlayingStatusFromWTMessageFlow msg = $msg"
        )
        val targetId = msg.getConversationId()?.toLongOrNull()
        if (msg.conversationType() == IM5ConversationType.GROUP) {
            GlobalScope.launch(Dispatchers.IO) {
                var userInfo: UserRelationInfo? = null
                msg.dispatchSuspend(imPushMessage = {
                    val userId = it.message.userInfo.userId.toLongOrNull()
                    val cache =
                        UserRelationCacheManager.getUserRelationInfoByUid(userId ?: 0L)
                    userInfo = cache ?: it.message.generatePushSenderInfo()
                        ?.toConvertUserRelationInfo(null)
                })
                withContext(Dispatchers.Main) {
                    updatePlayingStatus(targetId, userInfo)
                }
            }
        } else {
            updatePlayingStatus(targetId)
        }
    }

    private suspend fun updateAddressedUser(groupId: Long?, userInfo: UserRelationInfo?) {
        val currentItem = wtViewModel.getCurrentItem() ?: return
        if (currentItem.isGroup && currentItem.targetId == groupId) {
            if (userInfo != null) {
                recordViewModel.updateRecordBgType(RecordBgType.AddressBot)
                logInfo(
                    TAG,
                    "updateWalkieTalkieBackground updateAddressedUser hideAddressedRing result1"
                )
            } else {
                recordViewModel.updateRecordBgType(RecordBgType.Normal)
                RobotStatusHelper.updateGroupWaitingAiState(groupId ?: 0L, false)
                logInfo(
                    TAG,
                    "updateWalkieTalkieBackground updateAddressedUser hideAddressedRing result2"
                )
            }
        } else if (currentItem.isGroup || currentItem.targetId.isRobotSuspend(singleContext)
                .not()
        ) {
            recordViewModel.updateRecordBgType(RecordBgType.Normal)
            logInfo(
                TAG,
                "updateWalkieTalkieBackground updateAddressedUser hideAddressedRing result3"
            )
        }
        adapter.items.forEachIndexed { index, item ->
            if (item is WTItemBean && item.isGroup) {
                if (item.targetId != currentItem.targetId && item.addressedUser != null) {
                    RobotStatusHelper.updateGroupWaitingAiState(item.targetId ?: 0L, false)
                    item.addressedUser = null
                    adapter.notifyItemChanged(index, WTPayloadType.UpdateAddressedUser)
                } else if (item.targetId == currentItem.targetId) {
                    item.addressedUser = userInfo
                    adapter.notifyItemChanged(index, WTPayloadType.UpdateAddressedUser)
                }
            }
        }
    }

    private fun updatePlayingStatus(targetId: Long?, currentPlayingUser: UserRelationInfo? = null) {
        // todo: 检查为什么每次收到语音消息，这个方法要被调用5、6次？
        var targetItem: WTItemBean? = null
        var targetIndex = 0
        for ((index, item) in adapter.items.withIndex()) {
            if (item is WTItemBean) {
                if (item.targetId == targetId) {
                    targetItem = item
                    targetIndex = index
                } else if (item.userStatusInfo.isPlaying) {
                    item.userStatusInfo.isPlaying = false
                    adapter.notifyItemChanged(index, WTPayloadType.UpdatePlayingStatus)
                } else if (item.groupStatusInfo.currentPlayingUser != null) {
                    item.groupStatusInfo.currentPlayingUser = null
                    adapter.notifyItemChanged(index, WTPayloadType.UpdatePlayingStatus)
                }
            }
        }
        targetItem?.let {
            if (currentPlayingUser != null) {
                log(
                    TAG,
                    "更新首页列表[群聊]播放中状态，targetIndex = $targetIndex, targetId = $targetId, " +
                            "currentPlayingUser = [userName: ${currentPlayingUser.userName}, userId: ${currentPlayingUser.userId}]"
                )
                it.groupStatusInfo.currentPlayingUser = currentPlayingUser
                adapter.notifyItemChanged(targetIndex, WTPayloadType.UpdatePlayingStatus)
            } else {
                log(
                    TAG,
                    "更新首页列表[私聊]播放中状态，targetIndex = $targetIndex, targetId = $targetId"
                )
                it.userStatusInfo.isPlaying = true
                adapter.notifyItemChanged(targetIndex, WTPayloadType.UpdatePlayingStatus)
            }
        }
    }

    private fun scrollByTargetId(
        targetId: Long? = null,
        scrollToFirstIfNotFound: Boolean = true
    ) {
        // 当前打开的聊天页，对应的 targetId
        val chatPageConvId = ChatGlobalInfoRecorder.nowChatListTargetId.value
        val lastSelectTargetId = wtViewModel.lastSelectTargetId
        // 当前按下 ptt 按钮，对应的 targetId
        val currentSpeakingId = wtViewModel.currentSpeakingTargetId
        //优先级 currentSpeakingId > targetId > lastSelectTargetId
//        val scrollToId = chatPageConvId?:(currentSpeakingId ?: (targetId ?: lastSelectTargetId))
        val scrollToId = currentSpeakingId ?: (targetId ?: (chatPageConvId ?: lastSelectTargetId))
        val index = findPositionByTargetId(scrollToId)
        val pos = if (scrollToId == null) 0 else (if (index == -1) 0 else index)
        log(
            TAG,
            "scrollByTargetId index:$index,pos:$pos, target:$targetId, scrollToId:${scrollToId}, " +
                    "lastSelectTargetId:$lastSelectTargetId, currentSpeakingId:${currentSpeakingId} " +
                    "scrollToFirstIfNotFound:$scrollToFirstIfNotFound"
        )
        if (index == -1 && scrollToFirstIfNotFound.not()) {
            return
        }
        scrollByPosition(pos = pos, noSmooth = true)
    }

    private fun scrollByPosition(pos: Int, noSmooth: Boolean = false) {
        val runnable = Runnable {
            if (pos in 0 until adapter.itemCount) {
                log(TAG, "scrollByPosition :$pos")
                if (!layoutManager.isScrolling) {
                    layoutManager.scrollToPos(pos, noSmooth)
                }
            }
        }
        if (isInMainThread) {
            runnable.run()
        } else {
            mainThreadHandler.post(runnable)
        }
    }

    private fun showPersonalProfile(userID: Long) {
        routerServices<ContactsService>().value!!.getProfileDialog(
            userID,
            source = FriendApplySource.conversation, /* chat list source */
            trackerSource = ProfileSource.ON_HOME_PAGE.source,
            businessId = null,
        ).showDialog(fragment.activity)
    }

    private fun showRemoveGroupDialog(groupId: Long) {
        CommonBottomListDialog.build {
            addWarningOption(R.string.ic_blocklist, R.string.chat_remove_from_list) {
                wtViewModel.leaveGroup(groupId)
            }
        }.showDialog(fragment.activity)
    }

    private fun showAddFriendDialog() {
        if (afDialogIsVisible || isShowingAddFriendDialog) return
        isShowingAddFriendDialog = true
        val optionList = arrayListOf<BottomListDialogOption>().apply {
            add(
                BottomListDialogOption(
                    iconRes = R.string.ic_contact_add_solid,
                    title = R.string.ftue_v3_addFriends.asString(),
                    optionType = DialogOptionType.HighlightOption
                )
            )
            add(
                BottomListDialogOption(
                    iconRes = R.string.ic_group_member_solid,
                    title = R.string.contacts_create_group.asString(),
                    optionType = DialogOptionType.HighlightOption
                )
            )
            add(
                BottomListDialogOption(
                    iconRes = R.string.ic_scan,
                    title = R.string.scan_qr_code.asString(),
                )
            )
        }
        addFriendDialog = AddFriendBottomListDialog.newInstance().apply {
            setData(
                dataList = optionList,
                onClickCallback = { _, option ->
                    when (option.title) {
                        R.string.ftue_v3_addFriends.asString() -> startAddFriendsPage()
                        R.string.contacts_create_group.asString() -> startCreateGroupPage()
                        R.string.scan_qr_code.asString() -> startQRCodePage()
                    }
                },
                onDismissCallback = {
                    isShowingAddFriendDialog = false
                    addFriendDialog = null
                }
            )
        }
        addFriendDialog?.show(fragment.childFragmentManager, TAG_DIALOG_ADD_FRIEND)
    }

    private fun startAddFriendsPage() =
        startActivityByRouter(PATH_CONTACTS_ACTIVITY_ADD_FRIENDS, {
            withInt(Common.KEY_SOURCE, AddFriendPageSource.ChatHomeAdd.value)
            withTransition(anim.anim_nav_enter, anim.anim_nav_exit)
        })

    private fun startCreateGroupPage() {
        val context = fragment.context ?: return
        GroupFlowTracker.onClickCreateGroupEntrance(CreateGroupSource.HomeList.value)
        if (ABTestManager.isFTUECreateGroupPlanB){
            ChatNavigation.toCreateGroupActivity(context,CreateGroupSource.HomeList.value)
        }else {
            startActivityByRouter(PATH_CHAT_ACTIVITY_SELECT_GROUP_MEMBER, {
                withString(
                    Group.KEY_MEMBER_USER_IDS,
                    "${UserSessionManager.uid}"
                )
                withTransition(anim.anim_nav_enter, anim.anim_nav_exit)
            })
        }
    }

    private fun startQRCodePage() = startActivityByRouter(PATH_COMMON_ACTIVITY_QR_CODE, {
        withInt(Common.KEY_SOURCE, Home.value)
        withTransition(anim.anim_nav_enter, anim.anim_nav_exit)
    })

    private fun findItemByTargetId(targetId: Long?): Any? {
        return adapter.items.find {
            if (it is WTItemBean) {
                it.targetId == targetId
            } else {
                false
            }
        }
    }

    private fun findPositionByTargetId(targetId: Long?) =
        adapter.items.indexOf(findItemByTargetId(targetId))

    private fun removeAddFriendDialog() {
        fragment.childFragmentManager.findFragmentByTag(TAG_DIALOG_ADD_FRIEND)?.let {
            fragment.childFragmentManager.beginTransaction().remove(it).commitAllowingStateLoss()
        }
    }

    private fun bindFileDownloadConfirmDialog() {
        wtViewModel.showFileDownloadDialogFlow.collectIn(fragment.viewLifecycleOwner) { filePreview ->
            showConfirmDownloadFileDialog {
                wtViewModel.startDownload(filePreview)
            }
        }
    }

    override fun onStart() {
        super.onStart()
    }

    override fun onStop() {
        super.onStop()
        HeartBeatManager.unsubscribe(HeartBeatType.WT_ONLINE_GROUP_SELECTED)
    }

    override fun onDestroy() {
        super.onDestroy()
        wtViewModel.currentSpeakingTargetId = null
        unReadCountChangeObserver?.let {
            wtViewModel.wtItemUnReadCountChangeLiveData.removeObserver(it)
        }
        addFriendDialog?.dismissAllowingStateLoss()
        addFriendDialog = null
    }
}