<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_background_8_default"
    tools:layout_height="77dp">

    <View
        android:id="@+id/viewBg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_background_highlight_1_default"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMiniText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftBack"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:text="@string/ic_tel"
            android:textColor="@color/color_000000"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.interfun.buz.common.widget.view.PortraitImageView
            android:id="@+id/portraitImageView"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/common_user_default_portrait_round"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:barrierDirection="end"
            app:constraint_referenced_ids="portraitImageView,iftBack" />


        <TextView
            android:id="@+id/miniTextView"
            style="@style/text_label_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/color_text_black_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/barrier"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Buz Team" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>