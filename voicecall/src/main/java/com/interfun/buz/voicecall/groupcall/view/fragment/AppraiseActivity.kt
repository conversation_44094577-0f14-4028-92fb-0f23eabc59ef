package com.interfun.buz.voicecall.groupcall.view.fragment

import android.content.Intent
import android.os.Bundle
import androidx.compose.foundation.layout.*
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.bean.feedback.H5FeedbackSource
import com.interfun.buz.common.constants.PATH_FRAGMENT_APPRAISE
import com.interfun.buz.common.constants.RouterParamKey
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
@Route(path = PATH_FRAGMENT_APPRAISE)
class AppraiseActivity : SimpleContainerActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        showContent(intent)
    }

    private fun showContent(intent: Intent) {
        this.supportFragmentManager.beginTransaction().apply {
            replace(
                containerId,
                AppraiseFragment.newInstance(
                    intent.getParcelableExtra(RouterParamKey.VC.KEY_USER_INFO),
                    intent.getParcelableExtra(RouterParamKey.VC.KEY_GROUP_INFO),
                    intent.getParcelableExtra(RouterParamKey.VC.KEY_ROOM_INFO),
                    intent.getIntExtra(RouterParamKey.VC.KEY_H5_FEEDBACK_SOURCE, RouterParamKey.Feedback.FEEDBACK_SOURCE_UN_KNOWN),
                ), AppraiseFragment::class.java.simpleName
            )
            commitAllowingStateLoss()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        showContent(intent)
    }
}