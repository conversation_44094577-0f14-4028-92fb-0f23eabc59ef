package com.interfun.buz.compose.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.R
import com.interfun.buz.compose.ktx.asColor

/**
 * <AUTHOR>
 * @date 2024/12/24
 * @desc
 */
@Composable
fun CommonRedDot(
    size: Dp = 20.dp,
    strokeWidth: Dp = 6.dp,
    dotColor: Color = R.color.color_foreground_consequential_default.asColor(),
    strokeColor: Color = R.color.color_background_2_default.asColor(),
    modifier: Modifier = Modifier,
) {
    Canvas(modifier = modifier.size(size)) {
        drawCircle(
            color = strokeColor,
            radius = (size / 2f).toPx()
        )
        drawCircle(
            color = dotColor,
            radius = (size / 2f - strokeWidth).toPx()
        )
    }
}

@Composable
@Preview
private fun PreviewCommonRedDot() {
    Box(
        modifier = Modifier
            .background(color = Color.Gray)
            .padding(10.dp)
    ) {
        CommonRedDot()
    }
}