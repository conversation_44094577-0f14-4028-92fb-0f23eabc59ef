package com.interfun.buz.social.db.entity

import com.buz.idl.bot.bean.BotUserSetting
import com.interfun.buz.base.ktx.put

/**
 * @see BotSingleSettingEntity
 * ！！！注意！！！
 * ！！！注意！！！
 * ！！！注意！！！
 * 每次添加一个key都必须完善下面内容：
 * 1. [keyFiledMap] 数据
 * 名称映射如果在[BotUserSetting]的，需要按字段名命名，因为[BotUserSetting.convertDb]会使用反射处理
 */
object BotSettingKeyMap {
    val languageCode = 1 to "languageCode"
    val voiceStyleId = 2 to "voiceStyleId"
    val sourceLanguage = 3 to "sourceLanguage"
    val targetLanguage = 4 to "targetLanguage"

    internal val keyFiledMap = hashMapOf<Int, String>().apply {
        put(languageCode)
        put(voiceStyleId)
        put(sourceLanguage)
        put(targetLanguage)
    }

    private val fieldKeyMap = hashMapOf<String, Int>().apply {
        keyFiledMap.forEach { entry ->
            put(entry.value, entry.key)
        }
    }

    fun mapToSingleSettingEntity(
        botUserId: Long,
        field: String,
        value: String
    ): BotSingleSettingEntity? {
        val key = fieldKeyMap[field] ?: return null
        return BotSingleSettingEntity(botUserId, key, value)
    }
}

val BotSingleSettingEntity.fieldName
    get() = BotSettingKeyMap.keyFiledMap[this.key]
        ?: throw IllegalAccessException("fieldName is null for the given key:${key},have you registered it to fieldKeyMap?")