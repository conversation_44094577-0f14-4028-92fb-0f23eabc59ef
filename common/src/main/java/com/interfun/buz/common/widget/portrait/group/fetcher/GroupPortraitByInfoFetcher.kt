package com.interfun.buz.common.widget.portrait.group.fetcher

import android.os.Parcelable
import androidx.core.net.toUri
import coil.ImageLoader
import coil.fetch.FetchResult
import coil.fetch.Fetcher
import coil.request.Options
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.common.constants.CommonMMKV
import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @date 2023/8/17
 * @desc
 */
data class GroupPortraitFetcherInfo(
    val groupId: Long,
    val portraitList: List<String?>?,
    val legacyPortrait: String?,
)

class GroupPortraitByInfoFetcher(
    private val info: GroupPortraitFetcherInfo,
    private val imageLoader: ImageLoader,
    options: Options
) : GroupPortraitFetcher(options) {

    override suspend fun fetch(): FetchResult? {
        // If legacyPortrait(generated by the server side) exists, then use it directly
        info.legacyPortrait?.let { legacyPortrait ->
            if (legacyPortrait.notEmpty()) {
                val httpUriFetcher = imageLoader.components.newFetcher(
                    data = legacyPortrait.toUri(),
                    options = options,
                    imageLoader = imageLoader
                )?.first
                return httpUriFetcher?.fetch()
            }
        }
        return super.fetch(info.portraitList) {
            removeDiskCacheWhenGroupInfoUpdated()
        }
    }

    private fun removeDiskCacheWhenGroupInfoUpdated() {
        val cacheKey = options.diskCacheKey ?: return
        val map = CommonMMKV.groupPortraitDiskCacheKeyList?.map
            ?: ConcurrentHashMap<Long, String>().also {
                CommonMMKV.groupPortraitDiskCacheKeyList = GroupPortraitDiskCacheKeyMap(it)
            }
        val oldCacheKey = map.put(info.groupId, cacheKey)
        // If urlList changes and old cache not used by other group, then remove the old cache
        if (oldCacheKey != null && oldCacheKey != cacheKey && !map.containsValue(oldCacheKey)) {
            diskCache.remove(oldCacheKey)
            log(TAG, "removeDiskCacheWhenGroupInfoUpdated groupId: ${info.groupId}")
        }
    }

    @kotlinx.parcelize.Parcelize
    data class GroupPortraitDiskCacheKeyMap(val map: ConcurrentHashMap<Long, String>) : Parcelable

    class Factory : Fetcher.Factory<GroupPortraitFetcherInfo> {
        override fun create(
            info: GroupPortraitFetcherInfo,
            options: Options,
            imageLoader: ImageLoader
        ): Fetcher {
            return GroupPortraitByInfoFetcher(info, imageLoader, options)
        }
    }
}