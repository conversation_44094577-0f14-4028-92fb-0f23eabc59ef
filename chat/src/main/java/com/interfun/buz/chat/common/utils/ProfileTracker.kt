package com.interfun.buz.chat.common.utils

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.utils.BuzTracker
import com.lizhi.im5.sdk.conversation.IM5ConversationType

object ProfileTracker {

    private fun getPageBusinessType(targetId: Long, convType: IM5ConversationType): String {
        return when (convType) {
            IM5ConversationType.PRIVATE -> "private"
            else -> "group"
        }
    }

    /**
     * 资料页，点击自动播放开关
     */
    fun onClickAC2024081502(targetId: Long, convType: IM5ConversationType, switchState: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081502")
            put(TrackConstant.KEY_TITLE, "other_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "auto_play_setting")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            val elementBusinessId = if (switchState) {
                "open"
            } else {
                "off"
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, elementBusinessId)

        }
    }

    /**
     * 资料页，点击show notification开关
     */
    fun onClickAC2024081503(targetId: Long, convType: IM5ConversationType, switchState: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081503")
            put(TrackConstant.KEY_TITLE, "other_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "notification_setting")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            val elementBusinessId = if (switchState) {
                "open"
            } else {
                "off"
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, elementBusinessId)

        }
    }

    /**
     * 资料页，点击清除历史item
     */
    fun onClickAC2024081504(targetId: Long, convType: IM5ConversationType) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081504")
            put(TrackConstant.KEY_TITLE, "other_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "clear_chat_history")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
        }
    }

    /**
     * 资料页，点击屏蔽/取消屏蔽
     */
    fun onClickAC2024081505(targetId: Long, elementBusinessId: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081505")
            put(TrackConstant.KEY_TITLE, "other_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "block_user")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, elementBusinessId)
        }
    }


    /**
     * 资料页，点击report
     */
    fun onClickAC2024081506(targetId: Long, convType: IM5ConversationType) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081506")
            put(TrackConstant.KEY_TITLE, "other_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "report_user")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
        }
    }

    /**
     * 资料页，移除好友/移除群
     */
    fun onClickAC2024081507(targetId: Long, convType: IM5ConversationType) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081507")
            put(TrackConstant.KEY_TITLE, "other_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "remove_friend")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, getPageBusinessType(targetId, convType))
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
        }
    }

    /**
     * 资料页，群聊点击查看全部成员
     */
    fun onClickAC2024081508() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081508")
            put(TrackConstant.KEY_TITLE, "group_chat_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "view_members")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }
}