package com.interfun.buz.biz.center.voicefilter.compat

import com.interfun.buz.biz.center.voicefilter.model.VoiceFilterData

/**
 * <AUTHOR>
 * @date 2025/5/20
 * @desc
 */
@Deprecated("请不要再使用这个接口，不要新增接口，请使用VoiceFilterDataRepository")
interface VoiceFilterDataCompat {

    suspend fun getCachedVoiceFilterDataById(filterId: Long): VoiceFilterData?

    fun getCachedVoiceFilterById(filterId: Long): VoiceFilterData

    fun getCachedVoiceFilterNameById(filterId: Long): String
}