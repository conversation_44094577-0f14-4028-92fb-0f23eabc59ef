@file:OptIn(DelicateCoroutinesApi::class)

package com.interfun.buz.common.manager

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager.NameNotFoundException
import android.net.ConnectivityManager
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.text.format.Formatter
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.R
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.eventbus.BaseEvent
import com.interfun.buz.common.manager.network.request
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.lizhi.component.basetool.env.AppEnvironment
import com.lizhi.component.basetool.env.Environments
import kotlinx.coroutines.*
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.net.InetAddress
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.system.exitProcess

/**
 * <AUTHOR>
 * @date 2023/4/24
 * @desc
 */
object BindIPTagManager {

    const val TAG = "BindIPTagManager"

    //todo: Modify this tag when the iteration changes @All
    private const val CURRENT_TAG = "v_buz_vf_sharing"
    private const val URL_BIND = "https://lighthouse.vico.voxicer.com/lrdp/rd/sprint/bind_vtag/%s/buz"
    private const val URL_QUERY = "https://lighthouse.vico.voxicer.com/lrdp/rd/sprint/findVtagOfIp?ip=%s"

    var QABindIpTag = ""
    private val isTowerEnvironment = Environments.getEnv(appContext) == AppEnvironment.TOWER
    private val ipTag get() = QABindIpTag.ifEmpty { CURRENT_TAG }
    private var isDialogShowing = false

    fun bindIpTag() {
        GlobalScope.launch {
            val urlBind = String.format(URL_BIND, ipTag)
            request(urlBind)?.second?.let { body ->
                withMainContext {
                    try {
                        val jsonObject = JSONObject(body)
                        val msg = jsonObject.optString("msg")
                        if (isTowerEnvironment.not()) {
                            toast("Bind $ipTag $msg, 当前非灯塔环境，将切换到灯塔并杀进程，请手动重启APP")
                            ClearLocalMMKVEvent.post()
                            clearAllFile(appContext)
                            Environments.changeEnv(appContext, AppEnvironment.TOWER)
                            delay(500)
                            exitProcess(0)
                        } else {
                            toast("Bind $ipTag $msg")
                        }
                    } catch (e: JSONException) {
                        toast("Error, ${e.message}")
                    }
                }
            } ?: toast("Bind IP tag failed, request body is null")
        }
    }

    private fun clearAllFile(context: Context) {
        if (VERSION.SDK_INT >= VERSION_CODES.N) {
            context.dataDir.deleteRecursively()
        }else {
            val packageManager = context.packageManager
            try {
                val applicationInfo = packageManager.getApplicationInfo(context.packageName, 0)
                val dataDir = applicationInfo.dataDir
                File(dataDir).deleteRecursively()
            } catch (e: NameNotFoundException) {
                e.printStackTrace()
            }
        }
        logDebug(TAG, "clearAllFile")
    }

    @SuppressLint("MissingPermission")
    fun subscription() {
        doInMainThread {
            WifiListLiveData().observeForever {
                log(TAG, "on wifi change $it")
                queryCurrentIpTag()
            }
        }
    }

    fun queryCurrentIpTag() {
        if (isDialogShowing || isTowerEnvironment.not() || CommonMMKV.notShowAutoBindIPTagDialog) {
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            val wifiIp = getWifiIP() ?: return@launch
            if (isLocalIPAddress(wifiIp).not()) return@launch
            val urlQuery = String.format(URL_QUERY, wifiIp)
            request(urlQuery)?.second?.let { body ->
                try {
                    val jsonObject = JSONObject(body)
                    val tag = jsonObject.optString("versionTag")
                    log(TAG, "current bind tag: $tag")
                    if (tag != "$ipTag | Buz") {
                        showBindDialog(tag)
                    }
                } catch (e: JSONException) {
                    logError(TAG, e)
                }
            }
        }
    }

    private fun showBindDialog(tag: String) {
        doInMainThread {
            var click: (() -> Unit)? = null
            topActivity?.let { activity ->
                CommonAlertDialog(
                    context = activity,
                    title = "Switch IP tag?",
                    tips = buildSpannedString {
                        append("Current tag: ")
                        color(R.color.secondary_error.asColor()) { append(tag) }
                        append("\nLatest tag: ")
                        color(R.color.basic_primary.asColor()) { append("$ipTag | Buz") }
                        append("\nPlease confirm whether to switch?")
                        append("\n")
                        appendClickable("\nIf you don't want to see this dialog again, click here") {
                            CommonMMKV.notShowAutoBindIPTagDialog = true
                            click?.invoke()
                        }
                    },
                    positiveText = R.string.ok.asString(),
                    negativeText = R.string.cancel.asString(),
                    positiveCallback = {
                        bindIpTag()
                        it.dismiss()
                    },
                    negativeCallback = {
                        it.dismiss()
                    }
                ).apply {
                    click = { dismiss() }
                    doOnShow {
                        isDialogShowing = true
                    }
                    doOnDismiss {
                        isDialogShowing = false
                    }
                }.show()
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun getWifiIP(): String? {
        var wifiIp: String? = null
        if (VERSION.SDK_INT < VERSION_CODES.Q) {
            val wifiManager = appContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            wifiIp = getIPString(wifiManager.connectionInfo.ipAddress)
        } else {
            (appContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager).apply {
                activeNetwork?.let { network ->
                    (getNetworkCapabilities(network)?.transportInfo as? WifiInfo)?.let { wifiInfo ->
                        wifiIp = getIPString(wifiInfo.ipAddress)
                    }
                }
            }
        }
        return wifiIp
    }

    private fun getIPString(ipAddress: Int): String? {
        return if (VERSION.SDK_INT >= VERSION_CODES.N) {
            InetAddress.getByAddress(
                ByteBuffer
                    .allocate(Integer.BYTES)
                    .order(ByteOrder.LITTLE_ENDIAN)
                    .putInt(ipAddress)
                    .array()
            ).hostAddress
        } else {
            Formatter.formatIpAddress(ipAddress)
        }
    }

    private fun isLocalIPAddress(ip: String): Boolean {
        return ip.startsWith("192.168.")
            || ip.startsWith("10.")
            || ip.startsWith("172.")
            || ip == "127.0.0.1"
    }

    class ClearLocalMMKVEvent : BaseEvent() {

        companion object {
            fun post() {
                BusUtil.post(ClearLocalMMKVEvent())
            }
        }
    }
}