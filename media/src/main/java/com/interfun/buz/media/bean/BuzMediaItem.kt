package com.interfun.buz.media.bean

import android.os.Parcelable
import androidx.exifinterface.media.ExifInterface
import com.interfun.buz.base.ktx.screenHeight
import com.interfun.buz.base.ktx.screenWidth
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.io.File
import kotlin.LazyThreadSafetyMode.NONE

@Parcelize
sealed class MediaType : Parcelable {
    data object Video : MediaType()
    data object Image : MediaType()
}

@Parcelize
open class BuzMediaItem(
    open var mediaId: Long, // 唯一id
    open var mediaUri: String, // 视频链接 or 原图链接
    open var width: Int = screenWidth, // 可以不传
    open var height: Int = screenHeight,// 可以不传
    var remoteUrl: String = "", // 视频在线连接，主要是用于区分发送端的mediaUri，发送端mediaUri是本地路径
    var thumbnailUrl: String = "",  // 视频封面图缩略图 or 图片缩略图
    var hdCoverUrl: String = "", // 高清视频封面图（仅视频）
    open var type: MediaType = MediaType.Video,
    open var duration: Long = 0, // 视频时长
    // 图片加密数据，通过AesComponent#combineAesKeyAndIv可以转成ByteArray，
    // 使用时通过ByteArray可以生成AesComponent对象,结合ImageView.loadWithThumbnail可支持图片解密加载
    var combineAesIv: ByteArray? = null,
    var orientation: Int = ExifInterface.ORIENTATION_NORMAL,
    @IgnoredOnParcel
    var imMessage: IMessage? = null,
) : Parcelable {

    @IgnoredOnParcel
    var isDownloading: Boolean = false // 是否触发了下载

    @IgnoredOnParcel
    val isMediaUriLocalFile by lazy(NONE) {
        try {
            mediaUri.startsWith('/') && File(mediaUri).exists()
        } catch (t: Throwable) {
            false
        }
    }

    companion object {
        const val KEY_MESSAGE = "KEY_MESSAGE" // IM5Message消息
    }

    fun getMessage(): IMessage? {
        return imMessage
    }

    override fun equals(other: Any?): Boolean {
        if (other is BuzMediaItem) {
            return this.mediaId == other.mediaId
        }
        return super.equals(other)
    }

    override fun hashCode(): Int {
        return mediaId.hashCode()
    }

    override fun toString(): String {
        return "mediaItem@{mediaId=${mediaId},mediaUri=${mediaUri},duration=${duration}," +
                "thumbnailUrl=${thumbnailUrl},hdCoverUrl=${hdCoverUrl},type=${type}}"
    }

}
