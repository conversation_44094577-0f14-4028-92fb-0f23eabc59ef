<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_marginHorizontal="20dp"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="10dp"
    android:background="@drawable/common_rect_background_5_default_radius_30">


    <TextView
        android:id="@+id/tvNickname"
        style="@style/text_title_small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:singleLine="true"
        android:textColor="@color/color_text_white_important"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvContent"
        app:layout_constraintEnd_toStartOf="@+id/iftDenied"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Jenny WilsonJenny WilsonJenny WilsonJenny WilsonJenny Wilson" />

    <TextView
        android:id="@+id/tvContent"
        style="@style/text_body_medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:ellipsize="middle"
        android:maxLines="2"
        android:text="@string/incoming_call"
        android:textColor="@color/color_text_white_secondary"
        android:textSize="14dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintEnd_toStartOf="@+id/iftDenied"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@+id/tvNickname"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftAccept"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/common_oval_basic_primary"
        android:gravity="center"
        android:text="@string/ic_tel"
        android:textColor="@color/text_black_main"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftDenied"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/common_oval_secondary_error"
        android:gravity="center"
        android:text="@string/ic_invite_denied"
        android:textColor="@color/text_white_important"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iftAccept"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />


</com.interfun.buz.base.widget.round.RoundConstraintLayout>