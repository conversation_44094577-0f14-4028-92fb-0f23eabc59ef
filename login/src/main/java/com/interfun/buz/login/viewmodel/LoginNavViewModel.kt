package com.interfun.buz.login.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.launch
import com.interfun.buz.common.manager.Area
import com.interfun.buz.common.manager.AreaCodeManager
import com.interfun.buz.login.constants.LoginMMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
/**
 * <AUTHOR>
 * @date 2022/6/29
 * @desc 用于处理[com.interfun.buz.login.view.activity.LoginActivity]下的fragment数据共享
 * @desc Used to handle fragment data sharing in same Activity
 */
open class LoginNavViewModel : ViewModel() {

    companion object {
        // type：0 登录成功，进入主页 1 未完成注册，进入资料填写页
        const val LOGIN = 0
        const val REGISTER = 1
    }

    var pageType: PageType = PageType.Login

    protected val _accountTypeFlow = MutableStateFlow(AccountType.Phone)
    val accountTypeFlow = _accountTypeFlow.asStateFlow()
    private val _accountTypeChangeFlow = MutableSharedFlow<Unit>()
    val accountTypeChangeFlow = _accountTypeChangeFlow.asSharedFlow()

    val currentAccountType: AccountType
        get()  = _accountTypeFlow.value
    val traceIdLiveData = MutableLiveData<String>()

    val account: String?
        get() {
            return when (currentAccountType) {
                AccountType.Phone -> phoneNumber
                AccountType.Email -> email
            }
        }

    var bindingType: BindingType = BindingType.Default


    //用于记录当前输入的手机号
    val phoneLiveData = MutableLiveData<String>("")

    //用于记录当前的区号
    private val _areaFlow = MutableStateFlow<Area?>(null)
    val areaFlow = _areaFlow.asStateFlow()


    //组合带区号的手机号码
    val phoneNumber: String?
        get() {
            val code = areaFlow.value?.code ?: ""
            val phone = phoneLiveData.value ?: ""
            return if (code.isNotEmpty() && phone.isNotEmpty()) {
                "$code-$phone"
            } else {
                null
            }
        }

    //用于记录是否已获取到手机号
    val isRequestPhoneLiveData = MutableLiveData<Boolean>()

    val emailLiveData = MutableLiveData<String>("")

    val email get() = emailLiveData.value

    val accountFlow = combine(phoneLiveData.asFlow(), emailLiveData.asFlow()) { _, _ ->
        val account = when (currentAccountType) {
            AccountType.Phone -> phoneNumber
            AccountType.Email -> email
        }
        return@combine account
    }.distinctUntilChanged().stateIn(viewModelScope, SharingStarted.Eagerly, "")


    var limitResendCodeAccount: String?
        get() = when (currentAccountType) {
            AccountType.Phone -> LoginMMKV.limitResendCodePhone
            AccountType.Email -> LoginMMKV.limitResendCodeEmail
        }
        set(value) {
            when (currentAccountType) {
                AccountType.Phone -> LoginMMKV.limitResendCodePhone = value
                AccountType.Email -> LoginMMKV.limitResendCodeEmail = value
            }
        }

    var limitResendCodeTime: Long
        get() = when (currentAccountType) {
            AccountType.Phone -> LoginMMKV.limitResendCodePhoneTime
            AccountType.Email -> LoginMMKV.limitResendCodeEmailTime
        }
        set(value) {
            when (currentAccountType) {
                AccountType.Phone -> LoginMMKV.limitResendCodePhoneTime = value
                AccountType.Email -> LoginMMKV.limitResendCodeEmailTime = value
            }
        }

    fun setAccountType(accountType: AccountType) = launch {
        _accountTypeFlow.emit(accountType)
        _accountTypeChangeFlow.emit(Unit)
    }

    fun getDefaultArea() {
        viewModelScope.launch(Dispatchers.IO) {
            val area = AreaCodeManager.getStoredLocalAreaCode()
                ?: if (AreaCodeManager.isSimCardInserted()) {
                    AreaCodeManager.getSuggestedArea()
                } else {
                    null
                }
            setCurrentArea(area)
        }
    }

    fun setCurrentArea(area: Area?) {
        _areaFlow.tryEmit(area)
    }

    private var thirdInfo = ThirdInfo()

    fun preFillFirstName(): String? {
        return if (isThirdRegister()) thirdInfo.firstName
        else null
    }

    fun preFillLastName(): String? {
        return if (isThirdRegister()) thirdInfo.lastName
        else null
    }

    fun preFillHeadProfile(): String? {
        return if (isThirdRegister()) thirdInfo.headProfile
        else null
    }

    fun updateThirdInfo(firstName: String?, lastName: String?, headProfile: String?) {
        thirdInfo.lastName = lastName
        thirdInfo.firstName = firstName
        thirdInfo.headProfile = headProfile
    }

    fun clearThirdInfo() {
        thirdInfo.lastName = null
        thirdInfo.firstName = null
        thirdInfo.headProfile = null
        thirdInfo.formThirdRegister = false
    }

    fun isThirdRegister(): Boolean {
        return thirdInfo.formThirdRegister
    }

    fun setThirdRegister(isThirdRegister: Boolean) {
        thirdInfo.formThirdRegister = isThirdRegister
    }
}


enum class AccountType {
    Phone,
    Email
}

enum class PageType {
    Login,  //注登流程 Registration and login process
    Binding  //绑定新账号流程 Binding a new account process
}

enum class BindingType {
    Default,  // 默认，从个人设置，账号登页面点击tips提示补充手机号
    FromGGRegister  //GG新用户注册后，如果手机号绑定，再进入到首页
}


class ThirdInfo(
    var firstName: String? = null,
    var lastName: String? = null,
    var headProfile: String? = null,
    var formThirdRegister : Boolean = false
)