package com.interfun.buz.chat.voicepanel.view.fragment.subtabfragment

import android.graphics.Rect
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import androidx.recyclerview.widget.RecyclerView.State
import coil.size.Size
import com.interfun.buz.base.ktx.MultiTypeAdapter
import com.interfun.buz.base.ktx.collect
import com.interfun.buz.base.ktx.collectIn
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.fragment
import com.interfun.buz.base.ktx.fragmentViewModels
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.isNetworkAvailable
import com.interfun.buz.base.ktx.layoutMarginTop
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatFragmentVoicegifListBinding
import com.interfun.buz.chat.voicepanel.callback.VoiceItemCollectResultCallback
import com.interfun.buz.chat.voicepanel.callback.VoiceItemPanelEventListener
import com.interfun.buz.chat.voicepanel.callback.VoiceItemSentResultCallback
import com.interfun.buz.chat.voicepanel.callback.itemview.VoiceGifItemClickCallBack
import com.interfun.buz.chat.voicepanel.model.CollectVoiceItemData
import com.interfun.buz.chat.voicepanel.model.SendVoiceGifData
import com.interfun.buz.chat.voicepanel.model.SendVoiceItemData
import com.interfun.buz.chat.voicepanel.model.VoiceEmojiPanelType
import com.interfun.buz.chat.voicepanel.model.VoiceGifPreview
import com.interfun.buz.chat.voicepanel.view.itemview.holder.ItemViewType
import com.interfun.buz.chat.voicepanel.view.itemview.voicegif.VoiceGifItemView
import com.interfun.buz.chat.voicepanel.view.widget.previewview.VoiceItemPreviewView
import com.interfun.buz.chat.voicepanel.viewmodel.VoiceGifPlayStateViewModel
import com.interfun.buz.chat.voicepanel.viewmodel.VoiceGifPlayingState
import com.interfun.buz.chat.voicepanel.viewmodel.VoiceGifViewModel
import com.interfun.buz.chat.wt.entity.isUser
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.common.base.binding.ViewPager2LazyBindingFragment
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.widget.view.EmptyDataType
import com.interfun.buz.common.widget.view.EmptyDataViewListener

/**
 * 语音Gif和最近使用的语音Gif
 */
open class VoiceGifSubTabFragment :
    ViewPager2LazyBindingFragment<ChatFragmentVoicegifListBinding>(),
    VoiceGifItemClickCallBack {
    companion object {
        const val TAG = "VoiceGifSubTabFragment"
        const val VOICE_GIF_SUB_ID = "VOICE_GIF_SUB_ID"
        const val VOICE_GIF_SUB_NAME = "VOICE_GIF_SUB_NAME"
        const val KEY_PANEL_TYPE = "KEY_PANEL_TYPE"
        fun newInstance(subTabId: Long, subTabName: String, panelType: VoiceEmojiPanelType) =
            VoiceGifSubTabFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(KEY_PANEL_TYPE, panelType)
                    putLong(VOICE_GIF_SUB_ID, subTabId)
                    putString(VOICE_GIF_SUB_NAME, subTabName)
                }
            }
    }

    private lateinit var listener: VoiceItemPanelEventListener
    private val panelType: VoiceEmojiPanelType
        get() = arguments?.getParcelable(KEY_PANEL_TYPE) ?: VoiceEmojiPanelType.HomePagePanelType
    private val voiceGifViewModel by fragment.fragmentViewModels<VoiceGifViewModel>()
    private val subTabId get() = arguments?.getLong(VOICE_GIF_SUB_ID) ?: 0
    private val subTabName get() = arguments?.getString(VOICE_GIF_SUB_NAME) ?: ""
    private val isHome: Boolean get() = panelType == VoiceEmojiPanelType.HomePagePanelType
    private val voiceGifPlayStateViewModel by fragment.fragmentViewModels<VoiceGifPlayStateViewModel>()

    protected val mAdapter by lazy {
        MultiTypeAdapter {
            register(
                VoiceGifItemView(
                    this@VoiceGifSubTabFragment, panelType, getItemType()
                )
            )
        }
    }

    override fun initData() {
        super.initData()
        adjustRVMarginTop()
        loadVoiceGifData()
        observeVoiceGifPlayingState()
    }

    private fun observeVoiceGifPlayingState() {
        voiceGifPlayStateViewModel.currentPlayVoiceGifFlow.collectIn(viewLifecycleOwner) { playState ->
            val (state, data) = playState ?: return@collectIn
            if (data.subTabId == subTabId) {
                notifyItemPlayStateChange(
                    index = data.index,
                    voiceGifId = data.voiceGifEntity.id,
                    state = state
                )
            }
        }
    }

    private fun notifyItemPlayStateChange(
        index: Int,
        voiceGifId: String,
        state: VoiceGifPlayingState
    ) {
        if (index != -1) {
            val voiceGifItem = mAdapter.items[index] as? VoiceGifEntity
            if (voiceGifItem?.id != voiceGifId) return
            val newVoiceGifItem = voiceGifItem.copy(isPlaying = state == VoiceGifPlayingState.Start)
            val updatedList = mAdapter.items.toMutableList().apply {
                this[index] = newVoiceGifItem
            }
            mAdapter.items = updatedList
            mAdapter.notifyItemChanged(index, state)
        }
    }

    private fun adjustRVMarginTop() {
        voiceGifViewModel.showBannerTipsStateFlow.collectIn(viewLifecycleOwner) { showTips ->
            binding.root.layoutMarginTop(
                if (showTips && isHome) 8.dp
                else if (showTips) 6.dp
                else 0.dp
            )
        }
    }

    open fun loadVoiceGifData() {
        voiceGifViewModel.voiceGifTabListStateFlow.collect(viewLifecycleOwner) { uiState ->
            if (uiState?.isSuccess == false) {
                R.string.network_error_try_again.toast()
            }
            val voiceGifList = uiState?.voiceGifList
            logDebug(TAG, "loadVoiceGifData: $uiState")
            if (null != voiceGifList) {
                val isRefresh = uiState.queryParams == null
                binding.refreshLayout.visible()
                binding.emptyDataView.gone()
                val isFirstRequestFromCache = uiState.isFirstRequest && isNetworkAvailable
                if (isRefresh) {
                    if (voiceGifList.isEmpty() && !isFirstRequestFromCache) {
                        binding.emptyDataView.setLottieSize(100.dp)
                        if (isNetworkAvailable.not()){
                            binding.emptyDataView.setType(EmptyDataType.NO_NETWORK)
                            binding.emptyDataView.setListener(object : EmptyDataViewListener {
                                override fun onButtonClicked() {
                                    voiceGifViewModel.loadVoiceGifTabList(subTabId = subTabId, subTabName = subTabName)
                                }
                            })
                        }else{
                            binding.emptyDataView.setType(EmptyDataType.NO_DATA)
                        }
                        binding.emptyDataView.visible()
                        binding.refreshLayout.gone()
                    } else {
                        mAdapter.items = voiceGifList
                        mAdapter.notifyDataSetChanged()
                    }
                } else {
                    val positionStart = mAdapter.items.size
                    val newList = mAdapter.items.toMutableList().also {
                        it.addAll(voiceGifList)
                    }
                    mAdapter.items = newList
                    mAdapter.notifyItemRangeInserted(
                        positionStart, voiceGifList.size
                    )
                    binding.refreshLayout.finishLoadMore()
                }
                binding.refreshLayout.setEnableLoadMore(uiState.isLastPage.not())
            }
        }
        voiceGifViewModel.loadVoiceGifTabList(subTabId = subTabId, subTabName = subTabName)

        // 分页加载
        binding.refreshLayout.setOnLoadMoreListener {
            voiceGifViewModel.loadVoiceGifTabList(subTabId = subTabId, subTabName = subTabName)
        }
    }


    override fun initView() {
        super.initView()
        initRecyclerView()
    }

    fun setListener(listener: VoiceItemPanelEventListener) {
        this.listener = listener
    }

    private fun initRecyclerView() {
        binding.rvVoiceGifList.apply {
            val spanSize = 3
            val itemSpacing = 5.dp
            adapter = mAdapter
            layoutManager = GridLayoutManager(<EMAIL>, spanSize)
            addItemDecoration(object : ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: State
                ) {
                    // Reset all offsets to 0
                    outRect.apply {
                        top = 0.dp
                        bottom = itemSpacing
                        left = itemSpacing / 2
                        right = itemSpacing / 2

                        val itemSpanPosition = parent.getChildAdapterPosition(view)
                        if (LanguageManager.isArLanguage()) {
                            when (itemSpanPosition % spanSize) {
                                // first column
                                0 -> {
                                    left = itemSpacing / 3 * 2
                                    right = 0
                                }

                                1, spanSize - 2 -> {
                                    // second column
                                    if (itemSpanPosition % spanSize == 1) {
                                        right = itemSpacing / 3
                                    }

                                    // second last column
                                    if (itemSpanPosition % spanSize == spanSize - 2) {
                                        left = itemSpacing / 3
                                    }
                                }

                                // last column
                                spanSize - 1 -> {
                                    left = 0
                                    right = itemSpacing / 3 * 2
                                }
                            }
                        } else {
                            when (itemSpanPosition % spanSize) {
                                // first column
                                0 -> {
                                    left = 0
                                    right = itemSpacing / 3 * 2
                                }

                                1, spanSize - 2 -> {
                                    // second column
                                    if (itemSpanPosition % spanSize == 1) {
                                        left = itemSpacing / 3
                                    }
                                    // second last column
                                    if (itemSpanPosition % spanSize == spanSize - 2) {
                                        right = itemSpacing / 3
                                    }
                                }

                                // last column
                                spanSize - 1 -> {
                                    left = itemSpacing / 3 * 2
                                    right = 0
                                }
                            }
                        }
                    }
                }
            })
        }
        binding.refreshLayout.setEnableRefresh(false)
        binding.refreshLayout.setEnableLoadMore(false)
    }

    override fun onVoiceGifClick(
        voiceGifItem: VoiceGifEntity,
        location: IntArray,
        size: Size,
        index: Int
    ) {
        doSendingVoiceGif(
            sendVoiceItemData = SendVoiceGifData(voiceGifItem, size, location),
            voiceGifItem = voiceGifItem,
            fromPreview = false,
            index = index
        )
    }

    override fun onVoiceGifLongClick(
        voiceGifItem: VoiceGifEntity,
        collectId: Long?,
        location: IntArray, size: Size, viewType: ItemViewType,
        index: Int
    ) {
        val sendToUser = WTStatusManager.currentSelectedItem?.userInfo?.userName
        val sendToGroup = WTStatusManager.currentSelectedItem?.groupInfo?.groupName
        listener.showPreviewView(
            previewData = VoiceGifPreview(sendTo = if (WTStatusManager.currentSelectedItem.isUser) sendToUser else sendToGroup,
                voiceGifEntity = voiceGifItem,
                location = location,
                size = size,
                onSendClickCallback = { voiceItem ->
                    doSendingVoiceGif(
                        sendVoiceItemData = voiceItem,
                        voiceGifItem = voiceGifItem,
                        fromPreview = true,
                        index = index
                    )
                },
                onOperateCollectCallback = { collectionData, previewView ->
                    doCollectionVoiceGif(collectionData = collectionData, previewView = previewView)
                }), viewType = viewType
        )
    }

    private fun doCollectionVoiceGif(
        collectionData: CollectVoiceItemData,
        fromPreview: Boolean = true,
        previewView: VoiceItemPreviewView
    ) {
        listener.collectionVoiceItem(
            collectionData = collectionData,
            fromPreview = fromPreview,
            callback = object : VoiceItemCollectResultCallback {
                override fun onResult(code: Int, collectionData: CollectVoiceItemData) {
                    previewView.playGoneAnim()
                }
            }
        )
    }

    private fun doSendingVoiceGif(
        sendVoiceItemData: SendVoiceItemData,
        voiceGifItem: VoiceGifEntity,
        fromPreview: Boolean = false,
        index: Int,
    ) {
        /*val isQuietModeEnable = CommonMMKV.isQuietModeEnable
        val isOnRealTimeCall =
            routerServices<RealTimeCallService>().value?.isOnRealTimeCall() ?: false
        val isOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() ?: false
        val canPlayAudio = isQuietModeEnable.not()
                && isOnRealTimeCall.not()
                && isOnAir.not()
        if (canPlayAudio) {
            notifyItemPlayStateChange(voiceGifItem.id, VoiceGifPlayingState.Loading)
        }*/
        listener.sendVoiceItem(
            voiceItem = sendVoiceItemData,
            fromPreview = fromPreview,
            callback = object : VoiceItemSentResultCallback {
                override fun onResult(servMsgId: String?, success: Boolean) {
                    servMsgId ?: return
                    if (success) {
                        voiceGifPlayStateViewModel.addSendVoiceGif(
                            servMsgId = servMsgId,
                            subTabId = subTabId,
                            voiceGifEntity = voiceGifItem,
                            index = index
                        )
                    }
                }
            })
    }

    open fun getItemType(): ItemViewType {
        return ItemViewType.NormalVG
    }
}
