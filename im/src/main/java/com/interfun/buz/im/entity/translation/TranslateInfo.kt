package com.interfun.buz.im.entity.translation

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toSafeLong
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.im.entity.ShowTranslateTextOp
import com.interfun.buz.im.ktx.localExtraModel
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.utils.IM5MsgUtils

class TranslateInfo(
    val translateText: String? = null,
    val sourceLanguage: String? = null,
    val targetLanguage: String? = null,
    val translateCode: Int = TranslateConstant.UNKNOWN
)

data class TranslateResult(
    val msg: IMessage,
    var translateText: String? = null,
    var state: TranslateState = TranslateState.Idle,
    var isShow: Boolean = true
) {
    val TAG = "TranslateResult"

    fun isSuccess(): Boolean {
        return state == TranslateState.TranslateSuccess && !translateText.isNullOrEmpty()
    }

    /**
     * 这个标记当前这个关联 MSG 是否应该展示到列表中.这个展示是不区分成功失败的.返回 true就要展示,也许是展示错误或者 loading 等
     */
    fun shouldBeTranslation(): Boolean {

//        val enableTranslation = routerServices<ChatService>().value?.enableTranslationFun() == true
        val showTranslateText = userShowTranslateText()
        //这个回话是否开启了自动翻译
        val openSwitch = routerServices<ChatService>().value?.getConversationAutoTranslateSwitch(
            IM5MsgUtils.getConvTargetId(msg).toSafeLong()
        ) == true
        logInfo(TAG, "shouldBeTranslation invoke translateText = [${translateText}] , state = [${state}] isShow = [${isShow}] , showTranslateText = [${showTranslateText}] , openSwitch = [${openSwitch}]")
        //没有开启这个功能 不能展示
//        if (!enableTranslation) {
//            logInfo(TAG, "shouldBeTranslation return. !enableTranslation is true")
//            return false
//        }

        //源语言和目标语言一致的时候只有手动翻译打开的时候才显示
        if (!isShow && showTranslateText != ShowTranslateTextOp.MANUAL_OPEN) {
            logInfo(
                TAG,
                "shouldBeTranslation return. !isShow ${!isShow}  ShowTranslateTextOp ${showTranslateText}"
            )
            return false
        }

        //这种情况不展示
        if (!openSwitch && showTranslateText != ShowTranslateTextOp.MANUAL_OPEN) {
            logInfo(
                TAG,
                "shouldBeTranslation return. !openSwitch ${!openSwitch}  showTranslateText ${showTranslateText}"
            )
            return false
        }

        if (state == TranslateState.Idle) {
            logInfo(TAG, "shouldBeTranslation return. state is TranslateState.IDLE")
            return false
        }

        val ret = showTranslateText == ShowTranslateTextOp.MANUAL_OPEN
                || (showTranslateText == ShowTranslateTextOp.UN_OP && openSwitch)
        logInfo(TAG, "shouldBeTranslation ret ${ret} . showTranslateText ${showTranslateText} openSwitch ${openSwitch}")
        return ret
    }

    /**
     * 标记用户是否主动打开了某条消息的翻译.
     * 如果主动打开了那么为 true
     */
    @ShowTranslateTextOp
    fun userShowTranslateText(): Int {
        return msg.localExtraModel().showTranslateText
    }
    
}