package com.interfun.buz.notification.repository.mapping

import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.MessagingStyle
import androidx.core.app.NotificationCompat.MessagingStyle.Message
import androidx.core.app.Person
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.common.bean.push.extra.BasePushExtra.Companion.KEY_SER_MSG_ID
import com.interfun.buz.common.bean.push.extra.PrivateChatPushExtra.Companion.TAG
import com.interfun.buz.im.ktx.asrText
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.isOfficial
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.NotificationUtil.restoreMessagingStyle
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.im.ktx.getConversationId
import com.interfun.buz.im.ktx.isGroup
import com.interfun.buz.notification.model.Cancel
import com.interfun.buz.notification.model.CancelReason
import com.interfun.buz.notification.model.Data
import com.interfun.buz.notification.model.Error
import com.interfun.buz.notification.model.ErrorReason
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.notification.model.NotificationLogModel
import com.interfun.buz.notification.model.NotificationModel
import com.interfun.buz.notification.repository.NotificationRepository
import com.interfun.buz.notification.utils.NotificationUtils
import com.interfun.buz.notification.utils.getNotificationText
import com.interfun.buz.push.model.PushPayloadType
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage

internal class AutoPlayedMsgNotificationMapping {

    private val chatService by lazy { routerServices<ChatService>().value }

    suspend fun mapToNotificationModel(
        imMessage: IMessage,
        type: NotificationRepository.AutoPlayedMsgType
    ): NotificationHandleResult {
        return createNotificationModelForImMsg(imMessage, type)
    }


    private suspend fun createNotificationModelForImMsg(
        imMessage: IMessage,
        type: NotificationRepository.AutoPlayedMsgType
    ): NotificationHandleResult {
        if (isAppInForeground) {
            return Cancel(CancelReason.CHAT_APP_IN_FOREGROUND)
        }
        val isUpdateAsr = type == NotificationRepository.AutoPlayedMsgType.UPDATE_ASR
        if (isUpdateAsr && imMessage.asrText.isNullOrEmpty()) {
            return Cancel(CancelReason.CHAT_ASR_EDIT_TEXT_EMPTY)
        }
        if (MuteInfoManager.isMessageNotificationMuted(imMessage)) {
            log(
                TAG,
                "handleNotification message notification has been muted:${imMessage}"
            )
            return Cancel(CancelReason.CHAT_NOTIFICATION_MUTED)
        }
        if (imMessage.conversationType != IM5ConversationType.GROUP && imMessage.conversationType != IM5ConversationType.PRIVATE) {
            return Error(ErrorReason.CHAT_NOT_CHAT_PUSH_TYPE)
        }
        val isGroup = imMessage.conversationType == IM5ConversationType.GROUP
        val targetId = imMessage.getConvTargetIdLong()
        val notificationId = targetId.toInt().inv()
        val existStyle = restoreMessagingStyle(notificationId)
        if (isUpdateAsr) {
            if (existStyle == null) {
                return Cancel(CancelReason.CHAT_ASR_UPDATE_NOT_FOUND)
            } else {
                var isContain = false
                existStyle.messages.forEach {
                    val existStyleMessageSerMsgId = it.extras.getString(KEY_SER_MSG_ID)
                    if (existStyleMessageSerMsgId == imMessage.serMsgId) {
                        isContain = true
                    }
                }
                if (!isContain) {
                    return Cancel(CancelReason.CHAT_ASR_UPDATE_NOT_FOUND)
                }
            }
        }
        val isRecall = type == NotificationRepository.AutoPlayedMsgType.RECALL_MSG
        if (isRecall) {
            if (existStyle == null) {
                return Cancel(CancelReason.CHAT_RECALL_NOT_FOUND)
            } else {
                var isContain = false
                existStyle.messages.forEach {
                    val existStyleMessageSerMsgId = it.extras.getString(KEY_SER_MSG_ID)
                    if (existStyleMessageSerMsgId == imMessage.serMsgId) {
                        isContain = true
                    }
                }
                if (!isContain) {
                    return Cancel(CancelReason.CHAT_RECALL_NOT_FOUND)
                }
                if (isContain && existStyle.messages.size == 1) {
                    NotificationUtil.cancelNotification(appContext, notificationId)
                    return Cancel(CancelReason.CHAT_MSG_EMPTY_AFTER_RECALL)
                }
            }
        }
        val targetPerson = NotificationUtils.createTargetPerson(imMessage)
        //https://vocalbeats.sg.larksuite.com/wiki/V1gcwbOt3iJsL9ke2dMl71WWghh
        //  - 如果要跳转的对象聊天页已经打开，则页面不关闭
        //  - 如果要跳转的对象聊天页未打开,打开首页时，则直接对准对应好友
        val isTargetChatPageIsOpening = chatService?.nowChatListTargetId()?.value == targetId
        val router = if (isGroup) {
            if (isTargetChatPageIsOpening) {
                NotificationUtils.createGroupChatRouter(targetId, imMessage.serMsgId, null)
            } else {
                NotificationUtils.createHomeRouter(targetId = targetId.toString(), type = 2)
            }
        } else {
            if (isTargetChatPageIsOpening) {
                //需要注意因为消息可能是自己发的，所以不能用message.fromId跟targetId
                NotificationUtils.createPrivateChatRouter(
                    imMessage.getConversationId(),
                    UserSessionManager.uid.toString(),
                    imMessage.serMsgId,
                    null
                )
            } else {
                NotificationUtils.createHomeRouter(targetId = targetId.toString(), type = 1)
            }
        }
        val shortcutInfo = NotificationUtils.createShortcutInfo(targetId, targetPerson, isGroup)
        val style =
            getAutoPlayedVoiceMsgStyle(imMessage, targetPerson, existStyle)
        val number = style.messages.size
        val fromTargetType = if (isGroup) {
            "group"
        } else {
            if (isSenderOfficialAccount(targetId))
                "official_account"
            else
                "private"
        }
        val logModel = NotificationLogModel(
            pushType = if (isGroup) PushPayloadType.TYPE_GROUP else PushPayloadType.TYPE_PRIVATE,
            imMsgType = imMessage.msgType,
            fromTargetType = fromTargetType,
            convId = targetId.toString(),
            serMsgId = imMessage.serMsgId,
            isEditAsr = isUpdateAsr,
            isAutoPlayedMsg = true
        )
        val channel = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            NotificationChannelUtils.getAutoPlayedVoiceMsgChannel()
        } else {
            null
        }
        val model =
            NotificationModel(
                logModel = logModel,
                notificationId = notificationId,
                pendingRouter = router,
                number = number,
                channel = channel,
                shortcutInfo = shortcutInfo,
                onlyAlertOnce = isUpdateAsr || isRecall,
                style = style
            )
        return Data(model)
    }

    private suspend fun getAutoPlayedVoiceMsgStyle(
        message: IMessage,
        targetPerson: Person,
        existStyle: MessagingStyle?
    ): MessagingStyle {
        logInfo(
            TAG,
            "getAutoPlayedVoiceMsgStyle,msgId:${message.msgId},serMsgId:${message.serMsgId},targetId:${message.getConversationId()}"
        )
        val messagingStyle =
            NotificationCompat.MessagingStyle(targetPerson).setGroupConversation(message.isGroup)
        val person = NotificationUtils.createSendInfoPerson(message)
        val notificationMessage = Message(
            getNotificationText(message), message.createTime, person
        ).apply {
            extras.putString(KEY_SER_MSG_ID, message.serMsgId)
        }
        return if (existStyle != null) {
            logInfo(TAG, "getAutoPlayedVoiceMsgStyle:get existStyle ")
            existStyle.messages.forEach {
                val existStyleMessageSerMsgId = it.extras.getString(KEY_SER_MSG_ID)
                if ((message.msgType == IMType.TYPE_RECALL_MSG && message.serMsgId == existStyleMessageSerMsgId)) {
                    logInfo(TAG, "auto played pass serMsgId = $existStyleMessageSerMsgId ")
                    // 被撤回的消息不显示
                } else {
                    logInfo(TAG, "auto played reserve serMsgId = $existStyleMessageSerMsgId reserve")
                    messagingStyle.addMessage(it)
                }
            }
            val messageSerMsgId = message.serMsgId
            var hasReplace = false
            messagingStyle.messages.forEachIndexed { index, msg ->
                val existStyleMessageSerMsgId = msg.extras.getString(KEY_SER_MSG_ID)
                if (messageSerMsgId == existStyleMessageSerMsgId) {
                    logDebug(
                        TAG,
                        "replace messagingStyle: $messageSerMsgId"
                    )
                    messagingStyle.messages[index] = notificationMessage
                    hasReplace = true
                    return@forEachIndexed
                }
            }
            if (!hasReplace && message.msgType != IMType.TYPE_RECALL_MSG) {
                messagingStyle.addMessage(notificationMessage)
            }
            messagingStyle
        } else {
            messagingStyle.addMessage(notificationMessage)
            messagingStyle
        }
    }

    private suspend fun isSenderOfficialAccount(uid: Long?): Boolean {
        uid ?: return false
        val userInfo = UserRelationCacheManager.getUserRelationInfoFromCacheSync(uid)
        return userInfo?.isOfficial.getBooleanDefault()
    }


}