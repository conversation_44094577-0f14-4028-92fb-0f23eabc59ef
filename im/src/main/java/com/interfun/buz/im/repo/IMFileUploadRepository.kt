package com.interfun.buz.im.repo

import android.net.Uri

/**
 * Author: ChenYouSheng
 * Date: 2025/6/6
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 文件上传中的一个步骤，将系统的uri拷贝到应用的目录下
 */
interface IMFileUploadRepository {

    /**
     * 同步单个拷贝
     */
    suspend fun singleCopySync(uri: Uri): CopyResult

    /**
     * 清除所有等待上传的copy文件
     */
    fun cleanUploadFiles()

    /**
     * 清除指定uri的的copy文件
     */
    fun cleanUploadFileByUri(uri: Uri)


}

sealed interface CopyResult {
    val uri: Uri
    val path: String?

    data class CopySuccess(override val uri: Uri, override val path: String) : CopyResult
    data class CopyError(
        override val uri: Uri,
        override val path: String? = null,
        val errorMsg: String?
    ) : CopyResult
}