package com.interfun.buz.common.widget.view

import android.content.Context
import android.util.AttributeSet
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputConnection
import com.interfun.buz.base.ktx.CustomInputConnection
import com.interfun.buz.base.ktx.doOnKeyDownDel
import com.interfun.buz.base.ktx.textWithoutSpace
import com.interfun.buz.base.widget.view.PasteEditText

/**
 * <AUTHOR>
 * @date 2022/4/11
 * @desc
 */
open class PhoneEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
) : PasteEditText(context, attrs) {

    var areaCodeEditText: AreaCodeEditText? = null

    init {
        doOnKeyDownDel {
            if (areaCodeEditText != null && text.isNullOrEmpty()) {
                // 当内容为空且删除键被按下时，跳转到CountryCodeEditText
                areaCodeEditText?.requestFocus()
                true
            } else {
                false
            }
        }
    }

    fun getTextTrim() = textWithoutSpace

    override fun onCreateInputConnection(outAttrs: EditorInfo): InputConnection {
        return CustomInputConnection(super.onCreateInputConnection(outAttrs), true)
    }
}