package com.interfun.buz.liveplace.di

import android.app.Application
import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.interfun.buz.common.manager.cache.userPreferencesDataStore
import com.interfun.buz.liveplace.repository.LivePlaceBaseInfoRepositoryImpl
import com.interfun.buz.onair.standard.LivePlaceBaseInfoRepository
import com.interfun.buz.onair.standard.LivePlaceDevicesScoped
import com.interfun.buz.onair.standard.LivePlaceScoped
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class LivePlaceModule {
    companion object {
        private val Context.dataStore: DataStore<Preferences> by userPreferencesDataStore(name = "BuzLivePlace")
        private val Context.dataStoreDevices: DataStore<Preferences> by preferencesDataStore(name = "BuzLivePlace_Devices")

        @Provides
        @LivePlaceScoped
        fun provideLivePlaceDataStore(application: Application): DataStore<Preferences> {
            return application.dataStore
        }


        @Provides
        @LivePlaceDevicesScoped
        fun provideLivePlaceDataStoreDevices(application: Application): DataStore<Preferences> {
            return application.dataStoreDevices
        }


    }

    @Binds
    abstract fun bindLivePlaceBaseInfoRepository(livePlaceBaseInfoRepository: LivePlaceBaseInfoRepositoryImpl): LivePlaceBaseInfoRepository


}