@file:OptIn(ExperimentalCoroutinesApi::class, DelicateCoroutinesApi::class)

package com.interfun.buz.onair.ui.screen

import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.view.animation.Interpolator
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.detectDragGesturesAfterLongPress
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.changedToUp
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastForEachIndexed
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import coil.imageLoader
import coil.request.ImageRequest
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.compose.components.AndroidExternalSurfaceZOrder
import com.interfun.buz.compose.components.BuzAndroidExternalSurface
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.im.entity.OnAirGiftInfo
import com.interfun.buz.onair.R
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import java.util.LinkedList

/**
 * <AUTHOR>
 * @date 2024/8/19
 * @desc
 */
@Composable
internal fun GiftGuidance() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(R.color.black_60.asColor()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            modifier = Modifier
                .padding(horizontal = 20.dp),
            text = R.string.air_click_to_send_gift_tip.asString(),
            style = TextStyles.titleLarge(),
            textAlign = TextAlign.Center
        )
        VerticalSpace(dp = 10.dp)
        IconFontText(
            modifier = Modifier
                .width(72.dp)
                .height(108.dp),
            iconRes = R.string.ic_tap,
            iconColor = R.color.text_white_important.asColor(),
            iconSize = 72.dp
        )
    }
}

@Composable
internal fun GiftInput(onSendGift: (Int, Int, Boolean, Float, Float) -> Unit) {
    var isLongDrag by rememberMutableBoolean()
    val context = LocalContext.current
    Column(modifier = Modifier.fillMaxSize()) {
        VerticalSpace(dp = dimensionResource(id = R.dimen.title_bar_height))
        Box(modifier = Modifier
            .fillMaxWidth()
            .weight(1f)
            .pointerInput(Unit) {
                detectDragGesturesAfterLongPress(
                    onDragStart = {
                        isLongDrag = true
                        VibratorUtil.vibrator(context, 100)
                    },
                    onDragEnd = { isLongDrag = false },
                    onDragCancel = { isLongDrag = false },
                    onDrag = { _, _ -> }
                )
            }
            .pointerInput(Unit) {
                awaitEachGesture {
                    while (true) {
                        val event = awaitPointerEvent()
                        val fingerCount = event.changes.size
                        event.changes.fastForEachIndexed { fingerIndex, change ->
                            val changedToUp = change.changedToUp()
                            if (isLongDrag || changedToUp) {
                                val xPercent = change.position.x / size.width
                                val yPercent = change.position.y / size.height
                                onSendGift.invoke(
                                    fingerCount,
                                    fingerIndex,
                                    changedToUp,
                                    xPercent,
                                    yPercent
                                )
                            }
                        }
                    }
                }
            }
        )
        Box(
            modifier = Modifier
                .wrapContentHeight()
                .navigationBarsPadding()
        ) {
            VerticalSpace(dp = 82.dp)
        }
    }
}

private val singleContext = newSingleThreadContext("GiftDisplayScreen")

@Composable
internal fun BoxWithConstraintsScope.GiftDisplayScreen(
    giftBulletFlow: State<Flow<OnAirGiftInfo>>,
    maxShowNum: Int,
    isAr: Boolean
) {
    val maxWidthPx = maxWidth.dpToPx()
    // 坐标计算区域的0,0是从坐席区域开始算的，但需要扩展到状态栏部分，所以把这部分高度算出来手动offset
    val topAreaHeight= (getStatusBarHeight() + dimensionResource(id = R.dimen.title_bar_height)).dpToPx()
    val maxHeightPx = maxHeight.dpToPx() - topAreaHeight

    BuzAndroidExternalSurface(
        modifier = Modifier.fillMaxSize(),
        isOpaque = false,
        zOrder = AndroidExternalSurfaceZOrder.OnTop
    ) {surfaceView ->
        val paint = Paint()
        this.onSurface { surface, width, height ->
            val surfaceList = LinkedList<OnAirGiftInfoWrap>()
            launch(singleContext) {
                giftBulletFlow.value.collect { giftInfo ->
                    val sizeAndScale = createBitmapResizeAndScale(
                        screenWidth = maxWidthPx,
                        withRatio = giftInfo.widthRatio,
                        whRatio = giftInfo.whRatio
                    )
                    val info = OnAirGiftInfoWrap(
                        bitmapResize = sizeAndScale,
                        centerX = if (isAr) {
                            maxWidthPx - maxWidthPx * giftInfo.xPercent
                        } else {
                            maxWidthPx * giftInfo.xPercent
                        },
                        centerY = maxHeightPx * giftInfo.yPercent + topAreaHeight,
                        icoUrl = giftInfo.icoUrl,
                        rotation = giftInfo.rotation
                    )
                    val job = launch {
                        val request = ImageRequest.Builder(appContext)
                            .size(sizeAndScale.width, sizeAndScale.height)
                            .data(info.icoUrl)
                            .allowHardware(surfaceView.isHardwareAccelerated)
                            .build()
                        val resp = appContext.imageLoader.execute(request)
                        info.bitmap = (resp.drawable as? BitmapDrawable)?.bitmap
                    }
                    info.job = job
                    surfaceList.add(info)
                    launch {
                        launchAnimation(500) { fraction ->
                            // 从0.8到1的入场动画，再算上图片需要的缩放（图片都加载一样大的 bitmap，但缩放为不同大小渲染）
                            info.matrix.reset()
                            info.matrix.setTranslate(
                                info.centerX - info.bitmapResize.width / 2f,
                                info.centerY - info.bitmapResize.height / 2f
                            )
                            val totalScale = (0.8f + 0.2f * fraction) * info.bitmapResize.originScale
                            info.matrix.postScale(totalScale, totalScale, info.centerX, info.centerY)
                            info.matrix.postRotate(info.rotation, info.centerX, info.centerY)
                            info.alpha = (255 * fraction).toInt()
                        }
                        delay(2500)
                        launchAnimation(300) { fraction ->
                            info.alpha = (255 * (1 - fraction)).toInt()
                        }
                        delay(300)
                        info.dismiss = true
                        info.job?.cancel()
                        info.job = null
                        info.bitmap = null
                    }
                }
            }
            this.launch(singleContext) {
                while (this.isActive){
                    delay(35)
                    if (surfaceList.isNotEmpty()){
                        val canvas = try {
                            if (VERSION.SDK_INT >= VERSION_CODES.M && surfaceView.isHardwareAccelerated) {
                                surface.lockHardwareCanvas()
                            } else {
                                surface.lockCanvas(null)
                            }
                        } catch (t: Throwable) {
                            logError("GiftDisplayScreen","lock canvas error",t)
                            if (surfaceList.count { !it.dismiss } == 0) {
                                surfaceList.clear()
                            }
                            continue
                        }
                        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
                        var size = 0
                        val startIndex = if (surfaceList.size > maxShowNum) surfaceList.size - maxShowNum else 0
                        for (i in startIndex until surfaceList.size){
                            val info = surfaceList[i]
                            if (!info.dismiss){
                                size ++
                            }
                            if (!info.dismiss) {
                                info.bitmap?.let {
                                    paint.alpha = info.alpha
                                    canvas.drawBitmap(it, info.matrix, paint)
                                }
                            }
                        }
                        if (size == 0){
                            surfaceList.clear()
                        }
                        surface.unlockCanvasAndPost(canvas)
                    }
                }
            }
        }
    }
}

data class OnAirGiftInfoWrap(
    var bitmapResize: BitmapResize,
    val centerX: Float,
    val centerY: Float,
    val icoUrl: String,
    val rotation: Float,
    var alpha: Int = 255,
    var dismiss: Boolean = false,
    var bitmap: Bitmap? = null,
    var job: Job? = null,
    val matrix: Matrix = Matrix()
)

class BitmapResize(
    val width: Int,
    val height: Int,
    val originScale: Float //乘于此数 可得原始图片应该展示的大小
)

private fun createBitmapResizeAndScale(
    screenWidth: Float,
    withRatio: Float,
    whRatio: Float
): BitmapResize {
    //这两个值是根据默认大小是0.21算的，按照缩小40%来算的话，就是1.1开始
    val gap = 0.12f
    val start = 0.13f
    //每gap分一个块,向上取，减少bitmap加载数量,从start算起，因为不会有这么小的，可以更聚集
    val resizeRatio = if (withRatio > start) {
        (((withRatio - start) / gap).toInt() + 1) * gap
    } else {
        gap
    }
    val resizeWith = screenWidth * resizeRatio
    val resizeHeight = resizeWith / whRatio
    return BitmapResize(resizeWith.toInt(), resizeHeight.toInt(), withRatio / resizeRatio)
}

private fun CoroutineScope.launchAnimation(
    duration: Long = 250L,
    frameDelay: Long = 35L,
    interpolator: Interpolator? = FastOutSlowInInterpolator(),
    onUpdate: (fraction: Float) -> Unit
) = launch {
    val startTime = System.currentTimeMillis()
    val endTime = startTime + duration

    while (System.currentTimeMillis() < endTime) {
        val currentTime = System.currentTimeMillis()
        val fraction = (currentTime - startTime).toFloat() / duration
        onUpdate(interpolator?.getInterpolation(fraction) ?: fraction)
        delay(frameDelay)
    }
    onUpdate(1f)
}
