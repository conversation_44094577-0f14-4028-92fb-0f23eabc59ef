<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="220dp"
    android:height="220dp"
    android:viewportWidth="220"
    android:viewportHeight="220">
    <path
        android:fillColor="#141414"
        android:pathData="M110,2L110,2A108,108 0,0 1,218 110L218,110A108,108 0,0 1,110 218L110,218A108,108 0,0 1,2 110L2,110A108,108 0,0 1,110 2z" />
    <path android:pathData="M110,2L110,2A108,108 0,0 1,218 110L218,110A108,108 0,0 1,110 218L110,218A108,108 0,0 1,2 110L2,110A108,108 0,0 1,110 2z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:centerX="39.71"
                android:centerY="25.22"
                android:gradientRadius="233.98"
                android:type="radial">
                <item
                    android:color="#66FFFFFF"
                    android:offset="0" />
                <item
                    android:color="#51FFFFFF"
                    android:offset="0.21" />
                <item
                    android:color="#28FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillAlpha="0.3"
        android:pathData="M110,2L110,2A108,108 0,0 1,218 110L218,110A108,108 0,0 1,110 218L110,218A108,108 0,0 1,2 110L2,110A108,108 0,0 1,110 2z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="220"
                android:endY="220"
                android:startX="0"
                android:startY="0"
                android:type="linear">
                <item
                    android:color="#FF63C0E8"
                    android:offset="0" />
                <item
                    android:color="#FF7870DE"
                    android:offset="0.5" />
                <item
                    android:color="#FFD19C5A"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M110,2L110,2A108,108 0,0 1,218 110L218,110A108,108 0,0 1,110 218L110,218A108,108 0,0 1,2 110L2,110A108,108 0,0 1,110 2z"
        android:strokeWidth="4">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="220"
                android:endY="220"
                android:startX="0"
                android:startY="0"
                android:type="linear">
                <item
                    android:color="#FF63C0E8"
                    android:offset="0" />
                <item
                    android:color="#FF7870DE"
                    android:offset="0.5" />
                <item
                    android:color="#FFD19C5A"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>
