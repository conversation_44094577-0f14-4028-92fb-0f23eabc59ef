package com.interfun.buz.chat.common.view.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.InsetDrawable
import android.graphics.drawable.LayerDrawable
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.annotation.ColorInt
import androidx.core.animation.ArgbEvaluator
import androidx.core.animation.doOnEnd
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asDimension
import com.interfun.buz.base.ktx.dpFloat
import com.interfun.buz.chat.R
import com.interfun.buz.common.constants.RecordingConstant
import com.interfun.buz.common.widget.view.IconFontTextView
import kotlin.math.max
import kotlin.math.min

class ChatListRecordView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : IconFontTextView(context, attrs) {

    companion object {
        const val TAG = "ChatListRecordView"
        const val WHAT_ACTIVE = 1
        val EXPANDED_SIZE = 48.dpFloat
        val COLLAPSED_SIZE = R.dimen.chat_bottom_menu_button_size.asDimension()
        val bgColorExpand by lazy { R.color.basic_primary.asColor() }
        val bgColorCollapsed by lazy { R.color.color_background_5_default.asColor() }
        val colorTextExpand by lazy { R.color.color_text_black_primary.asColor() }
        val colorTextCollapsed by lazy { R.color.color_text_white_primary.asColor() }
        val dp_30 = 30.dpFloat
        val dp_200 = 200.dpFloat
    }
    private var downX = -1f
    private var downTime = 0L
    private var lastX = -1f
    var isActive = false
        private set
    private var stopMoving = false
    private var isCanceledByUser = false
    private val isRTL by lazy { layoutDirection == LAYOUT_DIRECTION_RTL }
    private val handler = Handler(Looper.getMainLooper(), Handler.Callback {
        return@Callback when (it.what) {
            WHAT_ACTIVE -> {
                true
            }

            else -> false
        }
    })
    private val scaleAnimator by lazy {
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.addUpdateListener { animator ->
            changeScaleAnimState(animator.animatedValue as Float)
        }
        animator.duration = 200
        animator
    }
    var callback: Callback? = null

    fun stopMoving() {
        stopMoving = true
//        animate().alpha(0f).setDuration(150).withEndAction {
//            isDeleting = false
//            onCancel(false)
//        }.start()
    }

    fun cancel(errorCode: Int=RecordingConstant.NORMAL) {
        this.isCanceledByUser = true
        onCancel(errorCode)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                if (callback?.onDown() == false) {
                    return false
                }
                onDown(event)
            }

            MotionEvent.ACTION_MOVE -> {
                move(event)
            }

            MotionEvent.ACTION_CANCEL -> {
                onCancel()
            }

            MotionEvent.ACTION_UP -> {
                if (isActive) {
                    onRelease()
                } else if (isCanceledByUser) {
                    resetLayout()
                } else {
                    onRelease()
                }
            }
        }
        return true
    }

    private fun move(event: MotionEvent) {
        val gap = event.rawX - lastX
        lastX = event.rawX
        if (stopMoving || isActive.not()) {
            return
        }
        val lastTranslationX = this.translationX
        //cannot drag to right side
        val translationX =
            if (isRTL) max(this.translationX + gap, 0f) else min(this.translationX + gap, 0f)
        val diff = translationX - lastTranslationX
        if (diff != 0f) {
            this.translationX = translationX
            callback?.onMove(diff, translationX)
        }
    }

    private fun postActiveEvent() {
        handler.sendEmptyMessageDelayed(WHAT_ACTIVE, 200)
    }

    private fun removeActiveEvent() {
        handler.removeMessages(WHAT_ACTIVE)
    }

    private fun onDown(event: MotionEvent) {
        this.isCanceledByUser = false
        downTime = System.currentTimeMillis()
        downX = event.rawX
        lastX = downX
        scaleAnimator?.cancel()
        scaleAnimator.start()
        onActive()
    }

    private fun onActive() {
        isActive = true
        stopMoving = false
        callback?.onActive()
    }

    private fun onCancel(errorCode: Int=RecordingConstant.NORMAL) {
        downX = -1f
        downTime = 0L
        isActive = false
        startResetAnim()
        callback?.onCanceled(errorCode)
    }

    private fun onRelease() {
        val oldIsActive = isActive
        downX = -1f
        downTime = 0L
        isActive = false
        startResetAnim()
        callback?.onReleased(oldIsActive)
    }

    private fun startResetAnim() {
        val animator = ValueAnimator.ofFloat(alpha, 0f)
        animator.addUpdateListener { animator ->
            alpha = animator.getAnimatedValue() as Float
        }
        animator.doOnEnd {
            alpha = 1f
            resetLayout()
        }
        animator.duration = 200
        animator.start()
    }

    private fun resetLayout() {
        changeScaleAnimState(0f)
        translationX = 0f
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        scaleAnimator.cancel()
    }

    private fun changeScaleAnimState(value: Float) {
        val bgColor = ArgbEvaluator.getInstance().evaluate(value, bgColorCollapsed, bgColorExpand)
        updateCircleColorInLayerList(bgColor)
        val textColor =
            ArgbEvaluator.getInstance().evaluate(value, colorTextCollapsed, colorTextExpand)
        setTextColor(textColor)
        val scale = 1 + value * ((EXPANDED_SIZE - COLLAPSED_SIZE) / COLLAPSED_SIZE)
        scaleX = scale
        scaleY = scale
    }

    private fun View.updateCircleColorInLayerList(@ColorInt color: Int) {
        val layerDrawable = this.background as? LayerDrawable
        layerDrawable?.let {
            val circleDrawable = it.getDrawable(it.numberOfLayers - 1) as? InsetDrawable
            val gradientDrawable = circleDrawable?.drawable as? GradientDrawable
            gradientDrawable?.setColor(color)
        }
    }

    override fun setPressed(pressed: Boolean) {
        usePressEffect = false
        super.setPressed(pressed)
    }

    interface Callback {
        fun onDown(): Boolean
        fun onActive()
        fun onReleased(isActive: Boolean)
        fun onCanceled(errorCode:Int)
        fun onMove(dx: Float, translationX: Float)
    }
}