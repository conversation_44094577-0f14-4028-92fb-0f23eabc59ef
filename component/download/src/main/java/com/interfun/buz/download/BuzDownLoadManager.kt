package com.interfun.buz.download

import android.content.Context
import android.os.SystemClock
import com.interfun.buz.download.BuzDownLoadManager.DownloadResult.FailureResult
import com.interfun.buz.download.BuzDownLoadManager.Operation.Cancel
import com.interfun.buz.download.BuzDownLoadManager.Operation.CancelAll
import com.interfun.buz.download.BuzDownLoadManager.Operation.Download
import com.interfun.buz.download.BuzDownLoadManager.Operation.GetCurrentTaskState
import com.interfun.buz.download.BuzDownLoadManager.Operation.Pause
import com.interfun.buz.download.BuzDownLoadManager.Operation.Resume
import com.interfun.buz.download.BuzDownLoadManager.Operation.ResumeFailureTask
import com.interfun.buz.download.IDownloadManager.Logger
import com.interfun.buz.download.bean.DownloadCancelInfo
import com.interfun.buz.download.bean.DownloadCancelInfo.DownloadInfo
import com.interfun.buz.download.bean.DownloadCancelInfo.FailureInfo
import com.interfun.buz.download.bean.DownloadCancelInfo.PauseInfo
import com.interfun.buz.download.bean.DownloadCancelInfo.PendingInfo
import com.interfun.buz.download.bean.DownloadStatus
import com.interfun.buz.download.bean.DownloadProgressChange
import com.interfun.buz.download.bean.DownloadStateInfo
import com.interfun.buz.download.bean.DownloadStatus.Companion.toDownloadStatus
import com.interfun.buz.download.db.BuzDownloadDatabase
import com.interfun.buz.download.db.entity.BuzDownloadCacheEntity
import com.interfun.buz.download.log.SystemLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ReceiveChannel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.selects.select
import java.io.File
import java.io.IOException
import java.io.RandomAccessFile
import java.net.HttpURLConnection
import java.net.URL
import java.security.MessageDigest
import java.util.LinkedList
import kotlin.coroutines.coroutineContext
import kotlin.math.min
import com.interfun.buz.download.utils.FileUtils
import java.io.FileNotFoundException

/**
 * Author: ChenYouSheng
 * Date: 2025/6/6
 * Email: <EMAIL>
 * Desc: https://vocalbeats.sg.larksuite.com/wiki/PUlvwj3eYiZBLBkLs1elRdNZgFd
 */
class BuzDownLoadManager(
    val appContext: Context,
    val downloadScope: CoroutineScope,
    val storagePathName: String,
    val logger: Logger = SystemLogger(),
    val readTimeout: Int = DEFAULT_READ_TIME_OUT,
    val connectTimeout: Int = DEFAULT_CONNECT_TIME_OUT
) : IDownloadManager {

    companion object {
        private const val MAX_CONCURRENT_TASKS = 5 // Maximum concurrency
        private const val PROGRESS_UPDATE_INTERVAL = 320L // 0.32 second throttling for progress
        private const val BUFFER_SIZE = 512 * 1024 // 512Kb buffer size
        private const val DEFAULT_READ_TIME_OUT = 10 * 60 * 1000 // 10 minutes read timeout
        private const val DEFAULT_CONNECT_TIME_OUT = 5 * 60 * 1000 // 5 minute connect timeout
        private const val TAG = "BuzDownLoadManager"
    }

    // Download home folder
    private val downloadFilesDir: File
        get() =
            FileUtils.getFilesDir(appContext, storagePathName).apply {
                if (!exists()) {
                    mkdirs()
                }
            }

    // Database instance
    private val downloadCacheDao = BuzDownloadDatabase.getInstance(appContext).getDownloadCacheDao()
    private val _progressFlow = MutableSharedFlow<DownloadProgressChange>()
    private val _stateFlow = MutableSharedFlow<DownloadStateInfo>()

    // 操作chanel
    private val operationChannel = Channel<Operation<*>>(Channel.UNLIMITED)

    /**
     * 下载任务
     */
    internal data class FileDownloadTask(
        val url: String, // download link
        val taskId: String, // taskId (generate by generateTaskId method)
        val fileName: String, // File name to save as
        val fileSize: Long = -1L, // Total file size, -1L if unknown
        val downloadedSize: Long = 0L, // Already downloaded size for resume
        val startTime: Long = 0L,
        val endTime: Long = 0L,
        val status: DownloadStatus,
        val filePath: String, // Full path to the downloaded file
        val downloadJob: Job? = null, // Coroutine Job for the download
        val source: String? = null, // For event tracking
    )

    internal sealed interface ISelectResult

    /**
     * 下载结果
     */
    internal sealed class DownloadResult(
        open val url: String,
        open val taskId: String
    ) : ISelectResult {
        data class ProgressResult(
            override val url: String,
            override val taskId: String,
            val downloadedSize: Long,
            val fileSize: Long,
            val fileName: String?,
            val progress: Int
        ) : DownloadResult(url, taskId)


        data class FailureResult(
            override val url: String,
            override val taskId: String,
            val reason: String
        ) : DownloadResult(url, taskId)

        data class CancelResult(
            override val url: String,
            override val taskId: String,
            val needDelete: Boolean
        ) :
            DownloadResult(url, taskId)

        data class FileSizeErrorResult(
            override val taskId: String,
            val fileName: String?,
            val reason: String,
            override val url: String
        ) : DownloadResult(url, taskId)

        data class PauseResult(
            override val url: String,
            override val taskId: String,
            val downloadedSize: Long
        ) : DownloadResult(url, taskId)

        data class FileSizeResult(
            override val url: String,
            override val taskId: String,
            val fileSize: Long
        ) : DownloadResult(url, taskId)

        data class SuccessResultWithProgress(
            override val url: String,
            override val taskId: String,
            val downloadedSize: Long,
            val fileSize: Long,
            val fileName: String?,
            val progress: Int
        ) : DownloadResult(url, taskId)
    }

    /**取消下载异常*/
    internal data class CancelException(val reason: String, val needDelete: Boolean) : CancellationException()

    /**暂停下载异常*/
    internal data class PauseException(val reason: String) : CancellationException()

    /**文件下载416异常*/
    internal data class FileSizeException(val reason: String) : IOException()

    /**
     * 用户操作行为
     */
    internal sealed interface Operation<T> : ISelectResult {
        val ack: CompletableDeferred<T>?

        data class Pending(
            val url: String,
            val fileName: String?,
            val serverFileSize: Long?,
            val source: String?,
            override val ack: CompletableDeferred<Boolean>?
        ) : Operation<Boolean>

        data class Download(
            override val ack: CompletableDeferred<Boolean>? = null
        ) : Operation<Boolean>

        data class Resume(
            val url: String,
            val fileName: String?,
            override val ack: CompletableDeferred<Boolean>? = null
        ) : Operation<Boolean>

        data class Pause(
            val url: String,
            val fileName: String?,
            override val ack: CompletableDeferred<Boolean>
        ) : Operation<Boolean>

        data class Cancel(
            val url: String,
            val fileName: String?,
            val needDelete: Boolean = false,
            override val ack: CompletableDeferred<Boolean>? = null
        ) : Operation<Boolean>

        data class CancelAll(
            override val ack: CompletableDeferred<List<DownloadCancelInfo>>
        ) : Operation<List<DownloadCancelInfo>>

        data class GetCurrentTaskState(
            val url: String,
            val fileName: String?,
            val checkDatabase: Boolean = true,
            override val ack: CompletableDeferred<DownloadStateInfo>? = null
        ) : Operation<DownloadStateInfo>

        data class DeleteFile(
            val url: String,
            val fileName: String?,
            override val ack: CompletableDeferred<Boolean>? = null
        ) : Operation<Boolean>

        data class ResumeFailureTask(
            override val ack: CompletableDeferred<Int>? = null
        ) : Operation<Int>
    }

    internal data class DownloadChanelResult(
        val url: String,
        val taskId: String,
        val channel: ReceiveChannel<DownloadResult>
    )

    internal val Operation<*>.taskId: String
        get() = when (this) {
            is Operation.Pending -> generateTaskId(url, fileName)
            is Operation.Resume -> generateTaskId(url, fileName)
            is Operation.Pause -> generateTaskId(url, fileName)
            is Operation.Cancel -> generateTaskId(url, fileName)
            is Operation.GetCurrentTaskState -> generateTaskId(url, fileName)
            is Operation.DeleteFile -> generateTaskId(url, fileName)
            is Operation.CancelAll -> md5("Operation.CancelAll@${this.hashCode()}")
            is Operation.Download -> md5("Operation.Download@${this.hashCode()}")
            is Operation.ResumeFailureTask -> md5("Operation.ResumeFailureTask@${this.hashCode()}")
        }


    init {
        downloadScope.launch {
            logInfo("[init] DownLoadManager coroutine started")
            val downloadingTasks = LinkedHashMap<String, FileDownloadTask>() // taskId -> task
            val pendingTasks = LinkedList<FileDownloadTask>()
            val pausedTasks = LinkedList<FileDownloadTask>()
            val errorTasks = LinkedList<FileDownloadTask>()
            val downloadStateList = ArrayList<DownloadChanelResult>()

            while (isActive) {
                val result = select<ISelectResult> {
                    // 等待操作指令
                    operationChannel.onReceiveCatching { ret ->
                        if (ret.isSuccess) {
                            val operation = ret.getOrThrow()
                            logInfo("[listenerResult] received ${operation.taskId} from operationChannel: $operation")
                            return@onReceiveCatching operation
                        } else {
                            logInfo("[listenerResult] received from operationChannel error")
                            throw ret.exceptionOrNull() ?: CancellationException()
                        }
                    }

                    downloadStateList.forEach { (url, taskId, channel) ->
                        channel.onReceiveCatching { ret ->
                            if (ret.isSuccess) {
                                val downloadResult = ret.getOrThrow()
                                if (downloadResult !is DownloadResult.ProgressResult) {
                                    logInfo("[listenerResult] received $taskId from DownloadResultChannel: $downloadResult")
                                }
                                return@onReceiveCatching downloadResult
                            } else {
                                val exception = ret.exceptionOrNull()
                                logInfo("[listenerResult] received $taskId from DownloadResultChannel error for $url: $exception")
                                throw exception ?: IllegalArgumentException()
                            }
                        }
                    }
                }
                when (result) {
                    is DownloadResult -> {
                        // 处理状态变更
                        handleDownloadResult(
                            result = result,
                            downloadingTasks = downloadingTasks,
                            downloadStateList = downloadStateList,
                            pendingTasks = pendingTasks,
                            pausedTasks = pausedTasks,
                            errorTasks = errorTasks,
                        )
                    }

                    is Operation<*> -> {
                        // 处理操作指令
                        handleOperation(
                            operation = result,
                            pausedTasks = pausedTasks,
                            pendingTasks = pendingTasks,
                            downloadStateList = downloadStateList,
                            downloadingTasks = downloadingTasks,
                            errorTasks = errorTasks
                        )
                    }
                }
            }
        }.invokeOnCompletion {
            logError(it,"[init] DownLoadManager invokeOnCompletion: $it")
            shutdown()
        }

        // 日志追踪进度
        downloadScope.launch {
            _progressFlow.collect { progress ->
                if (progress.progress % 20 == 0) {
                    logInfo("[progressFlow] ${progress.fileName}(${progress.progress}%):" +
                                "${progress.downloadedSize}/${progress.fileSize}, url=${progress.url}"
                    )
                }
            }
        }
        // 日志追踪状态
        downloadScope.launch {
            _stateFlow.collect { state ->
                if (state.progress == 0) {
                    logInfo("[stateFlow] status=${state.state}, fileName=${state.fileName}, url=${state.url}")
                }
            }
        }
    }

    /**
     * 处理操作指令
     * @param operation 操作指令
     * @param pausedTasks 暂停的任务
     * @param pendingTasks 等待下载的任务
     * @param downloadStateList 下载状态列表
     * @param downloadingTasks 正在下载的任务
     */
    private suspend fun handleOperation(
        operation: Operation<*>,
        pausedTasks: LinkedList<FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        errorTasks: LinkedList<FileDownloadTask>
    ) {
        logInfo("[handleOperation] operation=$operation, downloadStateList=$downloadStateList")
        when (operation) {
            is Operation.Pending -> {
                onPending(
                    operation = operation,
                    pausedTasks = pausedTasks,
                    pendingTasks = pendingTasks,
                    downloadingTasks = downloadingTasks
                )
                operation.ack?.complete(true)
            }

            is Operation.Download -> {
                onDownload(
                    downloadStateList = downloadStateList,
                    pendingTasks = pendingTasks,
                    downloadingTasks = downloadingTasks
                )
                operation.ack?.complete(true)
            }

            is Operation.Resume -> {
                onResume(
                    url = operation.url,
                    fileName = operation.fileName,
                    taskId = operation.taskId,
                    pausedTasks = pausedTasks,
                    pendingTasks = pendingTasks
                )
                operation.ack?.complete(true)
            }

            is Operation.Pause -> {
                onPause(
                    url = operation.url,
                    taskId = operation.taskId,
                    downloadingTasks = downloadingTasks,
                    pendingTasks = pendingTasks,
                    pausedTasks = pausedTasks,
                    downloadStateList = downloadStateList
                )
                operation.ack.complete(true)
            }

            is Operation.Cancel -> {
                onCancel(
                    operation = operation,
                    downloadingTasks = downloadingTasks,
                    pausedTasks = pausedTasks,
                    pendingTasks = pendingTasks,
                )
                operation.ack?.complete(true)
            }

            is Operation.CancelAll -> {
                val cancelInfo = onCancelAll(
                    downloadingTasks = downloadingTasks,
                    pausedTasks = pausedTasks,
                    pendingTasks = pendingTasks,
                    errorTasks = errorTasks
                )
                operation.ack.complete(cancelInfo)
            }

            is Operation.GetCurrentTaskState -> {
                onGetTaskState(
                    operation = operation,
                    pendingTasks = pendingTasks,
                    downloadingTasks = downloadingTasks,
                    pausedTasks = pausedTasks
                )
            }

            is Operation.DeleteFile -> {
                val success = onDeleteFile(operation = operation)
                operation.ack?.complete(success)
            }

            is Operation.ResumeFailureTask -> {
                val count = onResumeFailureTask(
                    downloadingTasks = downloadingTasks,
                    errorTasks = errorTasks
                )
                operation.ack?.complete(count)
            }
        }
    }




    /**
     * 处理下载结果
     * @param result 下载结果
     * @param downloadingTasks 正在下载的任务
     * @param downloadStateList 下载状态列表
     * @param pendingTasks 等待下载的任务
     * @param pausedTasks 暂停的任务
     */
    private suspend fun handleDownloadResult(
        result: DownloadResult,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>,
        pendingTasks: LinkedList<FileDownloadTask>,
        pausedTasks: LinkedList<FileDownloadTask>,
        errorTasks: LinkedList<FileDownloadTask>
    ) {
        if (result !is DownloadResult.ProgressResult) {
            logInfo("[handleDownloadResult] result=$result")
        }
        when (result) {
            is DownloadResult.ProgressResult -> {
                // 下载进度变更
                handleProgressResult(result,downloadingTasks)
            }

            is DownloadResult.FailureResult -> {
                // 下载失败
                handleFailureResult(
                    result = result,
                    downloadingTasks = downloadingTasks,
                    downloadStateList = downloadStateList,
                    pendingTasks = pendingTasks,
                    errorTasks = errorTasks
                )
            }

            is DownloadResult.SuccessResultWithProgress -> {
                // 本地已完成
                handleSuccessResultWithProgress(
                    result = result,
                    downloadStateList = downloadStateList,
                    downloadingTasks = downloadingTasks,
                    pendingTasks = pendingTasks
                )
            }

            is DownloadResult.CancelResult -> {
                // 下载取消，移除下载任务
                handleCancelResult(
                    result = result,
                    downloadingTasks = downloadingTasks,
                    pendingTasks = pendingTasks,
                    downloadStateList = downloadStateList
                )
            }

            is DownloadResult.PauseResult -> {
                // 下载暂停，移除下载任务
                handlePauseResult(
                    downloadingTasks = downloadingTasks,
                    result = result,
                    pausedTasks = pausedTasks,
                    pendingTasks = pendingTasks,
                    downloadStateList = downloadStateList
                )
            }

            is DownloadResult.FileSizeResult -> {
                // 下载文件大小
                handleFileSizeResult(result = result, downloadingTasks = downloadingTasks)
            }

            is DownloadResult.FileSizeErrorResult -> {
                // 下载文件大小错误
                handleFileSizeErrorResult(
                    result = result,
                    downloadingTasks = downloadingTasks,
                    pendingTasks = pendingTasks,
                    downloadStateList = downloadStateList
                )
            }
        }

    }


    ///////////////////////////////////////////////////////////
    ///////// Private API for handle operation ///////////////
    //////////////////////////////////////////////////////////

    private suspend fun onCancel(
        operation: Operation.Cancel,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        pausedTasks: LinkedList<FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>,
    ) {
        logInfo("[onCancel] call $operation")
        // 先检查正在下载的队列中是否有该任务，如果有，从下载队列中移除
        val downloadTask = downloadingTasks[operation.taskId]
        logInfo("[onCancel] downloadTask:${downloadTask}")

        downloadTask?.downloadJob?.cancel(
            CancelException(
                reason = "cancel by user",
                needDelete = operation.needDelete
            )
        )

        // 同时检查暂停队列中是否有该任务，如果有，从暂停队列中移除
        val pausedTask = pausedTasks.firstOrNull { it.taskId == operation.taskId }
        if (null != pausedTask) {
            logInfo("[onCancel] cancel pause task")
            val cancelTask = pausedTask.copy(
                status = DownloadStatus.CANCEL,
                endTime = System.currentTimeMillis()
            )
            saveDownloadCache(cancelTask)
            pausedTasks.remove(pausedTask)
            _stateFlow.emit(
                DownloadStateInfo(
                    operation.url,
                    cancelTask.fileName,
                    DownloadStatus.CANCEL
                )
            )
            // 触发下一个
            val first = pendingTasks.firstOrNull()
            if (null != first) {
                downloadAsync()
            }
        } else {
            // 再检查等待队列中是否有该任务，如果有，从等待队列中移除
            val pendingTask = pendingTasks.firstOrNull { it.taskId == operation.taskId }
            if (null != pendingTask) {
                logInfo("[onCancel] cancel pending task")
                val cancelTask = pendingTask.copy(
                    status = DownloadStatus.CANCEL,
                    endTime = System.currentTimeMillis()
                )
                saveDownloadCache(cancelTask)
                pendingTasks.remove(pendingTask)
                _stateFlow.emit(
                    DownloadStateInfo(
                        operation.url,
                        cancelTask.fileName,
                        DownloadStatus.CANCEL
                    )
                )
                // 触发下一个
                val first = pendingTasks.firstOrNull()
                if (null != first) {
                    downloadAsync()
                }
            }
        }
    }

    private fun onCancelAll(
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        pausedTasks: LinkedList<FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>,
        errorTasks: LinkedList<FileDownloadTask>
    ): List<DownloadCancelInfo> {
        val allTasks = downloadingTasks.values + pausedTasks + pendingTasks
        val activeCount = downloadingTasks.size + pendingTasks.size
        logInfo("[onCancelAll] call, allTasks size=${allTasks.size},activeCount=${activeCount}" +
                ",failureCount=${errorTasks.size},pausedCount=${pausedTasks.size}")
        val cancelInfoList = mutableListOf<DownloadCancelInfo>()
        if (downloadingTasks.isNotEmpty()) {
            cancelInfoList.addAll(downloadingTasks.values.map {
                DownloadInfo(url = it.url, fileName = it.fileName)
            })
        }
        if (pausedTasks.isNotEmpty()) {
            cancelInfoList.addAll(pausedTasks.map {
                PauseInfo(url = it.url, fileName = it.fileName)
            })
        }
        if (pendingTasks.isNotEmpty()) {
            cancelInfoList.addAll(pendingTasks.map {
                PendingInfo(url = it.url, fileName = it.fileName)
            })
        }
        if (errorTasks.isNotEmpty()) {
            cancelInfoList.addAll(errorTasks.map {
                FailureInfo(url = it.url, fileName = it.fileName)
            })
        }
        allTasks.forEach {
            cancelAsync(it.url, it.fileName)
        }
        errorTasks.clear()
        return cancelInfoList
    }

    private suspend fun onGetTaskState(
        operation: GetCurrentTaskState,
        pendingTasks: LinkedList<FileDownloadTask>,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        pausedTasks: LinkedList<FileDownloadTask>
    ) {
        val taskId = operation.taskId
        logInfo("onGetTaskState taskId=$taskId, operation=$operation")
        val isPending = pendingTasks.any { it.taskId == taskId }
        val isDownloading = downloadingTasks.containsKey(operation.taskId)
        val isPaused = pausedTasks.any { it.taskId == taskId }

        if (isPending) {
            operation.ack?.complete(
                DownloadStateInfo(
                    url = operation.url,
                    fileName = operation.fileName,
                    state = DownloadStatus.PENDING
                )
            )
        } else if (isDownloading) {
            val downloadingTask = downloadingTasks[taskId]
            val progress = if (downloadingTask != null && downloadingTask.fileSize > 0) {
                min(downloadingTask.downloadedSize * 100 / downloadingTask.fileSize, 100).toInt()
            } else 0
            operation.ack?.complete(
                DownloadStateInfo(
                    url = operation.url,
                    fileName = operation.fileName,
                    state = DownloadStatus.STARTED,
                    progress = progress
                )
            )
        } else if (isPaused) {
            operation.ack?.complete(
                DownloadStateInfo(
                    url = operation.url,
                    fileName = operation.fileName,
                    state = DownloadStatus.PAUSED
                )
            )
        } else if (operation.checkDatabase) {
            val cacheEntity = downloadCacheDao.querySingleCache(
                taskId = taskId,
                storagePath = storagePathName
            )
            if (cacheEntity?.status == DownloadStatus.SUCCESS.value) {
                val existFile = getDownloadFile(cacheEntity.remoteUrl, cacheEntity.fileName)
                if (existFile?.exists() == true && existFile.length() > 0) {
                    operation.ack?.complete(
                        DownloadStateInfo(
                            url = operation.url,
                            fileName = operation.fileName,
                            state = DownloadStatus.SUCCESS,
                            progress = 100
                        )
                    )
                } else {
                    operation.ack?.complete(
                        DownloadStateInfo(
                            url = operation.url,
                            fileName = operation.fileName,
                            state = DownloadStatus.IDLE
                        )
                    )
                }
            } else {
                operation.ack?.complete(
                    DownloadStateInfo(
                        url = operation.url,
                        fileName = operation.fileName,
                        state = cacheEntity?.status?.toDownloadStatus() ?: DownloadStatus.IDLE
                    )
                )
            }
        } else {
            operation.ack?.complete(
                DownloadStateInfo(
                    url = operation.url,
                    fileName = operation.fileName,
                    state = DownloadStatus.IDLE
                )
            )
        }
    }

    private suspend fun onPause(
        url: String,
        taskId: String,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>,
        pausedTasks: LinkedList<FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>
    ) {
        logInfo("[onPause] call taskId=$taskId, url=${url}")
        val downloadTask = downloadingTasks[taskId]
        // 取消正在进行的下载任务
        downloadTask?.downloadJob?.cancel(PauseException("pause by user"))

        // 如果在等等队列，就从等等队列中移除并加入到暂停队列中
        val pendingTask = pendingTasks.firstOrNull { it.taskId == taskId }
        if (null != pendingTask) {
            val pauseTask = pendingTask.copy(
                status = DownloadStatus.PAUSED,
                endTime = System.currentTimeMillis()
            )
            pendingTasks.remove(pendingTask)
            saveDownloadCache(pauseTask)
            pausedTasks.add(pauseTask)
            downloadStateList.filter { it.taskId == taskId }.forEach {
                it.channel.cancel()
            }
            downloadStateList.removeAll { it.taskId == taskId }
            _stateFlow.emit(DownloadStateInfo(url, pauseTask.fileName, DownloadStatus.PAUSED))
            // 触发下一个
            val first = pendingTasks.firstOrNull()
            if (null != first) {
                downloadAsync()
            }
        }
    }

    private suspend fun onResume(
        url: String,
        fileName: String?,
        taskId: String,
        pausedTasks: LinkedList<FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>
    ) {
        // 优先判断是否已经停了，已经停了，从暂停队列中移除，加入到等等队列中
        val pauseTask = pausedTasks.firstOrNull { it.taskId == taskId }
        if (null != pauseTask) {
            logInfo("[onResume] call from pause to resume: taskId=$taskId, $fileName: url=$url")
            pausedTasks.remove(pauseTask)
            val pendingTask = pauseTask.copy(
                status = DownloadStatus.PENDING
            )
            saveDownloadCache(pendingTask)
            pendingTasks.addFirst(pendingTask) // 加回到等待队列
            _stateFlow.emit(
                DownloadStateInfo(pendingTask.url, pendingTask.fileName, DownloadStatus.PENDING)
            )
            // 准备下载
            downloadAsync()
        } else {
            // 如果本身在等等队列，就调一下优先级
            val pendingTask = pendingTasks.firstOrNull { it.taskId == taskId }
            if (null != pendingTask) {
                logInfo("[onResume] call from pending to resume: taskId=$taskId, $fileName: url=$url")
                pendingTasks.remove(pendingTask)
                pendingTasks.addFirst(pendingTask)
                // 准备下载
                downloadAsync()
            } else {
                // 如果不在等等队列，就重新加到pending
                logInfo("[onResume] call from idle to resume: taskId=$taskId, $fileName: url=$url")
                pendingDownloadAsync(url, fileName)
            }
        }
    }

    private suspend fun onDownload(
        downloadStateList: ArrayList<DownloadChanelResult>,
        pendingTasks: LinkedList<FileDownloadTask>,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>
    ) {
        logInfo("[onDownload] call current downloading size: ${downloadingTasks.size}")
        if (downloadingTasks.size < MAX_CONCURRENT_TASKS) {
            pendingTasks.removeFirstOrNull()?.let { pendingTask ->
                val downloadTask = pendingTask.copy(
                    status = DownloadStatus.STARTED,
                    startTime = System.currentTimeMillis()
                )
                val downloadChannel = Channel<DownloadResult>(Channel.UNLIMITED)
                saveDownloadCache(downloadTask)
                downloadStateList.add(
                    DownloadChanelResult(
                        url = pendingTask.url,
                        taskId = pendingTask.taskId,
                        channel = downloadChannel
                    )
                )
                _stateFlow.emit(
                    DownloadStateInfo(
                        pendingTask.url,
                        downloadTask.fileName,
                        DownloadStatus.STARTED
                    )
                )
                // 并发执行下载
                val downloadJob = downloadScope.launch {
                    performDownload(
                        url = pendingTask.url,
                        taskId = pendingTask.taskId,
                        filePath = pendingTask.filePath,
                        fileName = pendingTask.fileName,
                        fileSize = pendingTask.fileSize,
                        downloadedSize = pendingTask.downloadedSize,
                        downloadChannel = downloadChannel
                    )
                }
                downloadingTasks[pendingTask.taskId] = downloadTask.copy(downloadJob = downloadJob)
            }
        } else {
            logInfo("[onDownload] downloading size >= $MAX_CONCURRENT_TASKS")
        }
    }


    private suspend fun onPending(
        operation: Operation.Pending,
        pausedTasks: LinkedList<FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>
    ) {
        val taskId = operation.taskId
        logInfo("[onPending] call taskId=$taskId, pending=$operation")
        // 如果已经等等队列或者暂停队列，直接调用resume
        val pauseTask = pausedTasks.firstOrNull { it.taskId == taskId }
        val pendingTask = pendingTasks.firstOrNull { it.taskId == taskId }
        val downloadingTask = downloadingTasks[taskId]
        if (null != pauseTask || null != pendingTask) {
            resumeAsync(operation.url, operation.fileName)
        } else if (downloadingTask == null) {
            // 创建下载的task，添加到等等队列中
            val nameToUse = operation.fileName ?: getFileNameFromUrl(operation.url)
            val filePath = File(getFileDirForUrl(operation.url), nameToUse).absolutePath
            val partialFile = File(filePath)
            val downloadedSize = if (partialFile.exists()) partialFile.length() else 0L
            val pendingTask = FileDownloadTask(
                url = operation.url,
                taskId = taskId,
                fileName = nameToUse,
                filePath = filePath,
                fileSize = operation.serverFileSize ?: -1,
                status = DownloadStatus.PENDING,
                downloadedSize = downloadedSize,
                source = operation.source,
                startTime = 0L,
                endTime = 0L,
            )

            if (downloadedSize == operation.serverFileSize && operation.serverFileSize > 0) {
                val successTask = pendingTask.copy(
                    startTime = System.currentTimeMillis(),
                    endTime = System.currentTimeMillis(),
                    status = DownloadStatus.SUCCESS
                )
                saveDownloadCache(successTask)
                _stateFlow.emit(
                    DownloadStateInfo(
                        operation.url,
                        successTask.fileName,
                        DownloadStatus.SUCCESS
                    )
                )
                logInfo("[onPending] already download success:${successTask}")
                return
            }

            if (pendingTask.fileSize > 0L && downloadedSize > pendingTask.fileSize) {
                partialFile.delete()
                logInfo("[onPending] downloadedSize large than fileSize")
            }

            logInfo("[onPending] $pendingTask, pendingTasks.size=${pendingTasks.size}")

            pendingTasks.add(pendingTask)
            saveDownloadCache(pendingTask)
            // 通知外部UI，先变成 PENDING 等待状态
            _stateFlow.emit(
                DownloadStateInfo(
                    operation.url,
                    pendingTask.fileName,
                    DownloadStatus.PENDING
                )
            )
            // 通知下载
            downloadAsync()
        } else {
            logInfo("[onPending] already in downloading:$downloadingTask")
            _stateFlow.emit(
                DownloadStateInfo(
                    operation.url,
                    operation.fileName,
                    DownloadStatus.STARTED
                )
            )
        }
    }

    private suspend fun onDeleteFile(operation: Operation.DeleteFile): Boolean {
        val file = getDownloadFile(operation.url, operation.fileName)
        val success = file?.delete() != false
        if (success) {
            downloadCacheDao.deleteDownloadCache(
                taskId = operation.taskId,
                storagePath = storagePathName
            )
            _stateFlow.emit(
                DownloadStateInfo(
                    url = operation.url,
                    state = DownloadStatus.IDLE,
                    fileName = operation.fileName,
                )
            )
        }
        logInfo("[onDeleteFile] final delete success=$success, operation=$operation")
        return success
    }

    private suspend fun onResumeFailureTask(
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        errorTasks: LinkedList<FileDownloadTask>
    ) : Int {
        // 排除正在下载中的
        val needResumeTasks = errorTasks.filterNot { it.taskId in downloadingTasks.keys }
        val count = needResumeTasks.size
        logInfo("[onResumeFailureTask] call, errorTasks size=${errorTasks.size}" +
                ",needResumeTasks=${count}")
        needResumeTasks.forEach { errorTask ->
            pendingDownloadAsync(
                url = errorTask.url,
                fileName = errorTask.fileName,
                serverFileSize = errorTask.fileSize,
                source = errorTask.source
            )
        }
        errorTasks.clear()
        return count
    }


    ///////////////////////////////////////////////////////////
    ///////// Private API for handle download result ///////////
    //////////////////////////////////////////////////////////

    private fun cancelDownloadChannel(
        taskId: String,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>
    ) {
        downloadingTasks.remove(taskId)
        downloadStateList.filter { it.taskId == taskId }.forEach {
            it.channel.cancel()
        }
        downloadStateList.removeAll { it.taskId == taskId }
    }


    private suspend fun handlePauseResult(
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        result: DownloadResult.PauseResult,
        pausedTasks: LinkedList<FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>
    ) {
        val downloadTask: FileDownloadTask? = downloadingTasks[result.taskId]
        val pauseTask = downloadTask?.copy(
            status = DownloadStatus.PAUSED,
            downloadedSize = result.downloadedSize,
            endTime = System.currentTimeMillis()
        )

        if (null != pauseTask) {
            pausedTasks.add(pauseTask)
            saveDownloadCache(pauseTask)
            cancelDownloadChannel(
                taskId = pauseTask.taskId,
                downloadingTasks = downloadingTasks,
                downloadStateList = downloadStateList
            )
        }

        _stateFlow.emit(DownloadStateInfo(result.url, pauseTask?.fileName, DownloadStatus.PAUSED))
        // 触发下一个
        val first = pendingTasks.firstOrNull()
        if (null != first) {
            downloadAsync()
        }
    }


    private suspend fun handleCancelResult(
        result: DownloadResult.CancelResult,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>
    ) {
        logInfo("[handleCancelResult] called: $result")
        val updateTask: FileDownloadTask? = downloadingTasks[result.taskId]
        val cancelTask = updateTask?.copy(
            status = DownloadStatus.CANCEL,
            endTime = System.currentTimeMillis()
        )
        var deleteSuccess = false
        if (null != cancelTask) {
            if (result.needDelete) {
                deleteSuccess = deleteFileInternal(
                    url = result.url,
                    fileName = cancelTask.fileName,
                    taskId = result.taskId
                )
            } else {
                saveDownloadCache(cancelTask)
            }

            cancelDownloadChannel(
                taskId = cancelTask.taskId,
                downloadingTasks = downloadingTasks,
                downloadStateList = downloadStateList
            )
        }

        _stateFlow.emit(
            DownloadStateInfo(
                result.url,
                cancelTask?.fileName,
                DownloadStatus.CANCEL
            )
        )

        if (deleteSuccess) {
            _stateFlow.emit(
                DownloadStateInfo(result.url, cancelTask?.fileName, DownloadStatus.IDLE)
            )
        }

        // 触发下一个
        val first = pendingTasks.firstOrNull()
        if (null != first) {
            downloadAsync()
        }
    }

    private suspend fun handleFailureResult(
        result: FailureResult,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>,
        pendingTasks: LinkedList<FileDownloadTask>,
        errorTasks: LinkedList<FileDownloadTask>
    ) {
        logInfo("[handleFailureResult] called result=${result}")
        val updateTask: FileDownloadTask? = downloadingTasks[result.taskId]
        val failureTask = updateTask?.copy(
            status = DownloadStatus.FAILURE(result.reason),
            endTime = System.currentTimeMillis()
        )
        if (null != failureTask) {
            saveDownloadCache(failureTask)
            cancelDownloadChannel(
                taskId = failureTask.taskId,
                downloadingTasks = downloadingTasks,
                downloadStateList = downloadStateList
            )

            logInfo("[handleFailureResult] add to errorTasks: ${failureTask.taskId}")
            errorTasks.add(failureTask)
            
            _stateFlow.emit(
                DownloadStateInfo(
                    result.url,
                    failureTask.fileName,
                    DownloadStatus.FAILURE(result.reason)
                )
            )
        }
        // 触发下一个
        val first = pendingTasks.firstOrNull()
        if (null != first) {
            downloadAsync()
        }

    }

    private suspend fun handleProgressResult(
        progressResult: DownloadResult.ProgressResult,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>
    ) {
        val downloadingTask = downloadingTasks[progressResult.taskId]
        if (null != downloadingTask) {
            val updateTask = downloadingTask.copy(
                downloadedSize = progressResult.downloadedSize,
                fileSize = progressResult.fileSize
            )
            downloadingTasks[progressResult.taskId] = updateTask
        }
        _progressFlow.emit(
            DownloadProgressChange(
                url = progressResult.url,
                progress = progressResult.progress,
                downloadedSize = progressResult.downloadedSize,
                fileSize = progressResult.fileSize,
                fileName = progressResult.fileName
            )
        )
        _stateFlow.emit(
            DownloadStateInfo(
                url = progressResult.url,
                state = DownloadStatus.STARTED,
                fileName = progressResult.fileName,
                progress = progressResult.progress
            )
        )
    }

    private suspend fun handleSuccessResultWithProgress(
        result: DownloadResult.SuccessResultWithProgress,
        downloadStateList: ArrayList<DownloadChanelResult>,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        pendingTasks: LinkedList<FileDownloadTask>
    ) {
        logInfo("[handleSuccessResultWithProgress] called: $result")
        val updateTask: FileDownloadTask? = downloadingTasks[result.taskId]
        val successTask = updateTask?.copy(
            downloadedSize = result.downloadedSize,
            fileSize = result.fileSize,
            endTime = System.currentTimeMillis(),
            status = DownloadStatus.SUCCESS
        )
        if (null != successTask) {
            saveDownloadCache(successTask)
            cancelDownloadChannel(
                taskId = successTask.taskId,
                downloadingTasks = downloadingTasks,
                downloadStateList = downloadStateList
            )
        }

        _progressFlow.emit(
            DownloadProgressChange(
                url = result.url,
                progress = 100,
                downloadedSize = result.downloadedSize,
                fileSize = result.fileSize,
                fileName = result.fileName
            )
        )
        _stateFlow.emit(
            DownloadStateInfo(
                url =result.url,
                fileName = successTask?.fileName,
                state = DownloadStatus.SUCCESS,
                progress = 100
            )
        )

        // 获取一个新的下载任务
        val first = pendingTasks.firstOrNull()
        if (null != first) {
            downloadAsync()
        }

    }

    private suspend fun handleFileSizeResult(
        result: DownloadResult.FileSizeResult,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>
    ) {
        val updateTask: FileDownloadTask? = downloadingTasks[result.taskId]
        if (null != updateTask) {
            val downloadTask = updateTask.copy(
                status = DownloadStatus.STARTED,
                fileSize = result.fileSize,
            )
            downloadingTasks[result.taskId] = downloadTask
            saveDownloadCache(downloadTask)
        }
    }

    private suspend fun handleFileSizeErrorResult(
        result: DownloadResult.FileSizeErrorResult,
        pendingTasks: LinkedList<FileDownloadTask>,
        downloadingTasks: LinkedHashMap<String, FileDownloadTask>,
        downloadStateList: ArrayList<DownloadChanelResult>
    ) {
        val updateTask: FileDownloadTask? = downloadingTasks[result.taskId]
        if (null != updateTask) {
            val finishTask = updateTask.copy(
                status = DownloadStatus.FAILURE(result.reason),
                endTime = System.currentTimeMillis()
            )
            deleteFileInternal(
                url = result.url,
                fileName = result.fileName,
                taskId = result.taskId
            )

            cancelDownloadChannel(
                taskId = finishTask.taskId,
                downloadingTasks = downloadingTasks,
                downloadStateList = downloadStateList
            )

            _stateFlow.emit(
                DownloadStateInfo(
                    result.url,
                    finishTask.fileName,
                    DownloadStatus.FAILURE(result.reason)
                )
            )
        }

        // 获取一个新的下载任务
        val first = pendingTasks.firstOrNull()
        if (null != first) {
            downloadAsync()
        }
    }

    private suspend fun performDownload(
        url: String,
        taskId: String,
        filePath: String,
        fileName: String,
        fileSize: Long,
        downloadedSize: Long,
        downloadChannel: Channel<DownloadResult>
    ) {
        logInfo("[performDownload] start taskId=${taskId}, $fileName (${downloadedSize}/${fileSize}): " +
                "url=$url,filePath=$filePath")
        var connection: HttpURLConnection? = null
        var randomAccessFile: RandomAccessFile? = null
        var currentDownloadedSize = if (downloadedSize < 0) 0L else downloadedSize
        var lastProgressUpdateTime = SystemClock.elapsedRealtime()
        var serverFileSize = -1L
        var responseCode = -1
        try {
            val httpUrl = URL(url)
            connection = httpUrl.openConnection() as HttpURLConnection
            connection.connectTimeout = connectTimeout
            connection.readTimeout = readTimeout

            if (currentDownloadedSize > 0) {
                connection.setRequestProperty("Range", "bytes=${currentDownloadedSize}-")
            }

            connection.connect()
            responseCode = connection.responseCode
            val contentLength = connection.getHeaderField("Content-Length")?.toLongOrNull()
            serverFileSize = contentLength ?: fileSize

            if (serverFileSize < 0) {
                throw IOException("Invalid server response, Content-Length is missing or invalid")
            }


            randomAccessFile = RandomAccessFile(filePath, "rw")
            
            // 文件总大小
            var totalFileSize = serverFileSize

            if (responseCode == HttpURLConnection.HTTP_PARTIAL) { // 断点下载
                randomAccessFile.seek(currentDownloadedSize)
                totalFileSize = serverFileSize + currentDownloadedSize // 断点下载需要累加已下载的大小
                val currentProgress = (currentDownloadedSize * 100 / totalFileSize).toInt()
                
                logInfo("[performDownload] start partial download ${fileName}(${currentProgress}%," +
                            "$currentDownloadedSize/$totalFileSize): $url ,$filePath")

                updateProgress(
                    downloadChannel,
                    url,
                    taskId,
                    currentDownloadedSize,
                    totalFileSize,
                    fileName,
                    currentProgress
                )
            } else if (responseCode == HttpURLConnection.HTTP_OK) { // 从头下载
                logInfo("[performDownload] start full download ${fileName}: $url ,$filePath")
                randomAccessFile.setLength(0)
                randomAccessFile.seek(0)
                currentDownloadedSize = 0L
                if (contentLength != fileSize && fileSize > 0) {
                    // 更新文件大小
                    downloadChannel.send(
                        DownloadResult.FileSizeResult(
                            url = url,
                            taskId = taskId,
                            fileSize = serverFileSize
                        )
                    )
                }
            } else if (responseCode == 416) {
                // local file length > server file length
                throw FileSizeException("Server 416 Exception:${fileName} ${downloadedSize}/${totalFileSize}")
            } else {
                throw IOException("Server $responseCode Exception:${fileName} ${connection.responseMessage}")
            }

            logInfo("[performDownload] responseCode:${responseCode}, " +
                    "contentLength:$contentLength, $fileName ($currentDownloadedSize/$totalFileSize):  $url")

            connection.inputStream.use { inputStream ->
                val buffer = ByteArray(BUFFER_SIZE)
                var bytesRead: Int
                var lastProgress = 0
                val outputFile = File(filePath)
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    coroutineContext.ensureActive()
                    randomAccessFile.write(buffer, 0, bytesRead)
                    currentDownloadedSize += bytesRead

                    
                    // update progress
                    val currentProgress = min((currentDownloadedSize * 100 / totalFileSize).toInt(), 100)
                    val currentTime = SystemClock.elapsedRealtime()
                    if (currentTime - lastProgressUpdateTime > PROGRESS_UPDATE_INTERVAL || currentProgress == 100) {
                        if (currentProgress != lastProgress) {
                            if (!outputFile.exists()) {
                                throw FileNotFoundException("File not found: $filePath")
                            }
                            updateProgress(
                                downloadChannel,
                                url,
                                taskId,
                                currentDownloadedSize,
                                totalFileSize,
                                fileName,
                                currentProgress
                            )
                            lastProgress = currentProgress
                            lastProgressUpdateTime = currentTime
                            randomAccessFile.fd.sync()
                        }
                    }
                }
                downloadChannel.send(
                    DownloadResult.SuccessResultWithProgress(
                        url = url,
                        taskId = taskId,
                        downloadedSize = currentDownloadedSize,
                        fileSize = currentDownloadedSize,
                        fileName = fileName,
                        progress = 100
                    )
                )
                logInfo("[performDownload] Download successfully for: $fileName, $url ,filePath=$filePath")
            }

        } catch (e: Exception) {
            when (e) {
                is PauseException -> {
                    downloadChannel.send(
                        DownloadResult.PauseResult(
                            url = url,
                            taskId = taskId,
                            downloadedSize = currentDownloadedSize
                        )
                    )
                }

                is CancelException -> {
                    downloadChannel.send(
                        DownloadResult.CancelResult(
                            url = url,
                            taskId = taskId,
                            needDelete = e.needDelete
                        )
                    )
                }

                is FileSizeException -> {
                    logError(e, "[performDownload] catch exception: ${e.message}")
                    downloadChannel.send(
                        DownloadResult.FileSizeErrorResult(
                            url = url,
                            taskId = taskId,
                            fileName = fileName,
                            reason = e.message.toString()
                        )
                    )
                }

                else -> {
                    logError(e, "[performDownload] catch exception: ${e.message}")
                    downloadChannel.send(
                        DownloadResult.FailureResult(
                            url = url,
                            taskId = taskId,
                            reason = e.message.toString()
                        )
                    )
                }
            }
        } finally {
            connection?.disconnect()
            try {
                randomAccessFile?.fd?.sync()
                randomAccessFile?.close()
            } catch (ignore: Exception) {

            }
            val totalSize = if (responseCode == HttpURLConnection.HTTP_PARTIAL) {
                currentDownloadedSize + serverFileSize
            } else serverFileSize
            logInfo(
                "[performDownload] finally end $fileName (${currentDownloadedSize}/${totalSize}): $url , $filePath"
            )
        }
    }

    private suspend fun updateProgress(
        downloadChannel: Channel<DownloadResult>,
        url: String,
        taskId: String,
        currentDownloadedSize: Long,
        totalFileSize: Long,
        fileName: String,
        currentProgress: Int
    ) {
        downloadChannel.send(
            DownloadResult.ProgressResult(
                url = url,
                taskId = taskId,
                downloadedSize = currentDownloadedSize,
                fileSize = totalFileSize,
                fileName = fileName,
                progress = currentProgress
            )
        )
    }

    private suspend fun saveDownloadCache(task: FileDownloadTask) {
        try {
            val cacheEntity = BuzDownloadCacheEntity(
                taskId = task.taskId,
                storagePath = storagePathName,
                remoteUrl = task.url,
                totalLength = task.fileSize,
                fileName = task.fileName,
                status = task.status.value,
                startTime = task.startTime,
                endTime = task.endTime,
            )
            downloadCacheDao.upsertDownloadCache(cacheEntity)
            logInfo("[saveDownloadCache] called: $task")
        } catch (e: Exception) {
            logError(e, "[saveDownloadCache] error: ${e.message}")
        }
    }

    private fun getFileNameFromUrl(url: String): String {
        return try {
            val path = URL(url).path
            val fileName = path.substringAfterLast('/')
            if (fileName.isBlank()) "downloaded_file_default" else fileName
        } catch (e: Exception) {
            "downloaded_file_default"
        }
    }

    private fun md5(str: String): String {
        val md = MessageDigest.getInstance("MD5")
        val bytes = md.digest(str.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    private fun getFileDirForUrl(url: String, autoCreate: Boolean = true): File {
        val dir = File(downloadFilesDir, md5(url))
        if (!dir.exists() && autoCreate) dir.mkdirs()
        return dir
    }


    private suspend fun deleteFileInternal(
        url: String,
        fileName: String?,
        taskId: String
    ): Boolean {
        val file = getDownloadFile(url, fileName)
        val success = file?.delete() != false
        if (success) {
            downloadCacheDao.deleteDownloadCache(taskId = taskId, storagePath = storagePathName)
        }
        logInfo("[deleteFileInternal] taskId=$taskId , success=${success}, file=$file")
        return success
    }

    private fun logInfo(msg: String) {
        logger.logI(tag = TAG, msg = msg)
    }

    private fun logError(throwable: Throwable?, msg: String) {
        logger.logE(tag = TAG, msg = msg, e = throwable)
    }

    ///////////////////////////////////////////////////////////
    ///////// Public API //////////////////////////////////////
    //////////////////////////////////////////////////////////

    override val progressFlow: SharedFlow<DownloadProgressChange> = _progressFlow.asSharedFlow()

    override val stateFlow: SharedFlow<DownloadStateInfo> = _stateFlow.asSharedFlow()


    // 生成任务id
    override fun generateTaskId(url: String, fileName: String?): String {
        val name = fileName ?: getFileNameFromUrl(url)
        val md5Name = md5(name)
        val finalUrl = if (url.contains("?")) {
            "$url&fileId=$md5Name$storagePathName"
        } else {
            "$url?fileId=$md5Name$storagePathName"
        }
        return md5(finalUrl)
    }

    override suspend fun getDownloadStateInfo(
        url: String,
        fileName: String?,
        checkDatabase: Boolean
    ): DownloadStateInfo {
        val ack = CompletableDeferred<DownloadStateInfo>()
        operationChannel.send(
            GetCurrentTaskState(
                url = url,
                fileName = fileName,
                checkDatabase = checkDatabase,
                ack = ack
            )
        )
        val complete = ack.await()
        logInfo("[getDownloadStatus] method call after: url=$url, complete=$complete")
        return complete
    }

    override fun isSameTask(
        currentStateInfo: DownloadStateInfo,
        url: String,
        fileName: String?
    ): Boolean {
        val currentTaskId = generateTaskId(currentStateInfo.url, currentStateInfo.fileName)
        val targetTaskId = generateTaskId(url, fileName)
        return currentTaskId == targetTaskId
    }

    override suspend fun pendingDownload(
        url: String,
        fileName: String?,
        serverFileSize: Long?,
        source: String?,
    ): Boolean {
        val ack = CompletableDeferred<Boolean>()
        operationChannel.send(
            Operation.Pending(
                url,
                fileName,
                serverFileSize,
                source,
                ack
            )
        )
        val complete = ack.await()
        logInfo("[pendingDownload] method call end: fileName=$fileName, " +
                    "serverFileSize=$serverFileSize, source=$source, url=$url")
        return complete
    }

    override suspend fun pendingDownloadAsync(
        url: String,
        fileName: String?,
        serverFileSize: Long?,
        source: String?,
    ) {
        operationChannel.send(Operation.Pending(url, fileName, serverFileSize, source, null))
        logInfo("[pendingDownloadAsync] method call end: fileName=$fileName, " +
                    "serverFileSize=$serverFileSize, source=$source, url=$url")
    }

    override suspend fun download(): Boolean {
        val ack = CompletableDeferred<Boolean>()
        operationChannel.send(Operation.Download(ack))
        val complete = ack.await()
        logInfo("[download] method call after complete=$complete")
        return complete
    }

    override fun downloadAsync() {
        downloadScope.launch {
            operationChannel.send(Download())
            logInfo("[downloadAsync] method call")
        }
    }

    override suspend fun pause(url: String, fileName: String?): Boolean {
        val ack = CompletableDeferred<Boolean>()
        operationChannel.send(Pause(url = url, fileName = fileName, ack = ack))
        val complete = ack.await()
        logInfo("[pause] method call after: url=$url, complete=$complete")
        return complete
    }


    override suspend fun resume(url: String, fileName: String?): Boolean {
        val ack = CompletableDeferred<Boolean>()
        operationChannel.send(Resume(url = url, fileName = fileName, ack = ack))
        val complete = ack.await()
        logInfo("[resume] method call after: url=$url, complete=$complete")
        return complete
    }

    override suspend fun resumeFailureTask(): Int {
        val ack = CompletableDeferred<Int>()
        operationChannel.send(ResumeFailureTask(ack = ack))
        val complete = ack.await()
        logInfo("[resumeFailureTask] method call after, complete=$complete")
        return complete
    }

    override fun resumeAsync(url: String, fileName: String?) {
        downloadScope.launch {
            operationChannel.send(Resume(url, fileName))
            logInfo("[resumeAsync] method call after: url=$url")
        }
    }

    override suspend fun cancel(url: String, fileName: String?, needDeleteFile: Boolean): Boolean {
        val ack = CompletableDeferred<Boolean>()
        operationChannel.send(
            Cancel(
                url = url,
                fileName = fileName,
                needDelete = needDeleteFile,
                ack = ack
            )
        )
        val complete = ack.await()
        logInfo("[cancel] method call after: url=$url, complete=$complete")
        return complete
    }

    override fun cancelAsync(url: String, fileName: String?) {
        downloadScope.launch {
            operationChannel.send(
                Cancel(
                    url = url,
                    fileName = fileName,
                    needDelete = false,
                    ack = null
                )
            )
            logInfo("[cancelAsync] method call after: url=$url")
        }
    }

    override suspend fun cancelAll(): List<DownloadCancelInfo> {
        val ack = CompletableDeferred<List<DownloadCancelInfo>>()
        operationChannel.send(CancelAll(ack = ack))
        val cancelInfo = ack.await()
        logInfo("[cancelAll] method call after, influence cancelInfo=$cancelInfo")
        return cancelInfo
    }


    override suspend fun deleteFile(
        url: String,
        fileName: String?,
    ): Boolean {
        logInfo("[deleteFile] start: url=$url, fileName=$fileName")
        val isDownloading = getDownloadStateInfo(
            url = url,
            fileName = fileName,
            checkDatabase = false
        ).state == DownloadStatus.STARTED
        if (isDownloading) {
            logInfo("[deleteFile] isDownloading, need cancel first")
            return cancel(url = url, fileName = fileName, needDeleteFile = true)
        }
        val ack = CompletableDeferred<Boolean>()
        operationChannel.send(
            Operation.DeleteFile(
                url = url,
                fileName = fileName,
                ack = ack
            )
        )
        return ack.await()
    }

    override suspend fun getDownloadFileSize(url: String, fileName: String?): Long =
        withContext(Dispatchers.IO) {
            val file = getDownloadFile(url, fileName)
            if (file?.exists() == true) {
                file.length()
            } else 0L
        }

    override suspend fun getDownloadFile(url: String, fileName: String?): File? =
        withContext(Dispatchers.IO) {
            val fileNameToUse = fileName ?: getFileNameFromUrl(url)
            val cacheDir = getFileDirForUrl(url, autoCreate = false)
            if (!cacheDir.exists()) {
                return@withContext null
            }
            File(cacheDir, fileNameToUse)
        }

    override suspend fun getAllDownloadFailureCache(): List<BuzDownloadCacheEntity> =
        withContext(Dispatchers.IO) {
            downloadCacheDao.getAllDownloadFailureCacheByBusinessType(storagePath = storagePathName)
        }

    override suspend fun queryAllDownloadCache(): List<BuzDownloadCacheEntity> =
        withContext(Dispatchers.IO) {
            downloadCacheDao.queryAllDownloadCache(storagePath = storagePathName)
        }

    override suspend fun removeCacheList(cacheList: List<BuzDownloadCacheEntity>) {
        withContext(Dispatchers.IO) {
            downloadCacheDao.removeCacheList(cacheList)
        }
    }

    override suspend fun getAllDownloadFileSize(): Long = withContext(Dispatchers.IO) {
        try {
            downloadFilesDir.walkTopDown()
                .filter { it.isFile }
                .sumOf { it.length() }
        } catch (e: Exception) {
            logInfo("[getAllDownloadFileSize] Failed: ${e.message}")
            0L
        }
    }

    override fun shutdown() {
        operationChannel.close()
        logInfo("[shutdown] called, operationChannel.closed")
    }

}