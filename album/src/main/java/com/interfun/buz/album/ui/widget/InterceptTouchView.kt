package com.interfun.buz.album.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View

/**
 * 拦截点击事件View
 * <AUTHOR>
 * @date 2024/4/8
 */
class InterceptTouchView(context: Context, attrs: AttributeSet) : View(context, attrs) {

    // 在上方的 View 中，重写 onTouchEvent 方法
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 判断点击事件的类型为 ACTION_DOWN，即按下事件
        if (event.action == MotionEvent.ACTION_DOWN) {
            // 判断点击点的位置是否在当前 View 区域内
            if (isInBound(event.x, event.y)) {
                // 在当前 View 区域内，消费点击事件，不继续透传到下方 View
                return true
            }
        }

        // 如果点击事件不在当前 View 区域内，不拦截事件，继续透传到下方 View
        return false
    }

    // 判断点击点是否在当前 View 区域内的方法
    private fun isInBound(x: Float, y: Float): Boolean {
        // 实现判断是否在 View 区域内的逻辑，例如可以通过 View 的宽高等属性来判断
        return x >= 0 && x <= width && y >= 0 && y <= height
    }
}