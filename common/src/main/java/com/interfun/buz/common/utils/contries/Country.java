package com.interfun.buz.common.utils.contries;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @desc
 */
public class Country {
    public String name;
    public String defaultName;
    public String code;
    public String shortname;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Country that = (Country) o;
        return Objects.equals(name, that.name) && Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, code);
    }
}
