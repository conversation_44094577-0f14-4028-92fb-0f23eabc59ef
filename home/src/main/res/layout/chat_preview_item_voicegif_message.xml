<?xml version="1.0" encoding="utf-8"?>
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:clipChildren="false"
    tools:layout_height="@dimen/home_preview_list_item_height"
    tools:background="@color/home_conv_list_bg"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:id="@+id/space_top"
        android:layout_width="match_parent"
        android:layout_height="10dp"/>

    <TextView
        android:id="@+id/tvTextMessage"
        style="@style/text_body_large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/home_wt_item_preview_horizontal_margin"
        android:layout_marginEnd="12dp"
        android:gravity="start"
        android:textColor="@color/color_text_white_secondary"
        android:maxLines="3"
        android:ellipsize="end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/pvVoiceGIF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space_top"
        tools:text="[VoiceGIF]" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clVoiceGIF"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/home_wt_item_preview_horizontal_margin"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space_top"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.interfun.buz.base.widget.round.RoundFrameLayout
            android:id="@+id/rfVoiceGif"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@color/color_background_5_default"
            app:round_bottom_end_radius="12dp"
            app:round_bottom_start_radius="12dp"
            app:round_top_end_radius="12dp"
            app:round_top_start_radius="12dp">

        <com.interfun.buz.common.widget.view.VoiceGifWebpAnimView
            android:id="@+id/pvVoiceGIF"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="-1dp"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/clBubble"
            app:layout_constraintTop_toTopOf="parent"
            android:scaleType="centerCrop"/>
        </com.interfun.buz.base.widget.round.RoundFrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clBubble"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="2dp"
            app:layout_constraintTop_toTopOf="@id/rfVoiceGif"
            app:layout_constraintStart_toEndOf="@id/rfVoiceGif">

            <com.interfun.buz.base.widget.round.RoundView
                android:id="@+id/middleCircle"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:layout_marginStart="-1dp"
                android:background="@drawable/chat_circle_overlay_grey_26"
                app:round_radius="8dp"
                app:layout_constraintBottom_toBottomOf="@id/bigCircle"
                app:layout_constraintStart_toStartOf="@id/bigCircle"/>

            <com.interfun.buz.base.widget.round.RoundConstraintLayout
                android:id="@+id/bigCircle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/chat_circle_overlay_grey_26"
                app:round_radius="20dp"
                android:paddingStart="2dp"
                android:paddingEnd="2dp">

                <ImageView
                    android:id="@+id/iftvBubbleLogo"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:gravity="center"
                    android:src="@drawable/common_ic_voice_moji_play"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.interfun.buz.base.widget.view.animContainer.AnimContainerView
                    android:id="@+id/pagBubbleLogo"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="@id/iftvBubbleLogo"
                    app:layout_constraintEnd_toEndOf="@id/iftvBubbleLogo"
                    app:layout_constraintStart_toStartOf="@id/iftvBubbleLogo"
                    app:layout_constraintTop_toTopOf="@id/iftvBubbleLogo" />

            </com.interfun.buz.base.widget.round.RoundConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>