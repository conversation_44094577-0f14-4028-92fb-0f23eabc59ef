package com.interfun.buz.demo.audio

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import androidx.annotation.RequiresApi
import com.lizhi.component.tekiplayer.util.TekiLog

class PlayerAudioFocusTestManager(val context: Context) : AudioManager.OnAudioFocusChangeListener {

    private var focusRequest: AudioFocusRequest? = null

    private val audioManager =
        context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    companion object {
        private const val TAG = "PlayerAudioFocusManager"
        const val UNSET = -1
    }

    var enabled: Boolean = true

    var audioFocusState: Int = UNSET

    fun request(): Boolean {
        if (!enabled) {
            return false
        }

        if (audioFocusState != UNSET) {
            return true
        }

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            internalRequestV26()
        } else {
            internalRequestDefault()
        }.also {
            if (it) {
                audioFocusState = AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            }
        }
    }

    private fun internalRequestDefault(): Boolean {
        val result = audioManager.requestAudioFocus(
            this,
            AudioManager.STREAM_MUSIC,
            AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
        )

        return onRequestResult(result)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun internalRequestV26(): Boolean {
        val playbackAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA)
            .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
            .build()

        val focusRequest =
            focusRequest ?: AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                .setAudioAttributes(playbackAttributes)
                .setAcceptsDelayedFocusGain(false)
                .setOnAudioFocusChangeListener(this)
                .build().also { this.focusRequest = it }

        val result = audioManager.requestAudioFocus(focusRequest)
        return onRequestResult(result)
    }

    private fun onRequestResult(result: Int): Boolean {
        val resultStr = when (result) {
            AudioManager.AUDIOFOCUS_REQUEST_FAILED -> "failed"
            AudioManager.AUDIOFOCUS_REQUEST_GRANTED -> "granted"
            AudioManager.AUDIOFOCUS_REQUEST_DELAYED -> "delayed"
            else -> "other"
        }
        TekiLog.i(TAG, "AudioManager requestAudioFocus result is $resultStr")

        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
    }

    fun abandon(): Boolean {
        if (!enabled) {
            return false
        }
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            internalAbandonV26()
        } else {
            internalAbandon()
        }.also {
            audioFocusState = UNSET
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun internalAbandonV26(): Boolean {
        val focusRequest = focusRequest ?: return false
        return audioManager.abandonAudioFocusRequest(focusRequest) == AudioManager.AUDIOFOCUS_REQUEST_GRANTED.also {
            this.focusRequest = null
        }
    }

    private fun internalAbandon(): Boolean {
        return audioManager.abandonAudioFocus(this) == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
    }

    override fun onAudioFocusChange(focusChange: Int) {
        TekiLog.i(TAG, "onAudioFocusChange, audioFocus=$focusChange")
        audioFocusState = focusChange
        if (!enabled) {
            return
        }
    }
}