package com.interfun.buz.chat.common.view.block


import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat.Type
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.interfun.buz.base.ktx.collect
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.chat.common.viewmodel.ChatBottomMenuPanelViewModel
import com.interfun.buz.chat.common.viewmodel.ChatMsgViewModelNew
import com.interfun.buz.chat.databinding.ChatFragmentMsgListBinding
import com.interfun.buz.chat.databinding.ChatInputEditTextBinding
import com.interfun.buz.common.base.binding.BaseBindingBlock

/**
 * <AUTHOR> <PERSON>
 * @date 2024/11/26
 * @desc 触发引用时，显示键盘
 */
class ChatReplyKeyBoardBlock(
    val fragment: Fragment,
    binding: ChatFragmentMsgListBinding,
    private val inputTextBinding: ChatInputEditTextBinding,
    chatMsgViewModelClass: Class<out ChatMsgViewModelNew>,
) : BaseBindingBlock<ChatFragmentMsgListBinding>(binding) {

    private val chatMsgViewModel by lazy { ViewModelProvider(fragment)[chatMsgViewModelClass] }
    private val chatBottomMenuPanelViewModel by fragment.viewModels<ChatBottomMenuPanelViewModel>()
    private var isFirstShow = true

    companion object {
        const val TAG = "ChatReplyKeyBoardBlock"
    }

    override fun initData() {
        super.initData()
        chatMsgViewModel.replyMsgStateFlow.collect(fragment.viewLifecycleOwner) { msg ->
            logDebug(TAG, "replyMsgStateFlow.collect, $isFirstShow")
            if (msg.isNotNull()) {
                if (isFirstShow) {
                    isFirstShow = false
                    showKeyboardDelay(200)
                } else {
                    showKeyboardDelay()
                }
            }
        }
    }

    private fun showKeyboardDelay(delay: Long = 100) {
        chatBottomMenuPanelViewModel.hideBottomPanel()
        inputTextBinding.etBottomInput.run {
            postDelayed({
                logDebug(TAG, "showKeyboardDelay delay: $delay")
                fragment.activity?.let { activity ->
                    if (!activity.isFinishing && !activity.isDestroyed) {
                        requestFocus()
                        WindowCompat.getInsetsController(activity.window, this).show(Type.ime())
                    }
                }
            }, delay)
        }
    }
}