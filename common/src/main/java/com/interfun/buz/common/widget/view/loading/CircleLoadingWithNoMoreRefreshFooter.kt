package com.interfun.buz.common.widget.view.loading

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.R
import com.interfun.buz.common.databinding.CommonRefreshLoadingWithNoMoreViewBinding
import com.scwang.smart.refresh.layout.api.RefreshComponent
import com.scwang.smart.refresh.layout.api.RefreshFooter
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle


class CircleLoadingWithNoMoreRefreshFooter constructor(
    context: Context,
    attrs: AttributeSet? = null
) : FrameLayout(context, attrs), RefreshComponent, RefreshFooter {

    private val mTextLoading = R.string.chat_leave_msg_loading.asString() //"正在加载..."
    private val mTextNothing = R.string.end_of_search_results.asString() //"没有更多数据了"

    private var mNoMoreData: Boolean = false
    private val binding: CommonRefreshLoadingWithNoMoreViewBinding =
        CommonRefreshLoadingWithNoMoreViewBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        binding.loadingView.pagColor = R.color.text_white_main.asColor(context)
        binding.loadingView.alpha = 0.4f
    }

    @SuppressLint("RestrictedApi")
    override fun onStateChanged(
        refreshLayout: RefreshLayout,
        oldState: RefreshState,
        newState: RefreshState
    ) {
        when (newState) {
            RefreshState.LoadFinish,
            RefreshState.None -> {
                binding.loadingView.stopLoading()
            }

            else -> {
                if (binding.loadingView.isPlaying.not()) {
                    binding.loadingView.startLoading()
                }
            }
        }
    }

    @SuppressLint("RestrictedApi")
    override fun setNoMoreData(noMoreData: Boolean): Boolean {
        if (mNoMoreData != noMoreData) {
            mNoMoreData = noMoreData
            if (noMoreData) {
                binding.tvTips.text = mTextNothing
                binding.loadingView.gone()
            } else {
                binding.tvTips.text = mTextLoading
                binding.loadingView.visible()
            }
        }
        return true
    }


    override fun getView(): View {
        return this
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return SpinnerStyle.Translate
    }

    @SuppressLint("RestrictedApi")
    override fun setPrimaryColors(vararg colors: Int) {
    }

    @SuppressLint("RestrictedApi")
    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {
    }

    @SuppressLint("RestrictedApi")
    override fun onMoving(
        isDragging: Boolean,
        percent: Float,
        offset: Int,
        height: Int,
        maxDragHeight: Int
    ) {
    }

    @SuppressLint("RestrictedApi")
    override fun onReleased(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
    }

    @SuppressLint("RestrictedApi")
    override fun onStartAnimator(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
    }

    @SuppressLint("RestrictedApi")
    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        if (!mNoMoreData) {
            binding.tvTips.text = mTextLoading
        }
        return 0
    }

    @SuppressLint("RestrictedApi")
    override fun onHorizontalDrag(percentX: Float, offsetX: Int, offsetMax: Int) {
    }

    override fun isSupportHorizontalDrag(): Boolean {
        return false
    }


}