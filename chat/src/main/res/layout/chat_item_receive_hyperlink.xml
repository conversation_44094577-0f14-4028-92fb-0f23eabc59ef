<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:paddingVertical="@dimen/chat_item_between_same_padding_top"
    tools:background="@color/overlay_grey_10"
    tools:paddingHorizontal="@dimen/chat_list_horizontal_padding">

    <TextView
        style="@style/caption"
        android:id="@+id/tvGroupMemberName"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:paddingHorizontal="6dp"
        android:gravity="center_vertical"
        android:background="@drawable/common_r10_overlay_white_6"
        android:textColor="@color/text_white_secondary"
        android:visibility="gone"
        android:maxLines="1"
        android:maxWidth="200dp"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="@id/clTextLayout"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/replyView"
        tools:layout_marginTop="@dimen/chat_item_between_different_padding_top"
        tools:visibility="visible"
        tools:text="Kathryn" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/clTextLayout"
        app:layout_constraintEnd_toEndOf="@+id/clTextLayout"
        app:layout_constraintStart_toStartOf="@+id/clTextLayout"
        app:layout_constraintTop_toTopOf="@+id/clTextLayout" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="@dimen/chat_item_portrait_size"
        android:layout_height="@dimen/chat_item_portrait_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:src="@drawable/common_user_default_portrait_round"
        tools:visibility="visible" />

    <com.interfun.buz.chat.common.view.widget.ReplyItemView
        android:layout_width="0dp"
        android:id="@+id/replyView"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/chat_dimen_reply_height_at_history_Bottom"
        android:paddingTop="@dimen/chat_dimen_reply_height_at_history_top"
        app:layout_constraintWidth_min="@dimen/chat_dimen_mim_width_reply"
        android:layout_marginTop="@dimen/chat_item_between_same_padding_top"
        android:layout_marginStart="@dimen/chat_item_receive_margin_start_with_portrait"
        android:layout_marginEnd="@dimen/chat_item_receive_margin_end_with_portrait"
        android:background="@color/overlay_black_80"
        app:round_top_left_radius="@dimen/chat_item_bg_radius"
        app:round_top_right_radius="@dimen/chat_item_bg_radius"
        app:round_bottom_left_radius="@dimen/chat_item_bg_radius_min"
        app:round_bottom_right_radius="@dimen/chat_item_bg_radius"
        app:layout_goneMarginTop="0dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/tvGroupMemberName"
        app:layout_goneMarginStart="0dp"/>

    <com.interfun.buz.base.widget.round.RoundConstraintLayout
        android:id="@+id/clTextLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/chat_item_receive_margin_start_with_portrait"
        android:layout_marginEnd="@dimen/chat_item_receive_margin_end_with_portrait"
        android:background="@color/color_background_5_default"
        android:minWidth="@dimen/chat_item_bg_min_height"
        android:minHeight="@dimen/chat_item_bg_min_height"
        android:paddingHorizontal="@dimen/chat_message_item_horizontal_padding"
        android:paddingVertical="@dimen/chat_message_item_vertical_padding"
        android:layout_marginTop="@dimen/chat_dimen_reply_height_at_history_diff"
        app:layout_goneMarginTop="0dp"
        android:textColor="@color/text_white_main"
        app:round_top_left_radius="@dimen/chat_item_bg_radius_min"
        app:round_top_right_radius="@dimen/chat_item_bg_radius"
        app:round_bottom_left_radius="@dimen/chat_item_bg_radius_min"
        app:round_bottom_right_radius="@dimen/chat_item_bg_radius"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/replyView"
        app:layout_goneMarginStart="0dp">

        <TextView
            android:id="@+id/tvLinkText"
            style="@style/body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textColor="@color/text_white_important"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Hey, please check this link and reply me soon https://medium.com" />

        <View
            android:id="@+id/viewLine"
            android:layout_width="0dp"
            android:layout_height="0.7dp"
            android:layout_marginTop="6dp"
            android:background="@color/color_outline_2_default"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvLinkText" />

        <View
            android:id="@+id/viewMetadata"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/viewLine"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <Space
            android:id="@+id/spaceViewLineBottom"
            android:layout_width="0dp"
            android:layout_height="6dp"
            app:layout_constraintTop_toBottomOf="@id/viewLine"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/siteBottomBarrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="ivSiteLogo, tvSiteName" />

        <com.interfun.buz.base.widget.round.RoundImageView
            android:id="@+id/ivSiteLogo"
            android:layout_width="16dp"
            android:layout_height="24dp"
            android:paddingTop="1dp"
            android:paddingBottom="7dp"
            android:visibility="gone"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/spaceViewLineBottom"
            app:round_radius="4dp"
            tools:visibility="visible"
            tools:background="@color/text_white_main" />

        <com.interfun.buz.common.widget.view.AutoDirectionTextView
            android:id="@+id/tvSiteName"
            style="@style/description"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:paddingBottom="6dp"
            android:layout_marginStart="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:gravity="start|center_vertical"
            android:textColor="@color/text_white_secondary"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivSiteLogo"
            app:layout_constraintTop_toBottomOf="@id/spaceViewLineBottom"
            app:layout_goneMarginStart="0dp"
            tools:text="Medium"/>

        <com.interfun.buz.base.widget.round.RoundImageView
            android:id="@+id/ivSiteCover"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:layout_constraintDimensionRatio="237:130"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/siteBottomBarrier"
            app:round_radius="@dimen/chat_item_hyperlink_cover_image_radius"
            tools:visibility="visible"
            tools:background="@color/text_white_main" />

        <TextView
            android:id="@+id/tvLinkTitle"
            style="@style/subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="center_vertical"
            android:textColor="@color/text_white_main"
            android:textSize="15sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivSiteCover"
            app:layout_goneMarginTop="0dp"
            tools:text="Creating memory models for users: a helpful guide to learning theory" />


        <com.interfun.buz.common.widget.view.AutoDirectionTextView
            android:id="@+id/tvLinkDesc"
            style="@style/description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="start|center_vertical"
            android:textColor="@color/text_white_secondary"
            android:textSize="14sp"
            android:ellipsize="end"
            android:maxLines="2"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvLinkTitle"
            tools:text="How to navigate redesigns due to changing business needs — “So it sounds like we’re starting over again.” I sighed, half expecting the" />

    </com.interfun.buz.base.widget.round.RoundConstraintLayout>

    <com.interfun.buz.common.widget.view.RoundIconFontView
        android:id="@+id/iftvTranslate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:background="@color/blue_20"
        android:padding="4dp"
        android:text="@string/ic_translate"
        android:textColor="@color/blue_100"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/spaceContent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/spaceContent"
        app:layout_constraintTop_toTopOf="@+id/spaceContent"
        app:round_radius="20dp" />

    <com.interfun.buz.chat.common.view.widget.ChatTranslationItemView
        android:layout_width="wrap_content"
        android:layout_marginTop="@dimen/chat_dimen_translation_mar_top"
        app:layout_constrainedWidth="true"
        android:layout_height="wrap_content"
        app:layout_constraintHorizontal_bias="0"
        android:visibility="gone"
        android:background="@drawable/chat_rec_translation_pop_bg"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/translateContainer"
        android:layout_marginEnd="@dimen/chat_item_portrait_size"
        app:layout_constraintStart_toStartOf="@id/spaceContent"
        app:layout_constraintTop_toBottomOf="@id/spaceContent" />




</com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout>