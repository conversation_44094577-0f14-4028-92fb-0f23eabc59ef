package com.interfun.buz.im.entity

/**
 * Author: Chen<PERSON>ouSheng
 * Date: 2023/5/10
 * Email: chen<PERSON><PERSON><PERSON>@lizhi.fm
 * Desc:
 */
object IMCommandBusinessType {
    const val TYPE_UNKNOWN = -1 // 未知命令
    const val TYPE_SERVER_REFRESH = 1 // 服务端用于刷新会话
    const val TYPE_INVERT_USER_TO_GROUP = 2 // 弹框显示邀请进群
    const val TYPE_GROUP_CENTER_NOTIFY = 3  // 群聊居中消息
    const val TYPE_CLICKABLE_CENTER_NOTIFY = 4  // 居中可点击消息
    const val TYPE_VOICE_CALL = 5 //voice call业务居中消息
    const val TYPE_ON_AIR = 6 //on air 业务居中消息
    const val TYPE_ON_LIVE_PLACE = 7 // live place 业务居中消息
}

/**
 * 相同的[IMCommandBusinessType]数据类型, 会通过[IMCommandSubBusinessType]来区分业务
 */
object IMCommandSubBusinessType {
    const val TYPE_UNKNOWN = -1  // 未知命令
    const val TYPE_UPDATE_GROUP_NAME = 1  // 更新群名提示
    const val TYPE_INVITE_TO_GROUP = 2 // 邀请进群提示
    const val TYPE_NEW_INVITE_TO_GROUP = 3 // 创建的时候邀请进群提示
    const val TYPE_AI_DESCRIPTION_INFO = 4 // AI描述信息

    const val TYPE_VOICE_CALL_STARTED = 5 //voice call message 'xx start a voice call'
    const val TYPE_VOICE_CALL_END = 6 //voice call end message
    const val TYPE_VOICE_CALL_MISSED = 7 //voice call missed message
    const val TYPE_VOICE_CALL_BUSY = 8 //line busy, call not received

    const val TYPE_ON_AIR_START = 9 //start on air message
    const val TYPE_ON_AIR_END = 10 //on air end message
    const val TYPE_ON_AIR_MISS = 11 //miss a on air invite message
    const val TYPE_ON_AIR_BUSY = 12 //private on air invite busy message
    //开启 LivePlace 居中消息
    const val TYPE_ON_LIVE_PLACE_START = 13
    //结束 LivePlace 居中消息
    const val TYPE_ON_LIVE_PLACE_END = 14
    //错过LivePlace邀请居中消息
    const val TYPE_ON_LIVE_PLACE_MISS = 15
    //LivePlace忙线未接听居中消息
    const val TYPE_ON_LIVE_PLACE_BUSY = 16
    //LivePlace Topic更新居中消息
    const val TYPE_ON_LIVE_PLACE_TOPIC_UPDATE = 17
    //创建 LivePlace 居中消息
    const val TYPE_ON_LIVE_PLACE_CREATE = 18
    //LivePlace居中消息,客态视角的敲门消息（I knocked XX's Live Place）
    const val TYPE_ON_LIVE_PLACE_KNOCK_GUEST = 19
    //LivePlace居中消息,主态视角的敲门消息（XXX knocked on your Live Place）
    const val TYPE_ON_LIVE_PLACE_KNOCK_HOST = 20
}