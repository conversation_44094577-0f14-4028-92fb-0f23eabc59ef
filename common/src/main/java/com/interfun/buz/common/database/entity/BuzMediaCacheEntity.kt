package com.interfun.buz.common.database.entity

import android.os.Parcelable
import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.Index
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import kotlinx.parcelize.Parcelize

/**
 * Author: ChenYouSheng
 * Date: 2024/6/18
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: see 77 toMediaCacheType
 */
enum class MediaCacheType(val value: Int) {
    Unknown(0),
    Image(1),
    Video(2),
    Audio(3),
    Gif(4),
    File(5),
}

enum class ConvType(val value: Int) {
    Unknown(0),
    PrivateChat(1),
    GroupChat(2)
}

/**
 * 媒体缓存记录表，当用户加载图片、视频、音频时添加记录
 */
@Keep
@Parcelize
@Entity(
    tableName = "buz_media_cache",
    primaryKeys = ["userId", "targetId", "convType", "servMsgId", "mediaIndex"],
    indices = [Index(value = ["mediaIndex"])]
)
@TypeConverters(MediaCacheConverter::class)
class BuzMediaCacheEntity : Parcelable {
    var mediaUrl: String = ""
    var mediaIndex: String = ""

    //- 1: Image缓存 - 2: Video缓存 - 3: Audio 音频缓存 -4: Gif 动图 -5: File 文件
    var mediaType: MediaCacheType = MediaCacheType.Unknown
    var updateTime: Long = System.currentTimeMillis()
    var userId: Long = -1
    var targetId: Long = -1

    //- 1 私聊 - 2 群聊
    var convType: ConvType = ConvType.Unknown
    var servMsgId: Long = -1

    override fun toString(): String {
        return "@MediaCacheEntity{mediaUrl=${mediaUrl}," +
                "mediaIndex=${mediaIndex}," +
                "mediaType=${mediaType}," +
                "updateTime=${updateTime}," +
                "userId=${userId}," +
                "targetId=${targetId}," +
                "convType=${convType}," +
                "servMsgId=${servMsgId}}"
    }
}


class MediaCacheConverter {

    @TypeConverter
    fun fromMediaCacheType(type: MediaCacheType): Int {
        return type.value
    }

    @TypeConverter
    fun toMediaCacheType(value: Int): MediaCacheType {
        return when (value) {
            1 -> MediaCacheType.Image
            2 -> MediaCacheType.Video
            3 -> MediaCacheType.Audio
            4 -> MediaCacheType.Gif
            5 -> MediaCacheType.File
            else -> MediaCacheType.Unknown
        }
    }

    @TypeConverter
    fun fromConvType(type: ConvType): Int {
        return type.value
    }

    @TypeConverter
    fun toConvType(value: Int): ConvType {
        return when (value) {
            1 -> ConvType.PrivateChat
            2 -> ConvType.GroupChat
            else -> ConvType.Unknown
        }
    }
}

