package com.interfun.buz.im.constants

object IMErrorCode {

    /**
     * 不在该群组中
     */
    const val GROUP_USER_NOT_IN_GROUP = 11001

    /**
     * 超过撤回时限，不能撤回
     */
    const val RECALL_MESSAGE_OUT_DATE = 10002

    /**
     * 罗马断网
     */
    const val RECALL_MESSAGE_ROME_NET_ERROR = -8888

    /**
     * 转发时，消息不支持转发（比如消息被撤回等）
     */
    const val FORWARD_MESSAGE_NOT_SUPPORT= 40000

    /**
     * https://vocalbeats.sg.larksuite.com/wiki/ROK3w7mMJi9HwEkqKPglAcZVgHc
     * 当次发送消息失败，但是进入自动重发，会通过发消息callback回调onError，
     * 业务可以通过该错误码知道消息发送失败，并且进入自动重试了。该错误不会通过全局回调通知
     */
    fun isMsgStartResend(errorType: Int, errorCode: Int): Boolean {
        return errorType == 13 && errorCode == 1
    }

    /**
     * https://vocalbeats.sg.larksuite.com/wiki/ROK3w7mMJi9HwEkqKPglAcZVgHc
     * 当自动重发的时候，发现超过了N时间，重发任务失败，通过全局回调
     */
    fun isOverMaxRetryTime(errorType: Int, errorCode: Int): Boolean {
        return errorType == 13 && errorCode == 2
    }

    /**
     * https://vocalbeats.sg.larksuite.com/wiki/ROK3w7mMJi9HwEkqKPglAcZVgHc
     * 当自动重发的时候, 发现是视频消息并且没有压缩，通过全局回调错误，但是消息发送状态还是Sending，
     * 业务需要压缩后，调用sendPreparedVideoMessage
     */
    fun isVideoNotCompressed(errorType: Int, errorCode: Int): Boolean {
        return errorType == 13 && errorCode == 3
    }

}
