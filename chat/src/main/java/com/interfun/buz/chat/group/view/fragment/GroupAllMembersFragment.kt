package com.interfun.buz.chat.group.view.fragment

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.buz.idl.group.bean.GroupMember
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.GroupFragmentAllMemberBinding
import com.interfun.buz.chat.group.view.itemdelegate.GroupMemberItemView
import com.interfun.buz.chat.group.viewmodel.GroupInfoAllMembersViewModel
import com.interfun.buz.chat.group.viewmodel.GroupInfoAllMembersViewModel.CustomGroupMember
import com.interfun.buz.chat.group.viewmodel.GroupInfoViewModel
import com.interfun.buz.chat.wt.entity.WTPayloadType
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.database.entity.chat.isBigGroup
import com.interfun.buz.common.eventbus.BackPressEvent
import com.interfun.buz.common.eventbus.BackPressKey
import com.interfun.buz.common.ktx.getName
import com.interfun.buz.common.ktx.isManager
import com.interfun.buz.common.ktx.isOwner
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.widget.dialog.AlertDialogWithPortrait
import com.interfun.buz.common.widget.dialog.bottomlist.CommonBottomListDialog
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.sync.Mutex


/**
 * 群资料页显示全部成员
 */
@AndroidEntryPoint
class GroupAllMembersFragment : BaseBindingFragment<GroupFragmentAllMemberBinding>() {

    companion object {
        const val TAG = "GroupAllMembersFragment"

        fun newInstance(groupInfo: GroupInfoBean) = GroupAllMembersFragment().apply {
            arguments = Bundle()
            arguments?.putParcelable(
                RouterParamKey.Group.KEY_GROUP_INFO,
                groupInfo
            )
        }
    }

    lateinit var mAdapter: MultiTypeAdapter
    private var itemList: MutableList<CustomGroupMember> = mutableListOf()

    private val groupInfoAllMembersViewModel by fragment.viewModels<GroupInfoAllMembersViewModel>()
    private val groupInfoViewModel by fragment.viewModels<GroupInfoViewModel>()


    private var lastClick = SystemClock.elapsedRealtime()
    private var groupInfo: GroupInfoBean? = null
    private val refreshMutex by lazy { Mutex() }
    private var groupMemberNum = 0


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (arguments?.containsKey(RouterParamKey.Group.KEY_GROUP_INFO) == true) {
            groupInfo = arguments?.getParcelable(RouterParamKey.Group.KEY_GROUP_INFO)
            groupMemberNum = groupInfo?.memberNum ?: 0
        }
    }


    override fun initBlock() {
        super.initBlock()
    }


    @SuppressLint("SetTextI18n")
    override fun initView() {
        binding.tvTitle.text = groupInfo?.groupName
        updateGroupMemberNums()

        binding.clRootLayout.setBackgroundColor(R.color.overlay_grey_10.asColor(
            appContext))
//        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftvLeftBack.click {
            back()
        }
        mAdapter = MultiTypeAdapter(itemList).apply {
            register(GroupMemberItemView().apply {
                addOnItemClickListener { holder, item, pos ->
                    isQuickClick {
                        handleClickMember(item.groupMember)
                    }
                }
            })

        }
        binding.clRootLayout.click {  }
        BusUtil.observe<BackPressEvent>(BackPressKey.GROUP_INFO_DIALOG,this){
            back()
        }
        binding.rclContent.apply {
            itemAnimator = null
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            addItemDecoration(LinearSpaceItemDecoration(bottom = 75.dp))
            adapter = mAdapter
        }
        binding.refreshLayout.setEnableRefresh(false)
        // 大群可以加载更多
        binding.refreshLayout.setEnableLoadMore(groupInfo.isBigGroup)
    }

    override fun initData() {
        super.initData()

        if (mAdapter.itemCount == 0) {
            routerServices<ContactsService>().value?.getFriendListAndUpdateDB()
        }

        groupInfoAllMembersViewModel.groupMemberListLiveData.observe(this) {
            log(TAG,"initData: groupMemberListLiveData update list size = ${it.size}")
            mAdapter.asyncSubmitItems(
                fragment.lifecycleScope,
                refreshMutex,
                it.toList(),
                areItemsTheSame = areItemsTheSame,
                areContentsTheSame = areContentsTheSame
            ) {
                itemList.clear()
                itemList.addAll(it)
                notifyRvItemChanged(WTPayloadType.UpdateUserOnlineStatus)
                //偶尔会出现加群后不是定位到列表顶部的情况，这里强制定位到顶部
                if (itemList.isNotEmpty()) {
                    binding.rclContent.layoutManager?.scrollToPosition(0)
                }
                binding.refreshLayout.setEnableLoadMore(groupInfo.isBigGroup)
            }
        }

        groupInfoAllMembersViewModel.groupMemberLoadMoreLiveData.observe(this) { pageBean ->
            logDebug(TAG,"groupMemberLoadMoreLiveData==>new:${pageBean.pageList.size},itemList:${itemList.size}")
            binding.refreshLayout.setEnableLoadMore(pageBean.isLastPage.not())
            if (pageBean.pageList.isNotEmpty()) {
                itemList.clear()
                itemList.addAll(groupInfoAllMembersViewModel.getvalGroupCustomGroupMemberList())
                mAdapter.asyncSubmitItems(
                    fragment.lifecycleScope,
                    refreshMutex,
                    itemList.toList(),
                    areItemsTheSame = areItemsTheSame,
                    areContentsTheSame = areContentsTheSame
                ) {
                    binding.refreshLayout.finishLoadMore()
                }
            } else {
                binding.refreshLayout.finishLoadMore()
            }
        }

        binding.refreshLayout.setOnLoadMoreListener {
            groupInfoAllMembersViewModel.loadMoreGroupMembers()
        }
        startRequestGroupMembersData()
    }

    private fun startRequestGroupMembersData() {
        groupInfo?.let { groupInfoAllMembersViewModel.requestGroupMember(it) }
    }

    private val areItemsTheSame: (Any, Any) -> Boolean = { oldItem, newItem ->
        if (oldItem is CustomGroupMember && newItem is CustomGroupMember) {
            oldItem == newItem && oldItem.backgroundRes == newItem.backgroundRes && oldItem.isShowTopLine == newItem.isShowTopLine
        }
        else {
            oldItem == newItem
        }
    }
    private val areContentsTheSame: (Any, Any) -> Boolean = { oldItem, newItem ->
        if (oldItem is CustomGroupMember && newItem is CustomGroupMember) {
            oldItem == newItem && oldItem.backgroundRes == newItem.backgroundRes && oldItem.isShowTopLine == newItem.isShowTopLine
        }  else {
            oldItem == newItem
        }
    }

    private fun isQuickClick(interval: Long = 500L, block: (() -> Unit)?) {
        val now = SystemClock.elapsedRealtime()
        if (now - lastClick > interval) {
            lastClick = now
            block?.invoke()
        }
    }

    private fun back(){
        removeSelf()
    }

    private fun removeSelf() {
    parentFragmentManager.beginTransaction()
        .setCustomAnimations(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
        .remove(this)
        .commitAllowingStateLoss()

    }

    private fun handleClickMember(groupMember: GroupMember) {
        if (groupMember.userInfo.userId != UserSessionManager.getSessionUid()) {
            // not myself
            if (groupInfo?.isOwner() == true) {
                showSelectDialog(groupMember)
            } else if (groupInfo?.isManager() == true) {
                if (groupMember.isManager() || groupMember.isOwner()) {
                    doGotoMemberPage(groupMember)
                } else {
                    showSelectDialog(groupMember)
                }
            } else if (groupMember.isRobot()) {
                showSelectDialog(groupMember)
            } else {
                doGotoMemberPage(groupMember)
            }
        }
    }

    private fun showSelectDialog(member: GroupMember) {
        CommonBottomListDialog.build {
            addDefaultOption(R.string.ic_account, R.string.profile) {
                doGotoMemberPage(member)
            }
            addWarningOption(R.string.ic_blocklist, R.string.remove) {
                doLeaveGroup(member)
            }
        }.showDialog(activity)
    }

    private fun doLeaveGroup(member: GroupMember) {
        if (context.isNull()) return
        val userInfo = member.userInfo
        if (groupInfo.isNull() || groupInfo?.groupId.isNull()|| userInfo.userId.isNull()) return
        val dialog = AlertDialogWithPortrait(context!!)
        dialog.b.tvTitle.text = ResUtil.getString(
            R.string.chat_remove_sb_from_group,
            member.userInfo.getName(),
            groupInfo?.groupName ?: ""
        )
        dialog.b.ivPortrait.setPortrait(userInfo.portrait)
        val isRoBot = member.isRobot()
        if (isRoBot) {
            dialog.b.tvTips.text = ""
        }else{
            dialog.b.tvTips.text = ResUtil.getString(R.string.buz_will_not_be_notified_tips,member.userInfo.getName())
        }
        dialog.b.btnPositive.setText(R.string.remove.asString())
        dialog.b.btnPositive.click {
            groupInfoAllMembersViewModel.kickOutGroupMember(
                groupInfo!!.groupId,
                member.userInfo.userId!!
            ) {
                viewLifecycleScope.launchMain {
                    groupInfoAllMembersViewModel.removeGroupMember(member)
                    decreaseGroupMemberNum()
                }
            }
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun doGotoMemberPage(member: GroupMember) {
        routerServices<ContactsService>().value?.getProfileDialog(
            userId = member.userInfo.userId ?: return,
            source = FriendApplySource.group /* group */,
            businessId = groupInfo?.groupId?.toString(),
            trackerSource = 2,//ProfileSource.GROUP,
            isFromGroup = true
        )?.showDialog(activity)
    }


    private fun notifyRvItemChanged(payload: WTPayloadType) {
        binding.rclContent.apply {
            repeat(childCount) { index ->
                getChildAt(index)?.let { child ->
                    val pos = getChildAdapterPosition(child)
                    mAdapter.apply {
                        if (pos in 0 until itemCount) {
                            notifyItemChanged(pos, payload)
                        }
                    }
                }
            }
        }
    }

    private fun updateGroupMemberNums() {
        binding.tvMemberCount.text = groupMemberNum.toString()
    }

    private fun decreaseGroupMemberNum() {
        groupMemberNum--
        updateGroupMemberNums()
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    inner class LinearSpaceItemDecoration(val bottom: Int = 0) :
        RecyclerView.ItemDecoration() {

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val position = parent.getChildAdapterPosition(view)
            val size = parent.adapter?.itemCount ?: 0
            if (position == 0 && position == size - 1) {
                outRect.bottom = 0
                return
            }
            when (position) {
                size - 1 -> {
                    if (groupInfoAllMembersViewModel.isLastPage) {
                        outRect.bottom = bottom
                    } else {
                        outRect.bottom = 0
                    }
                }
                else -> {
                    outRect.bottom = 0
                }
            }
        }
    }
}