package com.interfun.buz.common.bean.push

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.withMainContext
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.onair.standard.IGlobalOnAirController
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2024/3/6
 */
class PushOnAir : PushDataBase() {


    val TAG = "PushOnAir"

    /** op 6004 sendTimestamp 1724228203921 data {"dataVersion":6,"businessType":1,"channelId":5400212516158663295} */
    override fun handlePushEvent(op: Int, sendTimestamp: Long, data: JSONObject?) {

        if (data == null) {
            return
        }
        logInfo(TAG,"handlePushEvent op ${op} sendTimestamp ${sendTimestamp} data ${data}" )
//        GlobalScope.launch {
//            withMainContext {
//                routerServices<IGlobalOnAirController>().value?.onReceivePush(
//                    op,
//                    sendTimestamp,
//                    data
//                )
//            }
//        }

    }
}