package com.interfun.buz.user.view.activity

import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_UPDATE_PROFILE
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.UpdateProfileFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * Include：
 * [com.interfun.buz.user.view.fragment.UpdateProfileFragment]
 */
@Route(path = PATH_USER_ACTIVITY_UPDATE_PROFILE)
@AndroidEntryPoint
class UpdateProfileActivity : SimpleContainerActivity() {
    private val updateProfileFragmentTag = "UpdateProfileFragment"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
                overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
            }
        })

        val savedFragment = supportFragmentManager.findFragmentByTag(updateProfileFragmentTag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, UpdateProfileFragment(), updateProfileFragmentTag)
                .commit()
        }
    }

}