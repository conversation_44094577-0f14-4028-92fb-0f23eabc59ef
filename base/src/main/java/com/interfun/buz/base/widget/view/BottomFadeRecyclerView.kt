package com.interfun.buz.base.widget.view

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView

/**
 * <AUTHOR>
 * @date 2022/8/25
 * @desc
 */
class BottomFadeRecyclerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : RecyclerView(context, attrs) {

    override fun getTopFadingEdgeStrength(): Float {
        // 顶部不显示 fade 效果
        return 0f
    }
}