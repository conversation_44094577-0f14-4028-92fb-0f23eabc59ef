package com.interfun.buz.dokit

import android.app.Activity
import android.app.AlertDialog
import android.view.LayoutInflater
import com.interfun.buz.R
import com.interfun.buz.base.ktx.click
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.databinding.AppRouteTestLayoutBinding

/**
 * description: 跳转到Demo页
 */
class RouteTestKit : BaseBusinessKit(R.string.kit_widget_route_test) {

    override fun handleOnClick(activity: Activity) {
        val routeTestLayoutBinding = AppRouteTestLayoutBinding.inflate(LayoutInflater.from(activity))
        val dialog = AlertDialog.Builder(activity)
            .setView(routeTestLayoutBinding.root)
            .create()
        dialog.show()
        routeTestLayoutBinding.confirmButton.click {
            val routeJson = routeTestLayoutBinding.editText.text.toString()
            RouterManager.handle(activity,routeJson)
            dialog.dismiss()
        }
    }
}