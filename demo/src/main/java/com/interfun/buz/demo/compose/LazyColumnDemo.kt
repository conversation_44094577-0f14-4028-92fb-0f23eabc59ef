package com.example.dynamiclazycolumndemo

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.interfun.buz.compose.base.BaseComposeActivity
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.demo.R
import kotlin.random.Random

// Simple data class for demo items
data class DemoItem(val id: Int, val title: String, val description: String)

class TestActivity : BaseComposeActivity() {
    @Composable
    override fun ComposeContent() {
        DynamicPanelDemo()
    }
}

@Preview
@Composable
fun DynamicPanelDemo() {
    val items = remember {
        mutableStateListOf(
            DemoItem(1, "Item 1", "This is the first item"),
            DemoItem(2, "Item 2", "Another interesting item"),
            DemoItem(3, "Item 3", "This is the third item in the list")
        )
    }

    var isPanelVisible by remember { mutableStateOf(false) }
    val lazyListState = rememberLazyListState()

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // Control buttons at the top
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(onClick = { isPanelVisible = !isPanelVisible }) {
                    Text(text = if (isPanelVisible) "Close Panel" else "Open Panel")
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(
                    onClick = {
                        if (items.isNotEmpty()) {
                            val randomIndex = Random.nextInt(items.size)
                            val newItem = DemoItem(
                                items.maxOf { it.id } + 1,
                                "Item ${items.maxOf { it.id } + 1}",
                                "This is a new random item"
                            )
                            items.add(randomIndex, newItem)
                        } else {
                            items.add(
                                DemoItem(
                                    1,
                                    "Item 1",
                                    "This is the first item"
                                )
                            )
                        }
                    }
                ) {
                    Text("+1")
                }

                Button(
                    onClick = {
                        if (items.isNotEmpty()) {
                            items.removeAt(items.size - 1)
                        }
                    },
                    enabled = items.isNotEmpty()
                ) {
                    Text("-last")
                }

                Button(
                    onClick = {
                        if (items.size > 1) {
                            val randomIndex = Random.nextInt(items.size)
                            val itemToKeep = items[randomIndex]
                            items.clear()
                            items.add(itemToKeep)
                        }
                    },
                    enabled = items.size > 1
                ) {
                    Text("Left 1")
                }

                Button(
                    onClick = {
                        if (items.size >= 2) {
                            repeat(2) {
                                val randomIndex = Random.nextInt(items.size)
                                items.removeAt(randomIndex)
                            }
                        }
                    },
                    enabled = items.size >= 2
                ) {
                    Text("-2")
                }
            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(450.dp)
                .align(Alignment.BottomCenter)
                .background(color = R.color.basic_primary_20.asColor())
        ) {
            // Dynamic Panel
            AnimatedVisibility(
                visible = isPanelVisible,
                modifier = Modifier.align(Alignment.BottomCenter),
                enter = slideInVertically(initialOffsetY = { it }) + fadeIn(),
                exit = slideOutVertically(targetOffsetY = { it }) + fadeOut()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                        .wrapContentHeight(align = Alignment.Bottom)
                        .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                        .animateContentSize()
                ) {
                    LazyColumn(
                        state = lazyListState,
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .padding(16.dp)
                    ) {
                        items(
                            items = items,
                            key = { it.id }
                        ) { item ->
                            ItemCard(
                                item = item,
                                modifier = Modifier.animateItem()
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ItemCard(item: DemoItem, modifier: Modifier = Modifier) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = item.title,
                fontSize = 18.sp,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = item.description,
                fontSize = 14.sp
            )
        }
    }
}