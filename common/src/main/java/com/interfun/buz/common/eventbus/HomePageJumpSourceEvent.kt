package com.interfun.buz.common.eventbus

import com.interfun.buz.base.utils.BusUtil

/**
 * Author: ChenYouSheng
 * Date: 2024/1/4
 * Email: chenyoush<PERSON>@lizhi.fm
 * Desc: see the source [JumpToChatHomeSource]
 */
class HomePageJumpSourceEvent(
    val source: Int,
    val wtTargetId: Long,
    val exclusiveId: String? = null
) : BaseEvent() {
    companion object {
        fun post(source: Int, wtTargetId: Long, exclusiveId: String?) {
            BusUtil.post(HomePageJumpSourceEvent(source, wtTargetId, exclusiveId))
        }
    }
}