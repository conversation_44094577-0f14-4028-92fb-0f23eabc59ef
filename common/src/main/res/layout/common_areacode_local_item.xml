<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/overlay_grey_10">

    <TextView
        android:id="@+id/tvAreaHead"
        style="@style/body"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="13dp"
        android:paddingBottom="13dp"
        android:text="@string/current_country_and_region"
        android:textColor="@color/text_white_secondary"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="@id/areacodeItem"
        app:layout_constraintEnd_toEndOf="@id/areacodeItem"
        app:layout_constraintTop_toTopOf="@id/areacodeItem"
        app:layout_constraintBottom_toBottomOf="@id/areacodeItem"
        android:background="@drawable/common_rect_overlay_white_4_radius_8"/>


    <include
        android:id="@+id/areacodeItem"
        layout="@layout/common_areacode_item"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvAreaHead" />

    <TextView
        android:id="@+id/tvAreaFoot"
        style="@style/body"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="13dp"
        android:paddingBottom="13dp"
        android:text="@string/all"
        android:textColor="@color/text_white_secondary"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/areacodeItem" />

</androidx.constraintlayout.widget.ConstraintLayout>