@file:Suppress("unused")

package com.interfun.buz.base.ktx

import android.content.Context
import com.interfun.buz.base.utils.ScreenUtil

/**
 * 优先传入当前context，确保能获取到正确的window信息
 */

val Context?.screenWidth: Int get() = ScreenUtil.getScreenWidth(this ?: appContext)

val Context?.screenWidthReal: Int get() = ScreenUtil.getScreenWidthReal(this ?: appContext)

val Context?.screenHeight: Int get() = ScreenUtil.getScreenHeight(this ?: appContext)

val Context?.screenHeightReal: Int get() = ScreenUtil.getScreenHeightReal(this ?: appContext)

val Context?.statusBarHeight: Int get() = ScreenUtil.getStatusBarHeight(this ?: appContext)

val Context?.navigationBarHeight: Int get() = ScreenUtil.getNavigationHeight(this ?: appContext)

val Context?.screenHeightWithoutTopBottom: Int get() = screenHeightReal - statusBarHeight - navigationBarHeight

val Context?.deviceHeightWithoutTopBottom: Int get() = deviceHeight - statusBarHeight - navigationBarHeight

val Context?.deviceWidth: Int get() = ScreenUtil.getDeviceWidth(this ?: appContext)

val Context?.deviceHeight: Int get() = ScreenUtil.getDeviceHeight(this ?: appContext)

val deviceWidth: Int get() = ScreenUtil.getDeviceWidth()

val deviceHeight: Int get() = ScreenUtil.getDeviceHeight()

@Deprecated(
    message = "请传入Context",
    replaceWith = ReplaceWith("Context?.screenWidth")
)
val screenWidth: Int get() = ScreenUtil.getScreenWidth()

@Deprecated(
    message = "请传入Context",
    replaceWith = ReplaceWith("Context?.screenWidthReal")
)
val screenWidthReal: Int get() = ScreenUtil.getScreenWidthReal()

@Deprecated(
    message = "请传入Context",
    replaceWith = ReplaceWith("Context?.screenHeight")
)
val screenHeight: Int get() = ScreenUtil.getScreenHeight()

@Deprecated(
    message = "请传入Context",
    replaceWith = ReplaceWith("Context?.screenHeightReal")
)
val screenHeightReal: Int get() = ScreenUtil.getScreenHeightReal()

@Deprecated(
    message = "请传入Context",
    replaceWith = ReplaceWith("Context?.statusBarHeight")
)
val statusBarHeight: Int get() = ScreenUtil.getStatusBarHeight()

@Deprecated(
    message = "请传入Context",
    replaceWith = ReplaceWith("Context?.navigationBarHeight")
)
val navigationBarHeight: Int get() = ScreenUtil.getNavigationHeight()

@Deprecated(
    message = "请传入Context",
    replaceWith = ReplaceWith("Context?.screenHeightWithoutTopBottom")
)
val screenHeightWithoutTopBottom: Int get() = screenHeightReal - statusBarHeight - navigationBarHeight